import { cn } from "@/lib/utils";
import { Button } from "../ui/button";

interface CustomButtonProps {
  value: string;
  onClick?: any;
  className?: string;
  loading?: boolean;
  icon?: React.ReactNode;
  type?: "button" | "submit" | "reset";
  disabled?: boolean; // added
}

export default function CustomButton({ props }: { props?: CustomButtonProps }) {
  return (
    <Button
      className={cn(
        "flex w-full gap-1 text-sm justify-center items-center transition-all duration-150 text-white px-4 py-2",
        props?.loading
          ? "bg-voxa-neutral-50 dark:bg-voxa-neutral-900"
          : "bg-voxa-neutral-500 dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-800active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950",
        props?.className
      )}
      disabled={props?.disabled ?? props?.loading ?? false}
      onClick={props?.onClick}
      type={props?.type || "button"}
    >
      {props?.value}
      {props?.icon}
    </Button>
  );
}
