"use client";

import { Wall<PERSON>, Setting<PERSON> } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import BillingSettingsDialog from "./BillingSettingsDialog";

interface CurrentBalanceProps {
  balance: number;
  isLoading: boolean;
}

export default function CurrentBalance({
  balance,
  isLoading,
}: CurrentBalanceProps) {
  const [settingsOpen, setSettingsOpen] = useState(false);

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-3 w-full justify-between items-center bg-sidebar border-sidebar-border border rounded-lg p-3 sm:p-5 gap-3">
        <div className="sm:col-span-2 flex flex-col gap-2 w-full">
          <div className="flex gap-2 items-center">
            <Wallet className="w-5 h-5 text-voxa-teal-600" />
            <h3 className="text-lg sm:text-xl font-semibold text-foreground/70">
              Current Account Balance
            </h3>
          </div>
          {isLoading ? (
            <div className="h-8 w-32 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div>
          ) : (
            <h1 className="max-sm:text-center text-2xl sm:text-4xl font-bold text-voxa-teal-600">
              {balance?.toFixed(2)} €
            </h1>
          )}
        </div>
        {/* Settings icon button */}
        <div className="flex justify-end items-start sm:items-center">
          <Button
            variant="outline"
            size="sm"
            aria-label="Billing Settings"
            onClick={() => setSettingsOpen(true)}
            className="flex items-center gap-1 border border-voxa-neutral-200 rounded-md px-2 py-1 text-voxa-neutral-400 hover:text-voxa-teal-600"
          >
            <Settings className="w-5 h-5" />
            <span className="font-medium text-xs">Settings</span>
          </Button>
        </div>
        {/* Billing Settings Dialog */}
        <BillingSettingsDialog
          open={settingsOpen}
          onOpenChange={setSettingsOpen}
        />
      </div>
    </>
  );
}
