import { ClockIcon, CopyIcon } from "lucide-react";
import { handleCopyText } from "@/lib/Strings/Copy";
import {
  convertSecondsToMinutes,
  getTimeFromTimestamp,
} from "@/lib/Strings/DateFormat";
import { Card } from "@/components/ui/card";
import { JoinCallDialog } from "@/components/dialogs/JoinCall";
import { HangUp } from "@/components/dialogs/HangUp";

interface ConversationType {
  _id: string;
  callSid: string;
  client_name: string;
  callDuration: number | string;
  timestamp: string;
  status?: string;
  type?: string;
  direction?: string;
  voicemail_detected?: boolean;
}

const ConversationCard = ({
  conversation,
  goalID,
}: {
  conversation: ConversationType;
  goalID?: string;
}) => {
  let duration = conversation.callDuration;

  if (goalID) {
    const nowUTC = Date.now();
    const conversationTimeUTC = new Date(conversation.timestamp).getTime();
    duration = Math.floor((nowUTC - conversationTimeUTC) / 1000);
  }

  return (
    <Card className="flex flex-col gap-3 p-3.5 w-full hover:scale-105 hover:shadow-xl">
      {/* ID */}
      <div className="flex items-center justify-between">
        <div className="text-xs text-voxa-primary font-normal flex gap-[4.25rem] max-w-[90%]">
          <p className="text-sm font-semibold">ID:</p>
          <p className="truncate">{conversation._id}</p>
        </div>
        <CopyIcon
          onClick={() => handleCopyText(conversation._id)}
          className="text-voxa-primary w-4 h-4 cursor-pointer hover:text-voxa-secondary transition-colors duration-200"
        />
      </div>

      {/* Call SID */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-7 w-full max-w-[90%]">
          <div className="text-sm font-semibold text-voxa-secondary">
            Call SID:
          </div>
          <div className="flex-1 text-xs truncate">{conversation.callSid}</div>
        </div>
        <CopyIcon
          onClick={() => handleCopyText(conversation.callSid)}
          className="text-voxa-primary w-4 h-4 cursor-pointer hover:text-voxa-secondary transition-colors duration-200"
        />
      </div>

      {/* Client + Duration */}
      <div className="flex items-center gap-1">
        <div className="flex items-center gap-10 w-full">
          <span className="text-sm font-semibold text-voxa-primary max-w-[75%]">
            Client:
          </span>
          <span title={conversation.client_name} className="text-xs truncate">
            {conversation.client_name}
          </span>
        </div>
        <div className="flex items-center gap-1 w-fit">
          <ClockIcon className="w-4 h-4 text-voxa-secondary" />
          <div className="text-xs">
            {convertSecondsToMinutes(duration?.toString() || "0")}
          </div>
        </div>
      </div>

      {/* Initiated At DateTime */}
      <div className="flex items-center gap-1">
        <div className="text-sm font-semibold text-voxa-secondary">
          Initiated At:
        </div>
        <div className="text-xs">
          {getTimeFromTimestamp(conversation.timestamp)}
        </div>
      </div>

      {/* Take Over Button */}
      {goalID && (
        <div className="w-full flex gap-2">
          <JoinCallDialog callsid={conversation.callSid} goalid={goalID} />
          <HangUp callsid={conversation.callSid} goalid={goalID} />
        </div>
      )}
    </Card>
  );
};

export default ConversationCard;
