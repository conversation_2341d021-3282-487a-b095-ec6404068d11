"use server";

import dbConnect from "@/lib/mongodb";
import Webhook from "@/models/Webhook";

import { getEntrepriseByAdminID } from "./Entreprise";
import mongoose from "mongoose";
import { WebhookType } from "@/redux/BusinessDashboard/subSlices/WebhookSlice";
import crypto from "crypto";

function generateSecretKey(): string {
  return crypto.randomBytes(20).toString("hex");
}

export async function GetWebhooks(): Promise<{
  success: boolean;
  webhook?: WebhookType;
  error?: string;
}> {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success)
    return { success: false, error: entrepriseResponse.error };
  await dbConnect();
  const entrepriseID = entrepriseResponse.entreprise._id;
  try {
    let webhook = await Webhook.findOne({
      entreprise_id: entrepriseID,
    }).lean<WebhookType>();

    // If no webhook exists for this entreprise, create one with a secret key
    if (!webhook) {
      webhook = await Webhook.create({
        entreprise_id: entrepriseID,
        webhook_secret_key: generateSecretKey(),
        enabled: true,
        events: {
          "callback-status": { method: "POST", path: "" },
          "recording-status": { method: "POST", path: "" },
          "make-call": { method: "POST", path: "" },
        },
        logs_events: [],
      });
    }

    // Convert MongoDB objects to strings
    const webhookWithStringIds = {
      ...webhook,
      _id: webhook!._id.toString(),
      entreprise_id: webhook!.entreprise_id?.toString(),
    };

    return { success: true, webhook: webhookWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function GetWebhookById(id: string) {
  await dbConnect();
  try {
    const webhook = await Webhook.findById(id).lean<WebhookType>();
    if (!webhook) return { success: false, error: "Webhook not found" };
    const webhookWithStringIds = {
      ...webhook,
      _id: webhook._id.toString(),
      entreprise_id: webhook.entreprise_id?.toString(),
    };
    return { success: true, webhook: webhookWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function UpdateWebhook(id: string, update: Partial<WebhookType>) {
  await dbConnect();
  try {
    const webhook = await Webhook.findByIdAndUpdate(id, update, {
      new: true,
    }).lean<WebhookType>();
    if (!webhook) return { success: false, error: "Webhook not found" };
    const webhookWithStringIds = {
      ...webhook,
      _id: webhook._id.toString(),
      entreprise_id: webhook.entreprise_id?.toString(),
    };
    return { success: true, webhook: webhookWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function ResetWebhookSecretKey() {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success)
    return { success: false, error: entrepriseResponse.error };
  await dbConnect();
  const entrepriseID = entrepriseResponse.entreprise._id;
  try {
    const newSecretKey = generateSecretKey();
    const webhook = await Webhook.findOneAndUpdate(
      { entreprise_id: entrepriseID },
      { $set: { webhook_secret_key: newSecretKey } },
      { new: true }
    ).lean<WebhookType>();

    if (!webhook) return { success: false, error: "Webhook not found" };

    const webhookWithStringIds = {
      ...webhook,
      _id: webhook._id.toString(),
      entreprise_id: webhook.entreprise_id?.toString(),
    };

    return { success: true, webhook: webhookWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function SetWebhookEnabled(enabled: boolean) {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success)
    return { success: false, error: entrepriseResponse.error };
  await dbConnect();
  const entrepriseID = entrepriseResponse.entreprise._id;
  try {
    const webhook = await Webhook.findOneAndUpdate(
      { entreprise_id: entrepriseID },
      { $set: { enabled } },
      { new: true }
    ).lean<WebhookType>();

    if (!webhook) return { success: false, error: "Webhook not found" };

    const webhookWithStringIds = {
      ...webhook,
      _id: webhook._id.toString(),
      entreprise_id: webhook.entreprise_id?.toString(),
    };

    return { success: true, webhook: webhookWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function UpdateWebhookEvent({
  eventName,
  method,
  path,
}: {
  eventName: string;
  method: string;
  path: string;
}) {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success)
    return { success: false, error: entrepriseResponse.error };
  await dbConnect();
  const entrepriseID = entrepriseResponse.entreprise._id;
  try {
    // Create update object with the specific event
    const updateField = `events.${eventName}`;
    const update = {
      $set: {
        [updateField]: { method, path },
      },
    };

    const webhook = await Webhook.findOneAndUpdate(
      { entreprise_id: entrepriseID },
      update,
      { new: true, upsert: true }
    ).lean<WebhookType>();

    const webhookWithStringIds = {
      ...webhook,
      _id: webhook!._id.toString(),
      entreprise_id: webhook!.entreprise_id?.toString(),
    };

    return { success: true, webhook: webhookWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function DeleteWebhookEvent(eventName: string) {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success)
    return { success: false, error: entrepriseResponse.error };
  await dbConnect();
  const entrepriseID = entrepriseResponse.entreprise._id;
  try {
    const updateField = `events.${eventName}`;
    const update = {
      $unset: { [updateField]: "" },
    };

    const webhook = await Webhook.findOneAndUpdate(
      { entreprise_id: entrepriseID },
      update,
      { new: true }
    ).lean<WebhookType>();

    if (!webhook) return { success: false, error: "Webhook not found" };

    const webhookWithStringIds = {
      ...webhook,
      _id: webhook._id.toString(),
      entreprise_id: webhook.entreprise_id?.toString(),
    };

    return { success: true, webhook: webhookWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function AddWebhookLogEvent(logEvent: {
  url: string;
  method: string;
  response_code: string;
  timestamp: string;
  header: string;
  body: string;
}) {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success)
    return { success: false, error: entrepriseResponse.error };
  await dbConnect();
  const entrepriseID = entrepriseResponse.entreprise._id;
  try {
    const webhook = await Webhook.findOneAndUpdate(
      { entreprise_id: entrepriseID },
      { $push: { logs_events: logEvent } },
      { new: true }
    ).lean<WebhookType>();

    if (!webhook) return { success: false, error: "Webhook not found" };

    const webhookWithStringIds = {
      ...webhook,
      _id: webhook._id.toString(),
      entreprise_id: webhook.entreprise_id?.toString(),
    };

    return { success: true, webhook: webhookWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function ClearWebhookLogs() {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success)
    return { success: false, error: entrepriseResponse.error };
  await dbConnect();
  const entrepriseID = entrepriseResponse.entreprise._id;
  try {
    const webhook = await Webhook.findOneAndUpdate(
      { entreprise_id: entrepriseID },
      { $set: { logs_events: [] } },
      { new: true }
    ).lean<WebhookType>();

    if (!webhook) return { success: false, error: "Webhook not found" };

    const webhookWithStringIds = {
      ...webhook,
      _id: webhook._id.toString(),
      entreprise_id: webhook.entreprise_id?.toString(),
    };

    return { success: true, webhook: webhookWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function UpdateWebhookEvents(events: WebhookType["events"]) {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success)
    return { success: false, error: entrepriseResponse.error };
  await dbConnect();
  const entrepriseID = entrepriseResponse.entreprise._id;
  try {
    const webhook = await Webhook.findOneAndUpdate(
      { entreprise_id: entrepriseID },
      { $set: { events } },
      { new: true }
    ).lean<WebhookType>();

    if (!webhook) return { success: false, error: "Webhook not found" };

    const webhookWithStringIds = {
      ...webhook,
      _id: webhook._id.toString(),
      entreprise_id: webhook.entreprise_id?.toString(),
    };

    return { success: true, webhook: webhookWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
