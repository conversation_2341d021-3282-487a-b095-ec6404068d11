{"openapi": "3.0.0", "info": {"title": "Echoparrot API Documentation", "description": "API documentation for Echoparrot, including authentication, goals management, and more.", "version": "1.0.0"}, "servers": [{"url": "http://localhost:3000/api"}], "tags": [{"name": "Authentication", "description": "Endpoints related to user authentication"}], "paths": {"/auth/login_service": {"post": {"tags": ["Authentication"], "summary": "Authenticate a user", "requestBody": {"description": "Email and password of the user", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "mypassword123"}}, "required": ["email", "password"]}}}}, "responses": {"200": {"description": "Authentication successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Authentication successful."}, "token": {"type": "string", "example": "eyJraWQiOiJLT..."}}}}}}, "400": {"description": "Invalid input or incorrect credentials", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Incorrect email or password."}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Internal server error."}}}}}}}}}, "/auth/signup_service": {"post": {"tags": ["Authentication"], "summary": "Register a new entreprise admin", "requestBody": {"description": "Entreprise admin registration details", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "Entreprise X"}, "email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "securePassword123"}}, "required": ["name", "email", "password"]}}}}, "responses": {"201": {"description": "Entreprise admin account successfully created", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Entreprise account created successfully."}, "user": {"type": "object", "properties": {"id": {"type": "string", "example": "64ecf0c2a1b92d9a5a59f123"}, "name": {"type": "string", "example": "Entreprise X"}, "email": {"type": "string", "example": "<EMAIL>"}}}}}}}}, "400": {"description": "Missing fields or email already in use", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "This email is already in use."}}}}}}, "500": {"description": "Server error during entreprise account creation", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "An error occurred while creating the entreprise account."}}}}}}}}}, "/calls/get_call_logs": {"get": {"tags": ["Calls"], "summary": "Get call logs with filters", "description": "Retrieves filtered call logs using query parameters passed in the URL.", "parameters": [{"name": "CallSID", "in": "query", "schema": {"type": "string"}}, {"name": "conversationID", "in": "query", "schema": {"type": "string"}}, {"name": "recepientName", "in": "query", "schema": {"type": "string"}}, {"name": "recepientPhone", "in": "query", "schema": {"type": "string"}}, {"name": "dateFromString", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "dateToString", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "incoming", "in": "query", "schema": {"type": "boolean"}}, {"name": "outgoing", "in": "query", "schema": {"type": "boolean"}}, {"name": "missed", "in": "query", "schema": {"type": "boolean"}}, {"name": "whatsapp", "in": "query", "schema": {"type": "boolean"}}, {"name": "sms", "in": "query", "schema": {"type": "boolean"}}, {"name": "call", "in": "query", "schema": {"type": "boolean"}}, {"name": "meet", "in": "query", "schema": {"type": "boolean"}}, {"name": "itemsPerPage", "in": "query", "schema": {"type": "integer"}}, {"name": "skip", "in": "query", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Call logs retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"conversations": {"type": "array", "items": {"type": "object"}}, "totalCount": {"type": "integer"}, "loadedCount": {"type": "integer"}}}}}}, "400": {"description": "Invalid query parameters", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid query parameter."}}}}}}, "401": {"description": "Unauthorized - invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid or missing token."}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Internal server error."}}}}}}}}}, "/api/ep_group/create_ep_group": {"post": {"summary": "Create a new client group", "tags": ["Groups"], "description": "Creates a group from a list of phone numbers. Requires a valid JWT token in cookies (key: plugin_id_token).", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"numbers": {"type": "array", "items": {"type": "object", "properties": {"phone": {"type": "string", "example": "+21612345678"}, "name": {"type": "string", "example": "<PERSON>"}}, "required": ["phone"]}, "description": "Array of phone number objects"}, "group_name": {"type": "string", "example": "Sales Team"}}, "required": ["numbers"]}}}}, "responses": {"200": {"description": "Group created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Group created successfully"}, "group": {"$ref": "#/components/schemas/Group"}}}}}}, "400": {"description": "Invalid request data", "content": {"application/json": {"examples": {"Malformed JSON": {"value": {"error": "Malformed request. Please send valid JSON body."}}, "Missing Numbers": {"value": {"error": "At least one number is required."}}, "Invalid Entry": {"value": {"error": "Invalid phone number or name format in the list."}}}}}}, "401": {"description": "Unauthorized or invalid token", "content": {"application/json": {"examples": {"No Token": {"value": {"error": "Unauthorized"}}, "Invalid Token": {"value": {"error": "Invalid or expired token.", "detail": "The token is not valid JWT."}}}}}}, "404": {"description": "User or enterprise not found", "content": {"application/json": {"examples": {"User Not Found": {"value": {"error": "User not found."}}, "Enterprise Not Found": {"value": {"error": "Enterprise not found."}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"example": {"error": "Internal server error."}}}}}}}, "/api/ep_groups/get_groups": {"get": {"tags": ["Groups"], "summary": "Get groups by entreprise", "description": "Returns all groups associated with the entreprise of the authenticated admin user.", "security": [{"cookieAuth": []}], "responses": {"200": {"description": "Groups fetched successfully", "content": {"application/json": {"example": {"groups": [{"_id": "64f8c8ea379fd3c4f8b6c8f3", "name": "Sales Team", "members": [{"_id": "64f8c91f379fd3c4f8b6c8f4", "email": "<EMAIL>", "name": "<PERSON>"}], "entreprise": "64f8c8d1379fd3c4f8b6c8f2"}]}}}}, "401": {"description": "Unauthorized - Token missing, invalid or expired", "content": {"application/json": {"examples": {"noToken": {"value": {"error": "Unauthorized"}}, "invalidToken": {"value": {"error": "Invalid or expired token.", "detail": "Token signature is invalid."}}}}}}, "404": {"description": "Not Found - User, entreprise or groups not found", "content": {"application/json": {"examples": {"userNotFound": {"value": {"error": "User not found."}}, "entrepriseNotFound": {"value": {"error": "Entreprise not found."}}, "groupsNotFound": {"value": {"error": "No groups found."}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"example": {"error": "Internal server error."}}}}}}}, "/api/goals/get_goals": {"get": {"tags": ["Goals"], "summary": "Get all goals for an authenticated user's entreprise", "description": "Retrieves all goals for the authenticated user's entreprise. If `with_details=true` is passed, it returns full goal details. Otherwise, only the goal name and ID are returned.", "parameters": [{"name": "with_details", "in": "query", "required": false, "schema": {"type": "boolean"}, "description": "Set to true to include full goal details"}], "responses": {"200": {"description": "Goals fetched successfully", "content": {"application/json": {"example": {"success": true, "goals": [{"_id": "goal_id", "name": "Goal name"}]}}}}, "401": {"description": "Unauthorized - Token missing or invalid", "content": {"application/json": {"example": {"error": "Invalid or expired token.", "detail": "Token is not valid."}}}}, "404": {"description": "Entreprise not found", "content": {"application/json": {"example": {"error": "Entreprise not found."}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"example": {"error": "Internal server error."}}}}}}}, "/api/goals/outbound_calls_goal/create_goal": {"post": {"tags": ["Goals"], "summary": "Create a new goal", "description": "Creates a goal for the authenticated user's entreprise using provided details.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "assistantID": {"type": "string"}, "prompt": {"type": "string"}, "country": {"type": "string"}, "numberID": {"type": "string"}, "IncomingSelected": {"type": "boolean"}, "goalContext": {"type": "string"}, "groupid": {"type": "string"}, "NumberRetries": {"type": "integer"}, "ranges": {"type": "array", "items": {"type": "object"}}, "ForwardToNumber": {"type": "string"}, "incoming_call_transfer_on": {"type": "boolean"}, "messages_on_missed_call": {"type": "boolean"}, "message_on_missed_call_content": {"type": "string"}, "voicemail_drop_type": {"type": "string"}, "voicemail_drop_content": {"type": "string"}, "goal_assistant_name_for_male": {"type": "string"}, "goal_assistant_name_for_female": {"type": "string"}, "audio_blob": {"type": "string", "format": "binary"}, "audioDuration": {"type": "number"}, "pronounce_client_name_enabled": {"type": "boolean"}, "pronounce_client_honorific_enabled": {"type": "boolean"}, "duration_between_calls": {"type": "number"}, "ringing_duration": {"type": "number"}, "male_voice": {"type": "string"}, "female_voice": {"type": "string"}, "human_introduction_audio_blob": {"type": "string", "format": "binary"}, "human_introduction_audio_duration": {"type": "number"}, "human_introduction_enabled": {"type": "boolean"}}, "required": ["name", "assistantID", "prompt"]}, "example": {"name": "Lead Qualification", "assistantID": "asst_12345", "prompt": "Ask the client about their budget.", "country": "US", "numberID": "num_12345", "IncomingSelected": true, "goalContext": "qualifying new leads", "groupid": "group_456", "NumberRetries": 3, "ranges": [], "ForwardToNumber": "+11234567890", "incoming_call_transfer_on": true, "messages_on_missed_call": true, "message_on_missed_call_content": "Sorry we missed your call.", "voicemail_drop_type": "audio", "voicemail_drop_content": "voicemail.mp3", "goal_assistant_name_for_male": "<PERSON>", "goal_assistant_name_for_female": "<PERSON>", "audio_blob": "base64AudioData", "audioDuration": 10.5, "pronounce_client_name_enabled": true, "pronounce_client_honorific_enabled": true, "duration_between_calls": 60, "ringing_duration": 20, "male_voice": "en-US-<PERSON><PERSON><PERSON><PERSON>", "female_voice": "en-US-<PERSON><PERSON><PERSON><PERSON>", "human_introduction_audio_blob": "base64IntroAudio", "human_introduction_audio_duration": 8, "human_introduction_enabled": false}}}}, "responses": {"201": {"description": "Goal successfully created", "content": {"application/json": {"example": {"message": "Goal created successfully."}}}}, "400": {"description": "Invalid request or goal creation failed", "content": {"application/json": {"example": {"error": "Missing required fields or goal creation failed."}}}}, "401": {"description": "Unauthorized - token missing or invalid", "content": {"application/json": {"example": {"error": "Invalid or expired token.", "detail": "Token is not valid."}}}}, "404": {"description": "User or entreprise not found", "content": {"application/json": {"example": {"error": "Entreprise not found."}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"example": {"error": "Internal server error."}}}}}}}, "/api/numbers/get_numbers": {"get": {"tags": ["Numbers"], "summary": "Get Phone Numbers by Authenticated User", "description": "Returns all phone numbers associated with the entreprise of the authenticated user. Token must be provided via cookies under `plugin_id_token`.", "responses": {"200": {"description": "List of phone numbers", "content": {"application/json": {"schema": {"type": "object", "properties": {"numbers": {"type": "array", "items": {"type": "object", "properties": {"_id": {"type": "string"}, "entreprise_id": {"type": "string"}, "number": {"type": "string"}}}}}}, "example": {"numbers": [{"_id": "64a7ef3c428a4a0013a4f001", "entreprise_id": "64a7edc7428a4a0013a4ef12", "number": "+21612345678"}]}}}}, "401": {"description": "Unauthorized - Missing or invalid token", "content": {"application/json": {"example": {"error": "Invalid or expired token.", "detail": "Token is not valid."}}}}, "404": {"description": "User or Entreprise not found", "content": {"application/json": {"example": {"error": "Entreprise not found."}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"example": {"error": "Internal server error."}}}}}}}, "/api/script/create_script": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Create a new script", "description": "Creates a new script for the authenticated user's enterprise. The token must be passed via cookies under the key `plugin_id_token`.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "content"], "properties": {"name": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "content": {"type": "string", "example": "Hello, this is an example call script..."}}}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "<PERSON><PERSON><PERSON> created successfully"}}}}}}, "400": {"description": "Bad Request - Invalid JSON body", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Malformed request. Please send a valid JSON body."}}}}}}, "401": {"description": "Unauthorized - Missing or invalid token", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Unauthorized"}, "detail": {"type": "string", "example": "Invalid or expired token."}}}}}}, "404": {"description": "User or enterprise not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Enterprise not found."}}}}}}, "500": {"description": "Internal Server Error - Script creation failed", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Internal server error."}}}}}}}}}, "/api/scripts/get_scripts": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Get assistant scripts", "description": "Fetch all assistant scripts for the authenticated user's enterprise.", "parameters": [], "responses": {"200": {"description": "List of assistant scripts", "content": {"application/json": {"schema": {"type": "object", "properties": {"scripts": {"type": "array", "items": {"$ref": "#/components/schemas/AssistantScript"}}}}}}}, "401": {"description": "Unauthorized or invalid token", "content": {"application/json": {"examples": {"MissingToken": {"summary": "Missing Token", "value": {"error": "Unauthorized"}}, "InvalidToken": {"summary": "Invalid <PERSON>", "value": {"error": "Invalid or expired token.", "detail": "Token is not valid."}}}}}}, "404": {"description": "User or enterprise not found", "content": {"application/json": {"examples": {"UserNotFound": {"summary": "User Not Found", "value": {"error": "User not found."}}, "EnterpriseNotFound": {"summary": "Enterprise Not Found", "value": {"error": "Enterprise not found."}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"example": {"error": "Internal server error."}}}}}}}}}