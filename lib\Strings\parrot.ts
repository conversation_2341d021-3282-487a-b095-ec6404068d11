const parrotString = `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 228.36 220.5">
  <defs>
    <style>
      .cls-1 {
        opacity: .87;
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5, .cls-6, .cls-7 {
        fill: #222222;
        fill-rule: evenodd;
        isolation: isolate;
        stroke-width: 0px;
      }

      .cls-2 {
        opacity: .9;
      }

      .cls-3 {
        opacity: .86;
      }

      .cls-4 {
        opacity: .9;
      }

      .cls-5 {
        opacity: .85;
      }

      .cls-6 {
        opacity: .88;
      }

      .cls-7 {
        opacity: .88;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-4" d="M1.84,0c11.79,1.91,23.63,3.57,35.5,5,12.82-1.38,25.66-2.71,38.5-4,18.04-.06,32.54,6.94,43.5,21,1.19,1.54,1.52,3.21,1,5-3.86,3.51-8.36,5.01-13.5,4.5-14.58-17.13-31.25-19.13-50-6-23.42,19.68-31.92,44.51-25.5,74.5,6.38,23.76,17.71,44.76,34,63,6.86,7.2,14.03,14.03,21.5,20.5,13.73,10.06,27.73,19.72,42,29,2.04,1.71,3.71,3.71,5,6-.67.67-1.33,1.33-2,2-54.18-14.83-91.34-48.66-111.5-101.5-12.3-36.1-6.46-68.76,17.5-98-12.7.62-24.2-2.71-34.5-10-1-1.67-2-3.33-3-5C-.41,3.66.09,1.66,1.84,0Z"/>
      <path class="cls-6" d="M193.84,9c5.04-1.07,8.88.6,11.5,5,23.2,30.52,28.86,64.19,17,101-5.06,12.29-12.22,23.12-21.5,32.5-7.18,2.65-10.35.15-9.5-7.5,7.96-9.99,14.3-20.99,19-33,7.69-24.99,5.35-48.99-7-72-4.39-5.39-8.39-11.06-12-17-1.07-3.64-.24-6.64,2.5-9Z"/>
      <path class="cls-5" d="M78.84,27c4.88-.32,8.38,1.68,10.5,6,1.55,10.45-2.95,14.95-13.5,13.5-5.03-3.81-6.19-8.65-3.5-14.5,1.8-2.33,3.97-3.99,6.5-5Z"/>
      <path class="cls-7" d="M182.84,27c6.58.74,11.41,4.07,14.5,10,16.08,29.62,15.08,58.62-3,87-3.52,5.25-8.19,6.91-14,5-1.37-2.2-1.71-4.53-1-7,4.5-6.34,8.5-13,12-20,6.22-16.95,5.55-33.62-2-50-3.36-5.35-6.7-10.69-10-16-1.33-4.11-.17-7.11,3.5-9Z"/>
      <path class="cls-4" d="M122.84,33c3.15-.54,5.65.46,7.5,3,9.33,13.96,11.66,28.96,7,45-1.31,4.31-3.81,7.64-7.5,10-.59-.21-1.09-.54-1.5-1,.3-9.56-2.87-17.73-9.5-24.5-3.44-.69-6.77-1.69-10-3-3.24-5.6-3.74-11.43-1.5-17.5,4.78-4.62,9.95-8.62,15.5-12Z"/>
      <path class="cls-1" d="M167.84,43c6.63.07,11.46,3.07,14.5,9,9.96,18.31,9.96,36.64,0,55-3.62,7.07-9.12,9.57-16.5,7.5-.93-1.19-1.6-2.53-2-4,4.47-6.45,8.31-13.28,11.5-20.5,1.95-6.99,1.95-13.99,0-21-2.93-5.93-6.27-11.59-10-17-1.07-3.64-.24-6.64,2.5-9Z"/>
      <path class="cls-2" d="M94.84,51c1.52.01,2.68.68,3.5,2,1.67,12.59,8.84,19.42,21.5,20.5,1.08,1.73,1.24,3.56.5,5.5-7.56,7.7-16.73,12.03-27.5,13-2.82,12.97-8.32,24.64-16.5,35-.67,2-.67,4,0,6,18.76,26.45,42.59,46.95,71.5,61.5,11,5.17,22,10.33,33,15.5.93,1.19,1.26,2.52,1,4-11.2,1.14-22.2-.02-33-3.5-14.05-5.19-27.05-12.19-39-21-23.4-18.04-42.24-39.87-56.5-65.5-6.57-12.54-10.4-25.87-11.5-40-.79-6.69,2.04-10.69,8.5-12,1.81,0,3.31.66,4.5,2,2.17,13.35,5.67,26.35,10.5,39l2,2c3.72-4.77,6.39-10.11,8-16,2.09-10.31,4.09-20.65,6-31,2.87-7.03,7.37-12.7,13.5-17Z"/>
      <path class="cls-3" d="M156.84,56c7.06,1.9,11.22,6.57,12.5,14,2.83,12.15,0,22.65-8.5,31.5-4.86,1.75-7.86.25-9-4.5,2.78-5.34,4.78-11.01,6-17-1.09-6.36-3.09-12.36-6-18,.62-2.95,2.29-4.95,5-6Z"/>
    </g>
  </g>
</svg>`;

export default parrotString;
