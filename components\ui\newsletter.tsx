'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { toast } from 'sonner';
import clsx from 'clsx';
import { useSession } from 'next-auth/react';

export default function NewsletterPopup() {
  const [visible, setVisible] = useState(true);
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async(e: React.FormEvent) => {
    try{
        setLoading(true);
        e.preventDefault();
        const response = await fetch('/api/newsletter', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
              },
            body: JSON.stringify({ email: email.trim().toLowerCase() }),
        });
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to subscribe to the newsletter.');
        }
        setEmail('');
        setVisible(false);
        toast.success('Successfully subscribed to the newsletter!');
    }catch(err: any){
        toast.error(`Error: ${err.message}`);
    }finally{
        setLoading(false);
    }
  };

  const session = useSession()
  if (session.status === 'authenticated') {
    return null
  }

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed bottom-6 right-6 z-50 max-w-sm w-full bg-white border border-gray-200 rounded-2xl shadow-lg p-5 dark:bg-gray-900 dark:border-gray-700"
        >
          <div className="flex justify-between items-start mb-2">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Subscribe to our newsletter</h2>
            <button
              onClick={() => setVisible(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Stay updated with our latest news and offers.
          </p>

          <form onSubmit={handleSubmit} className="flex flex-col gap-3">
            <input
              type="email"
              required
              placeholder="<EMAIL>"
              className="px-4 py-2 text-sm rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <button
              type="submit"
                className={clsx(
                  'bg-blue-600 text-white text-sm font-medium py-2 rounded-lg hover:bg-blue-700 transition',
                  loading && 'opacity-50 cursor-not-allowed'
                )}
              disabled={loading}
            >
              {
                loading ? 'Subscribing...' : 'Subscribe Now'
              }
            </button>
          </form>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
