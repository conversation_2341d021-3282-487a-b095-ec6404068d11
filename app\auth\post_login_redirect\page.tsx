// app/(protected)/postLoginRedirect/page.tsx
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import authOptions from "@/lib/AuthOptions";
import dbConnect from "@/lib/mongodb";
import Entreprise from "@/models/Entreprise";

export default async function PostLoginRedirectPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return redirect("/");
  }

  await dbConnect();

  const entreprise = await Entreprise.findOne({ admin: session.user.id });

  if (!entreprise || !entreprise.siret || !entreprise.appartment) {
    return redirect("/auth/onboarding");
  }

  return redirect("/businessDash");
}
