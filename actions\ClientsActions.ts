"use server";

import dbConnect from "@/lib/mongodb";
import Client from "@/models/Client";
import { ClientType } from "@/types/Models";
import { getEntrepriseByAdminID } from "./Entreprise";
import Entreprise from "@/models/Entreprise";
import Goal from "@/models/Goal";
import mongoose from "mongoose";
import Group from "@/models/Groups";
import {
  CountryCode,
  formatNumber,
  isValidPhoneNumber,
} from "libphonenumber-js";
import { GetClientGender, GetClientsGender } from "@/lib/json/GetClientsGender";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";

interface ClientRow {
  firstValue: string;
  secondValue: string;
}

export const CreateClient = async (
  phone: string,
  country: string,
  name?: string,
  groupID?: string,
  gender?: string
): Promise<{
  success: boolean;
  error?: string;
  clientID?: string;
  gender?: string;
}> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }
    const entrepriseID = entrepriseResponse.entreprise._id;

    await dbConnect();

    let formattedNumber: string = phone;

    if (!phone.startsWith("+")) {
      formattedNumber = formatNumber(
        String(phone),
        country as CountryCode,
        "INTERNATIONAL"
      );
    }

    if (!isValidPhoneNumber(formattedNumber)) {
      return {
        success: false,
        error: `Invalid phone number: ${formattedNumber}`,
      };
    }
    const groupObjectID = new mongoose.Types.ObjectId(groupID as string);

    const formattedPhone = phoneNumberFormat(
      formattedNumber,
      country as CountryCode
    ).replace(/\s/g, "");

    let genderedClient = { name, phone: formattedPhone, country, gender };

    if (!gender) {
      genderedClient = await GetClientGender(
        name as string,
        formattedPhone,
        country
      );
    }

    const updatedClient = await Client.findOneAndUpdate(
      { phone: formattedPhone, entreprise_id: entrepriseID },
      {
        $set: {
          name: genderedClient.name,
          country: genderedClient.country,
          gender: genderedClient.gender,
        },
        $addToSet: {
          group: groupObjectID,
        },
      },
      { new: true, upsert: true }
    );

    if (!updatedClient) {
      return { success: false, error: "Failed to create or update client" };
    }

    //update in entreprise account
    const entreprise = await Entreprise.findOneAndUpdate(
      { _id: entrepriseID },
      { $push: { clients: updatedClient._id } }
    );
    if (!entreprise) {
      return { success: false, error: "Failed to add client to entreprise" };
    }
    return { success: true, clientID: updatedClient._id.toString() };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const SaveClients = async (
  data: ClientRow[],
  country: string,
  phoneColumn: number
) => {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success) {
    return { success: false, error: entrepriseResponse.error };
  }

  try {
    await dbConnect();
    const users = [];
    const entrepriseID = entrepriseResponse.entreprise._id;

    if (!data || data.length === 0) {
      return { success: false, error: "No data provided" };
    }

    const date = new Date();
    const formattedDateUTC = date
      .toLocaleString("en-GB", {
        weekday: "short",
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
        timeZone: "UTC",
      })
      .replace(/ /g, "_");

    const group = await Group.create({
      name: `Group_${formattedDateUTC}_${country}`,
      entreprise: entrepriseID,
    });

    if (!group) {
      return { success: false, error: "Can't create group" };
    }

    const clientList = data.map((row) => {
      const phone = phoneColumn === 0 ? row.firstValue : row.secondValue;
      let name = phoneColumn === 0 ? row.secondValue : row.firstValue;

      // Remove leading and trailing quotes (single or double)
      name = name.replace(/^['"]+|['"]+$/g, "");

      return { name, phone };
    });

    const genderedClients = await GetClientsGender(clientList);

    for (const client of genderedClients) {
      const { phone, name, gender } = client;

      if (phone && name) {
        const result = await CreateClient(
          phone,
          country,
          name,
          group._id,
          gender
        );
        if (result.success) {
          const clientObjectID = new mongoose.Types.ObjectId(result.clientID);

          await Entreprise.findOneAndUpdate(
            { _id: entrepriseID },
            { $push: { clients: clientObjectID } }
          );

          await group.updateOne({ $push: { members: clientObjectID } });

          users.push({
            phone: phoneNumberFormat(phone, country as CountryCode).replace(
              /\s/g,
              ""
            ),
            username: name,
            gender,
          });
        }
      }
    }

    return { success: true, users };
  } catch (error: any) {
    console.error("Error saving clients:", error);
    return { success: false, error: error.message };
  }
};

export const GetClientsByFirstLetter = async (
  letter: string
): Promise<{ success: boolean; error?: string; clients?: ClientType[] }> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success)
      return { success: false, error: entrepriseResponse.error };

    const entrepriseObjectID = entrepriseResponse.entreprise._id;
    await dbConnect();

    let query: Record<string, any> = { entreprise_id: entrepriseObjectID };

    if (letter === "favorites") {
      query.isFavorite = true;
    } else if (letter === "blacklist") {
      query.isBlacklisted = true;
    } else if (letter.trim() === "") {
      query = {
        ...query,
        $or: [{ name: { $exists: false } }, { name: "" }],
      };
    } else {
      query = {
        ...query,
        name: { $regex: `(^${letter}|\\s${letter})`, $options: "i" },
      };
    }

    const clients: any[] = await Client.find(query).populate("groups").lean();

    if (!clients.length) {
      return { success: true, clients: [] };
    }

    const sortedClients = clients.sort((a, b) => {
      if (!a.name) return 1;
      if (!b.name) return -1;
      return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
    });

    const formattedClients = sortedClients.map((client) => ({
      phone: client.phone,
      name: client.name,
      email: client.email,
      country: client.country,
      _id: client._id.toString(),
      linkedGoals:
        client.linkedGoals?.map((goal: any) => goal.toString()) || [],
      entreprise_id: client.entreprise_id.toString(),
      isBlacklisted: client.isBlacklisted || false,
      isFavorited: client.isFavorite || false,
      groups: (client.groups || []).map((group: any) => ({
        _id: group?._id?.toString(),
        name: group?.name,
        membersLen: group?.members?.length || 0,
      })),
    }));

    return { success: true, clients: formattedClients };
  } catch (err: any) {
    console.error("GetClientsByFirstLetter error:", err);
    return { success: false, error: err.message };
  }
};

export const ToggleClientFavorite = async (
  clientID: string
): Promise<{ success: boolean; error?: string; isFavorited?: boolean }> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success)
      return { success: false, error: entrepriseResponse.error };

    const entrepriseObjectID = entrepriseResponse.entreprise._id;
    await dbConnect();

    const clientObjectID = new mongoose.Types.ObjectId(clientID);
    const client = await Client.findOne({
      _id: clientObjectID,
      entreprise_id: entrepriseObjectID,
    });

    if (!client) {
      return { success: false, error: "Client not found" };
    }

    const updatedClient = await Client.findByIdAndUpdate(
      clientObjectID,
      { isFavorite: !client.isFavorite },
      { new: true }
    );

    return { success: true, isFavorited: updatedClient.isFavorite };
  } catch (err: unknown) {
    const error = err instanceof Error ? err.message : "Unknown error occurred";
    console.error("Toggle favorite error:", error);
    return { success: false, error };
  }
};

export const ToggleClientBlacklist = async (
  clientID: string
): Promise<{ success: boolean; error?: string; isBlacklisted?: boolean }> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success)
      return { success: false, error: entrepriseResponse.error };

    const entrepriseObjectID = entrepriseResponse.entreprise._id;
    await dbConnect();

    const clientObjectID = new mongoose.Types.ObjectId(clientID);
    const client = await Client.findOne({
      _id: clientObjectID,
      entreprise_id: entrepriseObjectID,
    });

    if (!client) {
      return { success: false, error: "Client not found" };
    }

    const updatedClient = await Client.findByIdAndUpdate(
      clientObjectID,
      { isBlacklisted: !client.isBlacklisted },
      { new: true }
    );

    return { success: true, isBlacklisted: updatedClient.isBlacklisted };
  } catch (err: unknown) {
    const error = err instanceof Error ? err.message : "Unknown error occurred";
    console.error("Toggle blacklist error:", error);
    return { success: false, error };
  }
};

export const CreateClientAndAddToGoal = async (
  phone: string,
  GoalID: string,
  country: string,
  name?: string,
  message_drop?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    await dbConnect();

    const EntrepriseID = entrepriseResponse.entreprise._id;
    const GoalObjectID = new mongoose.Types.ObjectId(GoalID);

    // Find the goal
    const goal = await Goal.findOne({ _id: GoalObjectID });
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    if (goal.clients.length >= 100) {
      return {
        success: false,
        error: "Goal has reached maximum number of clients (100)",
      };
    }

    // Check if the client already exists with the same phone in the entreprise
    let client = await Client.findOne({
      phone: phoneNumberFormat(phone, country as CountryCode).replace(
        /\s/g,
        ""
      ),
      entreprise_id: EntrepriseID,
    });

    const genderedClient = await GetClientGender(
      name as string,
      phoneNumberFormat(phone, country as CountryCode).replace(/\s/g, ""),
      country
    );

    console.log("genderedClient", genderedClient);

    if (!client) {
      // Create a new client if they don't exist
      client = await Client.create({
        name: genderedClient.name || "Unknown",
        phone: phoneNumberFormat(
          genderedClient.phone,
          country as CountryCode
        ).replace(/\s/g, ""),
        country: genderedClient.country,
        gender: genderedClient.gender,
        linkedGoals: [GoalObjectID],
        entreprise_id: EntrepriseID,
      });

      if (!client) {
        return { success: false, error: "Can't create new client" };
      }

      // Add client to entreprise
      const entrepriseUpdate = await Entreprise.findOneAndUpdate(
        { _id: EntrepriseID },
        { $push: { clients: client._id } }
      );

      if (!entrepriseUpdate) {
        return { success: false, error: "Can't add client to entreprise" };
      }
    } else {
      // If the client exists, ensure they are not already in the goal
      const isClientAlreadyInGoal = goal.clients.some(
        (goalClient: any) =>
          goalClient.client_id.toString() === client._id.toString()
      );

      if (isClientAlreadyInGoal) {
        return { success: false, error: "Client already exists in the goal" };
      }

      // Add this goal to the client's linkedGoals array
      await Client.findByIdAndUpdate(client._id, {
        $addToSet: { linkedGoals: GoalObjectID },
      });
    }

    console.log("client", client);

    // Add client to goal with required fields
    goal.clients.push({
      client_id: client._id,
      status: "NOT_CALLED",
      name: client.name,
      phone: client.phone,
      gender: client.gender,
      calls_count: 0,
      message_drop: message_drop || "",
      updated_at: Date.now(),
    });

    // Save the updated goal
    const GoalResponse = await goal.save();

    if (!GoalResponse) {
      return { success: false, error: "Can't add client to goal" };
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const AddClientToGoalWithoutSaving = async (
  phone: string,
  GoalID: string,
  country: string,
  name?: string,
  message_drop?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    await dbConnect();

    const GoalObjectID = new mongoose.Types.ObjectId(GoalID);

    // Find the goal
    const goal = await Goal.findOne({ _id: GoalObjectID });
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    if (goal.clients.length >= 100) {
      return {
        success: false,
        error: "Goal has reached maximum number of clients (100)",
      };
    }

    const formattedPhone = phoneNumberFormat(
      phone,
      country as CountryCode
    ).replace(/\s/g, "");

    // Check if a client with the same phone already exists in the goal
    const isClientAlreadyInGoal = goal.clients.some(
      (goalClient: any) => goalClient.phone === formattedPhone
    );

    if (isClientAlreadyInGoal) {
      return { success: false, error: "Client already exists in the goal" };
    }

    const genderedClient = await GetClientGender(
      name || "",
      formattedPhone,
      country
    );

    // Add temporary client data directly to the goal
    goal.clients.push({
      client_id: null, // No DB client ID
      status: "NOT_CALLED",
      name: genderedClient.name || "Unknown",
      phone: formattedPhone,
      gender: genderedClient.gender || "Unknown",
      calls_count: 0,
      message_drop: message_drop || "",
      updated_at: Date.now(),
    });

    // Save the updated goal
    const GoalResponse = await goal.save();

    if (!GoalResponse) {
      return { success: false, error: "Can't add client to goal" };
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const GetGoalClientNumbersArray = async (
  GoalID: string
): Promise<{ success: boolean; error?: string; numbers?: string[] }> => {
  try {
    await dbConnect();
    const GoalObjectID = new mongoose.Types.ObjectId(GoalID);

    // Populate the goal's clients (fetch only the client_id)
    const goal = await Goal.findOne({ _id: GoalObjectID }).populate(
      "clients.client_id",
      "phone"
    );

    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    // Extract phone numbers from populated client objects
    const numbers = goal.clients
      .map((client: any) => client.client_id?.phone)
      .filter(Boolean);

    return { success: true, numbers };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const GetClientsByGoalID = async (
  goalID: string
): Promise<{ success: boolean; error?: string; clients?: any[] }> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    await dbConnect();

    const GoalObjectID = new mongoose.Types.ObjectId(goalID);

    // Populate the clients field and access client_id to get client details
    const goal = await Goal.findOne({ _id: GoalObjectID });

    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    const clients = goal.clients;

    // Access and parse client information
    const sortedClients = clients.sort((a: any, b: any) => {
      if (!a.client_id?.name) return 1;
      if (!b.client_id?.name) return -1;

      const nameA = a.client_id?.name.toLowerCase();
      const nameB = b.client_id?.name.toLowerCase();

      return nameA.localeCompare(nameB);
    });

    const parsedClients = sortedClients.map((client: any) => {
      const clientData = client;
      return {
        _id: clientData?._id.toString(),
        name: clientData?.name,
        phone: clientData?.phone,
        client_id: clientData?.client_id?.toString(),
      };
    });

    console.log("parsedClients", parsedClients);

    return { success: true, clients: parsedClients };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const getClientGoalName = async (
  clientID: string
): Promise<{ success: boolean; error?: string; goal?: any }> => {
  try {
    const clientObjectID = new mongoose.Types.ObjectId(clientID);

    // Find the goal by checking if client_id is part of the goal's clients array
    const goal = await Goal.findOne({
      "clients.client_id": { $in: [clientObjectID] },
    });

    if (!goal) {
      return { success: false, error: "No Goal" };
    }

    return { success: true, goal: goal.name };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const getClientGoalNames = async (
  clientID: string
): Promise<{ success: boolean; error?: string; goals?: any[] }> => {
  try {
    const clientObjectID = new mongoose.Types.ObjectId(clientID);

    // Find goals by checking if client_id is part of the goal's clients array
    const goals = await Goal.find({
      "clients.client_id": { $in: [clientObjectID] },
    });

    if (!goals || goals?.length === 0) {
      return { success: false, error: "No Goals" };
    }

    const filteredGoals = goals.map((goal) => {
      return {
        _id: goal._id.toString(),
        name: goal.name,
      };
    });

    return { success: true, goals: filteredGoals };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const getAllEntrepriseGoals = async (
  clientID: string
): Promise<{
  success: boolean;
  error?: string;
  goals?: any[];
}> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }
    await dbConnect();
    const entrepriseID = entrepriseResponse.entreprise._id;
    const clientObjectID = new mongoose.Types.ObjectId(clientID);
    const goals = await Goal.find({
      entreprise: entrepriseID,
      goal_template_type: "DEMARCHAGE",
      "clients.client_id": { $ne: clientObjectID },
    });

    if (!goals) {
      return { success: false, error: "No goals found" };
    }
    const formattedGoals = goals.map((goal) => {
      return {
        _id: goal._id.toString(),
        name: goal.name,
      };
    });
    return { success: true, goals: formattedGoals };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const addClientToGoalByPhone = async (
  phone: string,
  goalID: string,
  country: string,
  name?: string,
  gender?: string,
  message_drop?: string
) => {
  try {
    await dbConnect();
    const goalObjectID = new mongoose.Types.ObjectId(goalID);
    const goal = await Goal.findById(goalObjectID);
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }
    if (goal.clients?.length >= 100) {
      return {
        success: false,
        error: "Goal has reached maximum number of clients (100)",
      };
    }
    if (goal.clients.some((client: any) => client.phone === phone)) {
      return { success: false, error: "Client already exists in the goal" };
    }
    goal.clients.push({
      client_id: null, // No DB client ID
      status: "NOT_CALLED",
      calls_count: 0,
      name: name || "Unknown",
      phone: phoneNumberFormat(phone, country as CountryCode).replace(
        /\s/g,
        ""
      ),
      country: country,
      gender: gender || "Unknown",
      message_drop,
      updated_at: new Date(),
    });
    const goalResponse = await goal.save();
    if (!goalResponse) {
      return { success: false, error: "Goal not found to add clients." };
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const AddClientToGoal = async (
  clientID: string,
  goalID: string,
  message_drop?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    await dbConnect();

    const clientObjectID = new mongoose.Types.ObjectId(clientID);
    const goalObjectID = new mongoose.Types.ObjectId(goalID);

    const goalDB = await Goal.findById(goalObjectID);
    if (!goalDB) {
      return { success: false, error: "Goal not found" };
    }

    if (goalDB.clients?.length >= 100) {
      return {
        success: false,
        error: "Goal has reached the maximum number of clients (100)",
      };
    }

    if (
      goalDB.clients.some(
        (client: any) =>
          client.client_id?.toString() === clientObjectID.toString()
      )
    ) {
      return { success: false, error: "Client already exists in the goal" };
    }

    const client = await Client.findOneAndUpdate(
      { _id: clientObjectID },
      { $push: { linkedGoals: goalObjectID } }
    );
    if (!client) {
      return { success: false, error: "Couldn't add goal to client." };
    }

    goalDB.clients.push({
      client_id: clientObjectID,
      status: "NOT_CALLED",
      calls_count: 0,
      name: client.name,
      phone: client.phone,
      gender: client.gender,
      message_drop,
      updated_at: new Date(),
    });

    const goal = await goalDB.save();
    if (!goal) {
      return { success: false, error: "Goal not found to add clients." };
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const RemoveClientFromGoal = async (
  clientID: string,
  goalID: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    await dbConnect();

    const clientObjectID = new mongoose.Types.ObjectId(clientID);
    const goalObjectID = new mongoose.Types.ObjectId(goalID);

    await Client.findOneAndUpdate(
      { _id: clientObjectID },
      { $pull: { linkedGoals: goalObjectID } }
    );

    const goalDB = await Goal.findById(goalObjectID);
    if (!goalDB) {
      return { success: false, error: "Goal not found" };
    }
    goalDB.clients = goalDB.clients.filter(
      (c: any) => c.client_id?.toString() !== clientObjectID.toString()
    );
    await goalDB.save();

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const getEntrepriseClientsWithNoGoal = async (): Promise<{
  success: boolean;
  error?: string;
  clients?: any[];
}> => {
  try {
    const EntrepriseResponse = await getEntrepriseByAdminID();
    if (!EntrepriseResponse.success) {
      return { success: false, error: EntrepriseResponse.error };
    }

    await dbConnect();

    const entrepriseID = EntrepriseResponse.entreprise._id;
    const entrepriseClients: any[] = await Client.find({
      entreprise_id: entrepriseID,
    }).lean();

    if (!entrepriseClients?.length) {
      return { success: false, error: "No clients found" };
    }
    const sortedClients = entrepriseClients.sort((a: any, b: any) => {
      if (!a.name) return 1;
      if (!b.name) return -1;

      const nameA = a.name.toLowerCase();
      const nameB = b.name.toLowerCase();

      return nameA.localeCompare(nameB);
    });
    const clients = sortedClients.map((client: any) => ({
      _id: client._id.toString(),
      name: client.name,
      phone: client.phone,
      country: client.country,
    }));

    if (clients?.length === 0) {
      return { success: true, clients: [] };
    }

    const clientIDs = clients.map(
      (client: any) => new mongoose.Types.ObjectId(client._id as string)
    );

    // Modify this query to match client objects in the Goal's clients array
    const clientsWithGoals = await Goal.find({
      "clients.client_id": { $in: clientIDs },
    }).distinct("clients.client_id");

    const clientsWithGoalsSet = new Set(
      clientsWithGoals.map((id: any) => id.toString())
    );

    const clientsWithNoGoal = clients.filter(
      (client: any) => !clientsWithGoalsSet.has(client._id)
    );

    return { success: true, clients: clientsWithNoGoal };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const GetClientsNotInGoalByNumber = async (
  goalID: string,
  phoneQuery: string
): Promise<{ success: boolean; error?: string; clients?: any[] }> => {
  try {
    const EntrepriseResponse = await getEntrepriseByAdminID();
    if (!EntrepriseResponse.success) {
      return { success: false, error: EntrepriseResponse.error };
    }

    await dbConnect();

    const entrepriseID = EntrepriseResponse.entreprise._id;

    const escapedQuery = phoneQuery.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    const clientsNotInGoal = await Client.find({
      entreprise_id: entrepriseID,
      phone: { $regex: escapedQuery, $options: "i" },
      $or: [
        { linkedGoals: { $exists: false } },
        { linkedGoals: { $ne: goalID } },
      ],
    })
      .populate("groups")
      .lean();

    if (!clientsNotInGoal?.length) {
      return { success: true, clients: [] };
    }

    const goalIDs = [
      ...new Set(
        clientsNotInGoal.flatMap((client: any) => client.linkedGoals || [])
      ),
    ];

    let goalMap = new Map();
    if (goalIDs?.length) {
      const goals = await Goal.find({
        "clients.client_id": {
          $in: goalIDs.map(
            (goalID: any) => new mongoose.Types.ObjectId(goalID as string)
          ),
        },
      })
        .select("name _id")
        .lean();

      goalMap = new Map(
        goals.map((goal: any) => [goal._id.toString(), goal.name])
      );
    }

    const sortedClients = clientsNotInGoal.sort((a: any, b: any) => {
      if (!a.name) return 1;
      if (!b.name) return -1;
      return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
    });

    const formattedClients = sortedClients.map((client: any) => ({
      _id: client._id.toString(),
      name: client.name,
      phone: client.phone,
      goalNames: (client.linkedGoals || [])
        .map((goalID: any) => goalMap.get(goalID.toString()) || null)
        .filter(Boolean),
      country: client.country,
      groups: (client.groups || []).map((group: any) => group._id?.toString()),
    }));

    console.log(formattedClients);
    return { success: true, clients: formattedClients };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const GetClientsNotInGoal = async (
  goalID: string,
  letters: string
): Promise<{ success: boolean; error?: string; clients?: any[] }> => {
  try {
    const EntrepriseResponse = await getEntrepriseByAdminID();
    if (!EntrepriseResponse.success) {
      return { success: false, error: EntrepriseResponse.error };
    }

    await dbConnect();

    const entrepriseID = EntrepriseResponse.entreprise._id;

    // Query for clients not in the specified goal
    const clientsNotInGoal = await Client.find({
      entreprise_id: entrepriseID,
      name: { $regex: `\\b${letters}`, $options: "i" },
      $or: [
        { linkedGoals: { $exists: false } },
        { linkedGoals: { $ne: goalID } },
      ],
    })
      .populate("groups") // Assuming the field is now 'groups'
      .lean();

    if (!clientsNotInGoal?.length) {
      return { success: true, clients: [] };
    }

    // Extract all unique linked goal IDs
    const goalIDs = [
      ...new Set(
        clientsNotInGoal.flatMap((client: any) => client.linkedGoals || [])
      ),
    ];

    let goalMap = new Map();
    if (goalIDs?.length) {
      // Fetch all the goals by ID and map them by goal ID
      const goals = await Goal.find({
        "clients.client_id": {
          $in: goalIDs.map(
            (goalID: any) => new mongoose.Types.ObjectId(goalID as string)
          ),
        },
      })
        .select("name _id")
        .lean();

      goalMap = new Map(
        goals.map((goal: any) => [goal._id.toString(), goal.name])
      );
    }

    const sortedClients = clientsNotInGoal.sort((a: any, b: any) => {
      if (!a.name) return 1;
      if (!b.name) return -1;
      return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
    });

    const formattedClients = sortedClients.map((client: any) => ({
      _id: client._id.toString(),
      name: client.name,
      phone: client.phone,
      goalNames: (client.linkedGoals || [])
        .map((goalID: any) => goalMap.get(goalID.toString()) || null)
        .filter(Boolean),
      country: client.country,
      groups: (client.groups || []).map((group: any) => group._id?.toString()),
    }));

    return { success: true, clients: formattedClients };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const GetClientsInGoalByGoalIDAndFirstLetter = async (
  goalID: string,
  letter: string
): Promise<{ success: boolean; error?: string; clients?: any[] }> => {
  try {
    await dbConnect();
    const goalObjectID = new mongoose.Types.ObjectId(goalID);

    const goalClients = await Client.find({
      linkedGoals: { $in: [goalObjectID] },
      name: { $regex: `^${letter}`, $options: "i" },
    });

    if (!goalClients || goalClients?.length === 0) {
      return {
        success: false,
        error: "No clients found for this goal and letter",
      };
    }
    const sortedClients = goalClients.sort((a: any, b: any) => {
      if (!a.name) return 1;
      if (!b.name) return -1;

      const nameA = a.name.toLowerCase();
      const nameB = b.name.toLowerCase();

      return nameA.localeCompare(nameB);
    });
    const formattedClients = sortedClients.map((client) => ({
      _id: client._id.toString(),
      name: client.name,
      phone: client.phone,
      country: client.country,
    }));

    return { success: true, clients: formattedClients };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const RemoveClientFromGoalWithNumber = async (
  phone: string,
  goalID: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    await dbConnect();
    console.log("Phone", phone);

    const goalObjectID = new mongoose.Types.ObjectId(goalID);

    const goal = await Goal.findOneAndUpdate(
      { _id: goalObjectID },
      { $pull: { clients: { phone: phone } } }
    );

    if (!goal) {
      return { success: false, error: "Can't remove client from goal" };
    }

    await Client.findOneAndUpdate(
      { phone: phone },
      { $pull: { linkedGoals: goalObjectID } }
    );

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export async function checkDuplicateNumbers(
  data: { firstValue: string; secondValue: string }[],
  phoneColumn: 0 | 1 | null,
  country?: string
): Promise<{ success: boolean; duplicateNumbers?: any[]; error?: string }> {
  try {
    await dbConnect();

    if (!data || data?.length === 0) {
      return { success: false, error: "No data provided" };
    }

    const csvData = data.map((row, idx) => {
      const phoneRaw = phoneColumn === 0 ? row.firstValue : row.secondValue;
      const name = phoneColumn === 0 ? row.secondValue : row.firstValue;
      console.log("phoneRaw", phoneRaw);
      console.log("name", name);
      let formattedNumber: string = phoneRaw.trim();

      if (!formattedNumber.startsWith("+")) {
        formattedNumber = formatNumber(
          String(formattedNumber),
          country as CountryCode,
          "INTERNATIONAL"
        );
      }

      if (!isValidPhoneNumber(formattedNumber)) {
        return {
          success: false,
          error: `Invalid phone number: ${formattedNumber}`,
        };
      }

      return {
        phone: phoneNumberFormat(
          formattedNumber,
          country as CountryCode
        ).replace(/\s/g, ""),
        name,
        index: idx,
      };
    });

    const csvNumbers = csvData.map(({ phone }) => phone);

    const duplicateClients = await Client.find({ phone: { $in: csvNumbers } });

    const duplicateNumbers = duplicateClients.map((client) => client.phone);

    const duplicatesFromCsv = csvData.filter(({ phone }) =>
      duplicateNumbers.includes(phone)
    );

    console.log(duplicatesFromCsv);

    return { success: true, duplicateNumbers: duplicatesFromCsv };
  } catch (error: any) {
    console.error(error);
    return { success: false, error: error.message };
  }
}

export async function GetGroups(
  search?: string,
  sortBy: "name" | "date" = "name",
  sortOrder: "asc" | "desc" = "asc"
): Promise<{
  success: boolean;
  groups?: any[];
  error?: string;
}> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }
    await dbConnect();

    const query: Record<string, any> = {
      entreprise: entrepriseResponse.entreprise._id,
    };

    if (search && search.trim() !== "") {
      // Add search filter for group name
      const escapeRegex = (str: string) =>
        str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
      const searchEscaped = escapeRegex(search);
      query.name = { $regex: searchEscaped, $options: "i" };
    }

    // Create sort object based on parameters
    const sortOptions: Record<string, 1 | -1> = {};
    sortOptions[sortBy === "name" ? "name" : "date"] =
      sortOrder === "asc" ? 1 : -1;

    const groups = await Group.find(query).sort(sortOptions).lean();
    const filteredGroups = groups.map((group: any) => ({
      _id: group._id.toString(),
      name: group.name,
      membersLen: group.members?.length || 0,
      date: group.date?.toISOString(),
      created_at: group.created_at?.toISOString(),
    }));

    return { success: true, groups: filteredGroups };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function DeleteClientCompletely(clientID: string) {
  try {
    await dbConnect();
    const clientObjectID = new mongoose.Types.ObjectId(clientID);

    const client = await Client.findById(clientObjectID);
    if (!client) {
      return { success: false, error: "Client not found" };
    }

    // Remove client from groups
    await Group.updateMany(
      { members: clientObjectID },
      { $pull: { members: clientObjectID } }
    );

    // Remove client from entreprise groups
    await Entreprise.updateMany(
      { "groups.members": clientObjectID },
      { $pull: { "groups.$[].members": clientObjectID } }
    );

    // Remove client from entreprise clients
    await Entreprise.updateMany(
      { clients: clientObjectID },
      { $pull: { clients: clientObjectID } }
    );

    // Update Goal model to remove client based on the client_id structure
    await Goal.updateMany(
      { "clients.client_id": clientObjectID },
      { $pull: { clients: { client_id: clientObjectID } } }
    );

    // Finally, delete the client from the Client collection
    await Client.findByIdAndDelete(clientObjectID);

    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function UpdateClientDetails(
  clientID: string,
  name: string,
  phone: string,
  gender: string
): Promise<{ success: boolean; error?: string }> {
  try {
    await dbConnect();
    const clientObjectID = new mongoose.Types.ObjectId(clientID);

    const client = await Client.findById(clientObjectID);
    if (!client) {
      return { success: false, error: "Client not found" };
    }

    client.name = name;
    client.phone = phoneNumberFormat(phone);
    client.gender = gender;
    await client.save();

    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function searchEntrepriseGroupsByFirstLetters(
  letters: string
): Promise<{ success: boolean; error?: string; groups?: any[] }> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }
    await dbConnect();
    const groups = await Group.find({
      name: { $regex: `^${letters}`, $options: "i" },
      entreprise: entrepriseResponse.entreprise._id,
    }).lean();
    if (!groups?.length) {
      return {
        success: false,
        error: "No groups found for the specified letters",
      };
    }
    const formattedGroups = groups.map((group: any) => ({
      _id: group._id.toString(),
      name: group.name,
    }));
    return { success: true, groups: formattedGroups };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function AddGroupMembersToGoal(
  groupID: string,
  goalID: string
): Promise<{ success: boolean; error?: string }> {
  try {
    await dbConnect();
    const groupObjectID = new mongoose.Types.ObjectId(groupID);
    const goalObjectID = new mongoose.Types.ObjectId(goalID);

    const group = await Group.findById(groupObjectID).populate("members");
    if (!group) {
      return { success: false, error: "Group not found" };
    }

    const goal = await Goal.findById(goalObjectID);
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    if (goal.clients?.length + group.members?.length > 100) {
      return {
        success: false,
        error: "Goal has reached maximum number of clients (100)",
      };
    }
    console.log(group);
    const membersObjectIDs = group.members.map(
      (member: any) => new mongoose.Types.ObjectId(member._id as string)
    );
    console.log("membersObjectIDs", membersObjectIDs);
    // If the clients field is now an array of objects with a client_id, update accordingly
    const goalClients = goal.clients.map((client: any) =>
      client?.client_id?.toString()
    );
    console.log("goalClients", goalClients);
    const newClients = membersObjectIDs.filter(
      (id: any) => !goalClients.includes(id.toString())
    );
    console.log("newClients", newClients);
    if (newClients?.length) {
      // Add only the new members who are not already added to the goal
      goal.clients.push(...newClients.map((id: any) => ({ client_id: id })));
      const goalUpdate = await goal.save();

      if (!goalUpdate) {
        return { success: false, error: "Can't add members to goal" };
      }

      // Add the goal to the clients' linkedGoals
      await Client.updateMany(
        { _id: { $in: newClients } },
        { $addToSet: { linkedGoals: goalObjectID } }
      );

      return { success: true };
    } else {
      return { success: false, error: "No new clients to add" };
    }
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function ImportClientsAndAddToGoal(
  goalID: string,
  data: { firstValue: string; secondValue: string; messageDrop?: string }[],
  country: string,
  phoneColumn: number,
  saveInDB: boolean
): Promise<{ success: boolean; error?: string; users?: any[] }> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    await dbConnect();

    const goalObjectID = new mongoose.Types.ObjectId(goalID);
    const goal = await Goal.findOne({ _id: goalObjectID });
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    if (data?.length < 1) {
      return { success: false, error: "No data provided" };
    }

    if (goal.clients?.length + data?.length > 100) {
      return {
        success: false,
        error:
          "Adding these clients would exceed the maximum limit of 100 clients per goal.",
      };
    }

    const entrepriseID = entrepriseResponse.entreprise._id;

    const clientList = data.map((row) => {
      const phone = phoneColumn === 0 ? row.firstValue : row.secondValue;
      let name = phoneColumn === 0 ? row.secondValue : row.firstValue;
      const message_drop = row.messageDrop || "";

      name = name.replace(/^['"]+|['"]+$/g, ""); // Trim quotes
      console.log({
        phone,
        name,
        message_drop,
      });
      return { name, phone, message_drop };
    });

    const genderedClients = await GetClientsGender(clientList);
    const users: any[] = [];

    let group: any = null;
    if (saveInDB) {
      const formattedDateUTC = new Date()
        .toLocaleString("en-GB", {
          weekday: "short",
          year: "numeric",
          month: "short",
          day: "numeric",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
          timeZone: "UTC",
        })
        .replace(/ /g, "_");

      group = await Group.create({
        name: `Group_${formattedDateUTC}_${goal.name}_${country}`,
        entreprise: entrepriseID,
      });

      if (!group) {
        return { success: false, error: "Can't create group" };
      }
    }

    for (const client of genderedClients) {
      const { phone, name, gender, message_drop } = client;

      if (phone && name) {
        if (saveInDB) {
          const result = await CreateClient(
            phone,
            country,
            name,
            group._id,
            gender
          );
          console.log("result", result);
          if (result.success) {
            const clientObjectID = new mongoose.Types.ObjectId(result.clientID);

            await Entreprise.findOneAndUpdate(
              { _id: entrepriseID },
              { $push: { clients: clientObjectID } }
            );

            const goalClients = goal.clients.map((c: any) =>
              c?.client_id?.toString()
            );
            if (!goalClients.includes(clientObjectID.toString())) {
              const res = await AddClientToGoal(
                clientObjectID.toString(),
                goalID,
                message_drop
              );
              console.log("res", res);
              if (!res.success) {
                return { success: false, error: res.error };
              }
            }

            await group.updateOne({ $push: { members: clientObjectID } });

            users.push({
              phone: phoneNumberFormat(phone, country as CountryCode).replace(
                /\s/g,
                ""
              ),
              name,
              gender,
            });
          }
        } else {
          // When not saving to DB: just simulate user entry
          const goalClientsNumbers = goal.clients.map((c: any) => c?.phone);
          if (
            !goalClientsNumbers.includes(
              phoneNumberFormat(phone, country as CountryCode).replace(
                /\s/g,
                ""
              )
            )
          ) {
            // If the phone number is not already in the goal, add it
            const AddClientResponse = await addClientToGoalByPhone(
              phone,
              goalID,
              country,
              name,
              gender,
              message_drop
            );
            if (!AddClientResponse.success) {
              return { success: false, error: AddClientResponse.error };
            }
          }
          users.push({
            phone: phoneNumberFormat(phone, country as CountryCode).replace(
              /\s/g,
              ""
            ),
            name,
            gender,
          });
        }
      }
    }

    return { success: true, users };
  } catch (err: any) {
    console.error(err);
    return { success: false, error: err.message };
  }
}

export async function DeleteGroupFromDB(
  groupID: string
): Promise<{ success: boolean; error?: string }> {
  try {
    await dbConnect();
    const groupObjectID = new mongoose.Types.ObjectId(groupID);

    // Delete the group from the database
    const group = await Group.findByIdAndDelete(groupObjectID);
    if (!group) {
      return { success: false, error: "Group not found" };
    }

    // Remove the group reference from clients
    await Client.updateMany(
      { groups: groupObjectID },
      { $pull: { groups: groupObjectID } }
    );

    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function removeClientFromGroupDB(
  clientID: string,
  groupID: string
): Promise<{ success: boolean; error?: string }> {
  try {
    await dbConnect();
    const clientObjectID = new mongoose.Types.ObjectId(clientID);
    const groupObjectID = new mongoose.Types.ObjectId(groupID);

    // Remove the client from the group
    const group = await Group.findByIdAndUpdate(
      groupObjectID,
      { $pull: { members: clientObjectID } },
      { new: true }
    );

    if (!group) {
      return { success: false, error: "Group not found" };
    }

    // Remove the group reference from the client
    await Client.findByIdAndUpdate(clientObjectID, {
      $pull: { groups: groupObjectID },
    });

    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function getClientsNotInGroup(group_id?: string) {
  try {
    // Connect to the database
    await dbConnect();
    let groups: any[] = [];
    if (!group_id) {
      // Find all groups
      groups = await Group.find({}).populate("members");
    } else {
      const groupObjectID = new mongoose.Types.ObjectId(group_id);
      // Find the groups where the given group_id is NOT in the members list
      groups = await Group.find({
        _id: { $ne: groupObjectID },
      }).populate("members");
    }

    // Flatten all members from all groups, remove duplicates by phone number
    const allMembers = groups.flatMap((group) => group.members);
    const uniqueMembers = Array.from(
      new Map(
        allMembers.map((member) => [
          member._id.toString(), // Use the member's ID as the key
          {
            name: member.name,
            phone: member.phone,
            _id: member._id.toString(),
          }, // The rest of the member data as the value
        ])
      ).values()
    );

    return { success: true, data: uniqueMembers };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function getClientsNotInGroupBySearchTerm(
  group_id?: string,
  query?: string
) {
  try {
    await dbConnect();

    const pipeline: any[] = [];

    // 1. Match the group if group_id is provided
    if (group_id) {
      pipeline.push({
        $match: { _id: new mongoose.Types.ObjectId(group_id) },
      });
    }

    // 2. Lookup members (clients)
    pipeline.push({
      $lookup: {
        from: "clients", // Client collection name
        localField: "members",
        foreignField: "_id",
        as: "membersData",
      },
    });

    // 3. Extract members' _id into excludedIds array
    pipeline.push({
      $project: {
        excludedIds: "$membersData._id",
      },
    });

    // 4. Lookup clients NOT in excludedIds and match by name if query is given
    pipeline.push({
      $lookup: {
        from: "clients",
        let: { excluded: "$excludedIds" },
        pipeline: [
          {
            $match: {
              $expr: {
                $not: { $in: ["$_id", "$$excluded"] },
              },
              ...(query && {
                name: { $regex: query, $options: "i" },
              }),
            },
          },
          {
            $addFields: {
              _id: { $toString: "$_id" },
            },
          },
          {
            $project: {
              _id: 1,
              name: 1,
              phone: 1,
            },
          },
        ],
        as: "availableClients",
      },
    });

    // 5. Final project to only return availableClients
    pipeline.push({
      $project: {
        _id: 0,
        availableClients: 1,
      },
    });

    const result = await Group.aggregate(pipeline);
    // Sort clients alphabetically by name
    const sortedClients = result[0]?.availableClients.sort((a: any, b: any) => {
      if (!a.name) return 1;
      if (!b.name) return -1;

      return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
    });

    return {
      success: true,
      data: sortedClients ?? [],
    };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function addClientsToGroup(
  group_id: string,
  client_ids: string[]
) {
  try {
    const groupObjectID = new mongoose.Types.ObjectId(group_id);
    const clientObjectIDs = client_ids.map(
      (client_id) => new mongoose.Types.ObjectId(client_id)
    );

    // Connect to the database
    await dbConnect();

    // Add clients to the group
    const group = await Group.findByIdAndUpdate(
      groupObjectID,
      { $addToSet: { members: { $each: clientObjectIDs } } },
      { new: true }
    );

    if (!group) {
      return { success: false, error: "Group not found" };
    }

    // Add the group to each client's groups array
    await Client.updateMany(
      { _id: { $in: clientObjectIDs } },
      { $addToSet: { groups: groupObjectID } }
    );

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function CreateGroupAndAddClients(
  groupName: string,
  clientIDs: string[]
): Promise<{ success: boolean; error?: string }> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    await dbConnect();
    const entrepriseID = entrepriseResponse.entreprise._id;
    const group = await Group.create({
      name: groupName,
      entreprise: entrepriseID,
      members: clientIDs.map(
        (clientID) => new mongoose.Types.ObjectId(clientID)
      ),
    });

    if (!group) {
      return { success: false, error: "Can't create group" };
    }

    // Add the group to each client's groups array
    await Client.updateMany(
      { _id: { $in: group.members } },
      { $addToSet: { groups: group._id } }
    );

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function getMeetGoalClients({ goalID }: { goalID: string }) {
  try {
    await dbConnect();
    const goalObjectID = new mongoose.Types.ObjectId(goalID);
    const goal = await Goal.findById(goalObjectID);
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }
    const clients = goal.clients.map((client: any) => ({
      google_meet_dial_in_number: client.google_meet_dial_in_number,
      google_meet_pin: client.google_meet_pin,
    }));
    return { success: true, clients };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export const getAllClients = async (
  search: string
): Promise<{ success: boolean; error?: string; clients?: ClientType[] }> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success)
      return { success: false, error: entrepriseResponse.error };

    const entrepriseObjectID = entrepriseResponse.entreprise._id;
    await dbConnect();

    let clients: any[] = [];
    if (search && search.trim() !== "") {
      // Escape regex special characters in search and searchNoSpaces
      const escapeRegex = (str: string) =>
        str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
      const searchEscaped = escapeRegex(search);
      const searchNoSpaces = search.replace(/\s+/g, "");
      const searchNoSpacesEscaped = escapeRegex(searchNoSpaces);

      clients = await Client.aggregate([
        { $match: { entreprise_id: entrepriseObjectID } },
        {
          $addFields: {
            phoneNoSpaces: {
              $replaceAll: { input: "$phone", find: " ", replacement: "" },
            },
          },
        },
        {
          $match: {
            $or: [
              { name: { $regex: searchEscaped, $options: "i" } },
              {
                phoneNoSpaces: { $regex: searchNoSpacesEscaped, $options: "i" },
              },
            ],
          },
        },
      ]);
      // Populate groups manually since aggregate doesn't support populate
      const clientIds = clients.map((c: any) => c._id);
      const groupsMap = Object.fromEntries(
        (await Group.find({ members: { $in: clientIds } }).lean()).map(
          (g: any) => [g._id.toString(), g]
        )
      );
      clients = clients.map((client: any) => ({
        ...client,
        groups: (client.groups || [])
          .map((groupId: any) => groupsMap[groupId?.toString()] || null)
          .filter(Boolean),
      }));
    } else {
      clients = await Client.find({ entreprise_id: entrepriseObjectID })
        .populate("groups")
        .lean();
    }

    if (!clients.length) {
      return { success: true, clients: [] };
    }

    const sortedClients = clients.sort((a, b) => {
      if (!a.name) return 1;
      if (!b.name) return -1;
      return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
    });

    const formattedClients = sortedClients.map((client) => ({
      phone: client.phone,
      name: client.name,
      email: client.email,
      country: client.country,
      _id: client._id.toString(),
      linkedGoals:
        client.linkedGoals?.map((goal: any) => goal.toString()) || [],
      entreprise_id: client.entreprise_id.toString(),
      isBlacklisted: client.isBlacklisted || false,
      isFavorited: client.isFavorite || false,
      groups: (client.groups || []).map((group: any) => ({
        _id: group?._id?.toString(),
        name: group?.name,
        membersLen: group?.members?.length || 0,
      })),
    }));

    return { success: true, clients: formattedClients };
  } catch (err: any) {
    console.error("getAllClients error:", err);
    return { success: false, error: err.message };
  }
};
