"use client";

import { getAssistantsWithDurations } from "@/actions/AssistantActions";
import { getFilteredGoals } from "@/actions/GoalActions";
import CustomButton from "@/components/CustomFormItems/Button";
import GoalCard from "@/components/Dashboard/Cards/GoalCard";
import MainLoader from "@/components/Loaders/MainLoader";
import CustomPagination from "@/components/pagination/CustomPagination";
import AddAssistant from "@/components/popups/AddAssistant";
import { Input } from "@/components/ui/input";
import OrganigrammeItem from "@/components/ui/organigramme";
import { useCallback, useEffect, useRef, useState } from "react";
import useSWR from "swr";
import { useTranslation } from "react-i18next";

const fetchAssistants = async () => {
  const assistants = await getAssistantsWithDurations();
  if (!assistants) {
    throw new Error("Failed to fetch assistants");
  }
  return assistants?.data || [];
};

const itemsPerPage = 42;

const fetchFilteredGoals = async (searchTerm: string, page: number) => {
  const response = await getFilteredGoals({
    searchTerm,
    page,
    limit: itemsPerPage,
  });
  if (!response.success) {
    throw new Error(response.error || "Failed to fetch goals");
  }
  return {
    goals: response.goals || [],
    totalPages: response.totalPages || 0,
    currentPage: response.currentPage || 1,
  };
};

export default function ContactHome() {
  const { t } = useTranslation("assistants");
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (debounceTimeout.current) clearTimeout(debounceTimeout.current);
    debounceTimeout.current = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);
    return () => {
      if (debounceTimeout.current) clearTimeout(debounceTimeout.current);
    };
  }, [searchTerm]);

  const {
    data: assistantsData,
    isLoading: assistantsLoading,
    error: assistantsError,
  } = useSWR("assistants", fetchAssistants);

  const {
    data: filteredGoalsData,
    isLoading: goalsLoading,
    error: goalsError,
  } = useSWR(
    ["filtered-goals", debouncedSearchTerm, currentPage],
    () => fetchFilteredGoals(debouncedSearchTerm, currentPage),
    {
      revalidateOnFocus: false,
      keepPreviousData: true,
    }
  );

  const handleSearch = useCallback(() => {
    if (searchTerm) {
      setShowSearchResults(true);
      setCurrentPage(1);
    }
  }, [searchTerm]);

  const handleClearSearch = () => {
    setSearchTerm("");
    setShowSearchResults(false);
    setCurrentPage(1);
  };

  useEffect(() => {
    if (debouncedSearchTerm) {
      setShowSearchResults(true);
    } else {
      setShowSearchResults(false);
    }
  }, [debouncedSearchTerm]);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const goals = filteredGoalsData?.goals || [];
  const totalPages = filteredGoalsData?.totalPages || 0;
  const totalGoals = totalPages * itemsPerPage;

  return (
    <div className="relative w-full flex flex-col gap-4">
      <div className="w-full max-sm:text-center flex max-md:flex-col sm:items-center gap-4 justify-between">
        <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
          {t("yourAssistants")}
        </h1>
        <AddAssistant />
      </div>
      <div className="xl:-mb-8 ">
        <div className="flex">
          <Input
            name="GoalSearch"
            placeholder={t("searchGoalsPlaceholder")}
            value={searchTerm}
            onChange={(e: any) => setSearchTerm(e.target.value)}
            className="w-full md:w-[335px] rounded-r-none rtl:rounded-l-none rtl:rounded-r-md"
            onKeyDown={(e: any) => {
              if (e.key === "Enter") {
                handleSearch();
              }
            }}
          />
          <CustomButton
            props={{
              value:
                goalsLoading && searchTerm !== ""
                  ? t("searching")
                  : t("search"),
              className:
                "dark:bg-voxa-neutral-800 rounded-l-none rtl:rounded-r-none rtl:rounded-l-md w-fit max-sm:px-2 py-5",
              loading: goalsLoading && searchTerm !== "",
              onClick: handleSearch,
            }}
          />
          {searchTerm && (
            <CustomButton
              props={{
                value: t("clear"),
                className:
                  "ml-2 dark:bg-voxa-neutral-800 w-fit max-sm:px-2 py-5",
                onClick: handleClearSearch,
              }}
            />
          )}
        </div>
        {showSearchResults && (
          <div className="mt-2 mb-8">
            <h3 className="text-lg font-semibold text-voxa-neutral-800 dark:text-voxa-neutral-100">
              {t("searchResultsFor", { searchTerm })}
            </h3>
            {/* {!goalsLoading && (
              <p className="text-sm text-voxa-neutral-600 dark:text-voxa-neutral-400">
                {t("foundGoals", { count: totalGoals })}
              </p>
            )} */}
          </div>
        )}
      </div>

      {showSearchResults ? (
        <div className="relative w-full">
          {goalsLoading ? (
            <div className="w-full absolute flex justify-center items-center h-[calc(100vh-26rem)] sm:h-[calc(100vh-27.5rem)] md:h-[calc(100vh-21rem)]">
              <MainLoader />
            </div>
          ) : goalsError ? (
            <div className="text-center py-8 text-red-500">
              {t("errorLoadingGoals", { message: goalsError.message })}
            </div>
          ) : goals.length > 0 ? (
            <>
              <div className="mb-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {goals.map((goal) => (
                  <GoalCard key={goal._id} goal={goal} />
                ))}
              </div>
              <CustomPagination
                itemsPerPage={itemsPerPage}
                totalItems={totalGoals}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            </>
          ) : (
            <div className="text-center py-8 text-voxa-neutral-500 dark:text-voxa-neutral-400">
              {t("noGoalsFound")}
            </div>
          )}
        </div>
      ) : (
        <>
          {assistantsLoading ? (
            <div className="absolute w-full flex justify-center items-center h-[calc(100vh-2rem)] sm:h-[calc(100vh-3rem)]">
              <MainLoader />
            </div>
          ) : assistantsError ? (
            <div className="w-full flex justify-center items-center h-[40rem]">
              <h1 className="dark:text-voxa-neutral-50 text-lg text-red-500">
                {t("failedToLoadAssistants")}
              </h1>
            </div>
          ) : assistantsData && assistantsData.length > 0 ? (
            assistantsData.map((assistant, index) => (
              <OrganigrammeItem key={index} assistant={assistant} />
            ))
          ) : (
            <div className="w-full flex justify-center items-center h-[40rem]">
              <h1 className="dark:text-voxa-neutral-50 text-lg">
                {t("noAssistantsFound")}
              </h1>
            </div>
          )}
        </>
      )}
    </div>
  );
}
