import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import axios from "axios";
import { LocateFixed } from "lucide-react";
import { toast } from "sonner";
import CircularLoaderSmall from "../Loaders/CircularLoaderSmall";
import { useTranslation } from "react-i18next";

export function UpdateChromaDB() {
  const { t } = useTranslation("rag");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const sendRagRequest = async () => {
    try {
      setLoading(true);
      const res = await axios.post("/api/rag/upload_files_for_rag");
      if (res.data.error) throw new Error(res.data.error);

      toast.success("Knowledge base updated successfully");
      setIsModalOpen(false);
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogTrigger asChild>
        <Button
          variant="default"
          className="max-md:w-full justify-start bg-red-500 hover:bg-inherit shadow-none text-sm group"
          onClick={() => setIsModalOpen(true)}
        >
          <p className="w-full group-hover:text-red-500 text-white">
            {t("update_knowledge_base")}
          </p>
          <LocateFixed className="group-hover:text-red-500 text-white" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("update_knowledge_base_title")}</DialogTitle>
        </DialogHeader>
        <DialogDescription className="text-sm">
          {t("update_knowledge_base_desc", { price: "£5.00" })}
        </DialogDescription>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="submit">{t("cancel")}</Button>
          </DialogClose>
          <Button
            disabled={loading}
            onClick={sendRagRequest}
            className={`text-voxa-neutral-100 flex gap-2 items-center justify-center ${
              loading
                ? "bg-blue-700"
                : "bg-blue-500 hover:bg-blue-600 active:bg-blue-500/70"
            }`}
          >
            {loading ? (
              <>
                {t("updating")}
                <CircularLoaderSmall />
              </>
            ) : (
              t("update")
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
