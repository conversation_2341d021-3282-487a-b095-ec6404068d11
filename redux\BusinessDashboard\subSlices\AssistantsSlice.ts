import {
  CreateAssistant,
  getEntrepriseAssistants,
} from "@/actions/AssistantActions";
import {
  addNumberToAssistant,
  CreateNumber,
  getEntrepriseNumbers,
} from "@/actions/NumberActions";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { BusinessDashboardState } from "../BusinessDashboardSlice";
import {
  CountryCode,
  formatNumber,
  isValidPhoneNumber,
} from "libphonenumber-js";
import {
  AddClientToGoal,
  AddClientToGoalWithoutSaving,
  AddGroupMembersToGoal,
  CreateClientAndAddToGoal,
  GetClientsNotInGoal,
  GetClientsNotInGoalByNumber,
  ImportClientsAndAddToGoal,
  searchEntrepriseGroupsByFirstLetters,
} from "@/actions/ClientsActions";
import {
  CreateNewTag,
  getTagsNotInGoal,
  addTagsToGoal,
  RemoveTagFromGoal,
} from "@/actions/TagsActions";
import {
  AddClientToGoogleMeet,
  GetCurrentGoalStatus,
  PauseGoal,
  StopGoal,
} from "@/actions/GoalActions";

interface FileMetadata {
  name: string;
  type: string;
  size: number;
}

interface AssistantsState {
  addGroupToGoal: boolean;
  addOneContactOpen: boolean;
  createAssistantOpen: boolean;
  files: FileMetadata[];
  error: boolean;
  manageNumbersOpen: boolean;
  createClientAndAddToGoalOpen: boolean;
  addClientToGoal: boolean;
  addContactsToGoal: boolean;
  addClientToMeetGoalOpen: boolean;
  GoalID: string;
  goal_template_type: string;
  numbers: any[];
  assistantDetails: any;
  assistantsData: any[];
  Name: string;
  phoneNumber: string;
  country: string;
  messageDrop: string;
  TagName: string;
  TagColor: string;
  assistantName: string;
  PhoneNumbers: any[];
  NumberCountry: string;
  assistantID: string;
  managenumbersLoading: boolean;
  clients: any[];
  selectedClientID: string;
  searchTerm: string;
  groups: any[];
  selectedGroupID: string;
  tags: any[];
  currentGoalStatus: string;
  GoalStarted: boolean;
  google_meet_dial_in_number: string;
  google_meet_pin: string;
  crm_client_id: string;
  filteredGoals: any[];
  goalSearchTerm: string;
  goalsLoading: boolean;
  openGoalID: string | null;
  tagsNotInGoal: any[];
  addTagToGoalLoading: boolean;
}

const initialState: AssistantsState = {
  addGroupToGoal: false,
  addOneContactOpen: false,
  createAssistantOpen: false,
  addClientToMeetGoalOpen: false,
  files: [],
  error: false,
  manageNumbersOpen: false,
  addClientToGoal: false,
  createClientAndAddToGoalOpen: false,
  addContactsToGoal: false,
  numbers: [],
  assistantDetails: null,
  GoalID: "",
  goal_template_type: "",
  assistantsData: [],
  Name: "",
  phoneNumber: "",
  country: "",
  messageDrop: "",
  TagName: "",
  TagColor: "",
  assistantName: "",
  PhoneNumbers: [],
  NumberCountry: "",
  assistantID: "",
  managenumbersLoading: false,
  clients: [],
  selectedClientID: "",
  searchTerm: "",
  groups: [],
  selectedGroupID: "",
  tags: [],
  currentGoalStatus: "",
  GoalStarted: false,
  google_meet_dial_in_number: "",
  google_meet_pin: "",
  crm_client_id: "",
  filteredGoals: [],
  goalSearchTerm: "",
  goalsLoading: false,
  openGoalID: null,
  tagsNotInGoal: [],
  addTagToGoalLoading: false,
};

export const fetchCurrentGoalStatus = createAsyncThunk(
  "BusinessDashboardAssistants/fetchCurrentGoalStatus",
  async ({ goalID }: { goalID: string }, { dispatch }) => {
    try {
      if (goalID === "") {
        console.log("No GoalID found");
        return;
      }

      const response = await GetCurrentGoalStatus(goalID);
      if (!response.success) {
        console.log("Failed response");
        return;
      }
      dispatch(setCurrentGoalStatus(response.status));
    } catch (err) {
      console.log(err);
      toast.error("Failed to fetch goal status");
    }
  }
);

export const OpenManageNumbers_GetNumbers = createAsyncThunk(
  "OpenManageNumbers_GetNumbers",
  async (_, { dispatch }) => {
    try {
      dispatch(setManageNumbersOpen(true));
      dispatch(setManageNumbersLoading(true));
      const response = await getEntrepriseNumbers();
      if (!response.success) {
        dispatch(setManageNumbersOpen(false));
        toast.error(response.error);
        return;
      }
      if (response.numbers) {
        dispatch(setNumbers(response.numbers));
      }
    } catch (err) {
      console.log(err);
      toast.error("Failed to get numbers");
    } finally {
      dispatch(setManageNumbersLoading(false));
    }
  }
);

export const GetAssistants = createAsyncThunk(
  "GetAssistants",
  async (_, { dispatch }) => {
    try {
      const response = await getEntrepriseAssistants();
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      if (response.assistants) {
        dispatch(setAssistantsData(response.assistants));
      }
    } catch (err) {
      console.log(err);
      toast.error("Failed to get assistants");
    }
  }
);

export const CreateNewClient = createAsyncThunk(
  "BusinessDashboardClients/CreateNewClient",
  async ({ saveToDB }: { saveToDB: boolean }, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { Name, country, phoneNumber, GoalID, messageDrop } =
        state.businessDashboard.businessDashboardAssistants;
      const formattedPhoneNumber = formatNumber(
        String(phoneNumber),
        country as CountryCode,
        "INTERNATIONAL"
      );
      if (!isValidPhoneNumber(formattedPhoneNumber)) {
        toast.error("Invalid phone number");
        return;
      }
      if (saveToDB) {
        const response = await CreateClientAndAddToGoal(
          formattedPhoneNumber,
          GoalID,
          country,
          Name,
          messageDrop
        );
        if (!response.success) {
          toast.error(response.error);
          return;
        }
        toast.success("User created successfully");
      } else {
        const response = await AddClientToGoalWithoutSaving(
          formattedPhoneNumber,
          GoalID,
          country,
          Name,
          messageDrop
        );
        if (!response.success) {
          toast.error(response.error);
          return;
        }
        toast.success("User added to goal successfully");
      }
      dispatch(setName(""));
      dispatch(setNumber(""));
      dispatch(setCountry(""));
      dispatch(setMessageDrop(""));
      dispatch(setAddOneContactOpen(false));
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const createTag = createAsyncThunk(
  "BusinessDashboardAssistants/CreateNewTag",
  async ({ TagName, TagColor }: { TagName: string; TagColor: string }) => {
    try {
      if (TagName === "" || TagColor === "") {
        toast.error("Please fill all fields");
        return;
      }
      const response = await CreateNewTag(TagName, TagColor);
      if (!response.success) {
        toast.error("Can't create new Tag");
        return;
      }
      toast.success("Tag created successfully");
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const requestNewAssistant = createAsyncThunk(
  "BusinessDashboardAssistants/requestNewAssistant",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { assistantName } =
        state.businessDashboard.businessDashboardAssistants;
      if (assistantName === "") {
        toast.error("Please fill all fields");
        return;
      }
      const response = await CreateAssistant(assistantName);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("Assistant request submitted successfully.");
      dispatch(setAssistantName(""));
      dispatch(setCreateAssistantOpen(false));
      toast.success("Assistant created successfully");
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const createNumber = createAsyncThunk(
  "BusinessDashboardAssistants/createNumber",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { NumberCountry } =
        state.businessDashboard.businessDashboardAssistants;
      if (NumberCountry === "") {
        toast.error("Please fill all fields");
        return;
      }
      const response = await CreateNumber(NumberCountry);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("Number Request submitted successfully.");
      dispatch(setNumberCountry(""));
      dispatch(setManageNumbersOpen(false));
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const addNumber = createAsyncThunk(
  "BusinessDashboardAssistants/addNumber",
  async ({ numberObject }: { numberObject: any }, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { PhoneNumbers, assistantDetails } =
        state.businessDashboard.businessDashboardAssistants;
      const updatedNumbers = PhoneNumbers.filter((number: any) => {
        return number.number !== numberObject.number;
      });
      dispatch(setPhoneNumbers(updatedNumbers));
      const res = await addNumberToAssistant(
        numberObject.id,
        assistantDetails._id
      );
      if (!res.success) {
        toast.error(res.error);
        return;
      }
      toast.success("Number added successfully");
      dispatch(setAssistantID(""));
      dispatch(setManageNumbersOpen(false));
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const addExistingClientToGoal = createAsyncThunk(
  "BusinessDashboardAssistants/addExistingClientToGoal",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { selectedClientID, GoalID } =
        state.businessDashboard.businessDashboardAssistants;
      if (selectedClientID === "") {
        toast.error("Please select a client");
        return;
      }
      if (GoalID === "") {
        toast.error("Please select a goal");
        return;
      }
      const response = await AddClientToGoal(selectedClientID, GoalID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("Client added to goal successfully");
      dispatch(setSelectedClientID(""));
      dispatch(setSearchTerm(""));
      dispatch(setAddClientToGoal(false));
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const fetchClients = createAsyncThunk(
  "BusinessDashboardAssistants/fetchClients",
  async (
    { searchTerm, GoalID }: { searchTerm: string; GoalID: string },
    { dispatch }
  ) => {
    try {
      const response = await GetClientsNotInGoal(GoalID, searchTerm);
      if (!response.success) {
        toast.error(response.error);
        dispatch(setClients([]));
        return;
      }
      if (response.clients) {
        dispatch(setClients(response.clients || []));
      }
    } catch (err) {
      console.log(err);
      toast.error("Failed to fetch clients");
    }
  }
);

export const fetchClientsByNumber = createAsyncThunk(
  "BusinessDashboardAssistants/fetchClientsByNumber",
  async (
    { searchTerm, GoalID }: { searchTerm: string; GoalID: string },
    { dispatch }
  ) => {
    try {
      const response = await GetClientsNotInGoalByNumber(GoalID, searchTerm);
      if (!response.success) {
        toast.error(response.error);
        dispatch(setClients([]));
        return;
      }
      if (response.clients) {
        dispatch(setClients(response.clients || []));
      }
    } catch (err) {
      console.log(err);
      toast.error("Failed to fetch clients");
    }
  }
);

export const addGroupToGoal = createAsyncThunk(
  "BusinessDashboardAssistants/addGroupToGoal",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { selectedGroupID, GoalID } =
        state.businessDashboard.businessDashboardAssistants;
      if (selectedGroupID === "") {
        toast.error("Please select a group");
        return;
      }
      if (GoalID === "") {
        toast.error("Please select a goal");
        return;
      }
      const response = await AddGroupMembersToGoal(selectedGroupID, GoalID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("Group added to goal successfully");
      dispatch(setSelectedGroupID(""));
      dispatch(setSearchTerm(""));
      dispatch(setAddGroupToGoal(false));
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const fetchGroups = createAsyncThunk(
  "BusinessDashboardAssistants/fetchGroups",
  async ({ searchTerm }: { searchTerm: string }, { dispatch }) => {
    try {
      const response = await searchEntrepriseGroupsByFirstLetters(searchTerm);
      if (!response.success) {
        toast.error(response.error);
        dispatch(setGroups([]));
        return;
      }
      if (response.groups) {
        dispatch(setGroups(response.groups || []));
      }
    } catch (err) {
      console.log(err);
      toast.error("Failed to fetch groups");
    }
  }
);

export const startGoal = createAsyncThunk(
  "BusinessDashboardAssistants/startGoal",
  async ({ GoalID }: { GoalID: string }, { dispatch }) => {
    try {
      const resp = await fetch("/api/assistant/StartNewGoal", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ goalID: GoalID }),
      });

      const data = await resp.json();

      if (!resp.ok) {
        toast.error(data.error || "Failed to start goal");
        return;
      }
      if (resp.status === 200) {
        dispatch(setGoalStarted(true));
        dispatch(setCurrentGoalStatus("STARTED"));
        toast.success("Goal started");
      }
    } catch (err) {
      console.log(err);
      toast.error("Failed to start goal");
    }
  }
);

export const stopGoal = createAsyncThunk(
  "BusinessDashboardAssistants/stopGoal",
  async ({ GoalID }: { GoalID: string }, { dispatch }) => {
    try {
      const response = await StopGoal(GoalID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      dispatch(setGoalStarted(false));
      dispatch(setCurrentGoalStatus("STOPPED"));
      toast.success("Goal stopped successfully.");
    } catch (err) {
      console.log(err);
      toast.error("Failed to stop goal");
    }
  }
);

export const pauseGoal = createAsyncThunk(
  "BusinessDashboardAssistants/pauseGoal",
  async ({ GoalID }: { GoalID: string }, { dispatch }) => {
    try {
      const response = await PauseGoal(GoalID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      dispatch(setGoalStarted(false));
      dispatch(setCurrentGoalStatus("PAUSED"));
      toast.success("Goal paused successfully.");
    } catch (err) {
      console.log(err);
      toast.error("Failed to pause goal");
    }
  }
);

export const deleteGoalByID = createAsyncThunk(
  "BusinessDashboardAssistants/deleteGoalByID",
  async (_, { getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { GoalID } = state.businessDashboard.businessDashboardAssistants;
      const response = await StopGoal(GoalID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("Goal deleted successfully.");
      window.location.reload();
    } catch (err) {
      console.log(err);
      toast.error("Failed to delete goal");
    }
  }
);

export const uploadClientsToGoal = createAsyncThunk(
  "BusinessDashboardAssistants/uploadClientsToGoal",
  async (
    {
      data,
      phoneColumn,
      saveToDB,
    }: { data: any; phoneColumn: 0 | 1; saveToDB: boolean },
    { dispatch, getState }
  ) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { country, GoalID } =
        state.businessDashboard.businessDashboardAssistants;
      if (!data) return toast.error("Please select a file");

      const response = await ImportClientsAndAddToGoal(
        GoalID,
        data,
        country,
        phoneColumn,
        saveToDB
      );
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("Clients imported and added to goal successfully");
      dispatch(setGoalID(""));
      dispatch(setGoalTemplateType(""));
      dispatch(setCountry(""));
      dispatch(handleFileUpload([]));
      dispatch(setError(false));
      dispatch(setFiles([]));
      dispatch(setAddContactsToGoal(false));
    } catch (err) {
      console.log(err);
      toast.error("Failed to upload clients");
    }
  }
);

export const SaveClientToGoogleMeetGoal = createAsyncThunk(
  "BusinessDashboardAssistants/SaveClientToGoogleMeetGoal",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { country, GoalID, google_meet_dial_in_number, google_meet_pin } =
        state.businessDashboard.businessDashboardAssistants;
      if (
        country === "" ||
        GoalID === "" ||
        google_meet_dial_in_number === "" ||
        google_meet_pin === ""
      ) {
        toast.error("Please fill all fields");
        return;
      }
      const formattedPhoneNumber = formatNumber(
        String(google_meet_dial_in_number),
        country as CountryCode,
        "INTERNATIONAL"
      );
      if (!isValidPhoneNumber(formattedPhoneNumber)) {
        toast.error("Invalid phone number");
        return;
      }
      const response = await AddClientToGoogleMeet({
        google_meet_dial_in_number,
        google_meet_pin,
        country,
        goal_id: GoalID,
      });
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("Client added to Google Meet goal successfully");
      dispatch(setNumber(""));
      dispatch(setCountry(""));
      dispatch(set_google_meet_dial_in_number(""));
      dispatch(set_google_meet_pin(""));
      dispatch(set_crm_client_id(""));
      dispatch(addClientToGoogleMeetOpen(false));
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const fetchTagsNotInGoal = createAsyncThunk(
  "BusinessDashboardAssistants/fetchTagsNotInGoal",
  async ({ goalID }: { goalID: string }, { dispatch }) => {
    try {
      dispatch(setAddTagToGoalLoading(true));
      const response = await getTagsNotInGoal(goalID);
      if (!response.success) {
        toast.error(response.error);
        dispatch(setTagsNotInGoal([]));
        return;
      }
      dispatch(setTagsNotInGoal(response.tags || []));
    } catch (err) {
      console.log(err);
      toast.error("Failed to fetch tags");
      dispatch(setTagsNotInGoal([]));
    } finally {
      dispatch(setAddTagToGoalLoading(false));
    }
  }
);

export const addTagsToGoalThunk = createAsyncThunk(
  "BusinessDashboardAssistants/addTagsToGoalThunk",
  async (
    { goalID, tagIDs }: { goalID: string; tagIDs: string[] },
    { dispatch }
  ) => {
    try {
      dispatch(setAddTagToGoalLoading(true));
      const response = await addTagsToGoal(goalID, tagIDs);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("Tags added to goal");
      dispatch(fetchTagsNotInGoal({ goalID }));
    } catch (err) {
      console.log(err);
      toast.error("Failed to add tags to goal");
    } finally {
      dispatch(setAddTagToGoalLoading(false));
    }
  }
);

export const removeTagFromGoalThunk = createAsyncThunk(
  "BusinessDashboardAssistants/removeTagFromGoal",
  async (
    { goalID, tagID }: { goalID: string; tagID: string },
    { dispatch }
  ) => {
    try {
      dispatch(setAddTagToGoalLoading(true));
      const response = await RemoveTagFromGoal(goalID, tagID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("Tag removed from goal");
      dispatch(fetchTagsNotInGoal({ goalID }));
    } catch (err) {
      console.log(err);
      toast.error("Failed to remove tag from goal");
    } finally {
      dispatch(setAddTagToGoalLoading(false));
    }
  }
);

const AssistantSlice = createSlice({
  name: "BusinessDashboardAssistants",
  initialState,
  reducers: {
    setAddGroupToGoal: (state, action) => {
      state.addGroupToGoal = action.payload;
    },
    setAddOneContactOpen: (state, action) => {
      state.createClientAndAddToGoalOpen = action.payload;
    },
    setAddClientToMeetGoalOpen: (state, action) => {
      state.addClientToMeetGoalOpen = action.payload;
    },
    setCreateAssistantOpen: (state, action) => {
      state.createAssistantOpen = action.payload;
    },
    setManageNumbersOpen: (state, action) => {
      state.manageNumbersOpen = action.payload;
    },
    setAddClientToGoal: (state, action) => {
      state.addClientToGoal = action.payload;
    },
    setAddContactsToGoal: (state, action) => {
      state.addContactsToGoal = action.payload;
    },
    setNumbers: (state, action) => {
      state.numbers = action.payload;
    },
    setAssistantDetails: (state, action) => {
      state.assistantDetails = action.payload;
    },
    setGoalID: (state, action) => {
      state.GoalID = action.payload;
    },
    setGoalTemplateType: (state, action) => {
      state.goal_template_type = action.payload;
    },
    setAssistantsData: (state, action) => {
      state.assistantsData = action.payload;
    },
    handleFileUpload: (state, action: PayloadAction<FileMetadata[]>) => {
      const files = action.payload;
      if (files.length !== 1) {
        state.error = true;
        return;
      }
      state.files = files;
      state.error = false;
    },
    //add client to goal
    setName: (state, action) => {
      state.Name = action.payload;
    },
    setNumber: (state, action) => {
      state.phoneNumber = action.payload;
    },
    setCountry: (state, action) => {
      state.country = action.payload;
    },
    setMessageDrop: (state, action) => {
      state.messageDrop = action.payload;
    },
    // create Tag
    setTagName: (state, action) => {
      state.TagName = action.payload;
    },
    setTagColor: (state, action) => {
      state.TagColor = action.payload;
    },
    //add assistant
    setAssistantName: (state, action) => {
      state.assistantName = action.payload;
    },
    // manage numbers
    setPhoneNumbers: (state, action) => {
      state.PhoneNumbers = action.payload;
    },
    setNumberCountry: (state, action) => {
      state.NumberCountry = action.payload;
    },
    setManageNumbersLoading: (state, action) => {
      state.managenumbersLoading = action.payload;
    },
    setAssistantID: (state, action) => {
      state.assistantID = action.payload;
    },
    //add existing client to goal
    setClients: (state, action) => {
      state.clients = action.payload;
    },
    setSelectedClientID: (state, action) => {
      state.selectedClientID = action.payload;
    },
    setSearchTerm: (state, action) => {
      state.searchTerm = action.payload;
    },
    //add group to goal
    setGroups: (state, action) => {
      state.groups = action.payload;
    },
    setSelectedGroupID: (state, action) => {
      state.selectedGroupID = action.payload;
    },
    //assistant goal dropdown states
    setTags: (state, action) => {
      state.tags = action.payload;
    },
    setCurrentGoalStatus: (state, action) => {
      state.currentGoalStatus = action.payload;
    },
    setGoalStarted: (state, action) => {
      state.GoalStarted = action.payload;
    },
    AddOneContact: (state, action) => {
      state.GoalID = action.payload.goalID;
      state.createClientAndAddToGoalOpen = true;
    },
    HandleAddGroupToGoal: (state, action) => {
      state.GoalID = action.payload.goalID;
      state.addGroupToGoal = true;
    },
    ImportClientsToGoal: (state, action) => {
      state.GoalID = action.payload.goalID;
      state.addContactsToGoal = true;
    },
    handleAddExistingClientToGoal: (state, action) => {
      state.GoalID = action.payload.goalID;
      state.addClientToGoal = true;
    },
    handleAddClientToMeetGoal: (state, action) => {
      state.GoalID = action.payload.goalID;
      state.goal_template_type = action.payload.goal_template_type;
      state.addClientToMeetGoalOpen = true;
    },
    addClientToGoogleMeetOpen: (state, action) => {
      state.addClientToMeetGoalOpen = action.payload;
    },
    //import files
    setError: (state, action) => {
      state.error = action.payload;
    },
    setFiles: (state, action) => {
      state.files = action.payload;
    },
    // google meet goal clients actions
    set_google_meet_dial_in_number: (state, action) => {
      state.google_meet_dial_in_number = action.payload;
    },
    set_google_meet_pin: (state, action) => {
      state.google_meet_pin = action.payload;
    },
    set_crm_client_id: (state, action) => {
      state.crm_client_id = action.payload;
    },
    setFilteredGoals: (state, action) => {
      state.filteredGoals = action.payload;
    },
    setGoalSearchTerm: (state, action) => {
      state.goalSearchTerm = action.payload;
    },
    setGoalsLoading: (state, action) => {
      state.goalsLoading = action.payload;
    },
    // goal dropdown state
    setOpenGoalID: (state, action) => {
      state.openGoalID = action.payload;
    },
    setTagsNotInGoal: (state, action) => {
      state.tagsNotInGoal = action.payload;
    },
    setAddTagToGoalLoading: (state, action) => {
      state.addTagToGoalLoading = action.payload;
    },
  },
});

export const {
  setAddGroupToGoal,
  setAddOneContactOpen,
  setCreateAssistantOpen,
  setManageNumbersOpen,
  setAddClientToGoal,
  setAddContactsToGoal,
  setNumbers,
  setAssistantDetails,
  setGoalID,
  setGoalTemplateType,
  setAssistantsData,
  handleFileUpload,
  setName,
  setNumber,
  setCountry,
  setTagName,
  setTagColor,
  setAssistantName,
  setPhoneNumbers,
  setNumberCountry,
  setAssistantID,
  setClients,
  setSelectedClientID,
  setSearchTerm,
  setGroups,
  setSelectedGroupID,
  setTags,
  setCurrentGoalStatus,
  setGoalStarted,
  AddOneContact,
  handleAddClientToMeetGoal,
  addClientToGoogleMeetOpen,
  HandleAddGroupToGoal,
  ImportClientsToGoal,
  handleAddExistingClientToGoal,
  setManageNumbersLoading,
  setError,
  setFiles,
  setMessageDrop,
  set_google_meet_dial_in_number,
  set_crm_client_id,
  set_google_meet_pin,
  setFilteredGoals,
  setGoalSearchTerm,
  setGoalsLoading,
  setOpenGoalID,
  setTagsNotInGoal,
  setAddTagToGoalLoading,
} = AssistantSlice.actions;
export default AssistantSlice.reducer;
