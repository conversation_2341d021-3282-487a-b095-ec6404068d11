"use client";

import * as React from "react";
import * as SliderPrimitive from "@radix-ui/react-slider";
import { MinusCircleIcon } from "lucide-react";
import { Button } from "../ui/button";
import { useTranslation } from "react-i18next";
import { AppDispatch, RootState } from "@/redux/store";
import { useDispatch, useSelector } from "react-redux";
import { setRanges } from "@/redux/BusinessDashboard/subSlices/GoalSlice";
import { cn } from "@/lib/utils";

const now = new Date();
const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
const dayStart = today.getTime();
const dayEnd = dayStart + 24 * 60 * 60 * 1000;
const current = now.getTime();
const oneHourLater = Math.min(current + 60 * 60 * 1000, dayEnd);
export const INITIAL_RANGE: [number, number] = [current, oneHourLater];

const DAY_START = new Date(now.setHours(0, 0, 0, 0)).getTime();
const DAY_END = new Date(now.setHours(23, 59, 0, 0)).getTime();
const SLOT_DURATION = 2 * 60 * 60 * 1000;
const STEP = 15 * 60 * 1000;
const SLIDER_RANGE = DAY_END - DAY_START;

export function sanitizeRanges(ranges: any[]): [number, number][] {
  return ranges.map(([start, end]) => [
    typeof start === "number"
      ? start
      : start instanceof Date
      ? start.getTime()
      : Number(start),
    typeof end === "number"
      ? end
      : end instanceof Date
      ? end.getTime()
      : Number(end),
  ]);
}
const normalizeToToday = (timestamp: number) => {
  const date = new Date(timestamp);
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const today = new Date();
  today.setHours(hours, minutes, 0, 0);
  return today.getTime();
};
const formatTime = (timestamp: number) => {
  return new Intl.DateTimeFormat("default", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  }).format(new Date(normalizeToToday(timestamp)));
};
const formatTotalTime = (totalMs: number) => {
  const totalMinutes = Math.floor(totalMs / (60 * 1000));
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
    2,
    "0"
  )}`;
};

export const RangesSlider: React.FC = () => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const { ranges } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );
  const allValues = ranges.flat().map(normalizeToToday);

  const handleTrackDoubleClick = () => {
    if (ranges.length >= 3) return;

    const lastRangeEnd =
      ranges.length > 0 ? ranges[ranges.length - 1][1] : DAY_START;
    let newStart = normalizeToToday(lastRangeEnd + STEP);
    let newEnd = newStart + SLOT_DURATION;

    if (newEnd > DAY_END) {
      newStart = Math.max(DAY_START, DAY_END - SLOT_DURATION);
      newEnd = DAY_END;
    }
    console.log("newStart:", newStart, "type:", typeof newStart);
    console.log("newEnd:", newEnd, "type:", typeof newEnd);
    dispatch(setRanges([...ranges, [newStart, newEnd]]));
  };

  const handleSliderValueChange = (newValues: number[]) => {
    const clamped = newValues.map((v) =>
      Math.min(Math.max(v, DAY_START), DAY_END)
    );
    const newRanges: [number, number][] = [];
    for (let i = 0; i < clamped.length; i += 2) {
      const start = clamped[i];
      const end = clamped[i + 1];
      if (start < end && end <= DAY_END && start >= DAY_START) {
        newRanges.push([start, end]);
      }
    }
    dispatch(setRanges(newRanges));
  };

  const getTotalMs = () => {
    return ranges.reduce((total, [start, end]) => total + (end - start), 0);
  };

  const removeRange = (index: number) => {
    dispatch(setRanges(ranges.filter((_, i) => i !== index)));
  };

  return (
    <div className="w-full p-4 rounded-xl border-2 bg-sidebar border-sidebar-border">
      <div className="flex justify-between mb-2">
        <h3 className="text-lg font-semibold">
          {t("createEditGoal.rangesSlider.selectTimeSlots")}
        </h3>
        <p className="font-semibold text-sm text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-900 px-4 py-2 rounded-lg">
          {t("createEditGoal.rangesSlider.totalHours", {
            hours: formatTotalTime(getTotalMs()),
          })}
        </p>
      </div>
      <p className="text-sm mb-3">
        {t("createEditGoal.rangesSlider.selectUpTo")}
      </p>

      <SliderPrimitive.Root
        className="relative flex w-full touch-none select-none items-center"
        value={allValues}
        onValueChange={handleSliderValueChange}
        min={DAY_START}
        max={DAY_END}
        step={STEP}
      >
        <SliderPrimitive.Track
          className="relative h-2 w-full bg-voxa-neutral-100 dark:bg-voxa-neutral-900 rounded-full cursor-pointer"
          onDoubleClick={handleTrackDoubleClick}
        >
          {ranges.map(([start, end], index) => {
            const normStart = normalizeToToday(start);
            const normEnd = normalizeToToday(end);
            return (
              <div
                key={index}
                className="absolute h-full bg-voxa-teal-600 rounded-full transition-all"
                style={{
                  left: `${((normStart - DAY_START) / SLIDER_RANGE) * 100}%`,
                  width: `${((normEnd - normStart) / SLIDER_RANGE) * 100}%`,
                }}
              />
            );
          })}
        </SliderPrimitive.Track>

        {allValues.map((value, index) => (
          <SliderPrimitive.Thumb
            key={index}
            className="block w-5 h-5 bg-white border-2 border-voxa-teal-600 rounded-full shadow-md cursor-pointer transition-transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-voxa-teal-500"
            onDoubleClick={handleTrackDoubleClick}
          />
        ))}
      </SliderPrimitive.Root>

      <div className="mt-4 flex flex-wrap gap-2">
        <Button
          onClick={handleTrackDoubleClick}
          disabled={ranges.length === 3}
          className="py-1.5"
        >
          {t("createEditGoal.rangesSlider.addSlot")}
        </Button>
        {ranges.map(([start, end], index) => (
          <div
            key={index}
            className={cn(
              "flex items-center gap-2 py-1.5 pl-3 pr-2 bg-voxa-neutral-100 dark:bg-voxa-neutral-900 w-fit rounded-full",
              ranges.length === 1 && "pr-3"
            )}
          >
            <p className="text-sm font-medium">
              {t("createEditGoal.rangesSlider.slot", {
                index: index + 1,
                start: formatTime(start),
                end: formatTime(end),
              })}
            </p>
            {ranges.length > 1 && (
              <button
                onClick={() => removeRange(index)}
                className="p-1 rounded-full text-red-500 hover:bg-gray-200 dark:hover:bg-gray-900 transition-colors duration-200"
              >
                <MinusCircleIcon className="w-4 h-4" />
              </button>
            )}
          </div>
        ))}
      </div>
      <p className="mt-2 text-xs">
        {t("createEditGoal.rangesSlider.doubleClickToAdd")}
      </p>
    </div>
  );
};
