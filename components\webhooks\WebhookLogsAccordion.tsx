import { But<PERSON> } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  WebhookType,
  clearWebhookLogs,
} from "@/redux/BusinessDashboard/subSlices/WebhookSlice";
import { Dispatch } from "redux";
import { Layers, Calendar, Trash2 } from "lucide-react";

interface WebhookLogsAccordionProps {
  webhook: WebhookType | null;
  dispatch: Dispatch<any>;
}

export default function WebhookLogsAccordion({
  webhook,
  dispatch,
}: WebhookLogsAccordionProps) {
  return (
    <div className="w-full mt-6">
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem
          value="logs"
          className="border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg overflow-hidden"
        >
          <AccordionTrigger className="py-5 px-4 font-medium text-base bg-voxa-neutral-100 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700 transition-colors flex items-center">
            <div className="flex items-center gap-2">
              <Layers className="w-5 h-5 text-voxa-teal-500 dark:text-voxa-teal-400" />
              <span>Webhook Logs</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4">
            <div className="mt-4 mb-4 w-full">
              {webhook &&
              webhook.logs_events &&
              webhook.logs_events.length > 0 ? (
                <div className="border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg overflow-hidden shadow-sm">
                  <div className="w-full flex justify-between items-center p-4 bg-voxa-neutral-50 dark:bg-voxa-neutral-900 border-b border-voxa-neutral-200 dark:border-voxa-neutral-700">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-voxa-neutral-500 dark:text-voxa-neutral-400" />
                      <h3 className="font-medium">Event History</h3>
                      <span className="bg-voxa-teal-100 dark:bg-voxa-teal-900 text-voxa-teal-700 dark:text-voxa-teal-300 text-xs px-2 py-1 rounded-full">
                        {webhook.logs_events.length}{" "}
                        {webhook.logs_events.length === 1 ? "entry" : "entries"}
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => dispatch(clearWebhookLogs() as any)}
                      className="text-sm bg-voxa-neutral-100 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700 transition-colors"
                      type="button"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Clear Logs
                    </Button>
                  </div>
                  <div className="max-h-96 overflow-y-auto">
                    <table className="w-full">
                      <thead className="bg-voxa-neutral-100 dark:bg-voxa-neutral-800 sticky top-0">
                        <tr>
                          <th className="p-3 text-left text-sm font-medium">
                            Timestamp
                          </th>
                          <th className="p-3 text-left text-sm font-medium">
                            URL
                          </th>
                          <th className="p-3 text-left text-sm font-medium">
                            Method
                          </th>
                          <th className="p-3 text-left text-sm font-medium">
                            Response Code
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {webhook.logs_events.map((log, index) => (
                          <tr
                            key={index}
                            className="border-t border-voxa-neutral-200 dark:border-voxa-neutral-800 hover:bg-voxa-neutral-50 dark:hover:bg-voxa-neutral-800 transition-colors"
                          >
                            <td className="p-3 text-sm">
                              {new Date(log.timestamp).toLocaleString()}
                            </td>
                            <td className="p-3 text-sm truncate max-w-[200px]">
                              {log.url}
                            </td>
                            <td className="p-3 text-sm">
                              <span
                                className={`px-2 py-1 rounded-md text-xs font-medium ${
                                  log.method === "GET"
                                    ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100"
                                    : log.method === "POST"
                                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
                                    : log.method === "DELETE"
                                    ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100"
                                    : "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100"
                                }`}
                              >
                                {log.method}
                              </span>
                            </td>
                            <td className="p-3 text-sm">
                              <span
                                className={`px-2 py-1 rounded-md text-xs font-medium ${
                                  log.response_code.startsWith("2")
                                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
                                    : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100"
                                }`}
                              >
                                {log.response_code}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                  <Calendar className="w-10 h-10 mx-auto mb-4 text-voxa-neutral-300 dark:text-voxa-neutral-600" />
                  <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 mb-2">
                    No webhook logs available
                  </p>
                  <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 text-sm">
                    Logs will appear here when webhook events are processed
                  </p>
                </div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
