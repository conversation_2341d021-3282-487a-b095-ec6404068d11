import { Label } from "@radix-ui/react-label";
import { useState, useEffect } from "react";
import ButtonLoader from "../Loaders/ButtonLoader";
import {
  fetchTagsNotInGoal,
  addTagsToGoalThunk,
} from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CircleFadingPlus, X } from "lucide-react";
import CustomSelect from "@/components/CustomFormItems/Select";
import { useTranslation } from "react-i18next";

export default function AddTagsToGoal({
  goalID,
  onTagsAdded,
}: {
  goalID: string;
  onTagsAdded?: () => void;
}) {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const { tagsNotInGoal, addTagToGoalLoading } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardAssistants
  );
  const [open, setOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<any[]>([]);
  const [selectValue, setSelectValue] = useState("");

  useEffect(() => {
    if (open) {
      dispatch(fetchTagsNotInGoal({ goalID }));
      setSelectedTags([]);
      setSelectValue("");
    }
  }, [open, goalID, dispatch]);

  const availableTags = tagsNotInGoal.filter(
    (tag) => !selectedTags.some((t) => t.id === tag._id)
  );

  const handleAddTag = (tagId: string) => {
    const tag = tagsNotInGoal.find((t) => t._id === tagId);
    if (tag && !selectedTags.some((t) => t._id === tag._id)) {
      setSelectedTags([...selectedTags, tag]);
      setSelectValue("");
    }
  };

  const handleRemoveTag = (tagId: string) => {
    setSelectedTags(selectedTags.filter((t) => t.id !== tagId));
  };

  const handleAddTags = async () => {
    if (selectedTags.length === 0) return;
    const tagIDs = selectedTags.map((tag) => tag._id);
    await dispatch(addTagsToGoalThunk({ goalID, tagIDs }));
    setSelectedTags([]);
    dispatch(fetchTagsNotInGoal({ goalID }));
    setOpen(false);
    if (onTagsAdded) onTagsAdded();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <button className="w-full hover:bg-voxa-neutral-50 dark:hover:bg-accent flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0">
          <CircleFadingPlus />
          <span>{t("addTags.addTags")}</span>
        </button>
      </DialogTrigger>
      <DialogContent className="max-w-96">
        <DialogHeader>
          <DialogTitle>{t("addTags.addTagsToGoal")}</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-3 mt-4">
          <Label className="text-sm pb-1">{t("addTags.selectTags")}</Label>
          <CustomSelect
            items={availableTags.map((tag) => ({
              value: tag._id,
              label: (
                <div className="flex items-center gap-2">
                  <span
                    className="w-3.5 h-3.5 rounded-full border"
                    style={{ background: tag.color }}
                  />
                  <span>{tag.name}</span>
                </div>
              ),
              searchText: tag.name,
            }))}
            value={selectValue}
            onValueChange={(val) => {
              setSelectValue(val);
              handleAddTag(val);
            }}
            placeholder={
              availableTags.length === 0
                ? t("addTags.noAvailableTags")
                : t("addTags.selectATag")
            }
            searchable
          />
          {selectedTags.length > 0 && (
            <div className="flex flex-wrap item-center justify-center gap-2 mt-2">
              {selectedTags.map((tag) => (
                <span
                  key={tag._id}
                  className="flex items-center gap-1 px-2 py-1 rounded-full bg-voxa-neutral-100 dark:bg-voxa-neutral-800 border text-xs"
                  style={{ borderColor: tag.color }}
                >
                  <span
                    className="w-3 h-3 rounded-full mr-1"
                    style={{ background: tag.color }}
                  />
                  {tag.name}
                  <button
                    type="button"
                    className="ml-1 text-xs text-gray-500 hover:text-red-500"
                    onClick={() => handleRemoveTag(tag.id)}
                    aria-label={t("addTags.removeTag", { tag: tag.name })}
                  >
                    <X className="size-3" style={{ color: tag.color }} />
                  </button>
                </span>
              ))}
            </div>
          )}
          <DialogFooter className="mt-8">
            <DialogClose asChild>
              <Button className="dark:hover:text-voxa-neutral-100 hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950">
                {t("addTags.cancel")}
              </Button>
            </DialogClose>
            <Button
              onClick={handleAddTags}
              className={`max-sm:mb-3 ${
                addTagToGoalLoading
                  ? "cursor-not-allowed bg-voxa-teal-400"
                  : "bg-voxa-teal-600 hover:bg-voxa-teal-500 "
              } transition-all duration-150 text-white px-4 py-2 rounded-md font-medium flex justify-center items-center gap-2`}
            >
              {addTagToGoalLoading ? (
                <>
                  {t("addTags.addingTags")}
                  <ButtonLoader />
                </>
              ) : (
                t("addTags.addTags")
              )}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
