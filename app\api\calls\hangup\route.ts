import { NextResponse } from "next/server";
import axios from "axios";
import Goal from "@/models/Goal";

const TELECOM_ENDPOINT = process.env.TELECOM_ENDPOINT;

export async function POST(req: Request) {
  try {
    const { call_sid } = await req.json();

    const res = await axios.post(
      `${TELECOM_ENDPOINT}/hangup?call_sid=${call_sid}`,
    );
    const data = await res.data;

    if (res.status !== 200) {
      return NextResponse.json(
        { error: "Failed to hangup call" },
        { status: 500 }
      );
    }

    return NextResponse.json({ status: 200, response: data });
  } catch (err: any) {

    if (axios.isAxiosError(err) && err?.response?.data?.detail) {
      console.log("Error details:", err?.response);
      return NextResponse.json(
        {
          error: err?.response?.data?.detail,
        },
        { status: err?.response?.status || 500 }
      );
    }

    // Generic fallback
    return NextResponse.json(
      { error: err.message || "Failed to Hangup Call" },
      { status: 500 }
    );
  }
}
