{"page": {"title": "Your Scripts", "totalScripts": "Total Scripts:", "viewJsonScript": "View JSON <PERSON>t", "uploadScript": "Upload Script", "createCustomScript": "Create Custom Script", "viewContent": "View Content"}, "importScript": {"title": "<PERSON><PERSON><PERSON>", "scriptName": "Script Name", "scriptNamePlaceholder": "Enter script name", "fileError": "Only one .txt file is allowed", "uploading": "Uploading Script", "upload": "Upload", "modified": "Modified on", "dropIt": "Drop it here"}, "createCustomScript": {"generateScript": {"call_context": "Call Context:", "your_role_label": "Your Role:", "your_role_content": "Your name is {{ai_name}}, you speak in {{language}}, and act as a {{ai_role}} for the company {{entreprise_name}} located at {{address}}, making prospecting calls.", "style_label": "Style:", "style_content": "A human tone, kind, clear, with natural hesitations like \"uh\", \"ah\", and if possible a {{accent}} accent.", "objective_label": "Objective:", "objective_content": "At the beginning of the conversation, you will introduce yourself, then ask the {{number_of_questions}} questions below and transfer the call to an expert if necessary:", "client": "Client:", "ai": "AI:", "fallback_response": "No worries! Feel free to contact us at this number if needed. Have a great day! → End of conversation.", "question_label": "Question {{index}}:", "possible_answers": "Possible answers:", "go_to_next_question": "Move to Question {{next_question}}.", "end_call": "{{message_when_end_call}} → End of conversation.", "transfer_call": "{{message_when_transfer_call}} → Respond with this only. Do not add **any other sentence**, or any polite formula.", "special_cases": "Special Cases:", "case_voicemail": "If you detect a voicemail like \"beep\", \"leave a message\", \"answering machine\"", "case_voicemail_response": "{{voicemail_message}} → End", "case_silence": "if the client remains silent for more than 10 seconds", "case_silence_response": "No problem. An expert will call you back. Goodbye → End", "case_callback_request": "if the client is not available and asks to be called back", "case_callback_response": "No problem. An expert will call you back. Do you prefer morning, noon or evening? → End", "case_multiple_voices": "Background noise / multiple voices", "case_multiple_voices_response": "Uh, sorry, I hear several voices… Can you please repeat more clearly?", "case_repeat_request": "Repeat request", "case_repeat_response": "Of course! I'll repeat: {{derniere_phrase_dite}}", "case_out_of_scope": "Out-of-scope question", "case_out_of_scope_response": "Oh, that's a bit out of my scope, but… can we get back to {{goal}}?", "case_client_hangs_up": "Client hangs up / says \"goodbye\"", "case_client_hangs_up_response": "Very well, goodbye and have a great day! → End"}, "scriptHTML": {"button": "Create a Custom Script", "title": "New Script Format", "description": "Fill in the fields below to create your custom script.", "fields": {"script_name": {"label": "Script Name", "placeholder": "Qualification Script"}, "script_tags": {"label": "Add tags", "placeholder": "Tag name", "max_tags": "You can only add up to 5 tags per script."}, "language": {"label": "Language", "placeholder": "Language"}, "accent": {"label": "Accent", "placeholder": "Select an accent", "options": {"south": "Southern Accent", "north": "Northern Accent", "paris": "Parisian Accent", "quebec": "Quebec Accent"}}, "ai_role": {"label": "AI Role", "placeholder": "Energy Transition Expert"}, "entreprise_name": {"label": "Company Name", "placeholder": "ONRTECH"}, "address": {"label": "Address", "placeholder": "France, Champigny-sur-Marne"}, "number_of_questions": {"label": "Number of Questions", "placeholder": "Number of questions"}, "goal": {"label": "Goal", "placeholder": "Assess their eligibility for financial assistance and direct them to an expert if needed."}, "question": "Question {{index}}", "response_1": {"label": "Answer 1", "placeholder": "Yes"}, "response_2": {"label": "Answer 2", "placeholder": "No"}, "questions": {"placeholder_1": "Are you the owner of a single-family house or an apartment?", "placeholder_2": "Do you have a heat pump?", "placeholder_3": "Would you like to be connected immediately with an expert to learn more about the available financial aid?"}, "include_name": "Include client name", "message_when_end_call": {"label": "Message to end the call if client not concerned", "placeholder": "Alright, you're not concerned by our program. Have a great day."}, "message_when_transfer_call": {"label": "Message to transfer the call if client is concerned", "placeholder": "Thank you! Please hold on, your call is being transferred."}, "introduction": {"label": "Introduction", "placeholder": "Hello, my name is {{ai_name}}..."}}, "fields_secondary": {"select_accent_label": "Accent", "select_accent_placeholder": "Select an accent", "select_number_of_questions_placeholder": "Number of questions"}, "preview_label": "Script Preview:", "button_create": "Creating Sc<PERSON>...", "button_save": "Save Changes", "scriptCreated": "<PERSON><PERSON><PERSON> successfully created", "error": "Error while creating the script"}}}