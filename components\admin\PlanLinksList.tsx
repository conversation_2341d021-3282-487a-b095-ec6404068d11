"use client";

import {
  getAllPlanLinksForAdmin,
  deletePlanLink,
} from "@/actions/BillingActions"; // adjust import if needed
import PlanLinkDeleteDialog from "@/components/admin/PlanLinkDeleteDialog";
import CustomPagination from "@/components/pagination/CustomPagination";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plan, PlanLink } from "@/types";
import { CopyIcon, Link as LinkIcon, RefreshCw, Trash2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import useSWR from "swr";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { formatDistanceToNow } from "date-fns";

// Fetcher function for SWR
async function fetchPlanLinks([, page, limit]: [string, number, number]) {
  const res = await getAllPlanLinksForAdmin(page, limit);
  if (!res.success) throw new Error(res.error || "Failed to load plan links");
  return res.data;
}

function copyPlanLink(code: string) {
  const url = `${process.env.NEXT_PUBLIC_APP_URL}/businessDash/settings/billing?code=${code}`;
  navigator.clipboard.writeText(url);
  toast.success("Link copied!");
}

export default function PlanLinksList({}) {
  // Local pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const limit = 10;

  // Fetch plan links with SWR
  const {
    data: planLinksData,
    isLoading,
    isValidating,
    mutate,
  } = useSWR(["admin-plan-links", currentPage, limit], fetchPlanLinks, {
    keepPreviousData: true,
  });

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Find plan link to delete from fetched data
  const planLinkToDelete = planLinksData?.planLinks?.find(
    (pl: PlanLink) => pl._id === deletingId
  );

  const handleDeleteClick = (id: string) => {
    setDeletingId(id);
    setDeleteDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDeleteDialogOpen(false);
    setDeletingId(null);
  };

  const handleConfirmDelete = async () => {
    if (!deletingId) return;
    setIsDeleting(true);
    const res = await deletePlanLink(deletingId);
    setIsDeleting(false);
    if (res.success) {
      toast.success("Plan link deleted");
      handleCloseDialog();
      mutate(); // Refresh plan links
    } else {
      toast.error(res.error || "Failed to delete plan link");
    }
  };

  // Pagination change handler
  const handlePageChange = (page: number) => {
    if (planLinksData && page >= 1 && page <= planLinksData.totalPages) {
      setCurrentPage(page);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>
              <LinkIcon className="inline w-5 h-5 mr-2" />
              Plan Links
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              List of all plan links and their details
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => mutate()}
            disabled={isValidating}
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${isValidating ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading &&
        (!planLinksData || planLinksData.planLinks.length === 0) ? (
          <div className="text-center py-8 text-muted-foreground">
            <RefreshCw className="w-4 h-4 animate-spin inline mr-2" />
            Loading plan links...
          </div>
        ) : !planLinksData || planLinksData.planLinks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No plan links available.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <div className="w-full flex justify-between items-center p-4 bg-voxa-neutral-50 dark:bg-voxa-neutral-900 border-b border-voxa-neutral-200 dark:border-voxa-neutral-700">
              <div className="flex items-center gap-2">
                <LinkIcon className="w-4 h-4 text-voxa-neutral-500 dark:text-voxa-neutral-400" />
                <h3 className="font-medium">Plan Links</h3>
                {planLinksData && (
                  <span className="bg-voxa-teal-100 dark:bg-voxa-teal-900 text-voxa-teal-700 dark:text-voxa-teal-300 text-xs px-2 py-1 rounded-full">
                    {(planLinksData.page - 1) * planLinksData.limit + 1} -{" "}
                    {Math.min(
                      planLinksData.page * planLinksData.limit,
                      planLinksData.total
                    )}{" "}
                    of {planLinksData.total}{" "}
                    {planLinksData.total === 1 ? "link" : "links"}
                  </span>
                )}
              </div>
            </div>
            <table className="w-full min-w-[600px]">
              <thead>
                <tr>
                  <th className="p-2 text-left text-xs font-medium">Code</th>
                  <th className="p-2 text-left text-xs font-medium">
                    Plan Name
                  </th>
                  <th className="p-2 text-left text-xs font-medium">
                    Uses Left
                  </th>
                  <th className="p-2 text-left text-xs font-medium">Created</th>
                  <th className="p-2 text-right text-xs font-medium">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {planLinksData.planLinks.map((link: PlanLink, ind: number) => (
                  <tr
                    key={link._id}
                    className="plan-link border-t border-voxa-neutral-200 dark:border-voxa-neutral-800 duration-300"
                  >
                    <td className="p-2 font-mono">{link.code}</td>
                    <td className="p-2">{(link.plan_id as Plan).name}</td>
                    <td className="p-2">
                      {link.uses_left === null ? "∞" : link.uses_left}
                    </td>
                    <td className="p-2 text-xs text-muted-foreground">
                      {link.createdAt ? (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="inline-block cursor-pointer w-fit">
                                <div>
                                  {new Date(link.createdAt).toLocaleDateString(
                                    "en-US",
                                    {
                                      year: "numeric",
                                      month: "short",
                                      day: "numeric",
                                    }
                                  )}
                                </div>
                                <div className="text-xs text-voxa-neutral-400">
                                  {formatDistanceToNow(
                                    new Date(link.createdAt),
                                    { addSuffix: true }
                                  )}
                                </div>
                              </span>
                            </TooltipTrigger>
                            <TooltipContent>
                              {new Date(link.createdAt).toLocaleString()}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      ) : (
                        ""
                      )}
                    </td>
                    <td className="p-2 text-right flex gap-4 justify-end">
                      <button
                        type="button"
                        onClick={() => copyPlanLink(link.code!)}
                        className="hover:text-voxa-teal-600 transition-colors"
                        title="Copy plan link"
                      >
                        <CopyIcon className="w-4 h-4" />
                      </button>
                      <button
                        type="button"
                        onClick={() => handleDeleteClick(link._id)}
                        className="hover:text-red-600 transition-colors"
                        title="Delete plan link"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {/* Pagination Controls */}
            {planLinksData && planLinksData.totalPages > 1 && (
              <CustomPagination
                itemsPerPage={planLinksData.limit}
                totalItems={planLinksData.total}
                currentPage={planLinksData.page}
                onPageChange={handlePageChange}
                className="mt-4 pt-4 border-t border-voxa-neutral-200 dark:border-voxa-neutral-700"
              />
            )}
          </div>
        )}
        {/* PlanLinkDeleteDialog */}
        <PlanLinkDeleteDialog
          isOpen={deleteDialogOpen}
          onClose={handleCloseDialog}
          onConfirmDelete={handleConfirmDelete}
          planLink={planLinkToDelete}
          isDeleting={isDeleting}
        />
      </CardContent>
    </Card>
  );
}
