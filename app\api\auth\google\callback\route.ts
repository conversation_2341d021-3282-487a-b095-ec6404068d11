import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export const runtime = "nodejs";

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const code = searchParams.get("code");
  const state = searchParams.get("state"); // drive, sheets, or calendar

  if (!code) {
    return NextResponse.json({ error: "Missing code" }, { status: 400 });
  }

  const body = new URLSearchParams({
    code,
    client_id: process.env.GOOGLE_SHEETS_CLIENT_ID!,
    client_secret: process.env.GOOGLE_SHEETS_CLIENT_SECRET!,
    redirect_uri: process.env.GOOGLE_SHEETS_REDIRECT_URI!,
    grant_type: "authorization_code",
  });

  const tokenRes = await fetch("https://oauth2.googleapis.com/token", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: body.toString(),
  });

  if (!tokenRes.ok) {
    const details = await tokenRes.text();
    return NextResponse.json({ error: "Token exchange failed", details }, { status: tokenRes.status });
  }

  const tokens = await tokenRes.json(); // { access_token, refresh_token, ... }

  // Store tokens in cookie
  (await cookies()).set({
    name: `google_tokens_${state}`,
    value: JSON.stringify(tokens),
    httpOnly: true,
    sameSite: "lax",
    path: "/",
    maxAge: 60 * 60 * 24 * 7, // 7 days
  });


  // Redirect based on state
  let REDIRECT_URI = SITE_URL;
  switch (state) {
    case "drive":
      REDIRECT_URI += "/businessDash/integrations/storage/providers/drive";
      break;
    case "sheets":
      REDIRECT_URI += "/businessDash/integrations/files/providers/sheets";
      break;
    case "calendar":
      REDIRECT_URI += "/businessDash/integrations/productivity/providers/google_calendar";
      break;
    default:
      REDIRECT_URI += "/"; // fallback
  }

  return NextResponse.redirect(REDIRECT_URI);
}
