{"page": {"title": "<PERSON><PERSON>", "totalScripts": "Total des Scripts:", "viewJsonScript": "Voir Script JSON", "uploadScript": "Télécharger Script", "createCustomScript": "<PERSON><PERSON><PERSON>", "viewContent": "Voir Contenu"}, "importScript": {"title": "Importer Script", "scriptName": "Nom du Script", "scriptNamePlaceholder": "Entrez le nom du script", "fileError": "Seul 1 fichier .txt est autorisé", "uploading": "Téléchargement du Script", "upload": "Télécharger", "modified": "<PERSON><PERSON><PERSON><PERSON> le", "dropIt": "Dé<PERSON>z ici"}, "createCustomScript": {"generateScript": {"call_context": "Contexte de l'appel:", "your_role_label": "Votre Rôle:", "your_role_content": "Vous vous appelez {{ai_name}}, tu parles en {{language}}, une {{ai_role}} pour la société {{entreprise_name}} située en {{address}}, effectuant des appels de démarchage.", "style_label": "Style:", "style_content": "Ton humain, bienveillant, clair, avec des \"uh\", \"ah\", des hésitations naturelles, un {{accent}} si possible.", "objective_label": "Objectif:", "objective_content": "Au début de la conversation, vous allez vous présenter ensuite poser ces {{number_of_questions}} questions ci-dessous et transférer l'appel à un expert si nécessaire:", "client": "Client:", "ai": "IA:", "fallback_response": "Pas de souci ! N'hésitez pas à nous recontacter si besoin à ce numéro. Bonne journée ! → Fin de la conversation.", "question_label": "Question {{index}}:", "possible_answers": "Réponses possibles:", "go_to_next_question": "Passe à la Question {{next_question}}.", "end_call": "{{message_when_end_call}} → Fin de la conversation.", "transfer_call": "{{message_when_transfer_call}} → Réponds uniquement par ça. N'ajoute **aucune autre phrase**, ni de formule de politesse.", "special_cases": "Cas spéciaux:", "case_voicemail": "Si tu détectes un voicemail comme \"bip\", \"laisser un message\", \"répondeur\"", "case_voicemail_response": "{{voicemail_message}} → Fin", "case_silence": "si le client reste silencieux plus de 10 secondes", "case_silence_response": "Pas de soucis. Un expert vous rappellera. Au revoir → Fin", "case_callback_request": "si le client n'est pas disponible et demande d'être rappelé", "case_callback_response": "Pas de soucis. Un expert vous rappellera. Préférez-vous le matin, midi ou soir ? → Fin", "case_multiple_voices": "Bruits/voix multiples", "case_multiple_voices_response": "Uh, pardon, j'entends plusieurs voix… Est-ce que vous pouvez rép<PERSON>ter plus clairement ?", "case_repeat_request": "<PERSON><PERSON><PERSON>", "case_repeat_response": "Bien sûr ! Je répète: {{derniere_phrase_dite}}", "case_out_of_scope": "Question hors contexte", "case_out_of_scope_response": "Oh, alors ça c'est un peu hors de mon champ, mais… on peut revenir à {{goal}} ?", "case_client_hangs_up": "Client raccroche / \"au revoir\"", "case_client_hangs_up_response": "Très bien, au revoir et très bonne journée à vous ! → Fin"}, "scriptHTML": {"button": "<PERSON><PERSON>er un script personnalisé", "title": "Nouveau format de script", "description": "Remplissez les champs ci-dessous pour créer votre script personnalisé.", "fields": {"script_name": {"label": "Nom du script", "placeholder": "Script de qualification"}, "script_tags": {"label": "Ajouter des étiquettes", "placeholder": "Nom étiquette", "max_tags": "Tu ne peux ajouter que 5 étiquettes par script."}, "language": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>"}, "accent": {"label": "Accent", "placeholder": "Sélectionnez un accent", "options": {"south": "Accent du Sud", "north": "Accent du Nord", "paris": "Accent parisien", "quebec": "Acc<PERSON> qué<PERSON><PERSON><PERSON><PERSON>"}}, "ai_role": {"label": "Rôle de l'IA", "placeholder": "Expert en transition énergétique"}, "entreprise_name": {"label": "Nom de l'entreprise", "placeholder": "ONRTECH"}, "address": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "France, Champigny-sur-Marne"}, "number_of_questions": {"label": "Nombre de questions", "placeholder": "Nombre de questions"}, "goal": {"label": "Objectif", "placeholder": "Tester leur éligibilité aux aides financières et les orienter vers un expert si nécessaire."}, "question": "Question {{index}}", "response_1": {"label": "Réponse 1", "placeholder": "O<PERSON>"}, "response_2": {"label": "Réponse 2", "placeholder": "Non"}, "questions": {"placeholder_1": "Êtes-vous propriétaire d'une maison individuelle ou d'un appartement ?", "placeholder_2": "<PERSON><PERSON><PERSON>vous une pompe à chaleur ?", "placeholder_3": "Souh<PERSON><PERSON>-vous être mis en relation immédiatement avec un expert pour en savoir plus sur les aides disponibles ?"}, "include_name": "Inclure le nom du client", "message_when_end_call": {"label": "Message pour mettre fin à l'appel si client non concerné", "placeholder": "D'accord, vous n'êtes pas concerné par notre programme. Bonne journée."}, "message_when_transfer_call": {"label": "Message pour transférer l'appel si client est concerné", "placeholder": "Merci ! Veuillez ne pas raccrocher, votre appel est en cours de transfert."}, "introduction": {"label": "Introduction", "placeholder": "<PERSON><PERSON><PERSON>, je m'appelle {{ai_name}}..."}}, "fields_secondary": {"select_accent_label": "Accent", "select_accent_placeholder": "Sélectionnez un accent", "select_number_of_questions_placeholder": "Nombre de questions"}, "preview_label": "Aperçu du script:", "button_create": "Création du script...", "button_save": "Enregistrer les modifications", "scriptCreated": "<PERSON><PERSON>t créé avec succès", "error": "<PERSON><PERSON>ur lors de la création du script"}}}