import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import dbConnect from "@/lib/mongodb";
import Entreprise from "@/models/Entreprise";
import { EntrepriseAdmin, Role } from "@/models/User";

export async function POST(req: Request) {
  try {
    await dbConnect();

    const { name, email, password } = await req.json();

    if (!name || !email || !password) {
      return NextResponse.json(
        { error: "Tous les champs sont obligatoires." },
        { status: 400 }
      );
    }

    const existingEntreprise = await EntrepriseAdmin.findOne({ email })

    if (existingEntreprise) {
      return NextResponse.json(
        { error: "Cet email est déjà utilisé." },
        { status: 400 }
      );
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const EntrepriseUser = await EntrepriseAdmin.create({
        name,
        email,
        role: Role.ENTREPRISE_ADMIN,
        password: hashedPassword,
    })

    const entreprise = await Entreprise.create({
      name,
      email,
      admin: EntrepriseUser._id,
    });

    if(!EntrepriseUser || !entreprise) {
        return NextResponse.json(
            { error: "Erreur lors de la création du compte entreprise." },
            { status: 500 }
        );
    }

    return NextResponse.json({
      message: "Compte entreprise créé avec succès.",
      user: {
        id: EntrepriseUser._id,
        name: EntrepriseUser.name,
        email: EntrepriseUser.email,
      },
    }, {status: 201})

  } catch (error) {
    console.error("Erreur d'inscription entreprise:", error);
    return NextResponse.json(
      { error: "Erreur interne du serveur." },
      { status: 500 }
    );
  }
}
