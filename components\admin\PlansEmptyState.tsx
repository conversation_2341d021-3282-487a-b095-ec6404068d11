"use client";

import React from "react";
import { Eye, RefreshCw } from "lucide-react";

interface PlansEmptyStateProps {
  isLoading: boolean;
}

export default function PlansEmptyState({ isLoading }: PlansEmptyStateProps) {
  if (isLoading) {
    return (
      <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
        <div className="flex items-center justify-center gap-2 text-muted-foreground">
          <RefreshCw className="w-4 h-4 animate-spin" />
          Loading plans...
        </div>
      </div>
    );
  }

  return (
    <div className="text-center py-12 border border-dashed rounded-lg">
      <Eye className="w-12 h-12 mx-auto mb-4 text-muted-foreground opacity-50" />
      <h3 className="text-lg font-medium text-muted-foreground mb-2">
        No plans created yet
      </h3>
      <p className="text-sm text-muted-foreground">
        Create your first subscription plan to get started.
      </p>
    </div>
  );
}
