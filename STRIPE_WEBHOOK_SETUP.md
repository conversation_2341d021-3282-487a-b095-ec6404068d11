# Stripe Webhook Configuration Guide

## What Are Webhooks?

Webhooks are HTTP POST requests that <PERSON><PERSON> sends to your server when specific events occur (like successful payments). They're completely separate from redirect URLs.

## Current Setup

### 1. **Webhook Endpoint**

- **URL**: `https://yourdomain.com/api/webhooks/stripe`
- **File**: `app/api/webhooks/stripe/route.ts`
- **Events**: Listening for `checkout.session.completed` and `payment_intent.succeeded`

### 2. **How It Works Now**

```
User clicks "Checkout"
    ↓
Stripe Checkout Session created with metadata
    ↓
User completes payment
    ↓
<PERSON><PERSON> automatically calls webhook → Balance updated
    ↓
User redirected back → Sees success message
```

## Required Stripe Dashboard Configuration

### 1. **Add Webhook Endpoint**

1. Go to Stripe Dashboard → Developers → Webhooks
2. Click "Add endpoint"
3. Enter URL: `https://yourdomain.com/api/webhooks/stripe`
4. Select events:
   - `checkout.session.completed`
   - `payment_intent.succeeded`

### 2. **Get Webhook Secret**

1. After creating webhook, click on it
2. <PERSON>py the "Signing secret"
3. Add to `.env.local`:
   ```
   STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
   ```

### 3. **Test Webhook**

- Use Stripe CLI: `stripe listen --forward-to localhost:3000/api/webhooks/stripe`
- Or use Stripe's webhook testing tools

## Current Flow

### ✅ **Correct Architecture Now:**

1. **Checkout Session Creation** (`createPaymentLink` function)

   - Creates session with proper metadata
   - Metadata includes: `entreprise_id`, `admin_id`, `amount`, `type`

2. **Webhook Processing** (`/api/webhooks/stripe/route.ts`)

   - Receives `checkout.session.completed` event
   - Extracts metadata from session
   - Updates enterprise balance automatically

3. **User Experience**
   - User clicks "Checkout" → Redirected to Stripe
   - Completes payment → Webhook fires → Balance updated
   - User redirected back → Sees success message

## Key Benefits

- ✅ **Reliable**: Works even if user closes browser
- ✅ **Secure**: Server-side processing only
- ✅ **Automatic**: No manual verification needed
- ✅ **Metadata**: Properly passed through checkout sessions

## Debugging

Check webhook logs in:

1. Stripe Dashboard → Developers → Webhooks → [Your endpoint] → Attempts
2. Your server logs for console.log messages
3. Network tab when testing payments
