import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import {
  Filters,
  setFiltersInput, // <-- use setFiltersInput
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import {
  PhoneIncoming,
  PhoneOutgoing,
  PhoneMissed,
  CheckSquare,
  Phone,
  PhoneForwarded,
  Check,
  MessageSquare,
  MessageCircle,
  Video,
} from "lucide-react";

const callTypeOptions = [
  {
    id: "inbound",
    label: "Inbound",
    icon: <PhoneIncoming className="text-green-500 h-4 w-4" />,
  },
  {
    id: "outbound",
    label: "Outbound",
    icon: <PhoneOutgoing className="text-blue-500 h-4 w-4" />,
  },
  {
    id: "missed",
    label: "Missed",
    icon: <PhoneMissed className="text-red-500 h-4 w-4" />,
  },
  {
    id: "answered",
    label: "Answered",
    icon: <CheckSquare className="text-green-600 h-4 w-4" />,
  },
  {
    id: "notTransferred",
    label: "Not Transferred",
    icon: <Phone className="text-gray-500 h-4 w-4" />,
  },
  {
    id: "transferred",
    label: "Transferred",
    icon: <PhoneForwarded className="text-purple-500 h-4 w-4" />,
  },
  {
    id: "calls",
    label: "Calls",
    icon: <Phone className="text-blue-600 h-4 w-4" />,
  },
  {
    id: "whatsapp",
    label: "WhatsApp",
    icon: <MessageCircle className="text-green-600 h-4 w-4" />,
  },
  {
    id: "sms",
    label: "SMS",
    icon: <MessageSquare className="text-yellow-600 h-4 w-4" />,
  },
  {
    id: "meet",
    label: "Meetings",
    icon: <Video className="text-purple-600 h-4 w-4" />,
  },
];

type CallTypeKey = keyof Filters["conversationTypes"];

export default function ConversationTypeFilters() {
  const dispatch = useDispatch<AppDispatch>();
  const callTypeFilters = useSelector(
    (state: RootState) => state.analytics.filtersInput.conversationTypes // <-- use filtersInput
  );

  const handleFilterToggle = (id: string) => {
    dispatch(
      setFiltersInput({
        conversationTypes: {
          ...callTypeFilters,
          [id]: !callTypeFilters[id as CallTypeKey],
        },
      })
    );
  };

  return (
    <div className="space-y-3 px-1">
      <h3 className="font-bold">Conversation Types</h3>
      <div className="flex flex-col gap-1.5">
        {callTypeOptions.map((option) => (
          <div
            key={option.id}
            onClick={() => handleFilterToggle(option.id)}
            className={`
              flex items-center gap-2 p-1.5 rounded-md cursor-pointer transition-all
              border border-border text-xs
              hover:scale-[1.02]
              ${
                callTypeFilters[option.id as CallTypeKey]
                  ? "bg-primary/10 border-primary shadow-sm"
                  : "bg-background hover:border-muted-foreground"
              }
            `}
          >
            <div className="flex-shrink-0">{option.icon}</div>
            <span className="text-xs font-medium">{option.label}</span>
            {callTypeFilters[option.id as CallTypeKey] && (
              <Check className="h-3.5 w-3.5 ml-auto text-primary" />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
