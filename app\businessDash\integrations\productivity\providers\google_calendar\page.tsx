'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

type CalendarEvent = {
  id: string;
  summary: string;
  start: {
    dateTime?: string;
    date?: string;
  };
};

export default function GoogleCalendarPage() {
  const [events, setEvents] = useState<CalendarEvent[] | null>(null);
  const [error, setError] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  const fetchEvents = async () => {
    try {
      setLoading(true);
      setError(null);

      const res = await fetch('/api/calendar/events');
      if (res.status !== 200) {
        setError(res.status);
        return;
      }

      const data = await res.json();
      setEvents(data);
    } catch (err) {
      setEvents(null);
      setError(500);
    } finally {
      setLoading(false);
    }
  };

  const disconnect = async () => {
    await fetch("/api/auth/google/logout", { method: "POST" });
    setEvents(null);
    setError(null);
    setLoading(false);
    router.refresh();
  };

  useEffect(() => {
    fetchEvents();
  }, []);

  // Not authorized
  if (error === 401) {
    return (
      <div className="p-10">
        <button
          className="px-4 py-2 bg-blue-600 text-white rounded"
          onClick={() => (window.location.href = "/api/auth/google/calendar")}
        >
          Connect Google Account
        </button>
      </div>
    );
  }

  // Loading state
  if (loading) {
    return <p className="p-10 text-gray-500">Chargement des événements…</p>;
  }

  // No events
  if (!events || events.length === 0) {
    return (
      <div className="p-10">
        <p className="text-gray-500">Aucun événement à venir.</p>
        <button className="mt-4 text-sm text-blue-600 hover:underline" onClick={fetchEvents}>
          ↻ Réessayer
        </button>
        <button className="block mt-6 text-sm text-red-600 hover:underline" onClick={disconnect}>
          ⎋ Se déconnecter
        </button>
      </div>
    );
  }

  // Connected view
  return (
    <div className="max-w-3xl mx-auto p-6">
      <div className="flex items-start justify-between">
        <h1 className="text-2xl font-semibold mb-4">Événements Google Calendar</h1>
        <button className="text-sm text-red-600 hover:underline" onClick={disconnect}>
          ⎋ Se déconnecter
        </button>
      </div>

      <ul className="space-y-2">
        {events.map((event) => (
          <li
            key={event.id}
            className="p-3 bg-gray-50 rounded shadow-sm"
          >
            <strong>{event.summary || "Sans titre"}</strong><br />
            <span className="text-sm text-gray-600">
              {event.start?.dateTime || event.start?.date || "Date inconnue"}
            </span>
          </li>
        ))}
      </ul>

      <div className="mt-8 flex items-center gap-4">
        <button className="text-sm text-gray-500 hover:underline" onClick={fetchEvents}>
          ↻ Rafraîchir
        </button>
      </div>
    </div>
  );
}
