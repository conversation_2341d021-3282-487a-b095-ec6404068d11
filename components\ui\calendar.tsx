"use client";

import * as React from "react";
import { DayPicker } from "react-day-picker";
import { cn } from "@/lib/utils";
import "react-day-picker/style.css";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
  showOutsideDays = false,
  className,
  classNames,
  disabled,
  ...props
}: CalendarProps) {
  return (
    <div className="calendar-custom">
      <DayPicker
        animate
        showOutsideDays={showOutsideDays}
        disabled={disabled}
        className={cn(
          "px-4 bg-voxa-neutral-50 dark:bg-voxa-neutral-950 text-voxa-neutral-900 dark:text-white border-white rounded-lg",
          className
        )}
        classNames={{
          months:
            "w-full flex flex-col items-center sm:items-start gap-4 sm:flex-row rounded-lg text-voxa-teal-600 dark:text-voxa-teal-500 text-medium",
          button_previous: "-mt-1",
          button_next: "-mt-1 mx-2",
          caption_label: "pl-1.5 pt-2.5 text-foreground font-semibold",
          chevron: "rtl:rotate-180 fill-voxa-teal-500 dark:fill-voxa-teal-600",
          day: "hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-800 rounded-full  p-0 text-[#343434] dark:text-voxa-neutral-200",
          day_button: "w-8 h-8 font-medium text-sm",
          range_start: "",
          range_middle: "",
          range_end: "",
          today: "",
          selected: "bg-voxa-teal-600 dark:bg-voxa-teal-600 text-white",
          disabled: "text-muted-foreground opacity-50",
          ...classNames,
        }}
        {...props}
      />
    </div>
  );
}
Calendar.displayName = "Calendar";

export { Calendar };
