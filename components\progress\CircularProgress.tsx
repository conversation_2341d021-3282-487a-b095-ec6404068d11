import React from "react";

const CircularProgress = ({ progress }: { progress: number }) => {
  const strokeDashoffset = 100 - progress; // Assuming full circle is 100

  return (
    <div className="relative size-70">
      <svg
        className="size-full -rotate-90"
        viewBox="0 0 36 36"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background Circle */}
        <circle
          cx="18"
          cy="18"
          r="16"
          fill="none"
          className="stroke-current text-gray-200 dark:text-voxa-neutral-700"
          strokeWidth="2"
        />
        {/* Progress Circle */}
        <circle
          cx="18"
          cy="18"
          r="16"
          fill="none"
          className="stroke-current text-blue-600 dark:text-blue-500"
          strokeWidth="2"
          strokeDasharray="100"
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
        />
      </svg>

      {/* Percentage Text */}
      <div className="absolute top-1/2 start-1/2 transform -translate-y-1/2 -translate-x-1/2">
        <span className="text-center text-4xl font-bold text-blue-600 dark:text-blue-500">
          {progress}%
        </span>
      </div>
    </div>
  );
};

export default CircularProgress;
