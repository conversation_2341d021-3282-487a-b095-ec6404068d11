"use client";

import React from "react";
import * as Flags from "country-flag-icons/react/3x2";
import { AssistantGoalDropdown } from "../../dropdowns/AssistantGoalDropdown";
import { PhoneOutgoingIcon, PhoneIncomingIcon, Languages } from "lucide-react";
import Image from "next/image";
import GoogleMeetImage from "@/public/images/Icons/google_meet.svg";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import { setOpenGoalID } from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import { AnyGoal } from "@/types";
import { useTranslation } from "react-i18next";

interface GoalCardProps {
  goal: AnyGoal;
}

const GoalCard: React.FC<GoalCardProps> = ({ goal }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { openGoalID } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardAssistants
  );
  const { t } = useTranslation("assistants");

  const FlagComponent = Flags[goal.country as keyof typeof Flags];

  return (
    <div className="h-fit grid grid-cols-5 p-1.5 gap-1 sm:gap-2 bg-voxa-neutral-50 dark:text-voxa-neutral-300 dark:bg-voxa-neutral-900/80 rounded-md items-center justify-between w-full">
      <div className="col-span-4 flex gap-1 sm:gap-2 items-center">
        <div className="w-5 h-4">
          {FlagComponent ? (
            <FlagComponent className="w-5 h-4" />
          ) : (
            <span className="w-5 h-4">🌍</span>
          )}
        </div>
        <span
          title={goal.name}
          className="w-full block text-sm font-semibold truncate"
        >
          {goal.name}
        </span>
      </div>
      <div className="w-full justify-end flex items-center gap-1 sm:gap-2 col-span-1">
        {goal.goal_template_type === "GOOGLE_MEET" ? (
          <Image
            src={GoogleMeetImage}
            alt={t("goalCard.googleMeetIconAlt")}
            width={16}
            height={16}
            className="w-4 h-4"
          />
        ) : goal.goal_template_type === "DEMARCHAGE" ? (
          goal.goalType && (
            <span className="text-xs font-medium lowercase">
              {goal.goalType === "OUTGOING" ? (
                <PhoneOutgoingIcon size={16} className="text-green-600" />
              ) : (
                goal.goalType === "INCOMING" && (
                  <PhoneIncomingIcon size={16} className="text-orange-600" />
                )
              )}
            </span>
          )
        ) : (
          goal.goal_template_type === "MULTI_TRANSLATION" && (
            <Languages className="w-4 h-4 text-indigo-500" />
          )
        )}
        <AssistantGoalDropdown
          goalName={goal.name!}
          goalID={goal._id!.toString()}
          goalStatus={goal.status!}
          goalType={goal.goalType!}
          isOpen={goal._id === openGoalID}
          setOpenGoalID={(id: string | null) => dispatch(setOpenGoalID(id))}
          goal_template_type={goal.goal_template_type || "DEMARCHAGE"}
        />
      </div>
    </div>
  );
};

export default GoalCard;
