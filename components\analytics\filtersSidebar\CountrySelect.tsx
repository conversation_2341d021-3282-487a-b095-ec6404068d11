"use client";

import { useState, useEffect } from "react";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover-dialog";
import {
  Command,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import { Button } from "@/components/ui/button";
// Replace Lucide icons with Material UI icons
import LanguageIcon from "@mui/icons-material/Language";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import Image from "next/image";
import { COUNTRIES_SIMPLE, CountryCode } from "@/lib/countries";
import { useDispatch, useSelector } from "react-redux";
import { setFiltersInput } from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { RootState } from "@/redux/store";

export default function CountrySelect() {
  const dispatch = useDispatch();
  const selectedCountries = useSelector(
    (state: RootState) => state.analytics.filtersInput.countries // <-- use filtersInput
  );

  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<CountryCode[]>(selectedCountries);

  // Update local state when redux state changes
  useEffect(() => {
    setSelected(selectedCountries);
  }, [selectedCountries]);

  const toggleCountry = (alpha2: CountryCode) => {
    const newSelected = selected.includes(alpha2)
      ? selected.filter((c) => c !== alpha2)
      : [...selected, alpha2];

    setSelected(newSelected);
    dispatch(setFiltersInput({ countries: newSelected })); // <-- use setFiltersInput
  };

  const clearCountries = () => {
    setSelected([]);
    dispatch(setFiltersInput({ countries: [] })); // <-- use setFiltersInput
  };

  const getFlagUrl = (alpha2: string) =>
    `https://flagcdn.com/w20/${alpha2.toLowerCase()}.png`;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          className="w-[240px] justify-start"
        >
          <LanguageIcon className="mr-2 h-5 w-5" />
          {selected.length > 0
            ? `${selected.length} ${
                selected.length === 1 ? "country" : "countries"
              } selected`
            : "All countries"}
        </Button>
      </PopoverTrigger>

      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput placeholder="Search countries..." className="h-9" />
          <CommandList>
            <CommandEmpty>No countries found.</CommandEmpty>
            <CommandGroup>
              {COUNTRIES_SIMPLE.map((country) => (
                <CommandItem
                  key={country.alpha2}
                  value={country.name}
                  onSelect={() => toggleCountry(country.alpha2)}
                  className="justify-between px-2 py-2"
                >
                  <div className="flex items-center gap-2">
                    <Image
                      src={getFlagUrl(country.alpha2)}
                      alt={country.name}
                      width={20}
                      height={15}
                    />
                    <span>{country.name}</span>
                  </div>
                  {selected.includes(country.alpha2) ? (
                    <CheckBoxIcon className="h-6 w-6 text-primary flex-shrink-0" />
                  ) : (
                    <CheckBoxOutlineBlankIcon className="h-6 w-6 text-muted-foreground flex-shrink-0" />
                  )}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
        <div className="flex items-center justify-end gap-2 p-2 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={clearCountries}
            className="text-xs"
          >
            Clear
          </Button>
          <Button size="sm" onClick={() => setOpen(false)} className="text-xs">
            OK
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
