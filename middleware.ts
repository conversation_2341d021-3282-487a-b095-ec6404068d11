import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import authOptions from "./lib/AuthOptions"; // Assuming authOptions is configured for NextAuth
import { getToken } from "next-auth/jwt";

export const config = {
  matcher: [
    /*
     * Match all paths except for:
     * 1. /api routes
     * 2. /_next (Next.js internals)
     * 3. /_static (inside /public)
     * 4. all root files inside /public (e.g. /favicon.ico)
     * 5. / root route
     */
    "/businessDash",
    "/((?!api/|_next/|_static/|temp-images|_vercel|[\\w-]+\\.\\w+).*)",
  ],
};

export default async function middleware(req: NextRequest) {
  const url = req.nextUrl;

  const newHeaders = new Headers(req.headers);
  newHeaders.set("x-current-path", req.url);
  if (req.headers.get("next-action")) {
    return NextResponse.next();
  }
  const path = url.pathname;
  if (path.startsWith("/businessDash")) {
    const token = await getToken({ req });
    if (!token) {
      const loginUrl = new URL("/", req.url);
      return NextResponse.redirect(loginUrl);
    }
    if (token.role !== "ENTREPRISE_ADMIN") {
      const NotAllowedUrl = new URL("/NotAllowed", req.url);
      return NextResponse.redirect(NotAllowedUrl);
    }
  }
  // Rewrite the response to include new headers and the same path
  return NextResponse.rewrite(new URL(url.pathname, req.url), {
    headers: newHeaders,
  });
}
