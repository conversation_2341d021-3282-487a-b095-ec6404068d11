import * as Flags from "country-flag-icons/react/3x2";
import { Earth } from "lucide-react";
import CustomSelect from "../CustomFormItems/Select";
import { useTranslation } from "react-i18next";

const countries = ["US", "TN", "FR", "DE", "IT", "ES", "GB", "CA", "AU"];

export function CountriesSelect({
  required,
  label,
  country,
  selectCountry,
  classnames,
  autoDefaultValue = false,
}: {
  required?: boolean;
  label?: string;
  country: string;
  selectCountry: any;
  classnames?: string;
  autoDefaultValue?: boolean;
}) {
  const { t } = useTranslation("assistants");
  const countryItems = countries.map((code) => {
    const FlagComponent = Flags[code as keyof typeof Flags];
    return {
      value: code,
      searchText: code,
      label: (
        <div className="flex items-center gap-2">
          {FlagComponent ? (
            <FlagComponent className="w-6 h-4 -translate-y-px" />
          ) : (
            <Earth className="w-6 h-4 -translate-y-px" />
          )}
          <span className="font-medium">{code}</span>
        </div>
      ),
    };
  });

  return (
    <CustomSelect
      required={required}
      label={label}
      value={country}
      autoDefaultValue={autoDefaultValue}
      onValueChange={selectCountry}
      placeholder={t("countriesSelect.selectCountry")}
      items={countryItems}
      searchable={true}
      className={classnames}
      parentClassName="w-full"
    />
  );
}
