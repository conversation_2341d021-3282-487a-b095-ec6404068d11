"use client";

import FilterAltRoundedIcon from "@mui/icons-material/FilterAltRounded";
import { AccordionLists } from "@/components/Dashboard/Accordions/WaitingList";
import { JSX, useEffect, useState, useCallback, useRef, useMemo } from "react";
import ClientCard from "@/components/Dashboard/Cards/ClientCard";
import CustomButton from "@/components/CustomFormItems/Button";
import { ChevronsDown, Loader2, PhoneCall, RedoDotIcon } from "lucide-react";
import { useSelector, useDispatch } from "react-redux";
import {
  fetchMoreConversations,
  getCallLogsStats,
  findConversationsByGoalID,
  setGoalID,
  setFiltersOpen,
  updateClientsFiltered,
  setExportOpen,
  handleCloseCallDetails,
  handleCloseTranscript,
} from "@/redux/BusinessDashboard/subSlices/RootSlice";
import { AppDispatch, RootState } from "@/redux/store";
import dynamic from "next/dynamic";
import { GetEntrepriseConversationLogsStats } from "@/actions/ConversationActions";
import { PhoneMissedIcon, Voicemail, MessageSquareText } from "lucide-react";
import CallLogsStatsCard from "@/components/Dashboard/Cards/CallLogsStatsCard";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { FilterBadges } from "@/components/popups/FiltersPopup";
import { useTranslation } from "react-i18next";
import { useSidebar } from "@/components/ui/sidebar";
import CircularLoaderSmall from "@/components/Loaders/CircularLoaderSmall";
import { usePathname } from "next/navigation";
import Transcript from "@/components/Dashboard/SideComponents/Transcript";
import History from "@/components/Dashboard/SideComponents/History";

const ExportPopup = dynamic(() => import("@/components/popups/ExportPopup"), {
  ssr: false,
});
const FiltersPopup = dynamic(() => import("@/components/popups/FiltersPopup"), {
  ssr: false,
});

const callTypeConfig: {
  icon: JSX.Element;
  color: string;
}[] = [
  {
    icon: <PhoneMissedIcon className="text-[#EF4444] w-5 h-5" />,
    color: "bg-[#ef4444]/20",
  },
  {
    icon: <PhoneCall className="text-[#F59E0B] w-5 h-5" />,
    color: "bg-[#F59E0B]/20",
  },
  {
    icon: <RedoDotIcon className="text-[#3B82F6] w-5 h-5" />,
    color: "bg-[#3B82F6]/20",
  },
  {
    icon: <MessageSquareText className="text-[#10B981] w-5 h-5" />,
    color: "bg-[#10B981]/20",
  },
  {
    icon: <Voicemail className="text-[#8B5CF6] w-5 h-5" />,
    color: "bg-[#8B5CF6]/20",
  },
];

interface CallLogGroupsProps {
  clientsFiltered: any[];
  cardRefs: React.RefObject<{ [key: string]: HTMLDivElement | null }>;
}

const CallLogGroups: React.FC<CallLogGroupsProps> = ({
  clientsFiltered,
  cardRefs,
}) => {
  const { t } = useTranslation("callLo");
  const renderedCallSIDs = new Set();
  const groups: (typeof clientsFiltered)[] = [];

  clientsFiltered?.forEach((call: any) => {
    if (renderedCallSIDs.has(call.CallSid)) return;

    const child = clientsFiltered.find(
      (c: any) => c.CallSid === call.child_call_sid
    );
    if (child) {
      groups.push([child, call]);
      renderedCallSIDs.add(call.CallSid);
      renderedCallSIDs.add(child.CallSid);
    } else if (
      !clientsFiltered.some((c: any) => c.child_call_sid === call.CallSid)
    ) {
      groups.push([call]);
      renderedCallSIDs.add(call.CallSid);
    }
  });

  return (
    <>
      {groups.map((group, groupIndex) => {
        const isGrouped = group.length > 1;
        const type = group[0].type;
        return (
          <div
            key={groupIndex}
            className={`relative ${
              isGrouped
                ? "bg-voxa-neutral-100 dark:bg-voxa-neutral-900 rounded-md p-2 flex flex-col gap-2"
                : ""
            } mb-2`}
          >
            {isGrouped &&
              (type === "CALL" ? (
                <div className="absolute -translate-y-2 left-2 z-10 bg-black border border-voxa-neutral-700 text-voxa-teal-600 text-xs font-semibold px-2 py-0.5 rounded-full shadow-sm">
                  {t("callLogGroups.transfer")}
                </div>
              ) : type === "WHATSAPP" ? (
                <div className="absolute -translate-y-2 left-2 z-10 bg-black border border-voxa-neutral-700 text-voxa-teal-600 text-xs font-semibold px-2 py-0.5 rounded-full shadow-sm">
                  WhatsApp
                </div>
              ) : type === "SMS" ? (
                <div className="absolute -translate-y-2 left-2 z-10 bg-black border border-voxa-neutral-700 text-voxa-teal-600 text-xs font-semibold px-2 py-0.5 rounded-full shadow-sm">
                  SMS
                </div>
              ) : (
                ""
              ))}

            {group.map((call: any) => (
              <div
                key={call._id}
                ref={(el) => {
                  if (el && call._id) {
                    cardRefs.current[call._id] = el;
                  }
                }}
              >
                <ClientCard call={call} />
              </div>
            ))}
          </div>
        );
      })}
    </>
  );
};

export default function CallLogs() {
  const { t } = useTranslation("callLogs");
  const dispatch = useDispatch<AppDispatch>();
  const pathname = usePathname();

  useEffect(() => {
    if (pathname === "/businessDash") {
      dispatch(handleCloseCallDetails());
      dispatch(handleCloseTranscript());
    }
  }, [pathname, dispatch]);

  const {
    clientsFiltered,
    stats,
    goalID,
    loadMore,
    filtersOpen,
    exportOpen,
    NotesOpen,
    callTranscriptOpen,
    totalLogs,
    loading,
    filtered,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardRoot
  );
  const { open } = useSidebar();
  const [searching, setSearching] = useState(false);
  const cardRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const translatedStats = useMemo(() => {
    return stats.map((stat: any) => {
      const translatedStat = {
        ...stat,
        title: stat.titleKey ? t(stat.titleKey) : stat.title,
      };
      if (stat.subStats) {
        const translatedSubStats: Record<string, any> = {};
        Object.entries(stat.subStats).forEach(([key, value]) => {
          if (key.endsWith("Key")) {
            return;
          }
          const translationKey = (stat.subStats as any)[`${key}Key`];
          if (translationKey) {
            translatedSubStats[t(translationKey)] = value;
          } else {
            translatedSubStats[key] = value;
          }
        });

        translatedStat.subStats = translatedSubStats;
      }

      return translatedStat;
    });
  }, [stats, t]);

  const getStats = useCallback(async () => {
    await dispatch(getCallLogsStats());
  }, [dispatch]);

  const getData = async () => {
    await GetEntrepriseConversationLogsStats();
  };

  useEffect(() => {
    getStats();
    getData();
  }, [dispatch, getStats]);

  useEffect(() => {
    if (clientsFiltered && clientsFiltered.length > 0) {
      const newLines: { top: number; left: number; height: number }[] = [];

      clientsFiltered.forEach((callA: any) => {
        const callB = clientsFiltered.find(
          (c: any) => c.child_call_sid === callA.CallSid
        );
        if (!callB) return;

        const fromEl = cardRefs.current[callA._id];
        const toEl = cardRefs.current[callB._id];

        if (fromEl && toEl) {
          const fromRect = fromEl.getBoundingClientRect();
          const toRect = toEl.getBoundingClientRect();
          const containerRect = fromEl.offsetParent?.getBoundingClientRect();

          if (!containerRect) return;

          newLines.push({
            top: fromRect.bottom - containerRect.top,
            left: fromRect.left + fromRect.width / 2 - containerRect.left,
            height: toRect.top - fromRect.bottom,
          });
        }
      });
    }
  }, [clientsFiltered]);

  const handleSearch = async () => {
    setSearching(() => true);
    dispatch(updateClientsFiltered([]));
    await dispatch(findConversationsByGoalID(goalID));
    setSearching(() => false);
  };

  const handleFetchMoreConversations = async () => {
    await dispatch(fetchMoreConversations());
  };

  return (
    <div className="flex">
      <div
        className={cn(
          "flex flex-col gap-4 w-[calc(100%+1.5rem)] sm:w-full duration-300 transition-all ease-in-out -mx-0",
          (NotesOpen || callTranscriptOpen) && "sm:-mx-6 md:-mx-8 lg:-mx-10"
        )}
      >
        <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
          {t("pageTitle")}
        </h1>
        <div
          className={cn(
            "grid gap-2 transition-all ease-in-out grid-cols-1",
            NotesOpen || callTranscriptOpen || open
              ? "lg:grid-cols-2 xl:grid-cols-6"
              : "md:grid-cols-2 lg:grid-cols-6 2xl:grid-cols-5"
          )}
        >
          {translatedStats.map((stat: any, i: number) => (
            <CallLogsStatsCard
              key={i}
              title={stat.title}
              value={stat.value}
              icon={callTypeConfig[i].icon}
              color={stat.color}
              subStats={stat.subStats}
              className={cn(
                callTypeConfig[i].color,
                "transition-all ease-in-out col-span-1",
                !NotesOpen && !callTranscriptOpen && !open && "2xl:col-span-1",
                NotesOpen || callTranscriptOpen || open
                  ? {
                      "xl:col-span-2": [0, 1, 2].includes(i),
                      "xl:col-span-3": i === 3,
                      "lg:col-span-2 xl:col-span-3": i === 4,
                    }
                  : {
                      "lg:col-span-2": [0, 1, 2].includes(i),
                      "lg:col-span-3": i === 3,
                      "md:col-span-2 lg:col-span-3": i === 4,
                    }
              )}
            />
          ))}
        </div>
        <h3 className=" mt-2 ml-2 font-semibold">{t("search.byGoalId")}</h3>
        <div
          className={cn(
            "w-full flex max-lg:flex-col gap-2 justify-between -mt-2",
            (NotesOpen || callTranscriptOpen) && "max-xl:flex-col"
          )}
        >
          <div className="flex">
            <Input
              name="GoalID"
              placeholder={t("search.goalId")}
              value={goalID}
              className="w-full lg:w-[300px] rounded-r-none rtl:rounded-l-none rtl:rounded-r-md max-md:w-full"
              onChange={(e: any) => dispatch(setGoalID(e.target.value))}
            />
            <CustomButton
              props={{
                value: searching ? t("buttons.searching") : t("buttons.search"),
                className:
                  "dark:bg-voxa-neutral-800 rounded-l-none rtl:rounded-r-none rtl:rounded-l-md w-fit max-sm:px-2 py-5",
                loading: searching,
                onClick: handleSearch,
              }}
            />
          </div>
          <div
            className={cn(
              "w-full flex max-sm:flex-wrap justify-between xl:justify-end gap-2",
              !NotesOpen && !callTranscriptOpen && "lg:justify-end"
            )}
          >
            <CustomButton
              props={{
                value: t("buttons.exportMissedCalls"),
                className: "dark:bg-voxa-neutral-800 w-full sm:w-fit py-5",
                onClick: () => dispatch(setExportOpen(true)),
              }}
            />
            <CustomButton
              props={{
                value: t("buttons.filter"),
                icon: <FilterAltRoundedIcon />,
                className: "dark:bg-voxa-neutral-800 w-full sm:w-fit py-5",
                onClick: () => dispatch(setFiltersOpen(true)),
              }}
            />
          </div>
        </div>
        {filtered ? (
          <div>
            <FilterBadges />
            {clientsFiltered?.length > 0 && (
              <p className="mb-2 w-full text-right rtl:text-left text-voxa-neutral-500">
                {loadMore.items} / {totalLogs} {t("buttons.filteredCalls")}
              </p>
            )}
            {loading ? (
              <div className="h-10 flex justify-center items-center w-full">
                <CircularLoaderSmall />
              </div>
            ) : clientsFiltered?.length > 0 ? (
              <>
                <CallLogGroups
                  clientsFiltered={clientsFiltered}
                  cardRefs={cardRefs}
                />
                {totalLogs && totalLogs > clientsFiltered.length && (
                  <CustomButton
                    props={{
                      value: t("buttons.loadMore"),
                      onClick: handleFetchMoreConversations,
                      className:
                        "mx-auto hover:bg-voxa-teal-500 bg-voxa-teal-600 dark:bg-voxa-teal-600 dark:hover:bg-voxa-teal-500 w-fit self-center mt-3 py-2 px-4 rounded-md font-medium flex justify-center items-center gap-1",
                      icon: loadMore.loading ? (
                        <Loader2 className="animate-spin" />
                      ) : (
                        <ChevronsDown />
                      ),
                      loading: loadMore.loading,
                    }}
                  />
                )}
              </>
            ) : (
              <div className="mt-12 text-center text-voxa-neutral-500 dark:text-voxa-neutral-400">
                {t("filters.noResults")}
              </div>
            )}
          </div>
        ) : (
          <AccordionLists />
        )}
      </div>
      {exportOpen && <ExportPopup />}
      {filtersOpen && <FiltersPopup />}
      {callTranscriptOpen && <Transcript />}
      {NotesOpen && <History />}
    </div>
  );
}
