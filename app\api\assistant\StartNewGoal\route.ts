import Goal from "@/models/Goal";
import axios from "axios";
import { NextResponse } from "next/server";

const TELECOM_ENDPOINT = process.env.TELECOM_ENDPOINT;

const PLATFORM_ORIGIN = {
  ECHOPARROT: "echoparrot",
  PLUGIN: "plugin",
  SALESFORCE: "salesforce"
}

export async function POST(req: Request) {
  try {
    const { goalID } = await req.json();

    const goal = await Goal.findOne({ _id: goalID });
    if (!goal) {
      return NextResponse.json({ error: "Goal not found" }, { status: 404 });
    }

    if (!goal.clients || goal.clients.length === 0) {
      return NextResponse.json(
        { error: "Goal has no clients yet!" },
        { status: 400 }
      );
    }

    /*
        // Get current time in France timezone
        const franceTime = DateTime.now().setZone("Europe/Paris");
        const currentMinutes = franceTime.hour * 60 + franceTime.minute; // Convert to minutes
         */
    /*
        const gmtPlus2Time = DateTime.now().setZone("UTC+2");
        const currentMinutes = gmtPlus2Time.hour * 60 + gmtPlus2Time.minute; // Convert to minutes
        
        // Check if the current time is within any of the availability ranges
        const isAvailable = goal.availability.some(({ start_time, end_time }: { start_time: number; end_time: number }) => 
            currentMinutes >= start_time && currentMinutes <= end_time
        );

        if (!isAvailable) {
            return NextResponse.json({
                error: "Adjust your planning first! You can't call when the goal is out of working hours."
            }, { status: 403 });
        }

*/
    const body = {
      sid: "**********************************",
      auth_token: "f3e709bbb7084af0451c9591da81f27b",
      goal_id: goalID,
      platform_origin: PLATFORM_ORIGIN.ECHOPARROT
    };

    const res = await axios.post(
      `${TELECOM_ENDPOINT}/make-transcript-call`,
      body
    );
    const data = await res.data;

    if (res.status !== 200) {
      return NextResponse.json(
        { error: "Failed to initiate Goal Calls" },
        { status: 500 }
      );
    }

    return NextResponse.json({ status: 200, response: data });
  } catch (err: any) {
    console.log("Error calling transcript service:", err);

    if (axios.isAxiosError(err) && err?.response?.data?.detail) {
      console.log("Error details:", err?.response);
      return NextResponse.json(
        {
          error: err?.response?.data?.detail,
        },
        { status: err?.response?.status || 500 }
      );
    }

    // Generic fallback
    return NextResponse.json(
      { error: err.message || "Failed to initiate Goal Calls" },
      { status: 500 }
    );
  }
}
