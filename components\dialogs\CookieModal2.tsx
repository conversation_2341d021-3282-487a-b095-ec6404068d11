"use client";
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ModalContent } from "../ui/animated-modal";
import Link from "next/link";
import clsx from "clsx";
import Image from "next/image";
import cookie from "@/public/images/cookie.svg";
import { cn } from "@/lib/utils";

const halfStyle =
  "h[100%+5rem] flex flex-col justify-between items-center p-8 -m-10";

const linkStyle =
  "w-full text-center font-bold rounded-lg text-voxa-neutral-50 text-sm px-6 py-2 hover:bg-voxa-neutral-600";

export function CookieModal() {
  return (
    <Modal>
      <ModalBody>
        <ModalContent className="grid grid-cols-1 sm:grid-cols-2">
          <div
            className={clsx(halfStyle, "max-sm:mb-0 sm:mr-0 bg-voxa-teal-600")}
          >
            <Image
              src={cookie}
              alt=""
              width={150}
              className="z-0 mt-10 rotate-30"
            />
            <h4 className="z-10 px-6 w-full text-voxa-neutral-50 dark:text-voxa-neutral-950 -ml-10 top-4  text-xl md:text-2xl font-bold text-center text-">
              General Terms and Polices
            </h4>
            <Link
              className={cn(
                linkStyle,
                "text-voxa-neutral-600 dark:text-voxa-neutral-50 hover:dark:bg-voxa-neutral-600 hover:text-voxa-neutral-50 bg-voxa-neutral-50 dark:bg-voxa-neutral-950"
              )}
              href="https://echoparrot.com/GeneralTerms"
              target="_blank"
            >
              Visit General Terms
            </Link>
          </div>
          <div
            className={cn(
              halfStyle,
              "max-sm:mt-0 sm:ml-0 bg-voxa-neutral-50 dark:bg-voxa-neutral-950"
            )}
          >
            <Image
              src={cookie}
              alt=""
              width={150}
              className="z-0 mt-10 -rotate-90"
            />
            <h4 className="z-10 px-6 w-full dark:text-voxa-neutral-50 text-voxa-neutral-600 -ml-10 top-4 text-xl md:text-2xl font-bold text-center text-">
              Take a look at our features
            </h4>
            <Link
              className={clsx(linkStyle, "bg-voxa-teal-600")}
              href="https://echoparrot.com/Tooltip"
              target="_blank"
            >
              Visit Features
            </Link>
          </div>
        </ModalContent>
      </ModalBody>
    </Modal>
  );
}
