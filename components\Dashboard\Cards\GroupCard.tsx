import React from "react";
import { DeleteGroupDialog } from "@/components/dialogs/DeleteGroup";
import { ShowGroupClients } from "@/components/Sidebars/GroupClientsSheet";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

interface GroupCardProps {
  group: {
    _id: string;
    name: string;
    membersLen: number;
  };
  index: number;
}

// Simple function to highlight matching text
function highlightMatch(text: string, pattern: string) {
  if (!pattern) return text;

  try {
    const regex = new RegExp(
      `(${pattern.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
      "gi"
    );
    const parts = text.split(regex);

    return parts.map((part, i) =>
      regex.test(part) ? (
        <b key={i} className="text-voxa-teal-700 dark:text-voxa-teal-400">
          {part}
        </b>
      ) : (
        <React.Fragment key={i}>{part}</React.Fragment>
      )
    );
  } catch {
    // If regex fails, return original text
    return text;
  }
}

export default function GroupCard({ group, index }: GroupCardProps) {
  // Get the search pattern from Redux store
  const { groupPattern } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardClients
  );

  return (
    <div
      key={index}
      className="h-full flex flex-col p-4 bg-sidebar border-sidebar-border border rounded-2xl transition-all duration-300 hover:scale-103 hover:shadow-lg"
    >
      {/* Header: Group Name & Delete Button */}
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-black/60 dark:text-voxa-neutral-50 text-xs font-semibold break-words max-w-[80%]">
          {highlightMatch(group.name, groupPattern)}
        </h3>
        <DeleteGroupDialog groupID={group._id} groupName={group.name} />
      </div>

      {/* Group Info */}
      <div className="flex items-center gap-2 text-gray-400 text-sm mb-3">
        <span>{group.membersLen || 0} Members</span>
      </div>

      {/* View Group Button */}
      <div className="mt-auto">
        <ShowGroupClients name={group.name} groupID={group._id} />
      </div>
    </div>
  );
}
