import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  GetWebhooks,
  GetWebhookById,
  UpdateWebhook,
  UpdateWebhookEvent,
  DeleteWebhookEvent,
  ResetWebhookSecretKey,
  SetWebhookEnabled,
  AddWebhookLogEvent,
  ClearWebhookLogs,
  UpdateWebhookEvents,
} from "@/actions/WebhookActions";
import { toast } from "sonner";

interface WebhookLogEventType {
  url: string;
  method: string;
  response_code: string;
  timestamp: string;
  header: string;
  body: string;
}

type WebhookEventNames = "callback-status" | "recording-status" | "make-call";

type WebhookEventType = Record<
  WebhookEventNames,
  { method: string; path: string }
>;

// WebhookType interface matching the model
export interface WebhookType {
  _id: string;
  entreprise_id: string;
  events?: WebhookEventType;
  logs_events?: WebhookLogEventType[];
  webhook_secret_key?: string;
  enabled?: boolean;
}

interface WebhookState {
  webhook: WebhookType | null | undefined;
  loading: boolean;
  error: string | null;
}

const initialState: WebhookState = {
  webhook: null,
  loading: false,
  error: null,
};

export const fetchWebhook = createAsyncThunk(
  "webhook/fetchWebhook",
  async (_, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));
      const response = await GetWebhooks();
      if (!response.success) throw new Error(response.error);
      dispatch(setWebhook(response.webhook || null));
      dispatch(setLoading(false));
      return response.webhook;
    } catch (err: any) {
      dispatch(setLoading(false));
      dispatch(setError(err.message));
      return rejectWithValue(err.message);
    }
  }
);

export const fetchWebhookById = createAsyncThunk(
  "webhook/fetchWebhookById",
  async (id: string, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));
      const response = await GetWebhookById(id);
      if (!response.success) throw new Error(response.error);
      dispatch(setWebhook(response.webhook || null));
      dispatch(setLoading(false));
      return response.webhook;
    } catch (err: any) {
      dispatch(setLoading(false));
      dispatch(setError(err.message));
      return rejectWithValue(err.message);
    }
  }
);

export const updateWebhook = createAsyncThunk(
  "webhook/updateWebhook",
  async (
    { id, update }: { id: string; update: Partial<WebhookType> },
    { dispatch, rejectWithValue }
  ) => {
    try {
      const response = await UpdateWebhook(id, update);
      if (!response.success) throw new Error(response.error);
      toast.success("Webhook updated successfully");
      dispatch(setWebhook(response.webhook || null));
      return response.webhook;
    } catch (err: any) {
      toast.error(err.message);
      return rejectWithValue(err.message);
    }
  }
);

export const updateWebhookEvent = createAsyncThunk(
  "webhook/updateWebhookEvent",
  async (
    data: { eventName: string; method: string; path: string },
    { dispatch, rejectWithValue }
  ) => {
    try {
      const response = await UpdateWebhookEvent(data);
      if (!response.success) throw new Error(response.error);
      toast.success("Webhook event updated successfully");
      dispatch(setWebhook(response.webhook || null));
      return response.webhook;
    } catch (err: any) {
      toast.error(err.message);
      return rejectWithValue(err.message);
    }
  }
);

export const deleteWebhookEvent = createAsyncThunk(
  "webhook/deleteWebhookEvent",
  async (eventName: string, { dispatch, rejectWithValue }) => {
    try {
      const response = await DeleteWebhookEvent(eventName);
      if (!response.success) throw new Error(response.error);
      toast.success("Webhook event deleted successfully");
      dispatch(setWebhook(response.webhook || null));
      return response.webhook;
    } catch (err: any) {
      toast.error(err.message);
      return rejectWithValue(err.message);
    }
  }
);

export const resetWebhookSecretKey = createAsyncThunk(
  "webhook/resetWebhookSecretKey",
  async (_, { dispatch, rejectWithValue }) => {
    try {
      const response = await ResetWebhookSecretKey();
      if (!response.success) throw new Error(response.error);
      toast.success("Webhook secret key reset successfully");
      dispatch(setWebhook(response.webhook || null));
      return response.webhook;
    } catch (err: any) {
      toast.error(err.message);
      return rejectWithValue(err.message);
    }
  }
);

export const setWebhookEnabled = createAsyncThunk(
  "webhook/setWebhookEnabled",
  async (enabled: boolean, { dispatch, rejectWithValue }) => {
    try {
      const response = await SetWebhookEnabled(enabled);
      if (!response.success) throw new Error(response.error);
      toast.success(`Webhook ${enabled ? "enabled" : "disabled"} successfully`);
      dispatch(setWebhook(response.webhook || null));
      return response.webhook;
    } catch (err: any) {
      toast.error(err.message);
      return rejectWithValue(err.message);
    }
  }
);

export const addWebhookLogEvent = createAsyncThunk(
  "webhook/addWebhookLogEvent",
  async (
    logEvent: {
      url: string;
      method: string;
      response_code: string;
      timestamp: string;
      header: string;
      body: string;
    },
    { dispatch, rejectWithValue }
  ) => {
    try {
      const response = await AddWebhookLogEvent(logEvent);
      if (!response.success) throw new Error(response.error);
      dispatch(setWebhook(response.webhook || null));
      return response.webhook;
    } catch (err: any) {
      return rejectWithValue(err.message);
    }
  }
);

export const clearWebhookLogs = createAsyncThunk(
  "webhook/clearWebhookLogs",
  async (_, { dispatch, rejectWithValue }) => {
    try {
      const response = await ClearWebhookLogs();
      if (!response.success) throw new Error(response.error);
      toast.success("Webhook logs cleared successfully");
      dispatch(setWebhook(response.webhook || null));
      return response.webhook;
    } catch (err: any) {
      toast.error(err.message);
      return rejectWithValue(err.message);
    }
  }
);

export const updateWebhookEvents = createAsyncThunk(
  "webhook/updateWebhookEvents",
  async (events: WebhookEventType, { dispatch, rejectWithValue }) => {
    try {
      const response = await UpdateWebhookEvents(events);
      if (!response.success) throw new Error(response.error);
      toast.success("Webhook events updated successfully");
      dispatch(setWebhook(response.webhook || null));
      return response.webhook;
    } catch (err: any) {
      toast.error(err.message);
      return rejectWithValue(err.message);
    }
  }
);

const webhookSlice = createSlice({
  name: "webhook",
  initialState,
  reducers: {
    setWebhook(state, action: PayloadAction<WebhookType | null>) {
      state.webhook = action.payload;
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
    clearError(state) {
      state.error = null;
    },
  },
});

export const { setWebhook, setLoading, setError, clearError } =
  webhookSlice.actions;
export default webhookSlice.reducer;
