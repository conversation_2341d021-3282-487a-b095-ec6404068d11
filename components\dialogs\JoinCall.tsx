"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import CustomInput from "../CustomFormItems/Input";
import { toast } from "sonner";

export function JoinCallDialog({
  callsid,
  goalid,
}: {
  callsid: string;
  goalid: string;
}) {
  const [phoneNumber, setPhoneNumber] = useState("");

  const handleTakeOver = async () => {
    if (!phoneNumber) {
      toast.error("Please enter your phone number.");
      return;
    }

    try {
      const res = await fetch("/api/calls/take-over", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          call_sid: callsid,
          goal_id: goalid,
          phone_number: phoneNumber,
        }),
      });

      if (!res.ok) {
        const errText = await res.text();
        throw new Error(errText || "Failed to take over the call.");
      }

      const data = await res.json();
      toast.success("Successfully took over the call.");
      console.log("API success:", data);
    } catch (error: any) {
      toast.error(`Error: ${error.message}`);
      console.error("Client Error:", error);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="h-6 w-full bg-voxa-teal-600 hover:bg-voxa-teal-500 dark:bg-voxa-teal-800 dark:hover:bg-voxa-teal-700 text-white text-[11px] rounded-md">
          Join Call
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Take Over</DialogTitle>
          <DialogDescription>
            Enter your phone number to take over the call.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 flex flex-col">
          <CustomInput
            props={{
              type: "text",
              placeholder: "Phone Number",
              className: "w-full",
              name: "phoneNumber",
              label: "Phone Number",
              value: phoneNumber,
              onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
                setPhoneNumber(e.target.value),
            }}
          />
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="submit"
              className="hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950"
            >
              Cancel
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              onClick={handleTakeOver}
              className="bg-voxa-teal-600 hover:bg-voxa-teal-500  text-white"
            >
              Take Over
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
