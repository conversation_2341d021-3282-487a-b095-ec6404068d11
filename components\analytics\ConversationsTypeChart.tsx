"use client";

import { useTheme } from "next-themes";
import { useEffect, useMemo, useRef } from "react";
import { useSelector } from "react-redux";
import { Label, Pie, PieChart } from "recharts";
import useSWR from "swr";

import ExportDropdown, {
  ExportDropdownItem,
} from "@/components/analytics/ExportDropdown";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Skeleton } from "@/components/ui/skeleton";
import {
  exportChartAsImage,
  exportChartAsPDF,
} from "@/lib/analytics/chartExport";
import {
  ConversationTypesStats,
  convertCountriesToUppercase,
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { CountryCode } from "@/lib/countries";
import { RootState, store } from "@/redux/store";

const chartConfig = {
  CALL: {
    label: "Calls",
    color: "#2a9d90",
  },
  SMS: {
    label: "SMS",
    color: "#eab308",
  },
  WHATSAPP: {
    label: "WhatsApp",
    color: "#e76e50",
  },
  MEET: {
    label: "Meetings",
    color: "#8b5cf6", // Purple color for meetings
  },
} satisfies ChartConfig;

// Direction colors for inbound/outbound display
const directionColors = {
  inbound: "#3b82f6", // blue
  outbound: "#f97316", // orange
};

// SWR fetcher for conversation types stats (local logic, not via redux thunk)
const fetcher = async (): Promise<ConversationTypesStats> => {
  // Get filters from store
  const filters = store.getState().analytics.filters;
  const {
    startDate,
    endDate,
    conversationTypes,
    countries,
    callDuration,
    ringingTime,
  } = filters;

  // Dynamically import getConversationsNumberByType
  const { getConversationsNumberByType } = await import(
    "@/actions/AnalyticsActions"
  );
  const res = await getConversationsNumberByType(
    startDate ? new Date(startDate) : undefined,
    endDate ? new Date(endDate) : undefined,
    conversationTypes,
    countries.length
      ? convertCountriesToUppercase(countries as CountryCode[])
      : countries,
    callDuration,
    ringingTime
  );

  if (!res.success) {
    throw new Error(res.error || "Failed to fetch conversation types");
  }
  if (!("data" in res)) {
    throw new Error("No data returned from getConversationsNumberByType");
  }
  return res.data as ConversationTypesStats;
};

export default function ConversationsTypesChart({
  isAnimationActive = true,
  setLoading,
}: {
  isAnimationActive?: boolean;
  setLoading?: (loading: boolean) => void;
}) {
  const { theme, systemTheme } = useTheme();
  const isDarkMode =
    theme === "dark" || (theme === "system" && systemTheme === "dark");

  // Get filters from Redux (for dependency tracking)
  const filters = useSelector((state: RootState) => state.analytics.filters);

  // Use SWR to fetch conversation types
  const {
    data: conversationTypes,
    isLoading: conversationTypesLoading,
    error,
  } = useSWR(["conversationTypes", filters], fetcher, {
    revalidateOnFocus: false,
    dedupingInterval: 10000, // 10 seconds
  });

  // Call setLoading when loading state changes
  useEffect(() => {
    if (setLoading) setLoading(conversationTypesLoading);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conversationTypesLoading]);

  // Add ref for export
  const chartRef = useRef<HTMLDivElement>(null);

  // Convert SWR data to chart format
  const chartData = useMemo(() => {
    if (!conversationTypes) return [];

    // Create data for the pie chart
    return Object.entries(conversationTypes).map(([type, data]) => {
      const typeData = data as {
        inbound: number;
        outbound: number;
        total: number;
      };
      return {
        conversationType: type,
        count: typeData?.total || 0,
        fill: chartConfig[type as keyof typeof chartConfig]?.color,
      };
    });
  }, [conversationTypes]);

  const totalConversations = useMemo(
    () => chartData.reduce((acc, curr) => acc + curr.count, 0),
    [chartData]
  );

  // Create expanded data for the legend with inbound/outbound breakdown
  const expandedData = useMemo(() => {
    if (!conversationTypes) return [];

    // Ensure all types are represented even if not in the data
    return Object.keys(chartConfig).map((type) => {
      const data = conversationTypes[type as keyof ConversationTypesStats] || {
        inbound: 0,
        outbound: 0,
        total: 0,
      };
      const config = chartConfig[type as keyof typeof chartConfig];

      // For meetings, we only have a single value (stored in outbound)
      const isMeeting = type === "MEET";

      return {
        type,
        label: config.label,
        color: config.color,
        inbound: data.inbound || 0,
        outbound: data.outbound || 0,
        total: data.total || 0,
        isMeeting, // Flag to identify meetings
      };
    });
  }, [conversationTypes]);

  // Define export items for the dropdown
  const exportItems: ExportDropdownItem[] = [
    {
      title: "Export as PNG",
      action: async () => {
        try {
          await exportChartAsImage(
            chartRef.current,
            "png",
            "conversation-types-chart"
          );
        } finally {
        }
      },
    },
    {
      title: "Export as SVG",
      action: async () => {
        try {
          await exportChartAsImage(
            chartRef.current,
            "svg",
            "conversation-types-chart"
          );
        } finally {
        }
      },
    },
    {
      title: "Export as PDF",
      action: async () => {
        try {
          await exportChartAsPDF(chartRef.current, "conversation-types-chart");
        } finally {
        }
      },
    },
  ];

  return (
    <div ref={chartRef} className="flex w-full">
      <Card className="bg-voxa-neutral-50 dark:bg-voxa-neutral-900 dark:border-voxa-neutral-700 transition-all duration-150 w-full">
        <CardHeader className="pb-0 flex flex-row items-start justify-between">
          <div>
            <CardTitle className="dark:text-voxa-neutral-50">
              Conversation Types
            </CardTitle>
          </div>

          <ExportDropdown
            items={exportItems}
            buttonText="Export"
            loadingText="Exporting..."
            className=""
          />
        </CardHeader>

        <CardContent className="flex flex-wrap justify-center items-center gap-6 mt-2">
          {conversationTypesLoading ? (
            <>
              <Skeleton className="aspect-square w-[200px] h-[200px] mx-auto" />
              <div className="flex flex-col justify-center gap-3 text-sm min-w-[180px]">
                <Skeleton className="h-6 w-32 mb-2" />
                <Skeleton className="h-6 w-32 mb-2" />
                <Skeleton className="h-6 w-32" />
              </div>
            </>
          ) : error ? (
            <div className="text-center text-red-500 p-4">
              Error loading conversation types data
            </div>
          ) : (
            <>
              {/* Chart */}
              {totalConversations === 0 ? (
                <div className="aspect-square w-[200px] mx-auto flex items-center justify-center">
                  <div className="relative w-[160px] h-[160px] flex items-center justify-center">
                    {/* Outer circle with shadow */}
                    <div className="absolute inset-0 rounded-full bg-gray-300 dark:bg-gray-700 shadow-[0_2px_8px_rgba(0,0,0,0.15)] dark:shadow-[0_2px_8px_rgba(0,0,0,0.35)]"></div>
                    {/* Inner circle (creates hollow effect) */}
                    <div className="absolute w-[120px] h-[120px] rounded-full bg-voxa-neutral-50 dark:bg-voxa-neutral-900 z-10"></div>
                    {/* Text in the middle */}
                    <div className="z-20 flex flex-col items-center justify-center">
                      <span className="text-2xl font-bold text-foreground">
                        0
                      </span>
                      <span className="text-sm text-muted-foreground">
                        Total
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <ChartContainer
                  config={chartConfig}
                  className="aspect-square w-[200px] mx-auto"
                >
                  <PieChart>
                    <ChartTooltip
                      cursor={false}
                      content={<ChartTooltipContent hideLabel />}
                    />
                    <Pie
                      data={chartData}
                      dataKey="count"
                      nameKey="conversationType"
                      innerRadius={60}
                      strokeWidth={0} // Remove stroke
                      isAnimationActive={isAnimationActive}
                    >
                      <Label
                        content={({ viewBox }) => {
                          if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                            return (
                              <text
                                x={viewBox.cx}
                                y={viewBox.cy}
                                textAnchor="middle"
                                dominantBaseline="middle"
                              >
                                <tspan
                                  x={viewBox.cx}
                                  y={viewBox.cy}
                                  style={{
                                    fill: isDarkMode ? "#ffffff" : "#000000",
                                    fontSize: "24px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  {totalConversations.toLocaleString()}
                                </tspan>
                                <tspan
                                  x={viewBox.cx}
                                  y={(viewBox.cy || 0) + 24}
                                  style={{
                                    fill: isDarkMode ? "#aaaaaa" : "#888888",
                                    fontSize: "14px",
                                  }}
                                >
                                  Total
                                </tspan>
                              </text>
                            );
                          }
                        }}
                      />
                    </Pie>
                  </PieChart>
                </ChartContainer>
              )}

              {/* Enhanced Legend with Inbound/Outbound breakdown */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-14 gap-y-6 text-sm min-w-[220px]">
                {expandedData.map((entry) => {
                  const percent =
                    totalConversations > 0
                      ? ((entry.total / totalConversations) * 100).toFixed(1)
                      : "0.0";

                  return (
                    <div key={entry.type} className="flex flex-col gap-1">
                      {/* Type heading with total */}
                      <div className="flex items-center justify-between gap-2">
                        <div className="flex items-center gap-2">
                          <span
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: entry.color }}
                          />
                          <span className="text-foreground font-medium">
                            {entry.label}
                          </span>
                        </div>

                        {/* Right: count + percentage in visible pills */}
                        <div className="flex gap-1">
                          <span className="rounded-md px-2 py-0.5 w-12 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-foreground">
                            {entry.total.toLocaleString()}
                          </span>
                          <span className="rounded-md px-2 py-0.5 w-12 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-muted-foreground">
                            {percent}%
                          </span>
                        </div>
                      </div>

                      {/* Inbound/Outbound details - Only for non-meeting types */}
                      {!entry.isMeeting && (
                        <div className="ml-5 flex flex-col gap-1 text-xs mt-1">
                          <div className="flex items-center justify-between gap-2">
                            <div className="flex items-center gap-1.5">
                              <span
                                className="w-2 h-2 rounded-full"
                                style={{
                                  backgroundColor: directionColors.inbound,
                                }}
                              />
                              <span className="text-muted-foreground">
                                Inbound
                              </span>
                            </div>
                            <span className="rounded-md px-2 py-0.5 w-12 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-foreground">
                              {entry.inbound.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex items-center justify-between gap-2">
                            <div className="flex items-center gap-1.5">
                              <span
                                className="w-2 h-2 rounded-full"
                                style={{
                                  backgroundColor: directionColors.outbound,
                                }}
                              />
                              <span className="text-muted-foreground">
                                Outbound
                              </span>
                            </div>
                            <span className="rounded-md px-2 py-0.5 w-12 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-foreground">
                              {entry.outbound.toLocaleString()}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
