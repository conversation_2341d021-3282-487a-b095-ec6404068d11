import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@radix-ui/react-label";
import { useState } from "react";
import { toast } from "sonner";
import ButtonLoader from "../Loaders/ButtonLoader";
import { CountriesSelect } from "../dropdowns/CountriesSelect";
import {
  setCountry,
  set_google_meet_dial_in_number,
  set_google_meet_pin,
  SaveClientToGoogleMeetGoal,
  addClientToGoogleMeetOpen,
} from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { CountryCode } from "libphonenumber-js";
import GoogleMeetIcon from "@/public/images/Icons/google_meet.svg";
import Image from "next/image";
import { useTranslation } from "react-i18next";
export default function AddClientToGoogleMeetGoal() {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const { google_meet_dial_in_number, country, GoalID, google_meet_pin } =
    useSelector(
      (state: RootState) => state.businessDashboard.businessDashboardAssistants
    );
  const [Loading, setLoading] = useState(false);

  const HandleCreateNewClient = async () => {
    if (
      google_meet_dial_in_number === "" ||
      GoalID === "" ||
      country === "" ||
      google_meet_pin === ""
    ) {
      toast.error(t("meet.allFieldsRequired"));
      return;
    }
    setLoading(true);
    await dispatch(SaveClientToGoogleMeetGoal());
    setLoading(false);
  };

  return (
    <Dialog
      open
      onOpenChange={() => dispatch(addClientToGoogleMeetOpen(false))}
    >
      <DialogContent className="p-5 rounded-md max-w-md w-full border-voxa-neutral-700">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-center gap-2">
            {t("meet.addClientToMeet")}
            <Image
              src={GoogleMeetIcon}
              alt="Google Meet Icon"
              className="w-5 h-5"
              width={20}
              height={20}
            />
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-3 mt-4">
          {/*
              <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="Name" className="text-sm">
              CRM Client ID
            </Label>
            <Input
              value={crm_client_id}
              onChange={(e) => dispatch(set_crm_client_id(e.target.value))}
              id="crm_client_id"
              placeholder="CRM Client ID"
            />
          </div>
    */}
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="Name" className="text-sm">
              {t("meet.country")} <span className="text-red-500">*</span>
            </Label>
            <CountriesSelect
              country={country}
              selectCountry={(newCountry: "" | CountryCode) =>
                dispatch(setCountry(newCountry))
              }
              classnames="translate-y-px py-5"
            />
          </div>
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="number" className="text-sm flex items-center">
              {t("meet.phoneNumber")} <span className="text-red-500">*</span>
            </Label>
            <Input
              value={google_meet_dial_in_number}
              onChange={(e) =>
                dispatch(set_google_meet_dial_in_number(e.target.value))
              }
              id="number"
              placeholder={t("meet.phoneNumber")}
            />
          </div>
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="number" className="text-sm flex items-center">
              {t("meet.googleMeetPin")} <span className="text-red-500">*</span>
            </Label>
            <Input
              value={google_meet_pin}
              onChange={(e) => dispatch(set_google_meet_pin(e.target.value))}
              id="number"
              placeholder={t("meet.googleMeetPin")}
            />
          </div>
          <button
            disabled={Loading}
            onClick={HandleCreateNewClient}
            className={`${
              Loading
                ? "cursor-not-allowed bg-voxa-teal-400"
                : "bg-voxa-teal-600 hover:bg-voxa-teal-500 "
            }
              transition-all duration-150 text-white px-4 py-2 rounded-md font-medium flex
              justify-center items-center gap-2 mt-8
            `}
          >
            {Loading ? (
              <>
                {t("meet.addingClient")}
                <ButtonLoader />
              </>
            ) : (
              t("meet.addClient")
            )}
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
