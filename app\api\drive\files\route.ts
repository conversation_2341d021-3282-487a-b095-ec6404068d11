import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export const runtime = "nodejs";

export async function GET() {
  const tokenCookie = (await cookies()).get("google_tokens_drive")?.value;
  if (!tokenCookie) {
    return NextResponse.json({ error: "Not authorized" }, { status: 401 });
  }

  const { access_token } = JSON.parse(tokenCookie);

  const res = await fetch("https://www.googleapis.com/drive/v3/files?pageSize=20&fields=files(id,name,mimeType,iconLink)", {
    headers: {
      Authorization: `Bearer ${access_token}`,
    },
  });

  if (!res.ok) {
    return NextResponse.json({ error: "Failed to fetch files" }, { status: res.status });
  }

  const data = await res.json();
  return NextResponse.json(data.files ?? []);
}
