import { Dispatch, SetStateAction } from "react";
import { useTranslation } from "react-i18next";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import AudioRecorder from "@/components/Player/AudioRecorder";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  setVoiceMailDetection,
  setVoiceMailDetectionContent,
  setAudioUrl,
  setAudioDuration,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";

export const VOICE_MAIL_DETECTION_INITIAL_VALUE =
  "En France, de nombreuses aides financières sont disponibles pour encourager les propriétaires à réaliser des travaux d'isolation, de chauffage plus performant, ou encore l'installation de systèmes d'énergie renouvelable comme des pompes à chaleur. Ces initiatives permettent de réduire significativement vos factures énergétiques. ONRBAT est une entreprise spécialisée, accompagne les particuliers dans ces démarches  à la réalisation des travaux, en passant par l'obtention des aides de l'État. N'hésitez pas à vous renseigner pour un avenir plus vert et plus économique ! Contactez-nous pour une étude gratuite.";

type VoicemailDropProps = {
  setAudioBlob: Dispatch<SetStateAction<Blob | null>>;
};

export const VoicemailDrop: React.FC<VoicemailDropProps> = ({
  setAudioBlob,
}) => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();

  const { voicemailDropType, voicemailDropContent, audioUrl } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );

  return (
    <div className="space-y-2">
      <div className="flex max-sm:flex-col sm:items-center gap-2">
        <h1 className="font-semibold text-lg">
          {t("createEditGoal.advanced.voicemailDrop")}
        </h1>
        <div className="flex items-center gap-2">
          <RadioGroup
            value={voicemailDropType}
            className="flex flex-wrap max-sm:text-xs"
            onValueChange={(value) => dispatch(setVoiceMailDetection(value))}
          >
            <div className="flex text-nowrap items-center space-x-1">
              <RadioGroupItem value="NONE" id="r1" />
              <Label htmlFor="r1">
                {t("createEditGoal.advanced.noVoicemail")}
              </Label>
            </div>
            <div className="flex items-center space-x-1">
              <RadioGroupItem value="TEXT" id="r3" />
              <Label htmlFor="r3">{t("createEditGoal.advanced.text")}</Label>
            </div>
            <div className="flex items-center space-x-1">
              <RadioGroupItem value="AUDIO" id="r2" />
              <Label htmlFor="r2">{t("createEditGoal.advanced.audio")}</Label>
            </div>
          </RadioGroup>
        </div>
      </div>
      <p className="text-voxa-neutral-700 text-sm flex flex-col">
        {t("createEditGoal.advanced.voicemailDropTextInfo")}
        {voicemailDropContent.length > 700 && voicemailDropType === "TEXT" && (
          <span className="text-xs text-orange-500 max-w-[35rem]">
            {t("createEditGoal.advanced.voicemailCharLimitInfo")}
          </span>
        )}
      </p>
      {voicemailDropType === "TEXT" && (
        <>
          <Textarea
            placeholder={t("createEditGoal.advanced.messagePlaceholder")}
            rows={5}
            onChange={(e) =>
              dispatch(setVoiceMailDetectionContent(e.target.value))
            }
            value={voicemailDropContent}
          />
          <span className="pr-2 pt-1 w-full flex justify-end text-xs font-medium text-nowrap">
            {voicemailDropContent.length}{" "}
            {t("createEditGoal.advanced.charactersLabel")}
          </span>
        </>
      )}

      {voicemailDropType === "AUDIO" && (
        <AudioRecorder
          setAudioBlob={(blob: Blob | null) => setAudioBlob(blob)}
          audioUrl={audioUrl}
          setAudioUrl={(url: string) => dispatch(setAudioUrl(url))}
          setDuration={(duration: number) =>
            dispatch(setAudioDuration(duration))
          }
        />
      )}
    </div>
  );
};
