import mongoose from "mongoose";

const ClientSchema = new mongoose.Schema({
  name: {
    type: String,
  },
  phone: {
    type: String,
  },
  isFavorite: {
    type: Boolean,
    default: false,
  },
  isBlacklisted: {
    type: Boolean,
    default: false,
  },
  conversations: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  linkedGoals: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Goal",
    },
  ],
  country: {
    type: String,
  },
  groups: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Group",
    },
  ],
  gender: {
    type: String,
  },
  entreprise_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Entreprise",
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
});

const Client = mongoose.models.Client || mongoose.model("Client", ClientSchema);
export default Client;
