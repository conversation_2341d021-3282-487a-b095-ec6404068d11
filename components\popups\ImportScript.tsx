import { FileUpload } from "@/components/ui/file-upload";
import ButtonLoader from "../Loaders/ButtonLoader";
import CustomInput from "../CustomFormItems/Input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { CloudUploadIcon } from "lucide-react";
import { Button } from "../ui/button";
import { useTranslation } from "react-i18next";

export default function ImportScript({
  handleFileUpload,
  files,
  error,
  scriptName,
  setScriptName,
  Loading,
  open,
  setOpen,
  uploadScript,
}: {
  setImportFile: any;
  setError: any;
  setFiles: any;
  handleFileUpload: any;
  files: any;
  error: any;
  scriptName: string;
  setScriptName: any;
  Loading: boolean;
  setLoading: any;
  open: boolean;
  setOpen: any;
  uploadScript: () => Promise<void>;
}) {
  const { t } = useTranslation("scripts");

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="max-md:w-full bg-voxa-neutral-700 hover:bg-voxa-neutral-600 text-voxa-neutral-50 flex justify-center items-center gap-2">
          <CloudUploadIcon className="w-5 h-5" />
          {t("page.uploadScript")}
        </Button>
      </DialogTrigger>
      <DialogContent className="p-5 rounded-md w-full max-w-[500px] border-voxa-neutral-700">
        <DialogHeader>
          <DialogTitle>{t("importScript.title")}</DialogTitle>
        </DialogHeader>
        <CustomInput
          props={{
            label: t("importScript.scriptName"),
            placeholder: t("importScript.scriptNamePlaceholder"),
            type: "text",
            name: "scriptName",
            parentClassName: "mt-4",
            required: true,
            onChange(e) {
              setScriptName(e.target.value);
            },
            value: scriptName,
          }}
        />

        <div className="flex flex-col gap-3 mt-4">
          <FileUpload
            onChange={(e: any) => {
              handleFileUpload(e);
            }}
            type="text/plain"
          />
          {error && (
            <p className="text-red-500 text-sm">
              {t("importScript.fileError")}
            </p>
          )}
          <Button
            disabled={files.length === 0 || error || Loading}
            onClick={uploadScript}
            className={`${
              Loading
                ? "cursor-not-allowed"
                : "bg-voxa-teal-600 hover:bg-voxa-teal-500"
            } mt-12 text-voxa-neutral-50 flex justify-center items-center gap-2 ${
              files.length === 0 || error
                ? "cursor-not-allowed"
                : "cursor-pointer"
            }`}
          >
            {Loading ? (
              <>
                {t("importScript.uploading")}
                <ButtonLoader />
              </>
            ) : (
              t("importScript.upload")
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
