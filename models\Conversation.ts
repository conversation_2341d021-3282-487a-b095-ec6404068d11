import mongoose, { InferSchemaType } from "mongoose";

const ConversationSchema = new mongoose.Schema({
  sid: {
    type: String,
  },
  assistant_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Assistant",
  },
  assistant_name: {
    type: String,
  },
  direction: {
    type: String,
  },
  duration: {
    type: String,
  },
  entreprise_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Entreprise",
  },
  from_country: {
    type: String,
  },
  from_number: {
    type: String,
  },
  goal_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Goal",
  },
  recording_sid: {
    type: String,
  },
  status: {
    type: String,
  },
  to_city: {
    type: String,
  },
  to_client_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Client",
  },
  to_country: {
    type: String,
  },
  to_name: {
    type: String,
  },
  to_number: {
    type: String,
  },
  transcript: {
    type: String,
  },
  voicemail_detected: {
    type: <PERSON><PERSON><PERSON>,
  },
  who_hang_up: {
    type: String,
  },
  initiated_time: {
    type: String,
  },
  ringing_time: {
    type: String,
  },
  inprogress_time: {
    type: String,
  },
  completed_time: {
    type: String,
  },
  child_sid: {
    type: String,
  },
  child_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Conversation",
  },
  child_name: {
    type: String,
  },
  child_type: {
    type: String,
  },
  parent_sid: {
    type: String,
  },
  feelings: {
    type: String,
  },
  merged_transcript: {
    type: [mongoose.Schema.Types.Mixed],
    default: [],
  },
  recording_status: {
    type: String,
  },
  recording_url: {
    type: String,
  },
  recording_duration: {
    type: String,
  },
  summary: {
    type: String,
  },
  transcript_whisper: {
    type: String,
  },
  forward_start_time_in_audio: {
    type: String,
  },
  type: {
    type: String,
  },
  body: {
    type: String,
  },
  is_voicemail_drop: {
    type: Boolean,
  },
  client_engagement: {
    type: Number,
  },
  total_speaking_time_seconds: {
    type: Number,
  },
  most_talkative_speaker_name: {
    type: String,
  },
  speaker_durations_list: {
    type: [mongoose.Schema.Types.Mixed],
    default: [],
  },
  tags: {
    type: [mongoose.Schema.Types.Mixed],
    default: [],
  },
  notes: {
    type: String,
    default: "",
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  isLiked: {
    type: Boolean,
    default: null,
  },
  batch_transcription: {
    type: [mongoose.Schema.Types.Mixed],
    default: [],
  },
  detected_entities: {
    type: [mongoose.Schema.Types.Mixed],
    default: [],
  },
  deepgram_live_transcription: {
    type: [mongoose.Schema.Types.Mixed],
    default: [],
  },
});

if (mongoose.models.Conversation) {
  delete mongoose.models.Conversation;
}

const Conversation = mongoose.model("Conversation", ConversationSchema);
export type ConversationType = InferSchemaType<typeof ConversationSchema> & {
  _id: mongoose.Types.ObjectId;
};
export default Conversation;
