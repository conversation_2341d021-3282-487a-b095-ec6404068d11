import mongoose from "mongoose";

const DocumentSchema = new mongoose.Schema({
  name: {
    type: String,
  },
  originalName: {
    type: String,
  },
  url: {
    type: String,
  },
  size: {
    type: Number,
  },
  type: {
    type: String,
  },
  category: {
    type: String,
    enum: ["billing", "legal", "other"],
    default: "billing",
  },
  entreprise_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Entreprise",
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
});

const Document =
  mongoose.models.Document || mongoose.model("Document", DocumentSchema);

export default Document;
