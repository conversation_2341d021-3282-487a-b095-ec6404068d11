import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import dbConnect from "@/lib/mongodb";
import { EntrepriseAdmin } from "@/models/User";

const JWT_SECRET = process.env.JWT_SECRET_KEY_SERVICE!;

export async function POST(req: Request) {
  try {
    await dbConnect();

    let body;
    try {
      body = await req.json();
    } catch (parseError) {
      console.error("Erreur de parsing de la requête:", parseError);
      return NextResponse.json(
        { error: "Requête mal formée. Veuillez envoyer un corps JSON valide." },
        { status: 400 }
      );
    }

    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json(
        { error: "L'email et le mot de passe sont requis." },
        { status: 400 }
      );
    }

    const user = await EntrepriseAdmin.findOne({ email });
    if (!user || !(await bcrypt.compare(password, user.password))) {
      return NextResponse.json(
        { error: "Identifiants invalides." },
        { status: 401 }
      );
    }

    const token = jwt.sign(
      { id: user._id, email: user.email, role: user.role },
      JWT_SECRET,
      { expiresIn: "7d" }
    );

    const response = NextResponse.json(
      { message: "Connexion réussie." },
      { status: 200 }
    );

    response.cookies.set("token", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      path: "/",
      maxAge: 60 * 60 * 24 * 7,
    });

    return response;
  } catch (err) {
    console.error("Erreur serveur lors de la connexion:", err);
    return NextResponse.json(
      { error: "Erreur interne du serveur." },
      { status: 500 }
    );
  }
}
