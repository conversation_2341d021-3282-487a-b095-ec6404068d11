import { FileUpload } from "@/components/ui/file-upload";
import { toast } from "sonner";
import { useEffect, useState } from "react";
import { checkDuplicateNumbers } from "@/actions/ClientsActions";
import ButtonLoader from "../Loaders/ButtonLoader";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { checkNumbersValidity, GetTableData } from "@/lib/csv/CSVFunctions";
import { ClientTable } from "../tables/ClientsData";
import { ScrollArea } from "../ui/scroll-area";
import { Label } from "../ui/label";
import { CountriesSelect } from "../dropdowns/CountriesSelect";
import { DuplicateNumbersTable } from "../tables/DuplicateClients";
import {
  setError,
  setAddContactsToGoal,
  setCountry,
  handleFileUpload,
  uploadClientsToGoal,
} from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { CountryCode } from "libphonenumber-js";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ForceUploadClients } from "../dialogs/ForceUploadClients";
import { FileStructureTable } from "../tables/FileStructureTable";
import { Button } from "../ui/button";
import { Switch } from "../ui/switch";
import { useTranslation } from "react-i18next";

export default function ImportClientsToAGoal() {
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation("assistants");
  const { files, error, country } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardAssistants
  );
  const [Loading, setLoading] = useState(false);
  const [invalidNumbers, setInvalidNumbers] = useState<any[]>([]);
  const [totalRows, setTotalRows] = useState<number>(0);
  const [valid_rows, set_valid_rows] = useState<number>(0);
  const [data_rows, set_data_rows] = useState<any[]>([]);
  const [duplicateNumbers, setDuplicateNumbers] = useState<any[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [phoneColumn, setPhoneColumn] = useState<0 | 1 | null>(null);
  const [saveToDB, setSaveToDB] = useState<boolean>(true);

  const handleMessageChange = (index: number, value: string) => {
    const updated = [...data_rows];
    updated[index] = { ...updated[index], messageDrop: value };
    set_data_rows(updated);
  };

  const handleChange = (UploadedFiles: any) => {
    if (UploadedFiles.length === 0) return;

    const file = UploadedFiles[0];
    const allowedTypes = ["text/csv", "text/plain"];
    if (!allowedTypes.includes(file.type)) {
      dispatch(setError(true));
      toast.error(t("import.selectCsvOrTextFile"));
      return;
    }

    setSelectedFile(file);

    dispatch(
      handleFileUpload([{ name: file.name, type: file.type, size: file.size }])
    );
  };

  const UploadUsers = async () => {
    if (!country) {
      toast.error(t("import.selectCountry"));
      return;
    }
    if (!selectedFile) {
      toast.error(t("import.fillAllFields"));
      return;
    }
    if (phoneColumn === null) {
      toast.error(t("import.selectPhoneColumn"));
      return;
    }
    const allowedTypes = ["text/csv", "text/plain"];
    if (!allowedTypes.includes(selectedFile.type)) {
      toast.error(t("import.selectCsvOrTextFile"));
      return;
    }

    setLoading(true);
    let cleanedData = [...data_rows];

    if (invalidNumbers.length > 0) {
      const invalidSet = new Set(
        invalidNumbers.map((entry) => entry.number.replace(/\s+/g, ""))
      );

      cleanedData = cleanedData.filter((row) => {
        const phone = (
          phoneColumn === 1 ? row.firstValue : row.secondValue
        ).replace(/\s+/g, "");
        return !invalidSet.has(phone);
      });
    }
    await dispatch(
      uploadClientsToGoal({ data: cleanedData, phoneColumn, saveToDB })
    );
    setLoading(false);
  };

  useEffect(() => {
    console.log("Selected file:", selectedFile);
    if (selectedFile) {
      if (!country) {
        toast.error(t("import.selectCountry"));
        return;
      }

      GetTableData(selectedFile)
        .then((rows: any) => {
          setTotalRows(rows.length);
          set_data_rows(rows);
        })
        .catch((err: any) => console.error("Error getting total rows:", err));
    }
  }, [selectedFile, country]);

  useEffect(() => {
    if (data_rows.length > 0 && country && phoneColumn !== null) {
      Promise.all([
        checkNumbersValidity(data_rows, phoneColumn, country),
        saveToDB
          ? checkDuplicateNumbers(data_rows, phoneColumn, country)
          : Promise.resolve({ success: true, duplicateNumbers: [] }),
      ])
        .then(([warnings, result]) => {
          setInvalidNumbers(warnings);

          if (result.success) {
            setDuplicateNumbers(result.duplicateNumbers || []);
            const invalidCount = warnings.length;
            const duplicateCount = result?.duplicateNumbers?.length || 0;

            set_valid_rows(data_rows.length - (invalidCount + duplicateCount));
          } else {
            setDuplicateNumbers([]);
            const invalidCount = warnings.length;

            set_valid_rows(data_rows.length - invalidCount);
            if (saveToDB) {
              console.error("Error checking duplicate numbers.");
            }
          }
        })
        .catch((err) => {
          console.error("Error checking numbers:", err);
        });
    }
  }, [data_rows, country, phoneColumn, saveToDB]);

  // Handle editing a phone number in data_rows
  const handleEditNumber = (index: number, newNumber: string) => {
    // Create a copy of data_rows
    const updatedRows = [...data_rows];

    // Update the phone number in the correct column
    if (phoneColumn !== null && updatedRows[index]) {
      if (phoneColumn === 0) {
        updatedRows[index].firstValue = newNumber;
      } else if (phoneColumn === 1) {
        updatedRows[index].secondValue = newNumber;
      }

      // Update the data_rows state
      set_data_rows(updatedRows);

      // Re-validate the numbers
      if (country) {
        Promise.all([
          checkNumbersValidity(updatedRows, phoneColumn, country),
          checkDuplicateNumbers(updatedRows, phoneColumn, country),
        ])
          .then(([warnings, result]) => {
            setInvalidNumbers(warnings);

            if (result.success) {
              setDuplicateNumbers(result.duplicateNumbers || []);
              const invalidCount = warnings.length;
              const duplicateCount = result?.duplicateNumbers?.length || 0;

              set_valid_rows(
                updatedRows.length - (invalidCount + duplicateCount)
              );
            }
          })
          .catch((err) => {
            console.error("Error checking numbers after edit:", err);
          });
      }
    }
  };

  // Handle deleting an invalid number from data_rows
  const handleDeleteNumber = (index: number) => {
    // Create a copy of data_rows
    const updatedRows = [...data_rows];

    // Remove the row at the specified index
    if (index >= 0 && index < updatedRows.length) {
      updatedRows.splice(index, 1);

      // Update the data_rows state
      set_data_rows(updatedRows);

      // Re-validate the numbers
      if (country && phoneColumn !== null) {
        Promise.all([
          checkNumbersValidity(updatedRows, phoneColumn, country),
          checkDuplicateNumbers(updatedRows, phoneColumn, country),
        ])
          .then(([warnings, result]) => {
            setInvalidNumbers(warnings);

            if (result.success) {
              setDuplicateNumbers(result.duplicateNumbers || []);
              const invalidCount = warnings.length;
              const duplicateCount = result?.duplicateNumbers?.length || 0;

              set_valid_rows(
                updatedRows.length - (invalidCount + duplicateCount)
              );
            }
          })
          .catch((err) => {
            console.error("Error checking numbers after deletion:", err);
          });
      }
    }
  };

  return (
    <Dialog
      open={true}
      onOpenChange={(open) => dispatch(setAddContactsToGoal(open))}
    >
      <DialogContent
        className={`py-5 px-2 sm:px-5 rounded-md w-full border-voxa-neutral-700 max-h-[calc(100vh-50px)] flex flex-col ${
          files?.length > 0 ? "max-w-[900px]" : "max-w-[800px]"
        }`}
      >
        <DialogHeader>
          <DialogTitle>{t("import.importContacts")}</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col gap-3 mt-4 grow overflow-auto">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="country" className="text-sm">
              {t("import.country")} <span className="text-red-500">*</span>
            </Label>
            <CountriesSelect
              country={country}
              selectCountry={(newCountry: "" | CountryCode) =>
                dispatch(setCountry(newCountry))
              }
              classnames="translate-y-px py-5"
            />
          </div>
          <div className="flex items-center space-x-2 mt-4">
            <Switch
              id="owner"
              checked={saveToDB}
              onCheckedChange={(checked) => setSaveToDB(checked)}
            />
            <Label htmlFor="saveToDB">
              {!saveToDB && t("import.dontSaveInDb")}
              {saveToDB && t("import.saveInDb")}
            </Label>
          </div>
          <p className="text-xs text-orange-400 text-center mt-4">
            {t("import.csvColumnsInfo")}
          </p>
          <div className="flex flex-col gap-3 mt-4">
            <FileUpload
              onChange={(e: any) => {
                handleChange(e);
              }}
              type=".csv, .txt"
            />
            {error && (
              <p className="text-red-500 text-sm">Only 1 csv file is allowed</p>
            )}

            {files.length > 0 && !error && country && (
              <Tabs defaultValue="fileDetails" className="w-full mt-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="fileDetails">File Details</TabsTrigger>
                  <TabsTrigger value="warnings" className="relative">
                    Warnings
                    {invalidNumbers.length > 0 && (
                      <span className="absolute -top-1 -right-2 flex h-4 w-4 items-center justify-center rounded-full bg-orange-500 text-xs text-white">
                        {invalidNumbers.length}
                      </span>
                    )}
                  </TabsTrigger>

                  <TabsTrigger value="duplicates" className="relative">
                    Duplicates
                    {duplicateNumbers.length > 0 && (
                      <span className="absolute -top-1 -right-2 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white">
                        {duplicateNumbers.length}
                      </span>
                    )}
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="fileDetails">
                  <div className="h-full p-3 text-gray-300 flex justify-between items-start">
                    <p>
                      <strong className="text-foreground">File Name:</strong>
                      &nbsp;
                      {files[0]?.name}
                    </p>
                    <p>
                      <strong className="text-foreground">Size:</strong>&nbsp;
                      {files[0]?.size} bytes
                    </p>
                    <p>
                      <strong className="text-foreground">Rows:</strong>&nbsp;
                      {totalRows}
                    </p>
                  </div>
                  <div className="p-3 gap-2 flex flex-col">
                    <span className="text-sm text-voxa-teal-500">
                      {t("import.selectPhoneColumnInfo")}
                    </span>
                    <ScrollArea className="h-48 w-full px-4">
                      <FileStructureTable
                        data={data_rows}
                        phoneColumn={phoneColumn}
                        setPhoneColumn={setPhoneColumn}
                        onMessageChange={handleMessageChange}
                        country={country}
                      />
                    </ScrollArea>
                  </div>
                </TabsContent>

                <TabsContent value="warnings">
                  {invalidNumbers.length > 0 ? (
                    <div className="p-3">
                      <ScrollArea className="h-48 w-full">
                        <p className="text-orange-500 text-sm text-center">
                          {t("import.invalidNumbersBelow")}
                        </p>
                        <ClientTable
                          data={invalidNumbers}
                          onEditNumber={handleEditNumber}
                          onDeleteNumber={handleDeleteNumber}
                        />
                      </ScrollArea>
                    </div>
                  ) : (
                    <div className="p-3">
                      <p className="text-green-500 text-sm text-center">
                        {t("import.allNumbersValid")}
                      </p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="duplicates">
                  <div className="p-3">
                    {duplicateNumbers.length > 0 ? (
                      <>
                        <p className="text-red-500 text-sm text-center">
                          {t("import.duplicatesBelow")}
                        </p>
                        <ScrollArea className="h-48 w-full">
                          <div className="p-3">
                            <DuplicateNumbersTable
                              duplicateNumbers={duplicateNumbers}
                              onEditNumber={handleEditNumber}
                              onDeleteNumber={handleDeleteNumber}
                            />
                          </div>
                        </ScrollArea>
                      </>
                    ) : (
                      <p className="text-green-500 text-sm text-center">
                        {t("import.noDuplicates")}
                      </p>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </div>
        </div>
        {files.length > 0 &&
        country &&
        invalidNumbers.length === 0 &&
        duplicateNumbers.length === 0 ? (
          <Button
            disabled={files.length === 0 || error || Loading || !country}
            onClick={UploadUsers}
            className={`${
              Loading && "cursor-not-allowed"
            } w-full text-voxa-neutral-50 flex justify-center items-center gap-2 ${
              files.length === 0 || error
                ? "cursor-not-allowed"
                : "cursor-pointer"
            }`}
          >
            {Loading ? (
              <>
                {t("import.uploadingContacts")}
                <ButtonLoader />
              </>
            ) : (
              t("import.upload")
            )}
          </Button>
        ) : (
          <ForceUploadClients
            handleUpload={UploadUsers}
            invalid_numbers={invalidNumbers.length}
            duplicate_numbers={duplicateNumbers.length}
            files={files}
            valid_rows={valid_rows}
            error={error}
            Loading={Loading}
            country={country}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
