import { NextAuthOptions, Session } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import Cognito from "next-auth/providers/cognito";
import bcrypt from "bcryptjs";
import dbConnect from "./mongodb";
import { User, Role } from "@/models/User";
import Entreprise from "@/models/Entreprise";
import { CreateConnection } from "@/actions/ConnectionActions";

const authOptions: NextAuthOptions = {
  pages: {
    signIn: "/",
  },
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      async profile(profile) {
        await dbConnect();

        let user = await User.findOne({ email: profile.email });

        if (!user) {
          user = await User.create({
            name: profile.name,
            email: profile.email,
            password: null,
            role: Role.ENTREPRISE_ADMIN,
          });
          Entreprise.create({
            name: profile.name,
            email: profile.email,
            admin: user._id,
          });
        }

        return {
          id: user._id.toString(),
          email: user.email,
          name: user.name,
          role: user.role,
        };
      },
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email", required: true },
        password: { label: "Password", type: "password", required: true },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email et mot de passe sont requis !");
        }

        await dbConnect();

        const user = await User.findOne({ email: credentials.email });

        if (!user) {
          throw new Error("Utilisateur non trouvé !");
        }

        const passwordMatch = await bcrypt.compare(
          credentials.password,
          user.password
        );
        if (!passwordMatch) {
          throw new Error("Mot de passe incorrect !");
        }

        return {
          id: user._id.toString(),
          email: user.email,
          name: user.name,
          role: user.role,
        };
      },
    }),
    Cognito({
      clientId: process.env.COGNITO_CLIENT_ID!,
      clientSecret: process.env.COGNITO_CLIENT_SECRET!,
      issuer: process.env.COGNITO_ISSUER!,
      async profile(profile) {
        await dbConnect();

        const email = profile.email;
        const name = profile.name || profile["cognito:username"];

        let user = await User.findOne({ email });

        if (!user) {
          user = await User.create({
            name,
            email,
            password: null,
            role: Role.ENTREPRISE_ADMIN,
          });

          await Entreprise.create({
            name,
            email,
            admin: user._id,
          });
        }

        return {
          id: user._id.toString(),
          email: user.email,
          name: user.name,
          role: user.role,
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.role = user.role;

        // Persist the initial jti as sessionId in the JWT
        if (!token.sessionId && token.jti) {
          token.sessionId = token.jti;
        }

        token.newSignIn = true;
      }
      // Ensure sessionId is always present in the JWT after first sign-in
      if (!token.sessionId && token.jti) {
        token.sessionId = token.jti;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.email = token.email;
        session.user.name = token.name;
        session.user.role = token.role as string;

        // Persisted sessionId from JWT
        session.sessionId = token.sessionId as string;

        if (token.newSignIn === true) {
          await CreateConnection(token.id as string, token.sessionId as string);
          token.newSignIn = false;
        }
      }

      session.sessionId = token.sessionId as string;

      delete session.user.image;
      return session;
    },
    async redirect({ url, baseUrl }) {
      return "/auth/post_login_redirect";
    },
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET || "default-secret",
};

export default authOptions;
