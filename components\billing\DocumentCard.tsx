"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreVertical, Download, Trash2, FileText } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { Document as DocumentType } from "@/types";

interface DocumentCardProps {
  document: DocumentType;
  actionLoading: string | null;
  onDownload: (documentId: string, fileName: string) => void;
  onDeleteClick: (documentId: string, documentName: string) => void;
}

export default function DocumentCard({
  document,
  actionLoading,
  onDownload,
  onDeleteClick,
}: DocumentCardProps) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "billing":
        return "bg-blue-100 hover:bg-blue-200 text-blue-800 dark:bg-blue-900 dark:hover:bg-blue-950 dark:text-blue-300";
      case "legal":
        return "bg-green-100 hover:bg-green-200 text-green-800 dark:bg-green-900 dark:hover:bg-green-950 dark:text-green-300";
      case "other":
        return "bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-gray-900 dark:hover:bg-gray-950 dark:text-gray-300";
      default:
        return "bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-gray-900 dark:hover:bg-gray-950 dark:text-gray-300";
    }
  };

  return (
    <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex items-center space-x-3 flex-1">
        <div className="flex-shrink-0">
          <FileText className="w-5 h-5 text-muted-foreground" />
        </div>
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <p className="w-0 grow font-medium truncate text-sm">
              {document.name}
            </p>
            <Badge
              variant="secondary"
              className={`${getCategoryColor(
                document.category || "other"
              )} text-xs`}
            >
              {document.category}
            </Badge>
          </div>
          <div className="flex items-center gap-3 text-xs text-muted-foreground">
            <span>{formatFileSize(document.size || 0)}</span>
            <span>•</span>
            <span>
              {formatDistanceToNow(new Date(document.created_at!))} ago
            </span>
          </div>
        </div>
      </div>
      <div className="flex-shrink-0">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              disabled={actionLoading === document._id}
              className="h-8 w-8 p-0"
            >
              <MoreVertical className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() =>
                onDownload(document._id, document.originalName || "document")
              }
              disabled={actionLoading === document._id}
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() =>
                onDeleteClick(
                  document._id,
                  document.name || document.originalName || "Document"
                )
              }
              disabled={actionLoading === document._id}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
