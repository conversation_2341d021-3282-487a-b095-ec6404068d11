"use server"
import dbConnect from "@/lib/mongodb";
import Newsletter from "@/models/Newsletter";

export async function subscribeToNewsletter(email: string) {
  try{
    await dbConnect()
    const newsletter = await Newsletter.create({
        email: email.trim().toLowerCase(),
    })
    if(!newsletter) {
      return {success: false, error: "Failed to subscribe to the newsletter."};
    }
    return {success: true, message: "Successfully subscribed to the newsletter."};
  }catch(err: any){
    return {success: false, error: err.message};
  }
}