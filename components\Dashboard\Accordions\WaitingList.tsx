"use client";

import { useState, useEffect, useRef } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import ClientCard from "../Cards/ClientCard";
import { GetConversationHistory } from "@/actions/ConversationActions";
import { toast } from "sonner";
import CircularLoaderSmall from "@/components/Loaders/CircularLoaderSmall";
import { getFormattedDate } from "@/lib/Strings/DateFormat";
import { useTranslation } from "react-i18next";
import { ChevronsDown, Loader2 } from "lucide-react";
import CustomButton from "@/components/CustomFormItems/Button";
import { useSelector, useDispatch } from "react-redux";
import {
  setCalls,
  updateCalls,
} from "@/redux/BusinessDashboard/subSlices/RootSlice";

export function AccordionLists() {
  const { t, i18n } = useTranslation("callLogs");
  const dispatch = useDispatch();
  const calls = useSelector(
    (state: any) => state.businessDashboard.businessDashboardRoot.calls
  );
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [openAccordion, setOpenAccordion] = useState<string | null>(null);
  const [page, setPage] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [loadedCount, setLoadedCount] = useState<number>(0);
  const LIMIT = 10;

  const cardRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const GetConversationsByDate = async (
    daysAgo: number,
    skip: number = 0,
    append: boolean = false
  ) => {
    if (skip === 0) {
      setLoading(true);
    } else {
      setLoadingMore(true);
    }

    try {
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const response = await GetConversationHistory(
        daysAgo,
        timeZone,
        LIMIT,
        skip
      );

      if (!response.success && response.error) {
        if (!append) {
          dispatch(setCalls([]));
          setTotalCount(0);
          setLoadedCount(0);
        }
        return toast.error(response.error);
      }

      if (response.success && response.conversations) {
        if (append) {
          dispatch(updateCalls(response.conversations || []));
        } else {
          dispatch(setCalls(response.conversations || []));
        }
        setTotalCount(response.totalCount || 0);
        setLoadedCount(response.loadedCount || 0);
        setHasMore(
          (response.totalCount || 0) > skip + response.conversations.length
        );
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast.error(err.message);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const daysAgoMap: { [key: string]: number } = {
    "item-2": 0,
    "item-3": 1,
    "item-4": 2,
    "item-5": 3,
    "item-6": 4,
  };

  const handleAccordionChange = (value: string) => {
    if (value && value !== openAccordion) {
      if (daysAgoMap[value] !== undefined) {
        setPage(0);
        setHasMore(true);
        setTotalCount(0);
        setLoadedCount(0);
        GetConversationsByDate(daysAgoMap[value], 0, false);
      }
    }
    setOpenAccordion(openAccordion === value ? null : value);
  };

  const loadMore = () => {
    if (openAccordion) {
      const nextPage = page + 1;
      setPage(nextPage);

      if (daysAgoMap[openAccordion] !== undefined) {
        GetConversationsByDate(
          daysAgoMap[openAccordion],
          nextPage * LIMIT,
          true
        );
      }
    }
  };

  useEffect(() => {
    const newLines: { top: number; left: number; height: number }[] = [];
    calls.forEach((callA: any) => {
      const callB = calls.find((c: any) => c.child_call_sid === callA.CallSid);
      if (!callB) return;
      const fromEl = cardRefs.current[callA._id];
      const toEl = cardRefs.current[callB._id];
      if (fromEl && toEl) {
        const fromRect = fromEl.getBoundingClientRect();
        const toRect = toEl.getBoundingClientRect();
        const containerRect = fromEl.offsetParent?.getBoundingClientRect();
        if (!containerRect) return;
        newLines.push({
          top: fromRect.bottom - containerRect.top,
          left: fromRect.left + fromRect.width / 2 - containerRect.left,
          height: toRect.top - fromRect.bottom,
        });
      }
    });
  }, [calls]);

  return (
    <Accordion
      type="single"
      collapsible
      value={openAccordion || ""}
      onValueChange={handleAccordionChange}
      className="w-full"
    >
      {/* Waiting List */}
      <AccordionItem value="item-1">
        <AccordionTrigger className="text-lg sm:text-xl font-semibold text-foreground/70">
          {t("waitingList.title")}
        </AccordionTrigger>
        <AccordionContent className="flex flex-col gap-2 px-2 sm:px-4">
          <div className="mb-3 w-full flex flex-wrap gap-2 items-center justify-between px-4">
            <p className="font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50 text-lg">
              {t("waitingList.noCallsMessage")}
            </p>
            <button className="bg-voxa-neutral-500 dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-800active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950 transition-all text-nowrap duration-150 text-white px-4 py-2 rounded-md font-medium">
              {t("waitingList.helpCenter")}
            </button>
          </div>
          <p className="text-voxa-neutral-950 dark:text-voxa-neutral-200">
            {t("waitingList.description")}
          </p>
        </AccordionContent>
      </AccordionItem>

      {[
        { value: "item-2", label: t("waitingList.today") },
        { value: "item-3", label: t("waitingList.yesterday") },
        { value: "item-4", label: getFormattedDate(2, i18n.language) },
        { value: "item-5", label: getFormattedDate(3, i18n.language) },
        {
          value: "item-6",
          label: t("waitingList.beforeDate", {
            date: getFormattedDate(4, i18n.language),
          }),
        },
      ].map(({ value, label }) => (
        <AccordionItem
          key={value}
          value={value}
          className="relative flex flex-col"
        >
          <AccordionTrigger className="w-full items-center flex justify-between text-lg sm:text-xl font-semibold text-foreground/70">
            <p>{label}</p>
            {openAccordion === value && !loading && calls.length !== 0 && (
              <p className="ml-auto rtl:ml-0 rtl:mr-auto transition-all duration-1000 text-sm font-medium text-voxa-neutral-500">
                {loadedCount} / {totalCount} {t("waitingList.calls")}
              </p>
            )}
          </AccordionTrigger>
          <AccordionContent className="relative flex flex-col mt-2">
            {loading ? (
              <div className="flex justify-center items-center w-full">
                <CircularLoaderSmall />
              </div>
            ) : (
              <>
                {calls.length === 0 ? (
                  <p className="text-center text-gray-500">
                    {t("waitingList.noCallsForDay")}
                  </p>
                ) : (
                  <>
                    {(() => {
                      const renderedCallSIDs = new Set();
                      const groups: (typeof calls)[] = [];
                      calls.forEach((call: any) => {
                        if (renderedCallSIDs.has(call.CallSid)) return;

                        const child = calls.find(
                          (c: any) => c.CallSid === call.child_call_sid
                        );
                        if (child) {
                          groups.push([child, call]);
                          renderedCallSIDs.add(call.CallSid);
                          renderedCallSIDs.add(child.CallSid);
                        } else if (
                          !calls.some(
                            (c: any) => c.child_call_sid === call.CallSid
                          )
                        ) {
                          groups.push([call]);
                          renderedCallSIDs.add(call.CallSid);
                        }
                      });
                      return groups.map((group, groupIndex) => {
                        const isGrouped = group.length > 1;
                        const type = group[0].type;
                        return (
                          <div
                            key={groupIndex}
                            className={`w-full relative ${
                              isGrouped
                                ? "bg-voxa-neutral-100 dark:bg-voxa-neutral-900 rounded-2xl p-2 flex flex-col gap-2"
                                : ""
                            } mb-2`}
                          >
                            {isGrouped &&
                              (type === "CALL" ? (
                                <div className="absolute -translate-y-2 left-2 z-10 bg-black/80 dark:bg-black  text-voxa-teal-600 text-xs font-semibold px-2 py-0.5 rounded-full shadow-sm">
                                  {t("callLogGroups.transfer")}
                                </div>
                              ) : type === "WHATSAPP" ? (
                                <div className="absolute -translate-y-2 left-2 z-10 bg-black/80 dark:bg-black  text-voxa-teal-600 text-xs font-semibold px-2 py-0.5 rounded-full shadow-sm">
                                  {t("filters.types.whatsapp")}
                                </div>
                              ) : type === "SMS" ? (
                                <div className="absolute -translate-y-2 left-2 z-10 bg-black/80 dark:bg-black  text-voxa-teal-600 text-xs font-semibold px-2 py-0.5 rounded-full shadow-sm">
                                  {t("filters.types.sms")}
                                </div>
                              ) : (
                                ""
                              ))}

                            {group.map((call: any) => (
                              <div
                                key={call._id}
                                ref={(el) => {
                                  if (el && call._id) {
                                    cardRefs.current[call._id] = el;
                                  }
                                }}
                              >
                                <ClientCard call={call} />
                              </div>
                            ))}
                          </div>
                        );
                      });
                    })()}
                    {/* Load More Button */}
                    {hasMore && (
                      <CustomButton
                        props={{
                          value: t("buttons.loadMore"),
                          onClick: loadMore,
                          className:
                            "mx-auto hover:bg-voxa-teal-500 bg-voxa-teal-600 dark:bg-voxa-teal-600 dark:hover:bg-voxa-teal-500 w-fit self-center mt-2 py-2 px-4 rounded-md font-medium flex justify-center items-center gap-1",
                          icon: loadingMore ? (
                            <Loader2 className="animate-spin" />
                          ) : (
                            <ChevronsDown />
                          ),
                          loading: loadingMore,
                        }}
                      />
                    )}
                  </>
                )}
              </>
            )}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}
