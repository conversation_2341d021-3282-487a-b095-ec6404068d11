import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { useState } from "react";
import { toast } from "sonner";
import ButtonLoader from "../Loaders/ButtonLoader";
import { DatePickerWithRange } from "@/components/Inputs/Datepicker";
import { DateRange } from "react-day-picker";
import {
  getExportedConversations,
  setDateRange,
  setExportOpen,
} from "@/redux/BusinessDashboard/subSlices/RootSlice";
import { useTranslation } from "react-i18next";
import { getTimeFromTimestamp } from "@/lib/Strings/DateFormat";

export default function ExportPopup() {
  const { t, i18n } = useTranslation("callLogs");
  const dispatch = useDispatch<AppDispatch>();
  const { dateRange } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardRoot
  );
  const [loading, setLoading] = useState(false);

  const exportConversations = async () => {
    try {
      setLoading(true);
      if (!dateRange.from || !dateRange.to) {
        toast.error("Please select a date range for export");
        return;
      }

      const resultAction = await dispatch(getExportedConversations(dateRange));
      if (getExportedConversations.rejected.match(resultAction)) {
        toast.error(
          (resultAction.payload as string) || "Failed to get conversations"
        );
        return;
      }

      const conversations = resultAction.payload;

      if (!conversations || conversations.length === 0) {
        toast.error("No missed calls found in the selected date range");
        return;
      }

      let csvContent = "Client Phone, Client Name, Missed Calls Count\n";
      conversations.forEach((call) => {
        csvContent += `${call.To}, ${call.client_name || ""}, ${
          call.conversation_count || 1
        }\n`;
      });

      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      const dateFrom = getTimeFromTimestamp(dateRange.from, i18n.language)
        .split(",")
        .slice(0, 3)
        .join(",");
      const dateTo = getTimeFromTimestamp(dateRange.to, i18n.language)
        .split(",")
        .slice(0, 3)
        .join(",");
      a.download = "missed calls from " + dateFrom + " to " + dateTo + ".csv";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      toast.success(`Exported ${conversations.length} missed calls`);
    } catch (err: any) {
      console.error("Error in exportConversations:", err);
      toast.error(err.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={(open) => dispatch(setExportOpen(open))}>
      <DialogContent className="flex flex-col gap-12 sm:max-w-[400px] border-voxa-neutral-700">
        <DialogHeader>
          <DialogTitle>{t("export.title")}</DialogTitle>
        </DialogHeader>
        <DatePickerWithRange
          buttonClass="h-10 w-full"
          date={{
            from: dateRange?.from ? new Date(dateRange.from) : undefined,
            to: dateRange?.to ? new Date(dateRange.to) : undefined,
          }}
          maxRangeDays={7}
          description={t("export.description")}
          setDate={(e: DateRange | undefined) =>
            dispatch(
              setDateRange(
                e
                  ? {
                      from: e.from ? e.from.toISOString() : undefined,
                      to: e.to ? e.to.toISOString() : undefined,
                    }
                  : { from: undefined, to: undefined }
              )
            )
          }
        />
        <Button
          onClick={exportConversations}
          disabled={loading}
          className={`
              w-full
              ${
                loading
                  ? "cursor-not-allowed bg-voxa-teal-400"
                  : "bg-voxa-teal-600 hover:bg-voxa-teal-500 text-voxa-neutral-50"
              }
            `}
        >
          {loading ? (
            <>
              submitting...
              <ButtonLoader />
            </>
          ) : (
            "Submit Export"
          )}
        </Button>
      </DialogContent>
    </Dialog>
  );
}
