"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input"; // Ensure you have an Input component
import { CountryCode, parsePhoneNumberFromString } from "libphonenumber-js";
import * as Flags from "country-flag-icons/react/3x2";

interface Entry {
  firstValue: string;
  secondValue: string;
  messageDrop?: string; // Optional third column
}

interface ClientTableProps {
  data: Entry[];
  phoneColumn: 0 | 1 | null;
  setPhoneColumn: (value: 0 | 1 | null) => void;
  onMessageChange?: (index: number, value: string) => void; // Optional callback
  country: string;
}

export function FileStructureTable({
  data,
  phoneColumn,
  setPhoneColumn,
  onMessageChange,
  country,
}: ClientTableProps) {
  // Helper to get flag component by country code
  function getFlagComponent(countryCode?: string) {
    if (!countryCode) return null;
    const Flag = (Flags as any)[countryCode.toUpperCase()];
    return Flag ? (
      <Flag
        style={{
          width: 24,
          display: "inline-block",
          marginRight: 4,
        }}
      />
    ) : null;
  }

  // Helper to extract country code from phone number
  function getCountryCode(phone: string) {
    try {
      const phoneNumber = parsePhoneNumberFromString(
        phone,
        country as CountryCode
      );
      return phoneNumber?.country;
    } catch {
      return undefined;
    }
  }

  return (
    <Table className="text-xs sm:text-sm">
      <TableHeader>
        <TableRow className="hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-950">
          <TableHead
            onClick={() => setPhoneColumn(0)}
            className={`cursor-pointer ${
              phoneColumn === 0 ? "bg-teal-400/20" : ""
            }`}
          >
            First Column
          </TableHead>
          <TableHead
            onClick={() => setPhoneColumn(1)}
            className={`cursor-pointer ${
              phoneColumn === 1 ? "bg-teal-400/20" : ""
            }`}
          >
            Second Column
          </TableHead>
          <TableHead>Message Drop</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map((entry, index) => {
          const phoneValue =
            phoneColumn === 0
              ? entry.firstValue
              : phoneColumn === 1
              ? entry.secondValue
              : "";
          const countryCode = getCountryCode(phoneValue);
          return (
            <TableRow
              key={index}
              className="hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900"
            >
              <TableCell
                className={phoneColumn === 0 ? "bg-teal-400/20" : ""}
                onClick={() => setPhoneColumn(0)}
              >
                {phoneColumn === 0 && getFlagComponent(countryCode)}
                {entry.firstValue}
              </TableCell>
              <TableCell
                className={phoneColumn === 1 ? "bg-teal-400/20" : ""}
                onClick={() => setPhoneColumn(1)}
              >
                {phoneColumn === 1 && getFlagComponent(countryCode)}
                {entry.secondValue}
              </TableCell>
              <TableCell>
                <Input
                  type="text"
                  value={entry.messageDrop || ""}
                  onChange={(e) => onMessageChange?.(index, e.target.value)}
                  placeholder="Optional"
                />
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
}
