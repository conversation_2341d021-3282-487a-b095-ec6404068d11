"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useTranslation } from "react-i18next";

interface TranslationState {
  text: string;
  loading: boolean;
}

export function useTranslate() {
  const { i18n } = useTranslation();
  const [translations, setTranslations] = useState<
    Record<string, Record<string, string>>
  >({});
  const [loadingTexts, setLoadingTexts] = useState<Set<string>>(new Set());
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const fetchTranslation = useCallback(
    async (text: string, targetLanguage: string) => {
      if (!isMountedRef.current) return;

      setLoadingTexts((prev) => new Set(prev).add(text));

      try {
        const response = await fetch("/api/translate", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ text, targetLanguage }),
        });

        const data = await response.json();

        if (isMountedRef.current) {
          setTranslations((prev) => ({
            ...prev,
            [text]: { ...prev[text], [targetLanguage]: data.translatedText },
          }));
        }
      } catch (error) {
        console.error("Translation failed:", error);
        if (isMountedRef.current) {
          setTranslations((prev) => ({
            ...prev,
            [text]: { ...prev[text], [targetLanguage]: text },
          }));
        }
      } finally {
        if (isMountedRef.current) {
          setLoadingTexts((prev) => {
            const newSet = new Set(prev);
            newSet.delete(text);
            return newSet;
          });
        }
      }
    },
    []
  );

  const getTranslation = useCallback(
    (text: string): TranslationState => {
      if (!text || text.trim() === "") {
        return { text, loading: false };
      }

      const currentLanguage = i18n.language;

      if (translations[text]?.[currentLanguage]) {
        return {
          text: translations[text][currentLanguage],
          loading: false,
        };
      }

      const isLoading = loadingTexts.has(text);

      if (!translations[text] && !isLoading) {
        setTranslations((prev) => ({ ...prev, [text]: {} }));
        fetchTranslation(text, currentLanguage);
      }

      return {
        text: isLoading ? "" : text,
        loading: isLoading,
      };
    },
    [i18n.language, translations, loadingTexts, fetchTranslation]
  );

  useEffect(() => {
    const currentLanguage = i18n.language;
    const textsToRetranslate = Object.keys(translations).filter(
      (text) => !translations[text][currentLanguage] && !loadingTexts.has(text)
    );

    textsToRetranslate.forEach((text) => {
      fetchTranslation(text, currentLanguage);
    });
  }, [i18n.language, translations, loadingTexts, fetchTranslation]);

  return {
    getTranslation,
    currentLanguage: i18n.language,
  };
}
