"use client";

import { createPaymentLink, topupWithCard } from "@/actions/BillingActions";
import AvailablePlans from "@/components/billing/AvailablePlans";
import BillingNotifications from "@/components/billing/BillingNotifications";
import ConfirmationDialog from "@/components/billing/ConfirmationDialog";
import CurrentBalance from "@/components/billing/CurrentBalance";
import CurrentSubscription from "@/components/billing/CurrentSubscription";
import DocumentsList from "@/components/billing/DocumentsList";
import InvoiceHistory from "@/components/billing/InvoiceHistory";
import PaymentPackagesGrid from "@/components/billing/PaymentPackagesGrid";
import SavedCards from "@/components/billing/SavedCards";
import SpecialPlan from "@/components/billing/SpecialPlan";
import SubscriptionHistory from "@/components/billing/SubscriptionHistory";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import type { BusinessDashboardState } from "@/redux/BusinessDashboard/BusinessDashboardSlice";
import {
  closeConfirmationDialog,
  openConfirmationDialog,
  processPayment,
  resetSelectedAmount,
  setIsProcessingPayment,
} from "@/redux/BusinessDashboard/subSlices/BillingSlice";
import { fetchEntreprise } from "@/redux/BusinessDashboard/subSlices/EntrepriseSlice";
import type { AppDispatch } from "@/redux/store";
import { Settings } from "lucide-react";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";
import { mutate } from "swr";

export default function Billing() {
  const dispatch = useDispatch<AppDispatch>();
  const {
    selectedAmount,
    isConfirmDialogOpen,
    isProcessingPayment,
    paymentPackages,
  } = useSelector(
    (state: { businessDashboard: BusinessDashboardState }) =>
      state.businessDashboard.businessDashboardBilling
  );
  const { entreprise, loading: entrepriseLoading } = useSelector(
    (state: { businessDashboard: BusinessDashboardState }) =>
      state.businessDashboard.businessDashboardEntreprise
  );
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    let timeout: NodeJS.Timeout;

    const paymentStatus = searchParams?.get("payment");
    const paymentAmount = searchParams?.get("amount");

    const shouldShowToast = paymentStatus === "success";

    if (shouldShowToast) {
      timeout = setTimeout(() => {
        toast.success("Payment completed successfully!");
      });

      // Clean the URL by removing query params using Next.js router
      const currentPath = window.location.pathname;
      router.replace(currentPath, { scroll: false });
    }

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [searchParams]);

  const handlePackageSelect = (amount: number) => {
    dispatch(openConfirmationDialog(amount));
  };

  const handlePayWithCard = async (cardId: string) => {
    if (!selectedAmount) return;

    try {
      dispatch(setIsProcessingPayment(true));
      const response = await topupWithCard(selectedAmount, cardId);
      if (response.success) {
        // Payment completed successfully with saved card
        dispatch(closeConfirmationDialog());
        setTimeout(() => {
          dispatch(fetchEntreprise(["balance"]));
          mutate(["invoices", 1]);
        }, 2000);
        toast.success("Payment completed successfully!");
      } else {
        toast.error(response.error || "Payment failed");
      }
    } catch (error) {
      console.error("Error processing payment with card:", error);
      toast.error("Payment failed. Please try again.");
    } finally {
      dispatch(setIsProcessingPayment(false));
    }
  };

  const handlePayWithLink = async () => {
    if (!selectedAmount) return;

    try {
      dispatch(setIsProcessingPayment(true));
      const response = await createPaymentLink(selectedAmount);
      if (response.success && response.data) {
        // Redirect to payment link
        window.location.href = response.data.url;
      } else {
        toast.error(response.error || "Failed to create payment link");
      }
    } catch (error) {
      console.error("Error creating payment link:", error);
      toast.error("Failed to create payment link. Please try again.");
    } finally {
      dispatch(setIsProcessingPayment(false));
    }
  };

  const handleCloseDialog = () => {
    dispatch(closeConfirmationDialog());
  };

  const handleCloseComplete = () => {
    dispatch(resetSelectedAmount());
  };

  return (
    <div className="w-full flex flex-col gap-6">
      {/* Header */}
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        Billing & Credits
      </h1>

      {/* Billing Notifications */}
      <BillingNotifications />

      {/* Current Balance */}
      <CurrentBalance
        balance={entreprise?.balance || 0}
        isLoading={entrepriseLoading}
      />

      <SpecialPlan />

      {/* Current Subscription */}
      <CurrentSubscription />

      {/* Payment Packages */}
      <PaymentPackagesGrid
        packages={paymentPackages}
        onSelectPackage={handlePackageSelect}
      />

      {/* Saved Cards */}
      <SavedCards />

      {/* Available Plans */}
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem
          value="subscription-plans"
          className="!border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg overflow-hidden"
        >
          <AccordionTrigger className="py-5 px-4 font-medium text-base bg-voxa-neutral-50 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700 transition-colors flex items-center">
            <div className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-voxa-teal-500 dark:text-voxa-teal-400" />
              <span>Available Plans</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-4">
            <AvailablePlans />
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Subscription History */}
      <SubscriptionHistory />

      {/* Invoice History */}
      <InvoiceHistory />

      {/* Payment History */}
      {/* <PaymentHistory onRefresh={handleRefreshHistory} /> */}

      {/* Documents Section */}
      <DocumentsList />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isConfirmDialogOpen}
        onClose={handleCloseDialog}
        onCloseComplete={handleCloseComplete}
        onPayWithCard={handlePayWithCard}
        onPayWithLink={handlePayWithLink}
        selectedAmount={selectedAmount}
        isProcessingPayment={isProcessingPayment}
      />
    </div>
  );
}
