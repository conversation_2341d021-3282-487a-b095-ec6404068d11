import mongoose from "mongoose";

enum GoalStatus {
  PENDING = "PENDING",
  PAUSED = "PAUSED",
  COMPLETED = "COMPLETED",
  STOPPED = "STOPPED",
  STARTED = "STARTED",
}

enum GoalType {
  INCOMING = "INCOMING",
  OUTGOING = "OUTGOING",
}

enum GoalVisisbility {
  VISIBLE = "VISIBLE",
  HIDDEN = "HIDDEN",
}

const Goal =
  mongoose.models.Goal ||
  mongoose.model(
    "Goal",
    new mongoose.Schema(
      {
        name: {
          type: String,
        },
        country: {
          type: String,
        },
        GoalContext: {
          type: String,
        },
        assistant: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Assistant",
        },
        status: {
          type: String,
          enum: GoalStatus,
          default: GoalStatus.PENDING,
        },
        entreprise: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Entreprise",
        },
        phoneNumber: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Phone",
        },
        assistant_number: {
          type: String,
        },
        goal_visibility: {
          type: String,
          enum: GoalVisisbility,
          default: GoalVisisbility.VISIBLE,
        },
        tags: [
          {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Tag",
          },
        ],
        updated_at: {
          type: Date,
        },
        created_at: {
          type: Date,
          default: Date.now,
        },
      },
      { discriminatorKey: "goal_template_type" }
    )
  );

const DemarchageGoalSchema = new mongoose.Schema({
  clients: [
    {
      client_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Client",
      },
      status: {
        type: String,
        enum: [
          "ANSWERED",
          "NOT_CALLED",
          "MISSED",
          "VOICEMAIL_DETECTED",
          "FAILED",
        ],
        default: "NOT_CALLED",
      },
      calls_count: {
        type: Number,
        default: 0,
      },
      name: {
        type: String,
      },
      phone: {
        type: String,
      },
      gender: {
        type: String,
      },
      message_drop: {
        type: String,
      },
      updated_at: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  goalType: {
    type: String,
    enum: GoalType,
    default: GoalType.OUTGOING,
  },
  conversations: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  prompt: {
    type: String,
  },
  GoalContext: {
    type: String,
  },
  availability: [
    {
      start_time: {
        type: Date,
      },
      end_time: {
        type: Date,
      },
    },
  ],
  retry_count: {
    type: Number,
    default: 0,
  },
  forwarded_to_number: {
    type: String,
  },
  forwarded_to_group_id: {
    type: String,
  },
  is_debug: {
    type: Boolean,
    default: false,
  },
  answered_conversations_ids: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  voicemail_detected_conversations_ids: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  failed_conversations_ids: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  missed_conversations_ids: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  incall_conversations_ids: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  avoid_script_and_direct_transfer: {
    type: Boolean,
    default: false,
  },
  messages_on_missed_call: {
    type: String,
    default: "NONE",
    enum: ["NONE", "SMS", "WHATSAPP"],
  },
  message_on_missed_call_content: {
    type: String,
  },
  voicemail_drop_type: {
    type: String,
    enum: ["TEXT", "AUDIO", "NONE"],
    default: "NONE",
  },
  voicemail_drop_content: {
    type: String,
  },
  voicemail_drop_audio_url: {
    type: String,
  },
  voicemail_drop_audio_duration: {
    type: Number,
  },
  is_pronounce_client_name_enabled: {
    type: Boolean,
    default: false,
  },
  is_pronounce_client_honorific_enabled: {
    type: Boolean,
    default: false,
  },
  ai_name_male: {
    type: String,
  },
  ai_name_female: {
    type: String,
  },
  duration_between_calls: {
    type: Number,
    default: 0,
  },
  ringing_duration: {
    type: Number,
    default: 10,
  },
  male_voice: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "AI_voices",
    },
    provider: String,
  },
  female_voice: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "AI_voices",
    },
    provider: String,
  },
  human_introduction_enabled: {
    type: Boolean,
    default: false,
  },
  human_introduction_url: {
    type: String,
  },
  human_introduction_audio_duration: {
    type: Number,
  },
  is_template: {
    type: Boolean,
    default: false,
  },
  template_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Goal",
  },
});

const GoogleMeetSchema = new mongoose.Schema({
  /*
  crm_id: {
    type: String
  },
  crm_token: {
    type: String
  },
  */
  clients: [
    {
      google_meet_dial_in_number: {
        type: String,
      },
      google_meet_pin: {
        type: String,
      },
    },
  ],
  enable_mute: {
    type: Boolean,
    default: false,
  },
  enable_AI_voice: {
    type: Boolean,
    default: false,
  },
  ai_name_male: {
    type: String,
  },
  ai_name_female: {
    type: String,
  },
  male_voice: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "AI_voices",
    },
    provider: String,
  },
  female_voice: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "AI_voices",
    },
    provider: String,
  },
  prompt: {
    type: String,
  },
  status: {
    type: String,
    enum: GoalStatus,
    default: GoalStatus.PENDING,
  },
});

const MultiTranslationSchema = new mongoose.Schema({
  clients: [
    {
      client_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Client",
      },
      status: {
        type: String,
        enum: [
          "ANSWERED",
          "NOT_CALLED",
          "MISSED",
          "VOICEMAIL_DETECTED",
          "FAILED",
        ],
        default: "NOT_CALLED",
      },
      calls_count: {
        type: Number,
        default: 0,
      },
      name: {
        type: String,
      },
      phone: {
        type: String,
      },
      gender: {
        type: String,
      },
      updated_at: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  conversations: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  answered_conversations_ids: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  voicemail_detected_conversations_ids: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  failed_conversations_ids: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  missed_conversations_ids: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],
  incall_conversations_ids: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
  ],

  availability: [
    {
      start_time: {
        type: Date,
      },
      end_time: {
        type: Date,
      },
    },
  ],
  duration_between_calls: {
    type: Number,
    default: 0,
  },
  ringing_duration: {
    type: Number,
    default: 10,
  },
  retry_count: {
    type: Number,
    default: 0,
  },
});

const DemarchageGoal =
  mongoose.models.DemarchageGoal ||
  Goal.discriminators?.["DEMARCHAGE"] ||
  Goal.discriminator("DEMARCHAGE", DemarchageGoalSchema);

const GoogleMeetGoal =
  mongoose.models.GoogleMeetGoal ||
  Goal.discriminators?.["GOOGLE_MEET"] ||
  Goal.discriminator("GOOGLE_MEET", GoogleMeetSchema);

const MultiTranslationGoal =
  mongoose.models.MultiTranslationGoal ||
  Goal.discriminators?.["MULTI_TRANSLATION"] ||
  Goal.discriminator("MULTI_TRANSLATION", MultiTranslationSchema);

export default Goal;
export { DemarchageGoal, GoogleMeetGoal, MultiTranslationGoal };
