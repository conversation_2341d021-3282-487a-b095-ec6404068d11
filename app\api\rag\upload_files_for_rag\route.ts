import { NextResponse } from "next/server";
import { GetEntrepriseRagCollectionLinks } from "@/actions/CollectionActions";
// import axios from "axios";

// req: Request
export async function POST() {
  try {
    const Collectionsresponse = await GetEntrepriseRagCollectionLinks();
    if (Collectionsresponse.error) {
      return NextResponse.json(
        { error: Collectionsresponse.error },
        { status: 400 }
      );
    }

    const links = Collectionsresponse.links;
    if (!links || links.length === 0) {
      return NextResponse.json(
        { error: "No collections found" },
        { status: 400 }
      );
    }

    // const RagResponse = await axios.post(`${process.env.BACKEND_URL}/chroma/populate-database/`,
    //     { pdf_urls: links },
    //     {
    //         headers: {
    //             "Content-Type": "application/json"
    //         }
    //     }
    // );

    return NextResponse.json({ success: true });
  } catch (err: any) {
    // Catch axios errors or any other errors and return the error message
    const errorMessage =
      err.response?.data?.detail || err.message || "Internal server error";
    return NextResponse.json(
      { error: errorMessage },
      { status: err.response?.status || 500 }
    );
  }
}
