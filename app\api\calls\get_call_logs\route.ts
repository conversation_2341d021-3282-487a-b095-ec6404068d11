import dbConnect from "@/lib/mongodb";
import Entreprise from "@/models/Entreprise";
import { User } from "@/models/User";
import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { verifyUserToken } from "@/lib/cognito/Token";
import { GetConversationsByFilters } from "@/actions/ConversationActions";

export async function GET(req: Request) {
  try {
    await dbConnect();

    const cookieStore = cookies();
    const token = (await cookieStore).get("plugin_id_token")?.value;

    console.log("Token from cookies:", token);

    if (!token) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    // ✅ Verify Cognito token
    const { valid, payload, error } = await verifyUserToken(token);
    console.log("Token verification result:", { valid, payload, error });

    if (!valid || !payload?.email) {
      return NextResponse.json(
        {
          error: "Token invalide ou expiré.",
          detail: error?.message || "Token non valide.",
        },
        { status: 401 }
      );
    }

    // ✅ Find user by email
    const user = await User.findOne({ email: payload.email });
    if (!user) {
      return NextResponse.json({ error: "Utilisateur introuvable." }, { status: 404 });
    }

    // ✅ Find entreprise by user ID
    const entreprise = await Entreprise.findOne({ admin: user._id });
    if (!entreprise) {
      return NextResponse.json({ error: "Entreprise introuvable." }, { status: 404 });
    }

    const { searchParams } = new URL(req.url);
    const callSid = searchParams.get("CallSID") || undefined;
    const conversationID = searchParams.get("conversationID") || undefined;
    const recepientName = searchParams.get("recepientName") || undefined;
    const recepientPhone = searchParams.get("recepientPhone") || undefined;
    const dateFromString = searchParams.get("dateFromString") || undefined;
    const dateToString = searchParams.get("dateToString") || undefined;
    const isIncoming = searchParams.get("incoming")?.toLowerCase() === "true";
    const isOutgoing = searchParams.get("outgoing")?.toLowerCase() === "true";
    const isMissed = searchParams.get("missed")?.toLowerCase() === "true";
    const whatsapp = searchParams.get("whatsapp")?.toLowerCase() === "true";
    const sms = searchParams.get("sms")?.toLowerCase() === "true";
    const call = searchParams.get("call")?.toLowerCase() === "true";
    const meet = searchParams.get("meet")?.toLowerCase() === "true";
    const itemsPerPage = parseInt(searchParams.get("itemsPerPage") || "10", 10);
    const skip = parseInt(searchParams.get("skip") || "0", 10);

    const dateRange: any = {};
    if (dateFromString) {
      dateRange.from = new Date(dateFromString);
    }

    if (dateToString) {
      dateRange.to = new Date(dateToString);
    }

    const response = await GetConversationsByFilters(
        {
            entreprise_id: entreprise._id,
            CallSID: callSid,
            conversationID,
            recepientName,
            recepientPhone,
            incoming: isIncoming,
            outgoing: isOutgoing,
            missed: isMissed,
            whatsapp,
            sms,
            call,
            meet,
            Items: itemsPerPage,
            skip,
            dateRange,
        }
    )

    console.log("Response from GetConversationsByFilters:", response);

    if (!response.success) {
      return NextResponse.json({ error: response.error }, { status: 400 });
    }

    const { conversations, totalCount, loadedCount } = response;

    return NextResponse.json({ conversations, totalCount, loadedCount }, { status: 200 });

  } catch (err: any) {
    console.error("Erreur serveur:", err);
    return NextResponse.json({ error: "Erreur interne du serveur." }, { status: 500 });
  }
}
