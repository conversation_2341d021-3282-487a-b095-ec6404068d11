"use client";

import useSWR from "swr";
import {
  getInvoices, // <-- update import
  payInvoice,
} from "@/actions/BillingActions";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import CustomPagination from "@/components/pagination/CustomPagination";
import { formatDistanceToNow } from "date-fns";
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  Download,
  Eye,
  FileText,
  RefreshCw,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { Invoice } from "@/types";

interface InvoiceHistoryProps {
  onRefresh?: () => void;
}

const PAGE_LIMIT = 20;

const fetcher = async (page: number) => {
  const res = await getInvoices(PAGE_LIMIT, page); // <-- update call
  if (!res.success) throw new Error(res.error || "Failed to fetch invoices");
  return res.data;
};

export default function InvoiceHistory({ onRefresh }: InvoiceHistoryProps) {
  const [page, setPage] = useState(1);
  const { data, error, isLoading, mutate, isValidating } = useSWR(
    ["invoices", page],
    () => fetcher(page),
    { keepPreviousData: true }
  );
  const [payingInvoice, setPayingInvoice] = useState<string | null>(null);

  const invoices: Invoice[] = data?.invoices || [];
  const totalInvoices = data?.total || 0;
  const totalPages = data?.totalPages || 1;
  const hasNext = data?.hasMore || false;
  const hasPrev = page > 1;

  const handleRefresh = async () => {
    await mutate();
    if (onRefresh) onRefresh();
  };

  const handlePayInvoice = async (invoiceId: string) => {
    setPayingInvoice(invoiceId);
    try {
      const response = await payInvoice(invoiceId);
      if (response.success && response.data?.payment_url) {
        window.location.href = response.data.payment_url;
      } else {
        toast.error(response.error || "Failed to create payment session");
      }
    } catch (error: any) {
      console.error("Error creating payment session:", error);
      toast.error("Failed to create payment session");
    } finally {
      setPayingInvoice(null);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    const symbol = currency === "eur" ? "€" : "$";
    return `${symbol}${Number(price.toFixed(2))}`;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      paid: { variant: "default" as const, label: "Paid", icon: CheckCircle },
      open: {
        variant: "destructive" as const,
        label: "Unpaid",
        icon: AlertCircle,
      },
      void: {
        variant: "secondary" as const,
        label: "Canceled",
        icon: AlertCircle,
      },
      draft: {
        variant: "secondary" as const,
        label: "Not Ready",
        icon: Clock,
      },
      upcoming: {
        variant: "outline" as const,
        label: "Upcoming (Auto-pay)",
        icon: Calendar,
      },
      uncollectible: {
        variant: "destructive" as const,
        label: "Payment Failed",
        icon: AlertCircle,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
      icon: AlertCircle,
    };

    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const getPeriodInfo = (invoice: Invoice) => {
    if (invoice.period_start && invoice.period_end) {
      const start = formatDate(new Date(invoice.period_start).getTime());
      const end = formatDate(new Date(invoice.period_end).getTime());
      return `${start} - ${end}`;
    }
    return "N/A";
  };

  const canPayInvoice = (invoice: Invoice) => {
    return invoice.status === "open" && invoice._id !== "upcoming";
  };

  return (
    <div className="w-full">
      <Accordion
        type="single"
        collapsible
        className="w-full"
        defaultValue="invoice-history"
      >
        <AccordionItem
          value="invoice-history"
          className="!border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg overflow-hidden"
        >
          <AccordionTrigger className="py-5 px-4 font-medium text-base bg-voxa-neutral-50 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700 transition-colors flex items-center">
            <div className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-voxa-teal-500 dark:text-voxa-teal-400" />
              <span>Invoice History</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4">
            <div className="mt-4 mb-4 w-full">
              {isLoading && (!invoices || !totalInvoices) ? (
                <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                  <div className="flex items-center justify-center gap-2 text-muted-foreground">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    Loading invoice history...
                  </div>
                </div>
              ) : error ? (
                <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                  <FileText className="w-10 h-10 mx-auto mb-4 text-voxa-neutral-300 dark:text-voxa-neutral-600" />
                  <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 mb-2">
                    Failed to load invoices
                  </p>
                  <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 text-sm">
                    {error.message}
                  </p>
                </div>
              ) : totalInvoices > 0 ? (
                <div className="w-0 min-w-full border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg shadow-sm overflow-auto">
                  <div className="min-w-[900px]">
                    <div className="w-full flex justify-between items-center p-4 bg-voxa-neutral-50 dark:bg-voxa-neutral-900 border-b border-voxa-neutral-200 dark:border-voxa-neutral-700">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-voxa-neutral-500 dark:text-voxa-neutral-400" />
                        <h3 className="font-medium">Invoice History</h3>
                        <span className="bg-voxa-teal-100 dark:bg-voxa-teal-900 text-voxa-teal-700 dark:text-voxa-teal-300 text-xs px-2 py-1 rounded-full">
                          {(page - 1) * PAGE_LIMIT + 1} -{" "}
                          {Math.min(page * PAGE_LIMIT, totalInvoices)} of{" "}
                          {totalInvoices}{" "}
                          {totalInvoices === 1 ? "invoice" : "invoices"}
                        </span>
                      </div>
                      <Button
                        variant="outline"
                        onClick={handleRefresh}
                        disabled={isValidating}
                        className="text-sm bg-voxa-neutral-100 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700 transition-colors"
                        type="button"
                      >
                        <RefreshCw
                          className={`w-4 h-4 mr-2 ${
                            isValidating ? "animate-spin" : ""
                          }`}
                        />
                        Refresh
                      </Button>
                    </div>
                    <div className="overflow-y-auto">
                      <table className="w-full">
                        <thead className="bg-voxa-neutral-100 dark:bg-voxa-neutral-800 sticky top-0">
                          <tr>
                            <th className="p-2 text-left text-xs font-medium">
                              Invoice
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Status
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Amount
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Period
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Date
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {invoices.map((invoice, index) => (
                            <tr
                              key={`${invoice.stripe_invoice_id}-${index}`}
                              className="border-t border-voxa-neutral-200 dark:border-voxa-neutral-800 hover:bg-voxa-neutral-50 dark:hover:bg-voxa-neutral-800 transition-colors"
                            >
                              <td className="p-2">
                                <div>
                                  {invoice.plan_name ? (
                                    <div className="text-xs text-voxa-teal-700 dark:text-voxa-teal-300 font-semibold">
                                      {invoice.plan_name}
                                    </div>
                                  ) : (
                                    <div className="text-xs text-voxa-teal-700 dark:text-voxa-teal-300 font-semibold">
                                      {formatPrice(
                                        invoice.subtotal / 100,
                                        invoice.currency
                                      )}{" "}
                                      Top-up
                                    </div>
                                  )}
                                  {invoice.description && (
                                    <div className="text-xs text-muted-foreground">
                                      {invoice.description}
                                    </div>
                                  )}
                                </div>
                              </td>
                              <td className="p-2">
                                <div className="flex items-center gap-2">
                                  {getStatusBadge(invoice.status)}
                                </div>
                              </td>
                              <td className="p-2">
                                <div className="font-medium text-sm">
                                  {formatPrice(
                                    invoice.amount_due / 100,
                                    invoice.currency
                                  )}
                                </div>
                                {invoice.amount_paid > 0 && (
                                  <div className="text-xs text-muted-foreground">
                                    Paid:{" "}
                                    {formatPrice(
                                      invoice.amount_paid / 100,
                                      invoice.currency
                                    )}
                                  </div>
                                )}
                              </td>
                              <td className="p-2 text-sm">
                                {getPeriodInfo(invoice)}
                              </td>
                              <td className="p-2 text-xs text-muted-foreground">
                                <div>
                                  {invoice.stripe_invoice_id === "upcoming"
                                    ? "Next billing"
                                    : formatDate(
                                        new Date(invoice.created).getTime()
                                      )}
                                </div>
                                {invoice.stripe_invoice_id !== "upcoming" && (
                                  <div className="text-xs">
                                    {formatDistanceToNow(
                                      new Date(invoice.created),
                                      {
                                        addSuffix: true,
                                      }
                                    )}
                                  </div>
                                )}
                                {invoice.due_date &&
                                  invoice.status === "open" && (
                                    <div className="text-xs text-orange-600 dark:text-orange-400">
                                      Due:{" "}
                                      {formatDate(
                                        new Date(invoice.due_date).getTime()
                                      )}
                                    </div>
                                  )}
                              </td>
                              <td className="p-2 text-center">
                                <div className="flex items-center gap-1">
                                  {invoice.hosted_invoice_url && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() =>
                                        window.open(
                                          invoice.hosted_invoice_url!,
                                          "_blank"
                                        )
                                      }
                                      className="text-xs h-7 px-2"
                                      type="button"
                                    >
                                      <Eye className="w-3 h-3 mr-1" />
                                      View
                                    </Button>
                                  )}
                                  {invoice.invoice_pdf && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() =>
                                        window.open(
                                          invoice.invoice_pdf!,
                                          "_blank"
                                        )
                                      }
                                      className="text-xs h-7 px-2"
                                      type="button"
                                    >
                                      <Download className="w-3 h-3 mr-1" />
                                      PDF
                                    </Button>
                                  )}
                                  {canPayInvoice(invoice) && (
                                    <Button
                                      variant="default"
                                      size="sm"
                                      onClick={() =>
                                        handlePayInvoice(
                                          invoice.stripe_invoice_id
                                        )
                                      }
                                      disabled={
                                        payingInvoice ===
                                        invoice.stripe_invoice_id
                                      }
                                      className="text-xs h-7 px-2 bg-voxa-teal-600 hover:bg-voxa-teal-700"
                                      type="button"
                                    >
                                      {payingInvoice ===
                                      invoice.stripe_invoice_id ? (
                                        <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                                      ) : (
                                        <CreditCard className="w-3 h-3 mr-1" />
                                      )}
                                      Pay Now
                                    </Button>
                                  )}
                                  {invoice.stripe_invoice_id === "upcoming" && (
                                    <span className="text-xs text-muted-foreground px-2">
                                      Auto-pay scheduled
                                    </span>
                                  )}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                    {/* Pagination Controls */}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                  <FileText className="w-10 h-10 mx-auto mb-4 text-voxa-neutral-300 dark:text-voxa-neutral-600" />
                  <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 mb-2">
                    No invoices available
                  </p>
                  <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 text-sm">
                    Your invoices will appear here once you have an active
                    subscription
                  </p>
                </div>
              )}
              {totalPages > 1 && (
                <CustomPagination
                  itemsPerPage={PAGE_LIMIT}
                  totalItems={totalInvoices}
                  currentPage={page}
                  onPageChange={setPage}
                  className="mt-4 pt-4 border-t border-voxa-neutral-200 dark:border-voxa-neutral-700"
                />
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
