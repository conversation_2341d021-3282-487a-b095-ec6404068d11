import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import jwt, { JsonWebTokenError } from "jsonwebtoken";
import dbConnect from "@/lib/mongodb";
import Entreprise from "@/models/Entreprise";
import { User } from "@/models/User";
import Goal from "@/models/Goal";
import { verifyUserToken } from "@/lib/cognito/Token";

const JWT_SECRET = process.env.JWT_SECRET_KEY_SERVICE!;

export async function GET(req: Request) {
  try {
    await dbConnect();

    const cookieStore = cookies();
    const token = (await cookieStore).get("plugin_id_token")?.value;

    if (!token) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    const { valid, payload, error } = await verifyUserToken(token);

    if (!valid || !payload?.email) {
      return NextResponse.json(
        { error: "Token invalide ou expiré.", detail: error?.message },
        { status: 401 }
      );
    }

    const email = payload.email;
    const userDetails = await User.findOne({ email: email });
    const entreprise = await Entreprise.findOne({ admin: userDetails._id });

    if (!entreprise) {
      return NextResponse.json(
        { error: "Entreprise introuvable." },
        { status: 404 }
      );
    }

    // ✅ Read with_details from the query string
    const { searchParams } = new URL(req.url);
    const withDetails = searchParams.get("with_details")?.toLowerCase() === "true";

    const goals = withDetails
      ? await Goal.find({ entreprise: entreprise._id })
      : await Goal.find({ entreprise: entreprise._id }).select("name _id");

    return NextResponse.json({ success: true, goals }, { status: 200 });

  } catch (err) {
    console.error("Erreur serveur:", err);
    return NextResponse.json(
      { error: "Erreur interne du serveur." },
      { status: 500 }
    );
  }
}
