import PersonRoundedIcon from "@mui/icons-material/PersonRounded";
import { Phone } from "lucide-react";
import { Trash2Icon } from "lucide-react";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";

interface ClientType {
  client_id: string;
  _id: string;
  name?: string;
  phone: string;
}

const ClientCard = ({
  client,
  onRemove,
}: {
  client: ClientType;
  onRemove: () => Promise<void>;
}) => {
  return (
    <div className="w-full flex flex-col sm:grid grid-cols-12 gap-2 sm:gap-1 rounded-lg bg-sidebar border border-sidebar-border p-3 items-center text-voxa-neutral-900 dark:text-voxa-neutral-50">
      <span className="col-span-5 flex gap-1 justify-start items-center">
        <PersonRoundedIcon
          fontSize="small"
          className="dark:text-white flex-shrink-0"
        />
        <span className="text-xs sm:text-sm max-w-[140px] truncate">
          {client?.name ? client.name : "No name."}
        </span>
      </span>
      <div className="col-span-6 flex gap-1 items-center">
        <Phone className="dark:text-white  w-4 h-4 flex-shrink-0" />
        <p className="text-xs sm:text-sm text-nowrap">
          {phoneNumberFormat(client.phone)}
        </p>
      </div>
      <Trash2Icon
        onClick={onRemove}
        className="max-sm:absolute left-[42%] max-sm:w-full text-right h-4 cursor-pointer text-red-500 hover:text-red-600 active:text-red-500/80"
      />
    </div>
  );
};

export default ClientCard;
