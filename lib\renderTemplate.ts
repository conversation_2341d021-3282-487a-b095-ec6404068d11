import { Parser } from "expr-eval";

type Variables = Record<string, number | string>;

export function renderTemplate(template: string, variables: Variables): string {
  const parser = new Parser();

  return template.replace(/{{(.*?)}}/g, (_: string, rawExpr: string) => {
    // Replace $vars with actual values from the variables object
    const expr = rawExpr.replace(
      /\$([a-zA-Z_][a-zA-Z0-9_]*)/g,
      (_: string, varName: string) => {
        const value = variables[varName];
        if (value === undefined)
          throw new Error(`Variable "${varName}" is not defined`);
        return value.toString();
      }
    );

    try {
      const result = parser.evaluate(expr);
      return result.toString();
    } catch (e) {
      console.warn(`Failed to evaluate expression: "${expr}"`, e);
      return `{{${rawExpr}}}`; // Fallback: keep original if failed
    }
  });
}
