import { JSX, useState } from "react";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Eye, EyeOff } from "lucide-react";

interface InputProps {
  name?: string;
  label?: any;
  type?: string;
  placeholder?: string;
  required?: boolean;
  value?: string | number;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  readonly?: boolean;
  disabled?: boolean;
  className?: string;
  labelIcon?: JSX.Element;
  parentClassName?: string;
  accept?: string;
  isPassword?: boolean;
  pattern?: string;
  maxLength?: number;
  minLength?: number;
  inputMode?: "none" | "text" | "tel" | "email" | "url" | "numeric" | "decimal";
}

export default function CustomInput({ props }: { props?: InputProps }) {
  const [showPassword, setShowPassword] = useState(false);
  const isPasswordField = props?.isPassword && props?.type === "password";

  return (
    <div
      className={`relative grid w-full items-center gap-1.5 ${props?.parentClassName}`}
    >
      {props?.label && (
        <Label
          className="text-sm flex gap-2 items-center text-foreground"
          htmlFor={props?.name}
        >
          {props?.labelIcon}
          {props?.label}
          {props?.required && (
            <span className="text-red-500 -translate-x-1"> * </span>
          )}
        </Label>
      )}
      <div className="relative">
        <Input
          className={props?.className}
          placeholder={props?.placeholder}
          type={isPasswordField && showPassword ? "text" : props?.type}
          name={props?.name}
          required={props?.required}
          value={props?.value}
          onChange={props?.onChange}
          onBlur={props?.onBlur}
          onFocus={props?.onFocus}
          disabled={props?.disabled}
          readOnly={props?.readonly}
          accept={props?.accept}
          pattern={props?.pattern}
          maxLength={props?.maxLength}
          minLength={props?.minLength}
          inputMode={props?.inputMode}
        />
        {isPasswordField && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors"
          >
            {showPassword ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        )}
      </div>
    </div>
  );
}
