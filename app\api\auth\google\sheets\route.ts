import { NextResponse } from "next/server";

export const runtime = "nodejs";

export async function GET() {
  const params = new URLSearchParams({
    client_id: process.env.GOOGLE_SHEETS_CLIENT_ID!,
    redirect_uri: process.env.GOOGLE_SHEETS_REDIRECT_URI!,
    response_type: "code",
    scope: "https://www.googleapis.com/auth/spreadsheets",
    access_type: "offline",
    prompt: "consent",
    state: "sheets",
  });

  const url = `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
  return NextResponse.redirect(url);
}
