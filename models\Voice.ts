import mongoose from "mongoose";

const VoiceSchema = new mongoose.Schema({
  instructions: [
    {
      displayed_instructions_title: {
        type: String,
      },
      origin_instructions_title: {
        type: String,
      },
      instructions_text: {
        type: String,
      },
    },
  ],
  url: {
    type: String,
  },
  // voice_id: {
  //   type: String,
  // },
  displayed_ai_voice: {
    type: String,
  },
  origin_ai_voice: {
    type: String,
  },
  gender: {
    type: String,
  },
  available: {
    type: Boolean,
  },
  price: {
    type: String,
  },
  provider: {
    type: String,
  },
});

const Voice =
  mongoose.models.AI_voices ||
  mongoose.model("AI_voices", VoiceSchema, "AI_voices");

export default Voice;
