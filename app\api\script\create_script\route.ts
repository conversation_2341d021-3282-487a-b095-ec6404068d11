import dbConnect from "@/lib/mongodb";
import Entreprise from "@/models/Entreprise";
import { User } from "@/models/User";
import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { verifyUserToken } from "@/lib/cognito/Token";
import AssistantScript from "@/models/AssistantScript";

export async function POST(req: Request) {
  try {
    await dbConnect();

    const cookieStore = cookies();
    const token = (await cookieStore).get("plugin_id_token")?.value;

    console.log("Token from cookies:", token);

    if (!token) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    // ✅ Verify Cognito token
    const { valid, payload, error } = await verifyUserToken(token);

    if (!valid || !payload?.email) {
      return NextResponse.json(
        {
          error: "Token invalide ou expiré.",
          detail: error?.message || "Token non valide.",
        },
        { status: 401 }
      );
    }

    let body;
    try {
        body = await req.json();
    } catch (parseError) {
        console.error("Erreur de parsing de la requête:", parseError);
        return NextResponse.json(
        { error: "Requête mal formée. Veuillez envoyer un corps JSON valide." },
        { status: 400 }
        );
    }

    const {
        name,
        content
    } = body
    
    // ✅ Find user by email
    const user = await User.findOne({ email: payload.email });
    if (!user) {
        return NextResponse.json({ error: "Utilisateur introuvable." }, { status: 404 });
    }

    // ✅ Find entreprise by user ID
    const entreprise = await Entreprise.findOne({ admin: user._id });
    if (!entreprise) {
        return NextResponse.json({ error: "Entreprise introuvable." }, { status: 404 });
    }

    const scriptResponse = await AssistantScript.create({
        entreprise: entreprise._id,
        name,
        content,
    })

    if (!scriptResponse) {
      return NextResponse.json({ error: "Échec de la création du script." }, { status: 500 });
    }

    return NextResponse.json({ message: "Script créé avec succès" }, { status: 201 });

  } catch (err: any) {
    console.error("Erreur serveur:", err);
    return NextResponse.json({ error: "Erreur interne du serveur." }, { status: 500 });
  }
}
