import { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface WebhookEnabledToggleProps {
  enabled: boolean;
  onToggle: () => Promise<void>;
}

export default function WebhookEnabledToggle({
  enabled,
  onToggle,
}: WebhookEnabledToggleProps) {
  const [isTogglingEnabled, setIsTogglingEnabled] = useState(false);

  const handleEnabledToggle = async () => {
    if (!isTogglingEnabled) {
      setIsTogglingEnabled(true);
      await onToggle();
      setIsTogglingEnabled(false);
    }
  };

  return (
    <div className="w-full flex justify-between flex-wrap items-center gap-2 p-3 border border-voxa-neutral-200 dark:border-voxa-neutral-800 rounded-lg">
      <div className="flex items-center gap-2 flex-wrap">
        <Label htmlFor="webhook-enabled" className="font-medium text-base">
          Enable Webhooks
        </Label>
        <p className="text-sm text-voxa-neutral-400 dark:text-voxa-neutral-500">
          {enabled ? "Active" : "Inactive"}
        </p>
      </div>
      <div className="flex items-center ms-auto">
        <Switch
          id="webhook-enabled"
          checked={enabled || false}
          onCheckedChange={handleEnabledToggle}
          disabled={isTogglingEnabled}
          className="data-[state=unchecked]:bg-voxa-neutral-200 dark:data-[state=checked]:bg-voxa-teal-500 dark:data-[state=unchecked]:bg-voxa-neutral-800"
        />
      </div>
    </div>
  );
}
