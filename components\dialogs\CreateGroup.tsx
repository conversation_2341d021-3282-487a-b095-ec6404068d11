import {
  CreateGroupAndAddClients,
  getClientsNotInGroupBySearchTerm,
} from "@/actions/ClientsActions";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";
import { setImportContactsDialogOpen } from "@/redux/BusinessDashboard/subSlices/ClientsSlice";
import clsx from "clsx";
import { Trash2Icon } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "sonner";
import CustomInput from "../CustomFormItems/Input";
import { Label } from "../ui/label";

export function CreateGroupDialog({
  addGroupOpen,
  setAddGroupOpen,
}: {
  addGroupOpen: boolean;
  setAddGroupOpen: any;
}) {
  const dispatch = useDispatch();
  const [groupName, setGroupName] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [clients, setClients] = useState<any[]>([]);
  const [selectedClients, setSelectedClients] = useState<any[]>([]);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedDropdownOpen, setSelectedDropdownOpen] = useState(false);
  const clientDropdownRef = useRef<HTMLDivElement>(null);
  const selectedDropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const delayDebounceFn = setTimeout(async () => {
      const response = await getClientsNotInGroupBySearchTerm("", searchTerm);
      if (response.success && response.data) {
        setClients(response.data);
      } else {
        toast.error("Failed to fetch clients:", response.error);
      }
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        clientDropdownRef.current &&
        !clientDropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
      if (
        selectedDropdownRef.current &&
        !selectedDropdownRef.current.contains(event.target as Node)
      ) {
        setSelectedDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleToggleClient = (client: any) => {
    const exists = selectedClients.find((c) => c._id === client._id);
    if (exists) {
      setSelectedClients((prev) => prev.filter((c) => c._id !== client._id));
    } else {
      setSelectedClients((prev) => [...prev, client]);
    }
  };

  const handleCreateGroup = async () => {
    try {
      const clientIDs = selectedClients.map((client) => client._id);
      if (!groupName) {
        toast.error("Group name is required");
        return;
      }
      if (clientIDs.length === 0) {
        toast.error("At least one client is required");
        return;
      }
      const result = await CreateGroupAndAddClients(groupName, clientIDs);

      if (result.success) {
        toast.success("Group created successfully");
        setAddGroupOpen(false);
        setGroupName("");
        setSelectedClients([]);
        setSearchTerm("");
        window.location.reload();
      } else {
        console.error("Error creating group:", result.error);
      }
    } catch (err: any) {
      console.error(err);
      toast.error("An error occurred while creating the group");
    }
  };

  const handleOpenImportContacts = () => {
    setAddGroupOpen(false); // Close CreateGroup dialog
    dispatch(setImportContactsDialogOpen(true)); // Open ImportContacts dialog
  };

  return (
    <Dialog
      open={addGroupOpen}
      onOpenChange={(open) => {
        setAddGroupOpen(open);
        if (!open) {
          setGroupName("");
          setSelectedClients([]);
          setSearchTerm("");
          setDropdownOpen(false);
          setSelectedDropdownOpen(false);
        }
      }}
    >
      <DialogTrigger asChild>
        <Button className="p-2 px-3 w-fit h-9 rounded-md bg-voxa-neutral-50 dark:bg-voxa-neutral-800 text-voxa-neutral-500 dark:text-voxa-neutral-50 hover:bg-voxa-neutral-100 dark:hover:bg-voxa-neutral-600">
          Create Group
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>Create new Group</DialogTitle>
        </DialogHeader>

        <CustomInput
          props={{
            value: groupName,
            onChange: (e) => setGroupName(e.target.value),
            placeholder: "Group Name",
            label: "Group Name",
            type: "text",
            required: true,
            name: "groupName",
          }}
        />

        {/* Select Clients Dropdown */}
        <div className="relative" ref={clientDropdownRef}>
          <Label className="text-black dark:text-white text-sm">
            Add Clients to Group
          </Label>
          <div
            className="border border-sidebar-border py-1.5 px-2 h-10 rounded-md bg-sidebar dark:bg-voxa-neutral-950 text-muted-foreground/60 mt-1.5 cursor-pointer text-base"
            onClick={() => setDropdownOpen((prev) => !prev)}
          >
            {selectedClients.length === 0
              ? "Select clients..."
              : `Clients selected (${selectedClients.length})`}
          </div>

          {dropdownOpen && (
            <div className="absolute z-50 mt-1 max-h-[250px] w-full overflow-y-auto border rounded-lg text-forground bg-white dark:bg-voxa-neutral-950 shadow-xl">
              <div className="p-1.5">
                <CustomInput
                  props={{
                    value: searchTerm,
                    onChange: (e) => setSearchTerm(e.target.value),
                    placeholder: "Search client...",
                    parentClassName: "w-full",
                    type: "text",
                    required: false,
                    name: "search",
                    className: "rounded",
                  }}
                />
              </div>
              {clients.length > 0 ? (
                clients.map((client) => {
                  const isSelected = selectedClients.some(
                    (c) => c._id === client._id
                  );
                  return (
                    <div
                      key={client._id}
                      onClick={() => handleToggleClient(client)}
                      className={clsx(
                        "px-4 py-2 mx-1 mb-1 rounded text-sm font-medium hover:bg-tail-700/10 text-black/80 dark:text-white flex justify-between items-center cursor-pointer",
                        isSelected
                          ? "bg-voxa-teal-600/40 hover:bg-voxa-teal-600/50"
                          : "text-black/60 hover:bg-voxa-teal-600/20 dark:text-voxa-neutral-200"
                      )}
                    >
                      <span>{client.name}</span>
                      <span className="text-xs text-black/60 dark:text-voxa-neutral-200">
                        {phoneNumberFormat(client.phone, client.country)}
                      </span>
                    </div>
                  );
                })
              ) : (
                <p className="text-center text-gray-400 p-2 text-sm">
                  No clients found
                </p>
              )}
            </div>
          )}
        </div>

        {/* Selected Clients Dropdown */}
        {selectedClients.length > 0 && (
          <div className="relative" ref={selectedDropdownRef}>
            <Label className="text-black dark:text-white text-sm">
              Selected Clients
            </Label>
            <div
              className="border border-sidebar-border py-1.5 px-2 h-10 rounded-md bg-sidebar dark:bg-voxa-neutral-950 text-muted-foreground/60 mt-2 cursor-pointer text-base"
              onClick={() => setSelectedDropdownOpen((prev) => !prev)}
            >
              {`View selected clients (${selectedClients.length})`}
            </div>

            {selectedDropdownOpen && (
              <div className="absolute z-50 mt-1 max-h-[250px] w-full overflow-y-auto border rounded-lg text-forground bg-white dark:bg-voxa-neutral-950 shadow-xl">
                {selectedClients.map((client) => (
                  <div
                    key={client._id}
                    onClick={() => handleToggleClient(client)}
                    className="m-1 rounded px-4 py-2 text-sm flex justify-between items-center font-medium cursor-pointer hover:bg-red-700/30 text-black/60 dark:text-voxa-neutral-50"
                  >
                    <div className="flex items-center gap-2">
                      <Trash2Icon className="w-4 h-4" />
                      <span>{client.name}</span>
                    </div>

                    <span className="text-xs text-black/80 dark:text-voxa-neutral-200">
                      {phoneNumberFormat(client.phone)}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        <DialogFooter className="mt-8 flex">
          <div className="flex flex-col gap-4 w-full">
            <div className="flex gap-2 w-full">
              <Button
                className="w-full text-voxa-neutral-50 bg-voxa-teal-600 hover:bg-voxa-teal-500"
                onClick={handleCreateGroup}
              >
                Create Group
              </Button>
              <Button
                className="w-full text-nowrap text-sm bg-voxa-neutral-500 dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-800 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950 transition-all duration-150 text-white pl-2 pr-4 py-2 rounded-md font-medium flex justify-center items-center gap-1"
                onClick={() => setAddGroupOpen(false)}
              >
                Cancel
              </Button>
            </div>
            <div className="h-px bg-slate-400"></div>
            <Button
              className="w-full text-voxa-teal-500 border-voxa-teal-500 hover:text-voxa-teal-400 hover:border-voxa-teal-400 font-semibold transition-colors rounded-md"
              onClick={handleOpenImportContacts}
              variant={"secondary"}
            >
              Create from CSV
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
