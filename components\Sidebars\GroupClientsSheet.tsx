import {
  addClientsToGroup,
  getClientsNotInGroupBySearchTerm,
} from "@/actions/ClientsActions";
import { GetGroupClients } from "@/actions/EPGroupsActions";
import {
  Sheet,
  SheetContent,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Label } from "@/components/ui/label";
import { removeClientFromGroup } from "@/redux/BusinessDashboard/subSlices/ClientsSlice";
import { AppDispatch } from "@/redux/store";
import { LogOutIcon, Trash2Icon } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { useDispatch } from "react-redux";
import { toast } from "sonner";
import clsx from "clsx";
import CustomButton from "../CustomFormItems/Button";
import CircularLoaderSmall from "../Loaders/CircularLoaderSmall";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";

export function ShowGroupClients({
  groupID,
  name,
}: {
  groupID: string;
  name: string;
}) {
  const dispatch = useDispatch<AppDispatch>();

  const [clients, setClients] = useState<any[]>();
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedDropdownOpen, setSelectedDropdownOpen] = useState(false);
  const [clientsNotInGroup, setClientsNotInGroup] = useState<any[]>([]);
  const [selectedClients, setSelectedClients] = useState<any[]>([]);

  const clientDropdownRef = useRef<HTMLDivElement>(null);
  const selectedDropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const delayDebounceFn = setTimeout(async () => {
      const response = await getClientsNotInGroupBySearchTerm(
        groupID,
        searchTerm
      );
      if (response.success && response.data) {
        setClientsNotInGroup(response.data);
      } else {
        toast.error("Failed to fetch clients:", response.error);
      }
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm, groupID]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        clientDropdownRef.current &&
        !clientDropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
      if (
        selectedDropdownRef.current &&
        !selectedDropdownRef.current.contains(event.target as Node)
      ) {
        setSelectedDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const getGroupClients = async () => {
    try {
      setLoading(true);
      const response = await GetGroupClients(groupID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      setClients(response.clients);
    } catch (err) {
      console.error(err);
      toast.error("Error fetching group clients");
    } finally {
      setLoading(false);
    }
  };

  const handleClickRemove = async (clientID: string) => {
    try {
      await dispatch(removeClientFromGroup({ groupID, clientID })).unwrap();
      setClients((prev) => prev?.filter((client) => client._id !== clientID));
    } catch (err: any) {
      toast.error(err.message);
    }
  };

  const handleToggleClient = (client: any) => {
    const exists = selectedClients.find((c) => c._id === client._id);
    if (exists) {
      setSelectedClients((prev) => prev.filter((c) => c._id !== client._id));
    } else {
      setSelectedClients((prev) => [...prev, client]);
    }
  };

  const handleAddClients = async () => {
    try {
      const clientIDs = selectedClients.map((client) => client._id);
      if (clientIDs.length === 0) {
        toast.error("At least one client is required");
        return;
      }
      const result = await addClientsToGroup(groupID, clientIDs);

      if (result.success) {
        toast.success("Clients added to group successfully");
        setClients((prev) => [...(prev || []), ...selectedClients]);
        setSelectedClients([]);
        setSearchTerm("");
        const response = await getClientsNotInGroupBySearchTerm(
          groupID,
          searchTerm
        );
        if (response.success && response.data) {
          setClientsNotInGroup(response.data);
        } else {
          toast.error("Failed to fetch clients:", response.error);
        }
      } else {
        toast.error("Error creating group:", result.error);
      }
    } catch (err: any) {
      toast.error(err.message);
    }
  };

  return (
    <Sheet>
      <SheetTrigger className="mt-auto flex w-full">
        <div
          onClick={() => getGroupClients()}
          className="bg-voxa-teal-600 hover:bg-voxa-teal-500 w-full text-xs px-4 py-2 rounded-lg transition-all duration-200 text-white"
        >
          View
        </div>
      </SheetTrigger>
      <SheetContent className="flex flex-col justify-between h-screen overflow-y-auto w-[600px]">
        <div className="flex flex-col gap-2">
          <SheetTitle className="text-black/60 dark:text-voxa-neutral-50 text-center text-xl">
            Group Clients
          </SheetTitle>
          <p className="mt-6 mb-4 font-semibold dark:text-voxa-neutral-50 w-full text-center truncate">
            {name}
          </p>
          {clients && "Here's a list of all group clients:"}
          {clients && clients?.length > 0 ? (
            <div className="overflow-y-auto max-h-[290px] mt-4 px-4">
              {clients.map((client, index) => (
                <div
                  key={index}
                  className="p-3 w-full rounded-xl border border-voxa-neutral-200 bg-voxa-neutral-50 dark:bg-voxa-neutral-900 hover:border-voxa-neutral-800 dark:hover:border-voxa-neutral-200 dark:border-voxa-neutral-900 hover:scale-y-104 hover:scale-x-102 flex items-center justify-between my-2"
                >
                  <div>
                    <p className="text-voxa-neutral-800 dark:text-voxa-neutral-50 text-xs font-semibold overflow-hidden">
                      {client.name}
                    </p>
                    <p className="text-xs font-light">{client.email}</p>
                  </div>
                  <div className="flex gap-2 items-center">
                    <p className="text-sm font-medium">
                      {phoneNumberFormat(client.phone, client.country)}
                    </p>
                    <button
                      onClick={() => handleClickRemove(client._id)}
                      className="text-red-500 hover:text-red-600 active:text-red-500/80 transition-all duration-150 text-xs font-medium mt-1 -translate-y-px"
                    >
                      <LogOutIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex justify-center items-center h-[290px]">
              {loading ? <CircularLoaderSmall /> : "No clients found"}
            </div>
          )}
          {/* Select Clients Dropdown */}
          <div className="relative mt-4" ref={clientDropdownRef}>
            <div className="flex flex-col gap-2">
              <Label className="dark:text-white text-md text-center">
                Add Clients to Group
              </Label>
              <span className="text-center text-sm text-orange-600 dark:text-orange-500 italic">
                You can select multiple clients at once!
              </span>
            </div>
            <div
              className="border-2 border-input py-1.5 px-2 h-10 mt-1 flex w-full rounded-md focus:border-input text-base shadow-sm text-foreground/80 bg-voxa-neutral-50 dark:bg-voxa-neutral-900 items-center"
              onClick={() => setDropdownOpen((prev) => !prev)}
            >
              {selectedClients.length === 0
                ? "Select clients..."
                : `Clients selected (${selectedClients.length})`}
            </div>

            {dropdownOpen && (
              <div className="absolute z-50 mt-1 max-h-[200px] w-full overflow-y-auto border rounded-lg bg-background shadow-lg  dark:bg-voxa-neutral-950">
                <div className="p-2">
                  <input
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search client..."
                    className="w-full border px-2 py-1 rounded text-sm text-foreground/80 bg-voxa-neutral-50 dark:bg-voxa-neutral-900"
                  />
                </div>
                {clientsNotInGroup.length > 0 ? (
                  clientsNotInGroup.map((client) => {
                    const isSelected = selectedClients.some(
                      (c) => c._id === client._id
                    );
                    return (
                      <div
                        key={client._id}
                        onClick={() => handleToggleClient(client)}
                        className={clsx(
                          "mx-2 mb-1 px-4 py-2 text-sm rounded-md font-medium flex justify-between items-center cursor-pointer transition-colors",
                          isSelected
                            ? "bg-voxa-teal-600/40 hover:bg-voxa-teal-600/50"
                            : "text-black/60 hover:bg-voxa-teal-600/20 dark:text-voxa-neutral-200"
                        )}
                      >
                        <span>{client.name}</span>
                        <span className="text-xs dark:text-gray-300">
                          {phoneNumberFormat(client.phone, client.country)}
                        </span>
                      </div>
                    );
                  })
                ) : (
                  <p className="text-center dark:text-gray-400 p-2 text-sm">
                    No clients found
                  </p>
                )}
              </div>
            )}
          </div>
          {/* Selected Clients Dropdown */}
          {selectedClients.length > 0 && (
            <div className="relative mt-4" ref={selectedDropdownRef}>
              <Label className="dark:text-white text-md flex justify-center">
                Selected Clients
              </Label>
              <div
                className="border-2 border-input py-1.5 px-2 h-10 mt-2 flex w-full rounded-md focus:border focus:border-input text-base shadow-sm text-foreground/80 bg-voxa-neutral-50 dark:bg-voxa-neutral-900 items-center"
                onClick={() => setSelectedDropdownOpen((prev) => !prev)}
              >
                {`View selected clients (${selectedClients.length})`}
              </div>

              {selectedDropdownOpen && (
                <div className="absolute z-50 mt-1 max-h-[200px] w-full overflow-y-auto border rounded-lg bg-background  dark:bg-voxa-neutral-950 shadow-xl">
                  {selectedClients.map((client) => (
                    <div
                      key={client._id}
                      onClick={() => handleToggleClient(client)}
                      className="my-1 mx-2 rounded-md px-4 py-2 text-sm flex justify-between items-center font-medium cursor-pointer group hover:bg-red-100 text-red-500 hover:text-red-700"
                    >
                      <div className="flex items-center gap-2">
                        <Trash2Icon className="w-4 h-4" />
                        <span>{client.name}</span>
                      </div>

                      <span className="text-xs text-voxa-neutral-950 dark:text-gray-200 group-hover:text-gray-800">
                        {phoneNumberFormat(client.phone, client.country)}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
        <CustomButton
          props={{
            value: "Add Clients",
            className:
              "mt-10 bg-voxa-teal-600 hover:bg-voxa-teal-500 text-voxa-neutral-50",
            onClick: handleAddClients,
            loading: loading || selectedClients.length === 0,
          }}
        />
      </SheetContent>
    </Sheet>
  );
}
