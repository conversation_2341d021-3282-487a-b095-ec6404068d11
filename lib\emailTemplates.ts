/*
 * Generates a modern, responsive HTML email alert when user balance <= threshold.
 * Features: Clean design, brand logo, progress indicator, mobile-responsive layout
 */
export function generateBalanceAlertEmailBody(
  userName: string,
  currentBalance: number,
  balanceThreshold: number,
  currencySymbol: string = "€",
  appUrl: string = "https://app.echoparrot.com/businessDash/settings/billing",
  supportUrl: string = "https://echoparrot.com/support"
): string {
  const formattedCurrent = currentBalance.toFixed(2);
  const formattedThreshold = balanceThreshold.toFixed(2);
  const year = new Date().getFullYear();

  // Use hosted PNG logo instead
  const logoWhiteImgUrl =
    "https://app.echoparrot.com/images/Icons/parrot-white.png";
  const logoBlackImgUrl =
    "https://app.echoparrot.com/images/Icons/parrot-black.png";

  return `
  <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Balance Alert - EchoParrot</title>
    <style>
      * { margin: 0; padding: 0; box-sizing: border-box; }
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        line-height: 1.6;
        color: #333333;
        background-color: #f5f7fa;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }
      .container {
        max-width: 600px;
        width: 100%;
        margin: 40px auto;
        background: #ffffff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        border: 1px solid #e2e8f0;
      }
      .header {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        padding: 32px 24px;
        text-align: center;
        color: #ffffff;
      }
      .logo img, .footer-logo img {
        display: block;
        margin: 0 auto;
        max-width: 48px;
        height: auto;
      }
      .alert-badge {
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 8px 16px;
        border-radius: 20px;
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 600;
        letter-spacing: 0.5px;
      }
      .header h1 {
        font-size: 28px;
        font-weight: 700;
        letter-spacing: -0.5px;
        margin: 0;
      }
      .content {
        padding: 40px 32px;
        color: #374151;
      }
      .greeting {
        font-size: 18px;
        color: #111827;
        margin-bottom: 24px;
      }
      .alert-message {
        background: #fff7ed;
        border-left: 4px solid #f59e0b;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 32px;
        font-size: 16px;
        line-height: 1.6;
      }
      .balance-card {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 32px;
      }
      .balance-details {
        display: grid;
        gap: 16px;
      }
      .balance-row {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        padding: 16px 0;
      }
      .balance-row:first-of-type {
        border-bottom: 1px solid #e2e8f0;
      }
      .balance-row:last-child {
        border-bottom: none;
      }
      .balance-label {
        font-size: 15px;
        color: #64748b;
        font-weight: 500;
        line-height: 1.4;
      }
      .balance-value {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
        line-height: 1.4;
        margin-left: 16px;
      }
      .balance-current {
        color: #f59e0b;
      }
      .cta-section {
        text-align: center;
        margin-bottom: 32px;
      }
      .cta-button {
        background: #ef4444;
        color: #ffffff !important;
        text-decoration: none;
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;
        display: inline-block;
        box-shadow: 0 4px 12px rgba(239,68,68,0.3);
        transition: background 0.3s ease, box-shadow 0.3s ease, filter 0.3s ease;
      }
      .cta-button:hover {
        background: #dc2626;
        box-shadow: 0 6px 18px rgba(239,68,68,0.35);
        filter: brightness(1.08);
        text-decoration: none;
        transition: all 0.3s ease;
      }
      .support-section {
        border-top: 1px solid #e5e7eb;
        padding-top: 24px;
        text-align: center;
        font-size: 14px;
        color: #6b7280;
      }
      .support-section a {
        color: #ef4444;
        text-decoration: none;
      }
      .footer {
        background: #f8fafc;
        border-top: 1px solid #e5e7eb;
        padding: 24px;
        text-align: center;
      }
      .footer-logo {
        margin-bottom: 16px;
      }
      .footer-brand {
        color: #374151;
        font-size: 14px;
        font-weight: 600;
        margin-top: 8px;
      }
      .footer-copyright {
        font-size: 12px;
        color: #9ca3af;
        margin: 0;
      }
      .footer-unsubscribe {
        font-size: 11px;
        color: #9ca3af;
        margin: 8px 0 0;
      }
      .footer-unsubscribe a {
        color: #6b7280;
      }
      
      @media screen and (max-width: 600px) {
        .container { 
          margin: 20px; 
          border-radius: 8px;
        }
        .content { 
          padding: 24px 20px;
        }
        .header { 
          padding: 24px 20px;
        }
        .header h1 {
          font-size: 24px;
        }
        .cta-button { 
          display: block;
          width: 100%;
          box-sizing: border-box;
        }
        .balance-row {
          flex-direction: column;
          text-align: center;
          gap: 8px;
          align-items: center;
        }
      }
    </style>
  </head>
  <body>
    <!-- Preheader -->
    <div style="display:none;font-size:1px;color:#f5f7fa;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;">
      Your EchoParrot balance has reached your alert threshold at ${formattedCurrent}${currencySymbol}
    </div>

    <div class="container">
      <!-- Header -->
      <div class="header">
        <div class="logo">
          <img src="${logoWhiteImgUrl}" alt="EchoParrot Logo" width="48" height="46" style="display:block;margin:0 auto;">
        </div>
        <div class="alert-badge">
          ⚠️ BALANCE ALERT
        </div>
        <h1>Balance Threshold Reached</h1>
      </div>

      <!-- Main Content -->
      <div class="content">
        <!-- Greeting -->
        <p class="greeting">
          Hello <strong>${userName}</strong>,
        </p>

        <!-- Alert Message -->
        <div class="alert-message">
          Your EchoParrot account balance has reached your alert threshold. Your current balance is 
          <strong style="color:#f59e0b;">${formattedCurrent}${currencySymbol}</strong>, 
          and your configured alert threshold is <strong>${formattedThreshold}${currencySymbol}</strong>.
        </div>

        <!-- Balance Overview Card -->
        <div class="balance-card">
          <!-- Balance Details -->
          <div class="balance-details">
            <div class="balance-row">
              <span class="balance-label">Current Balance</span>
              <span class="balance-value balance-current">
                ${formattedCurrent}${currencySymbol}
              </span>
            </div>
            <div class="balance-row">
              <span class="balance-label">Alert Threshold</span>
              <span class="balance-value">
                ${formattedThreshold}${currencySymbol}
              </span>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="cta-section">
          <a href="${appUrl}" class="cta-button">
            💳 Add Funds Now
          </a>
        </div>

        <!-- Support Information -->
        <div class="support-section">
          Need assistance? Our support team is here to help.<br>
          📧 <a href="mailto:<EMAIL>"><EMAIL></a> • 
          💬 <a href="${supportUrl}">Support Center</a>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <div class="footer-logo">
          <img src="${logoBlackImgUrl}" alt="EchoParrot Logo" width="32" height="30" style="display:block;margin:0 auto;">
          <div class="footer-brand">EchoParrot</div>
        </div>
        
        <p class="footer-copyright">
          &copy; ${year} EchoParrot. All rights reserved.
        </p>
        
        <p class="footer-unsubscribe">
          You're receiving this because it's a critical account alert. 
          <a href="#">Manage email preferences</a>
        </p>
      </div>
    </div>
  </body>
  </html>
  `;
}
