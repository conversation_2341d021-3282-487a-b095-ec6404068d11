import React, { useEffect } from "react";
import CustomInput from "@/components/CustomFormItems/Input";
import { CountriesSelect } from "@/components/dropdowns/CountriesSelect";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { CountryCode } from "libphonenumber-js";
import { useTranslation } from "react-i18next";
import {
  setGoalContext,
  setGoalName,
  setCountry,
  checkPhoneHasIncomingGoals,
  // setCanTransferIncomingCalls,
  // setIncomingSelected,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";
// import { Switch } from "../ui/switch";
// import { Label } from "../ui/label";
// import { UpdateIncomingGoalTransfer } from "@/actions/NewGoalActions";
// import { toast } from "sonner";

interface GoalDetailsProps {
  goalID: string;
  templateType: string;
  phoneID: string;
}

export const GoalDetails: React.FC<GoalDetailsProps> = ({
  goalID,
  phoneID,
  // templateType,
}) => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const {
    goalName,
    goalContext,
    country,
    // canTransferIncomingCalls,
    // goalType,
    // canSelectIncomingCalls,
    // incomingSelected,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );
  useEffect(() => {
    if (goalID === "create") {
      dispatch(checkPhoneHasIncomingGoals({ phoneID }));
    }
  }, [goalID, phoneID, dispatch]);

  // const handleDebugModeToggle = async (status: boolean) => {
  //   try {
  //     const response = await UpdateIncomingGoalTransfer(goalID, status);
  //     if (!response.success) return toast.error(response.error);
  //     toast.success(
  //       `Transfer ${status ? "activated" : "deactivated"} successfully!`
  //     );
  //   } catch (err: any) {
  //     toast.error(err.message);
  //   }
  // };

  return (
    <div className="space-y-2">
      <span className="md:col-span-2 text-xl font-semibold">
        {t("createEditGoal.details.section")}
        {"  "}
      </span>
      {/* {goalID === "create"
        ? templateType === "DEMARCHAGE" &&
          (canSelectIncomingCalls ? (
            <>
              <div className="flex items-center space-x-2">
                <Switch
                  id="GoalType"
                  onCheckedChange={(checked) =>
                    dispatch(setIncomingSelected(checked))
                  }
                />
                <Label htmlFor="GoalType">
                  {incomingSelected
                    ? t("createEditGoal.details.incoming")
                    : t("createEditGoal.details.outgoing")}
                </Label>
              </div>
              {incomingSelected && (
                <span className="text-sm text-orange-500">
                  {t("createEditGoal.details.incomingInfo")}
                </span>
              )}
            </>
          ) : (
            <span className="text-sm text-red-500">
              {t("createEditGoal.details.outgoingOnlyInfo")}
            </span>
          ))
        : goalType === "INCOMING" && (
            <div className="flex text-nowrap items-center gap-2">
              <Switch
                id="debug"
                checked={canTransferIncomingCalls}
                onCheckedChange={(checked) => {
                  handleDebugModeToggle(checked);
                  setCanTransferIncomingCalls(!checked);
                }}
              />
              <Label htmlFor="script">
                {canTransferIncomingCalls ? "Transfer ON" : "Transfer OFF"}
              </Label>
            </div>
          )} */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <CustomInput
          props={{
            name: "goalName",
            label: t("createEditGoal.details.goalName"),
            required: true,
            type: "text",
            placeholder: t("createEditGoal.details.goalNamePlaceholder"),
            value: goalName,
            onChange: (e) => dispatch(setGoalName(e.target.value)),
            className: "w-full",
          }}
        />
        <CustomInput
          props={{
            name: "goalContext",
            label: t("createEditGoal.details.goalContext"),
            required: false,
            type: "text",
            placeholder: t("createEditGoal.details.goalContextPlaceholder"),
            value: goalContext,
            onChange: (e) => dispatch(setGoalContext(e.target.value)),
            className: "w-full",
          }}
        />
        <CountriesSelect
          required
          label={t("createEditGoal.details.targetCountry")}
          country={country}
          selectCountry={(value: CountryCode) => dispatch(setCountry(value))}
        />
      </div>
    </div>
  );
};
