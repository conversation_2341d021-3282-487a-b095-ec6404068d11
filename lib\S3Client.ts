import { S3Client } from "@aws-sdk/client-s3";

export const BusinessFilesS3 = new S3Client({
    region: process.env.AWS_BUCKET_REGION!,
    credentials: {
        accessKeyId: process.env.AWS_BUCKET_ACCESS_KEY!,
        secretAccessKey: process.env.AWS_BUCKET_SECRET!
    }
})

export const AudioS3 = new S3Client({
    region: process.env.AWS_BUCKET_REGION!,
    credentials: {
        accessKeyId: process.env.AWS_AUDIO_ACCESS_KEY!,
        secretAccessKey: process.env.AWS_AUDIO_SECRET_KEY!
    }
})

export const TwilioLogsS3 = new S3Client({
    region: process.env.TWILIO_LOGS_BUCKET_REGION!,
    credentials: {
        accessKeyId: process.env.TWILIO_LOGS_BUCKET_ACCESS_KEY!,
        secretAccessKey: process.env.TWILIO_LOGS_BUCKET_SECRET!
    }
})
