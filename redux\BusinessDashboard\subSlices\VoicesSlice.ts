import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { GetVoices } from "@/actions/VoiceActions";
import { toast } from "sonner";

interface VoiceInstruction {
  displayed_instructions_title: string;
  origin_instructions_title: string;
  instructions_text: string;
}

export interface Voice {
  _id: string;
  displayed_ai_voice: string;
  origin_ai_voice: string;
  gender: string;
  language: string;
  url: string;
  price: string;
  provider: string;
  description?: string;
  cloning_price?: string;
  available?: boolean;
  instructions: VoiceInstruction[];
}

interface VoicesState {
  voices: Voice[];
  loading: boolean;
  error: string;
  selectedVoice: Voice | null;
  selectedInstruction: VoiceInstruction | null;
}

const initialState: VoicesState = {
  voices: [],
  loading: false,
  error: "",
  selectedVoice: null,
  selectedInstruction: null,
};

export const fetchVoices = createAsyncThunk(
  "businessDashboardVoices/fetchVoices",
  async (_, { dispatch }) => {
    try {
      dispatch(setLoading(true));
      const response = await GetVoices();

      if (!response.success) {
        dispatch(setError(response.error || "Failed to fetch voices"));
        return;
      }

      dispatch(setVoices(response.voices || []));
    } catch (err: any) {
      dispatch(
        setError(err.message || "An error occurred while fetching voices")
      );
    } finally {
      dispatch(setLoading(false));
    }
  }
);

export const selectVoice = createAsyncThunk(
  "businessDashboardVoices/selectVoice",
  async (voiceId: string, { dispatch, getState }) => {
    try {
      const state = getState() as any;
      const { voices } = state.businessDashboard.businessDashboardVoices;

      const voice = voices.find((v: Voice) => v._id === voiceId);
      if (!voice) {
        throw new Error("Voice not found");
      }

      dispatch(setSelectedVoice(voice));
      return voice;
    } catch (err: any) {
      toast.error(err.message || "Failed to select voice");
    }
  }
);

export const selectInstruction = createAsyncThunk(
  "businessDashboardVoices/selectInstruction",
  async (
    {
      voiceId,
      instructionTitle,
    }: { voiceId: string; instructionTitle: string },
    { dispatch, getState }
  ) => {
    try {
      const state = getState() as any;
      const { voices } = state.businessDashboard.businessDashboardVoices;

      const voice = voices.find((v: Voice) => v._id === voiceId);
      if (!voice) {
        throw new Error("Voice not found");
      }

      const instruction = voice.instructions.find(
        (i: VoiceInstruction) =>
          i.origin_instructions_title === instructionTitle
      );

      if (!instruction) {
        throw new Error("Instruction not found");
      }

      dispatch(setSelectedInstruction(instruction));
      return instruction;
    } catch (err: any) {
      toast.error(err.message || "Failed to select instruction");
    }
  }
);

const voicesSlice = createSlice({
  name: "businessDashboardVoices",
  initialState,
  reducers: {
    setVoices(state, action: PayloadAction<Voice[]>) {
      state.voices = action.payload;
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setError(state, action: PayloadAction<string>) {
      state.error = action.payload;
    },
    setSelectedVoice(state, action: PayloadAction<Voice | null>) {
      state.selectedVoice = action.payload;
    },
    setSelectedInstruction(
      state,
      action: PayloadAction<VoiceInstruction | null>
    ) {
      state.selectedInstruction = action.payload;
    },
    clearSelections(state) {
      state.selectedVoice = null;
      state.selectedInstruction = null;
    },
  },
});

export const {
  setVoices,
  setLoading,
  setError,
  setSelectedVoice,
  setSelectedInstruction,
  clearSelections,
} = voicesSlice.actions;

export default voicesSlice.reducer;
