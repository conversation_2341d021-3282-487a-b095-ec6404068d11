"use client";

import React from "react";
import * as Flags from "country-flag-icons/react/3x2";
import { TimerIcon } from "lucide-react";
import clsx from "clsx";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";
import { AssistantWithDurations } from "@/actions/AssistantActions";
import { CountryCode } from "libphonenumber-js";
import { formatDuration } from "@/lib/durations";
import { useTranslation } from "react-i18next";

interface PhoneCardProps {
  phone: AssistantWithDurations["numbers"][number];
}

const PhoneCard: React.FC<PhoneCardProps> = ({ phone }) => {
  const { t } = useTranslation("assistants");
  const FlagComponent = Flags[phone.country as keyof typeof Flags];

  return (
    <div className="lg:col-span-2 grid grid-cols-2 lg:flex border-2 border-sidebar-foreground/15 shadow-sm sm:justify-between gap-1 bg-white/90 dark:bg-voxa-neutral-900 rounded-md items-center justify-items-center place-items-center p-2">
      <div className="max-md:col-span-2 flex gap-1 items-center">
        <div className="w-5 h-4">
          {FlagComponent ? (
            <FlagComponent className="w-5 h-4" />
          ) : (
            <span className="w-5 h-4">🌍</span>
          )}
        </div>
        <p className="font-medium text-sm text-nowrap">
          {phoneNumberFormat(phone.number, phone.country as CountryCode)}
        </p>
      </div>

      <div
        className={clsx(
          "text-[11px] font-semibold max-md:col-span-2 lg:text-nowrap"
        )}
      >
        <span className="text-purple-500 dark:text-purple-400">
          {t("phone.calls")}
        </span>
        {!phone.is_sms && !phone.is_whatsapp && (
          <span className="text-purple-500 dark:text-purple-400">
            {" "}
            {t("phone.only")}
          </span>
        )}

        {phone.is_sms && " - "}
        {phone.is_sms && (
          <span className="text-blue-500">{t("phone.sms")}</span>
        )}

        {phone.is_whatsapp && " - "}
        {phone.is_whatsapp && (
          <span className="text-green-500">{t("phone.whatsapp")}</span>
        )}
      </div>

      <div className="flex gap-1 text-xs">
        <TimerIcon className="w-4 h-4" />
        {formatDuration(phone.time)}
      </div>

      <div
        className={`text-xs ${
          phone.status === "ACTIVE" ? "text-teal-500" : "text-orange-400"
        }`}
      >
        {t(`phone.status.${phone.status.toLowerCase()}`)}
      </div>
    </div>
  );
};

export default PhoneCard;
