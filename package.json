{"name": "ai-caller-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint --format stylish"}, "eslintConfig": {"rules": {"no-var": "off", "@typescript-eslint/no-explicit-any": "off"}}, "dependencies": {"@amcharts/amcharts5": "^5.13.3", "@amcharts/amcharts5-geodata": "^5.1.4", "@aws-sdk/client-cognito-identity-provider": "^3.817.0", "@aws-sdk/client-s3": "^3.848.0", "@aws-sdk/s3-request-presigner": "^3.750.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.3.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.6.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@tabler/icons-react": "^3.31.0", "@types/dagre": "^0.7.52", "@uiw/react-json-view": "^2.0.0-alpha.32", "ajv": "^8.11.0", "apexcharts": "^4.7.0", "aws-jwt-verify": "^5.1.0", "axios": "^1.7.9", "bcryptjs": "^3.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookie": "^1.0.2", "country-flag-icons": "^1.5.16", "csv-parse": "^5.6.0", "dagre": "^0.8.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "expr-eval": "^2.0.2", "get-blob-duration": "^1.1.2", "google-auth-library": "^10.1.0", "heroicons": "^2.2.0", "html-to-image": "^1.11.13", "html2pdf.js": "^0.10.3", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.5", "i18next-http-backend": "^3.0.2", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.8", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "luxon": "^3.5.0", "mongoose": "^8.10.0", "motion": "^12.5.0", "next": "^15.3.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "openai": "^4.97.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-country-region-selector": "^4.0.2", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "reactflow": "^11.11.4", "recharts": "^2.15.1", "redoc": "^2.5.0", "socket.io-client": "^4.8.1", "sonner": "^1.7.4", "stripe": "^18.3.0", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "vaul": "^1.1.2", "wavesurfer.js": "^7.9.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.4.3", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.8", "@types/luxon": "^3.4.2", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}