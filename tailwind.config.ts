import type { Config } from "tailwindcss";
import { PluginAPI } from "tailwindcss/types/config";
/** @type {import('tailwindcss').Config} */
import animate from "tailwindcss-animate";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      screens: {},
      boxShadow: {
        "even-sm": "0 0 2px rgba(0, 0, 0, 0.05)",
        "even-md": "0 0 4px rgba(0, 0, 0, 0.1)",
        "even-lg": "0 0 8px rgba(0, 0, 0, 0.12)",
        "even-xl": "0 0 12px rgba(0, 0, 0, 0.16)",
        "even-2xl": "0 0 20px rgba(0, 0, 0, 0.2)",
      },
      scale: {
        "25": "0.25",
        "101": "1.01",
        "102": "1.02",
        "103": "1.03",
        "104": "1.04",
        "115": "1.15",
        "120": "1.20",
      },
      colors: {
        voxa: {
          stone: {
            "50": "#faf5f0",
            "100": "#e6dcd2",
            "200": "#d2c3b4",
            "300": "#b4a596",
            "400": "#968778",
            "500": "#78695a",
            "600": "#5a5041",
            "700": "#463c32",
            "800": "#322a23",
            "900": "#231c16",
            "950": "#14100c",
          },
          cryan: {
            "50": "#ecfeff",
            "100": "#cffafe",
            "200": "#a5f3fc",
            "300": "#67e8f9",
            "400": "#22d3ee",
            "500": "#06b6d4",
            "600": "#0891b2",
            "700": "#0e7490",
            "800": "#155e75",
            "900": "#164e63",
            "950": "#082632",
          },
          teal: {
            "50": "#ebfffc",
            "100": "#bef0e6",
            "200": "#91e1d2",
            "300": "#69c8b9",
            "400": "#41afa0",
            "500": "#23968c",
            "600": "#1e7d73",
            "700": "#19645f",
            "800": "#14504b",
            "900": "#0f3c37",
            "950": "#0a2826",
          },
          neutral: {
            "50": "hsl(240, 5%, 96%)",
            "100": "hsl(240, 4%, 90%)",
            "200": "hsl(240, 3%, 82%)",
            "300": "hsl(240, 2%, 72%)",
            "400": "hsl(240, 1%, 60%)",
            "500": "hsl(240, 1%, 48%)",
            "600": "hsl(240, 1%, 36%)",
            "700": "hsl(240, 1%, 26%)",
            "800": "hsl(240, 1%, 16%)",
            "900": "hsl(0, 0%, 12%)",
            "950": "hsl(0, 0%, 9%)",
          },
        },
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          borded: "hsl(var(--sidebar-borded))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "background-shine": {
          from: {
            backgroundPosition: "0 0",
          },
          to: {
            backgroundPosition: "-200% 0",
          },
        },
        "loop-scroll": {
          from: {
            transform: "translateX(0)",
          },
          to: {
            transform: "translateX(-100%)",
          },
        },
      },
      animation: {
        "loop-scroll": "loop-scroll 10s linear infinite",
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "background-shine": "background-shine 2s linear infinite",
      },
    },
  },
  plugins: [
    function ({ addUtilities }: PluginAPI) {
      const newUtilities = {
        ".mask-gradient": {
          "-webkit-mask":
            "linear-gradient(90deg, transparent, white 20%, white 80%, transparent)",
          mask: "linear-gradient(90deg, transparent, white 20%, white 80%, transparent)",
        },
      };
      addUtilities(newUtilities);
    },
    animate,
  ],
} satisfies Config;
