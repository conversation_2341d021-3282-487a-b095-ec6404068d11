import React, { useState, useEffect } from "react";
import { Languages } from "lucide-react";
import { cn } from "@/lib/utils";
import { useTranslate } from "@/hooks/useTranslate";

interface UseTranslationButtonOptions {
  text: string;
  buttonClassName?: string;
  iconClassName?: string;
}

export function useTranslationButton({
  text,
  buttonClassName = "",
  iconClassName = "",
}: UseTranslationButtonOptions) {
  const { getTranslation, currentLanguage } = useTranslate();
  const [showTranslation, setShowTranslation] = useState(false);
  const [lastLang, setLastLang] = useState<string>("");

  const translation = getTranslation(text);

  useEffect(() => {
    if (showTranslation && lastLang !== currentLanguage) {
      setLastLang(currentLanguage);
    }
  }, [currentLanguage, showTranslation, lastLang]);

  const handleToggle = () => {
    if (!showTranslation) {
      setLastLang(currentLanguage);
      setShowTranslation(true);
    } else {
      setShowTranslation(false);
    }
  };

  const translatedText = showTranslation
    ? translation.loading
      ? null
      : translation.text
    : text;

  const button = (
    <button
      className={cn(
        "p-1 text-xs rounded-sm underline hover:opacity-80",
        showTranslation
          ? "bg-voxa-teal-600"
          : "text-voxa-teal-600 border border-voxa-teal-600",
        buttonClassName
      )}
      onClick={handleToggle}
      type="button"
    >
      <Languages className={cn("size-4", iconClassName)} />
    </button>
  );

  return {
    translatedText,
    loading: showTranslation ? translation.loading : false,
    showTranslation,
    button,
  };
}
