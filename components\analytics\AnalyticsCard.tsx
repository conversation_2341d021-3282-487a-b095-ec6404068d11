import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { Info } from "lucide-react";

interface InfoCardProps {
  icon?: React.ReactNode;
  title: string;
  tooltip?: string;
  value: string | number;
  rightLabel?: string;
  className?: string;
  loading?: boolean; // <-- Add loading prop
}

export function AnalyticsCard({
  icon,
  title,
  tooltip,
  value,
  rightLabel,
  className,
  loading = false, // <-- Default to false
}: InfoCardProps) {
  return (
    <Card
      className={cn(
        "w-fit grow rounded-xl border p-4 flex items-center gap-4 bg-voxa-neutral-50 dark:bg-voxa-neutral-900 dark:border-voxa-neutral-700 transition-all duration-150",
        className
      )}
    >
      {icon && (
        <div className="bg-voxa-neutral-200 dark:bg-voxa-neutral-800 text-foreground dark:text-voxa-neutral-50 p-2 rounded-full flex items-center justify-center">
          {icon}
        </div>
      )}

      <div className="flex-1">
        <div className="flex items-center gap-1 text-sm text-muted-foreground dark:text-voxa-neutral-200">
          <span className="font-medium">{title}</span>
          {tooltip && (
            <TooltipProvider>
              <Tooltip delayDuration={100}>
                <TooltipTrigger asChild>
                  <Info className="w-4 h-4 cursor-pointer" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>{tooltip}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        <div className="text-xl font-bold dark:text-voxa-neutral-50">
          {loading ? <Skeleton className="h-7 w-20 rounded-md" /> : value}
        </div>
      </div>

      {rightLabel && (
        <div className="self-end bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-foreground dark:text-voxa-neutral-200 text-xs px-2 py-1 rounded-md whitespace-nowrap">
          {loading ? <Skeleton className="h-5 w-12 rounded-md" /> : rightLabel}
        </div>
      )}
    </Card>
  );
}
