import { DuplicateGoal } from "@/actions/GoalActions";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { GoalIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

export function DuplicateGoalDialog({ goalID }: { goalID: string }) {
  const { t } = useTranslation("assistants");
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const handleDuplicateGoal = async () => {
    try {
      setLoading(true);
      const response = await DuplicateGoal(goalID);
      if (!response.success) {
        toast.error(
          t("duplicateGoal.errorDuplicating", { error: response.error })
        );
      } else {
        toast.success(t("duplicateGoal.success"));
        router.push(response.callback_url as string);
      }
    } catch (err: any) {
      toast.error(t("duplicateGoal.errorDuplicating", { error: err.message }));
    } finally {
      setLoading(false);
    }
  };
  return (
    <Dialog>
      <DialogTrigger asChild className="-my-1.5 -mx-2  h-8">
        <Button
          variant="default"
          className="px-6 w-[calc(100%+16px)] justify-start text-white bg-voxa-teal-700 hover:bg-voxa-teal-500  shadow-none text-sm group"
        >
          <GoalIcon className="w-5 h-5" />
          <span>{t("duplicateGoal.duplicateGoal")}</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-96">
        <DialogHeader>
          <DialogTitle>{t("duplicateGoal.duplicateGoal")}</DialogTitle>
        </DialogHeader>
        <DialogDescription className="my-3">
          {t("duplicateGoal.confirmation")}
        </DialogDescription>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="submit"
              disabled={loading}
              className="hover:text-white hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950"
            >
              {t("duplicateGoal.cancel")}
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              onClick={handleDuplicateGoal}
              disabled={loading}
              className={`${
                loading
                  ? "bg-voxa-neutral-300 dark:bg-voxa-neutral-900"
                  : "bg-voxa-teal-700 hover:bg-voxa-teal-500 "
              }
                    text-white`}
            >
              {t("duplicateGoal.duplicateGoal")}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
