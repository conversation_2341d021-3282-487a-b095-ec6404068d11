"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Plan, PlanLink } from "@/types";

interface PlanLinkDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirmDelete: () => void;
  planLink?: PlanLink;
  isDeleting: boolean;
}

export default function PlanLinkDeleteDialog({
  isOpen,
  onClose,
  onConfirmDelete,
  planLink,
  isDeleting,
}: PlanLinkDeleteDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Plan Link</DialogTitle>
        </DialogHeader>
        <DialogDescription>
          {planLink ? (
            <>
              Are you sure you want to permanently delete the plan link{" "}
              <span className="font-mono bg-muted px-1 py-0.5 rounded">
                {planLink.code}
              </span>{" "}
              for plan <b>{(planLink.plan_id as Plan).name}</b>? This action
              cannot be undone.
            </>
          ) : (
            <>No plan link selected.</>
          )}
        </DialogDescription>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isDeleting}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirmDelete}
            disabled={isDeleting || !planLink}
          >
            {isDeleting ? (
              <>
                Deleting...
                <div className="ml-2 animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              </>
            ) : (
              "Delete Plan Link"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
