"use server"

import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { AudioS3 } from "@/lib/S3Client";

export async function getPresignedUrl(key: string) {
  const command = new GetObjectCommand({
    Bucket: process.env.AUDIO_BUCKET_NAME!,
    Key: key,
    ResponseContentDisposition: "attachment",
  });

  return await getSignedUrl(AudioS3, command, { expiresIn: 3600 })
}
