"use client";

import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { AccordionContent } from "@radix-ui/react-accordion";
import { CreateTAUserDialog } from "@/components/dialogs/CreateTaUser";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";
import { UsersRoundIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { getTaAgents } from "@/actions/TaActions";
import { toast } from "sonner";

export default function SettingsAgents() {
  const [agents, setAgents] = useState<any[]>([]);

  const handleGetAgents = async () => {
    try {
      const response = await getTaAgents();
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      setAgents(response.agents || []);
    } catch (err: any) {
      toast.error(err.message);
    }
  };

  useEffect(() => {
    handleGetAgents();
  }, []);
  return (
    <div className="w-full flex flex-col gap-4">
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        Entreprise Agents
      </h1>
      <Accordion type="single" defaultValue="item-1" className="w-full">
        <AccordionItem value="item-1">
          <AccordionTrigger className="hidden">
            {/* gap-2 flex items-center w-full border-y border-voxa-neutral-200 dark:border-voxa-neutral-900 justify-between  */}
            <div className="flex items-center gap-2">
              <UsersRoundIcon className="w-4 h-4" /> Entreprise Agents
            </div>
          </AccordionTrigger>
          <AccordionContent className="py-4 flex flex-col gap-4">
            <CreateTAUserDialog />
            {agents.length > 0 ? (
              <div className="flex flex-col gap-4">
                {agents.map((agent, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between w-full border-b border-voxa-neutral-200 dark:border-voxa-neutral-900 pb-2"
                  >
                    <div className="flex flex-col gap-1">
                      <h1 className="text-lg font-semibold text-foreground/70">
                        {agent.name}
                      </h1>
                      <p className="text-sm text-foreground/50">
                        {agent.email}
                      </p>
                      <p className="text-sm text-foreground/50">
                        {phoneNumberFormat(agent.phone, agent.country)}
                      </p>
                    </div>
                    <button
                      onClick={() => toast.error("Feature not available yet")}
                      className="text-red-500 hover:text-red-500/70 active:text-red-600 transition-all duration-150"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-foreground/50 text-center">
                No agents found
              </p>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
