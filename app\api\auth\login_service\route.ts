import { NextResponse } from "next/server";
import { CognitoIdentityProviderClient, AdminInitiateAuthCommand } from "@aws-sdk/client-cognito-identity-provider";
import crypto from "crypto";

function getSecretHash(username: string, clientId: string, clientSecret: string) {
  return crypto
    .createHmac("sha256", clientSecret)
    .update(username + clientId)
    .digest("base64");
}

const client = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.COGNITO_API_USER_ACCESS_KEY!,
    secretAccessKey: process.env.COGNITO_API_USER_SECRET_KEY!,
  },
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json({ error: "Email et mot de passe requis." }, { status: 400 });
    }

    const clientId = process.env.COGNITO_CLIENT_ID!;
    const clientSecret = process.env.COGNITO_CLIENT_SECRET!;
    const secretHash = getSecretHash(email, clientId, clientSecret);

    const command = new AdminInitiateAuthCommand({
      UserPoolId: process.env.COGNITO_USER_POOL_ID!,
      ClientId: clientId,
      AuthFlow: "ADMIN_NO_SRP_AUTH",
      AuthParameters: {
        USERNAME: email,
        PASSWORD: password,
        SECRET_HASH: secretHash,
      },
    });

    const response = await client.send(command);

    const idToken = response.AuthenticationResult?.IdToken;
    const accessToken = response.AuthenticationResult?.AccessToken;
    const refreshToken = response.AuthenticationResult?.RefreshToken;

    if (!idToken) {
      return NextResponse.json({ error: "Authentification échouée." }, { status: 401 });
    }

    const res = NextResponse.json(
      { message: "Authentification réussie.", token: idToken },
      { status: 200 }
    );

    console.log("Authentification réussie pour l'utilisateur:", idToken);

    res.cookies.set("plugin_id_token", idToken, {
      httpOnly: true,
      secure: true,
      path: "/",
      maxAge: 60 * 60 * 24,
    });

    return res;

  } catch (error: any) {
    console.error("Erreur Cognito:", error);

    // Gestion des erreurs Cognito spécifiques
    if (error.name === "NotAuthorizedException") {
      return NextResponse.json(
        { error: "Email ou mot de passe incorrect." },
        { status: 400 }
      );
    }

    // Erreur générique
    return NextResponse.json(
      { error: "Erreur d'authentification avec Cognito." },
      { status: 500 }
    );
  }
}
