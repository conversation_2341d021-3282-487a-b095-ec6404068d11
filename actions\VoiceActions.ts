"use server";

import dbConnect from "@/lib/mongodb";
import Voice from "@/models/Voice";

export async function GetVoices() {
  try {
    await dbConnect();

    const voices = await Voice.find({});
    if (!voices) return { success: false, error: "No voices found" };

    const formattedVoices = voices.map((voice: any) => {
      const plain = voice.toObject();
      return {
        ...plain,
        _id: plain._id.toString(),
        // voice_id:plain.voice_id.toString() || "",
        instructions: plain.instructions.map((instruction: any) => ({
          displayed_instructions_title:
            instruction.displayed_instructions_title,
          origin_instructions_title: instruction.origin_instructions_title,
          instructions_text: instruction.instructions_text,
        })),
      };
    });
    return { success: true, voices: formattedVoices };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
