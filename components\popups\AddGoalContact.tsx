import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
// import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import { Label } from "../ui/label";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import ButtonLoader from "../Loaders/ButtonLoader";
import { CountriesSelect } from "../dropdowns/CountriesSelect";
import {
  setCountry,
  setNumber,
  setName,
  setAddOneContactOpen,
  CreateNewClient,
  setMessageDrop,
} from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { CountryCode } from "libphonenumber-js";
import { Switch } from "../ui/switch";
import { useTranslation } from "react-i18next";

export default function AddClientToGoal() {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const {
    Name,
    phoneNumber,
    country,
    GoalID,
    messageDrop,
    createClientAndAddToGoalOpen,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardAssistants
  );
  const [Loading, setLoading] = useState(false);
  const [saveToDB, setSaveToDB] = useState(true);

  const HandleCreateNewClient = async () => {
    if (phoneNumber === "" || GoalID === "" || country === "") {
      toast.error(t("addContact.requiredFields"));
      return;
    }
    setLoading(true);
    await dispatch(CreateNewClient({ saveToDB }));
    setLoading(false);
  };

  useEffect(() => {
    console.log("createClientAndAddToGoalOpen", createClientAndAddToGoalOpen);
  }, [createClientAndAddToGoalOpen]);

  return (
    <Dialog
      open={createClientAndAddToGoalOpen}
      onOpenChange={(open) => dispatch(setAddOneContactOpen(open))}
    >
      <DialogContent className="p-5 rounded-md max-w-md w-full border-voxa-neutral-700">
        <DialogHeader>
          <DialogTitle>{t("addContact.addNewContact")}</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-3 mt-4">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="Name" className="text-sm">
              {t("addContact.name")}
            </Label>
            <Input
              value={Name}
              onChange={(e) => dispatch(setName(e.target.value))}
              id="Name"
              placeholder={t("addContact.name")}
            />
          </div>
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="Name" className="text-sm">
              {t("addContact.country")} <span className="text-red-500">*</span>
            </Label>
            <CountriesSelect
              country={country}
              selectCountry={(newCountry: "" | CountryCode) =>
                dispatch(setCountry(newCountry))
              }
              classnames="translate-y-px py-5"
            />
          </div>
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="number" className="text-sm flex items-center">
              {t("addContact.phoneNumber")}{" "}
              <span className="text-red-500">*</span>
            </Label>
            <Input
              value={phoneNumber}
              onChange={(e) => dispatch(setNumber(e.target.value))}
              id="number"
              placeholder={t("addContact.phoneNumber")}
            />
          </div>
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="number" className="text-sm flex items-center">
              {t("addContact.messageDrop")}{" "}
              <span className="text-gray-500">
                ({t("addContact.optional")})
              </span>
            </Label>
            <Input
              value={messageDrop}
              onChange={(e) => dispatch(setMessageDrop(e.target.value))}
              id="messageDrop"
              placeholder={t("addContact.messageDrop")}
            />
          </div>
          <div className="flex items-center space-x-2 mt-2">
            <Switch
              id="owner"
              checked={saveToDB}
              onCheckedChange={(checked) => setSaveToDB(checked)}
            />
            <Label htmlFor="saveToDB">
              {!saveToDB && t("addContact.dontSaveInDb")}
              {saveToDB && t("addContact.saveInDb")}
            </Label>
          </div>
          <button
            disabled={Loading}
            onClick={HandleCreateNewClient}
            className={`${
              Loading
                ? "cursor-not-allowed bg-voxa-teal-400"
                : "bg-voxa-teal-600 hover:bg-voxa-teal-500 "
            }
              transition-all duration-150 text-white px-4 py-2 rounded-md font-medium flex
              justify-center items-center gap-2 mt-8
            `}
          >
            {Loading ? (
              <>
                {t("addContact.addingContact")}
                <ButtonLoader />
              </>
            ) : (
              t("addContact.addContact")
            )}
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
