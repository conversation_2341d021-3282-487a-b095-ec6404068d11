"use client";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ModalContent } from "../ui/animated-modal";
import Link from "next/link";
import clsx from "clsx";
import Image from "next/image";
import cookie from "@/public/images/cookie.svg";
import { cn } from "@/lib/utils";

const fullStyle =
  "h-[100%+5rem] flex flex-col justify-between items-center p-8 -m-10 dark:bg-voxa-teal-700 bg-black";

const buttonStyle =
  "w-full text-center font-bold rounded-lg text-voxa-neutral-50 text-sm px-6 py-2 transition-colors duration-200";

export function CookieModal() {
  const setCookie = (name: string, value: string, days: number) => {
    const expires = new Date(Date.now() + days * 864e5).toUTCString();
    document.cookie = `${name}=${value}; path=/; expires=${expires}`;
  };

  const restoreScroll = () => {
    document.body.style.overflow = "";
  };

  const [show, setShow] = useState(false);

  useEffect(() => {
    const choice = localStorage.getItem("cookieConsent");
    if (!choice) {
      setShow(true);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem("cookieConsent", "accepted");
    setCookie("cookieConsent", "accepted", 365);
    restoreScroll();
    setShow(false);

    console.log("Accepted: Enable analytics.");
  };

  const handleDecline = () => {
    localStorage.setItem("cookieConsent", "declined");
    setCookie("cookieConsent", "declined", 365);
    restoreScroll();
    setShow(false);

    console.log("Declined: Do not load tracking scripts.");
  };

  if (!show) return null;

  return (
    <Modal>
      <ModalBody>
        <ModalContent className="grid grid-cols-1">
          <div className={clsx(fullStyle)}>
            <Image
              src={cookie}
              alt="Cookie icon"
              width={150}
              className="z-0 mt-10 rotate-30"
            />
            <h4 className="z-10 px-6 w-full text-voxa-neutral-50 dark:text-voxa-neutral-950 -ml-10 top-4 text-xl md:text-2xl font-bold text-center">
              We Value Your Privacy
            </h4>
            <p className="text-center text-sm text-voxa-neutral-500 dark:text-voxa-neutral-300 px-4 mt-2">
              This site uses cookies to improve your experience, analyze
              traffic, and show personalized content. By continuing, you consent
              to the use of cookies as described in our policies.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 w-full mt-6">
              <button
                onClick={handleDecline}
                className={cn(
                  buttonStyle,
                  "text-voxa-neutral-600 dark:text-voxa-neutral-50 hover:dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-900 hover:text-voxa-neutral-50 bg-voxa-neutral-50 dark:bg-voxa-neutral-950"
                )}
              >
                Decline
              </button>
              <button
                onClick={handleAccept}
                className={clsx(
                  buttonStyle,
                  "bg-voxa-teal-600 hover:bg-voxa-teal-500"
                )}
              >
                Accept Cookies
              </button>
            </div>
            <p className="text-xs text-voxa-neutral-500 dark:text-voxa-neutral-400 mt-4 text-center">
              By using this site, you agree to our{" "}
              <Link
                href="https://echoparrot.com/GeneralTerms"
                className="text-white hover:underline"
                target="_blank"
              >
                General Terms
              </Link>
              . You can manage your cookie preferences in your browser settings.
            </p>
          </div>
        </ModalContent>
      </ModalBody>
    </Modal>
  );
}
