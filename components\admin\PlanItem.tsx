"use client";

import PlanFeatures from "@/components/admin/PlanFeatures";
import PlanLinkDialog from "@/components/admin/CreatePlanLinkDialog";
import PlanSubscribersDialog from "@/components/admin/PlanSubscribersDialog";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { formatBillingPeriod } from "@/lib/billingUtils";
import { Plan } from "@/types";
import {
  Calendar,
  DollarSign,
  Edit,
  Link as LinkIcon,
  MoreVertical,
  Star,
  Trash2,
  Users,
} from "lucide-react";
import { useState } from "react";

interface PlanItemProps {
  plan: Plan;
  onEdit?: (plan: Plan) => void;
  onDelete?: (
    planId: string,
    planName: string,
    subscriptionCount: number
  ) => void;
  onToggleVisibility?: (planId: string, visible: boolean) => void;
  actionLoading?: string | null;
}

export default function PlanItem({
  plan,
  onEdit,
  onDelete,
  onToggleVisibility,
  actionLoading,
}: PlanItemProps) {
  const formatPrice = (price: number, currency: string) => {
    const symbol = currency === "eur" ? "€" : "$";
    return `${symbol}${price}`;
  };

  // Helper function to get available billing periods with pricing
  const getAvailableBillingPeriods = () => {
    // Handle new billing_options structure
    if (plan.billing_options) {
      return Object.entries(plan.billing_options)
        .filter(([_, options]) => options && options.current_price > 0)
        .map(([period, options]) => ({
          period,
          ...options,
        }));
    }

    // Fallback for old structure (backward compatibility)
    const oldPlan = plan as any;
    if (oldPlan.current_price > 0) {
      return [
        {
          period: oldPlan.billing_period || "monthly",
          current_price: oldPlan.current_price || 0,
          original_price: oldPlan.original_price || 0,
        },
      ];
    }

    return [];
  };

  // Get the primary billing period (first available one) for summary display
  const getPrimaryBillingOption = () => {
    const available = getAvailableBillingPeriods();
    return available.length > 0 ? available[0] : null;
  };

  // Get all pricing options for detailed view
  const getAllPricingOptions = () => {
    return getAvailableBillingPeriods();
  };

  const handleVisibilityToggle = async (currentVisible: boolean) => {
    if (onToggleVisibility) {
      await onToggleVisibility(plan._id, !currentVisible);
    }
  };

  const handleDeleteClick = () => {
    if (onDelete) {
      onDelete(plan._id, plan.name || "", plan.subscription_count || 0);
    }
  };

  // PlanLink dialog state
  const [planLinkDialogOpen, setPlanLinkDialogOpen] = useState(false);

  // PlanSubscribers dialog state
  const [subscribersDialogOpen, setSubscribersDialogOpen] = useState(false);

  return (
    <>
      <AccordionItem
        key={plan._id}
        value={plan._id}
        className="border rounded-lg bg-card data-[state=open]:shadow-md transition-all duration-200"
      >
        <div className="p-4 md:p-6">
          {/* Header with title, badges and actions */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 min-w-0 flex-1">
              <h3 className="text-lg font-semibold text-foreground truncate">
                {plan.name}
              </h3>
              <div className="flex items-center gap-2 flex-wrap">
                <Badge
                  variant={plan.visible ? "default" : "secondary"}
                  className={
                    plan.visible
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                      : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                  }
                >
                  {plan.visible ? "Visible" : "Hidden"}
                </Badge>
                {plan.mostPopular && (
                  <Badge className="bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 text-white flex items-center gap-1">
                    <Star className="w-3 h-3" />
                    Most Popular
                  </Badge>
                )}
                <Badge variant="outline" className="text-xs">
                  #{plan.sort_order}
                </Badge>
              </div>
            </div>

            {/* Actions - separate from accordion trigger */}
            <div className="flex items-center gap-2 flex-shrink-0">
              {/* Visibility Toggle */}
              {onToggleVisibility && (
                <div className="flex items-center gap-1">
                  <Switch
                    checked={plan.visible}
                    onCheckedChange={(checked) =>
                      handleVisibilityToggle(!checked)
                    }
                    disabled={actionLoading === plan._id}
                  />
                </div>
              )}

              {/* More Actions */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    disabled={actionLoading === plan._id}
                    className="h-7 w-7 p-0"
                  >
                    <MoreVertical className="w-3 h-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onEdit && (
                    <DropdownMenuItem
                      onClick={() => onEdit(plan)}
                      disabled={actionLoading === plan._id}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Plan
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    onClick={() => setPlanLinkDialogOpen(true)}
                    disabled={actionLoading === plan._id}
                  >
                    <LinkIcon className="w-4 h-4 mr-2" />
                    Create Plan Link
                  </DropdownMenuItem>
                  {onDelete && (
                    <DropdownMenuItem
                      onClick={handleDeleteClick}
                      disabled={actionLoading === plan._id}
                      className="text-destructive dark:text-red-600 focus:text-destructive"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Plan
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Price and stats summary */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-6 text-sm text-muted-foreground mb-4">
            {/* Price Summary */}
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              {(() => {
                const primaryOption = getPrimaryBillingOption();
                const availableOptions = getAvailableBillingPeriods();

                if (primaryOption) {
                  return (
                    <>
                      <span className="font-medium text-primary">
                        {formatPrice(
                          primaryOption.current_price,
                          plan.currency!
                        )}
                      </span>
                      <span className="text-xs sm:text-sm">
                        {formatBillingPeriod(primaryOption.period)}
                      </span>
                      {availableOptions.length > 1 && (
                        <span className="text-xs text-muted-foreground">
                          +{availableOptions.length - 1} more
                        </span>
                      )}
                    </>
                  );
                } else {
                  return (
                    <span className="text-muted-foreground text-sm">
                      No pricing configured
                    </span>
                  );
                }
              })()}
            </div>

            {/* Subscription Count */}
            {plan.subscription_count !== undefined && (
              <div
                className="flex items-center gap-1 cursor-pointer hover:underline"
                onClick={() => setSubscribersDialogOpen(true)}
                title="View subscribers"
                role="button"
                tabIndex={0}
                style={{ userSelect: "none" }}
              >
                <Users className="w-4 h-4" />
                <span className="text-xs sm:text-sm">
                  {plan.subscription_count} subscribers
                </span>
              </div>
            )}
          </div>

          {/* Accordion Trigger */}
          <AccordionTrigger className="hover:no-underline p-3 mt-2 rounded-md border border-dashed border-muted-foreground/30 hover:border-muted-foreground/50 hover:bg-muted transition-colors">
            <span className="text-sm font-medium text-muted-foreground group-hover:text-foreground">
              {plan.description ? "View details" : "View pricing & features"}
            </span>
          </AccordionTrigger>
        </div>

        <AccordionContent className="px-4 pb-4 md:px-6 md:pb-6">
          {/* Description */}
          {plan.description && (
            <p className="text-muted-foreground text-sm mb-4">
              {plan.description}
            </p>
          )}

          {/* Stats Row */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-6 text-sm mb-6">
            {plan.subscription_count !== undefined && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Users className="w-4 h-4" />
                <span>
                  {plan.subscription_count} subscription
                  {plan.subscription_count !== 1 ? "s" : ""}
                  {plan.active_subscription_count !== undefined &&
                    ` (${plan.active_subscription_count} active)`}
                </span>
              </div>
            )}
            <div className="flex items-center gap-2 text-muted-foreground">
              <Calendar className="w-4 h-4" />
              <span className="text-xs sm:text-sm">
                Created{" "}
                {plan.created_at
                  ? new Date(plan.created_at).toLocaleDateString()
                  : "N/A"}
              </span>
            </div>
          </div>

          <div className="space-y-6">
            {/* Pricing Details */}
            <div className="space-y-3">
              <h4 className="font-medium text-foreground flex items-center gap-2">
                <DollarSign className="w-4 h-4" />
                Pricing Details
              </h4>
              {(() => {
                const allOptions = getAllPricingOptions();

                if (allOptions.length === 0) {
                  return (
                    <div className="p-3 md:p-4 bg-muted/20 rounded-lg">
                      <span className="text-muted-foreground">
                        No pricing configured
                      </span>
                    </div>
                  );
                }

                return (
                  <div className="space-y-3">
                    {allOptions.map((option) => (
                      <div
                        key={option.period}
                        className="p-3 md:p-4 bg-muted/20 rounded-lg"
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-medium capitalize text-foreground">
                            {option.period} Billing
                          </span>
                        </div>
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                          {option.original_price > option.current_price && (
                            <span className="text-base sm:text-lg text-muted-foreground line-through">
                              {formatPrice(
                                option.original_price,
                                plan.currency!
                              )}
                            </span>
                          )}
                          <span className="text-xl sm:text-2xl font-bold text-primary">
                            {formatPrice(option.current_price, plan.currency!)}
                          </span>
                          <span className="text-xs sm:text-sm text-muted-foreground">
                            {formatBillingPeriod(option.period)}
                          </span>
                        </div>
                        {option.original_price > option.current_price && (
                          <div className="mt-2">
                            <Badge
                              variant="outline"
                              className="bg-green-50 text-green-700 dark:bg-green-950 dark:text-green-300 text-xs"
                            >
                              Save{" "}
                              {formatPrice(
                                option.original_price - option.current_price,
                                plan.currency!
                              )}
                            </Badge>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                );
              })()}
            </div>

            {/* Features */}
            <PlanFeatures features={plan.features || []} />
          </div>
        </AccordionContent>
      </AccordionItem>

      {/* PlanLink Dialog as separate component */}
      <PlanLinkDialog
        open={planLinkDialogOpen}
        onOpenChange={setPlanLinkDialogOpen}
        planId={plan._id}
      />

      {/* PlanSubscribers Dialog */}
      <PlanSubscribersDialog
        open={subscribersDialogOpen}
        onOpenChange={setSubscribersDialogOpen}
        plan={plan}
      />
    </>
  );
}
