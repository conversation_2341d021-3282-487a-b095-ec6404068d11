import { JSX, useState } from "react";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Eye, EyeOff } from "lucide-react";
import { cn } from "@/lib/utils";

interface CustomInputProps {
  name?: string;
  label?: any;
  type?: string;
  placeholder?: string;
  required?: boolean;
  value?: string | number;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  readonly?: boolean;
  disabled?: boolean;
  className?: string;
  labelIcon?: JSX.Element;
  parentClassName?: string;
  accept?: string;
  isPassword?: boolean;
  pattern?: string;
  maxLength?: number;
  minLength?: number;
  inputMode?: "none" | "text" | "tel" | "email" | "url" | "numeric" | "decimal";
}

export default function CustomInput({
  name,
  label,
  type,
  placeholder,
  required,
  value,
  onChange,
  onBlur,
  onFocus,
  readonly,
  disabled,
  className,
  labelIcon,
  parentClassName,
  accept,
  isPassword,
  pattern,
  maxLength,
  minLength,
  inputMode,
}: CustomInputProps) {
  const [showPassword, setShowPassword] = useState(false);
  const isPasswordField = isPassword && type === "password";

  return (
    <div
      className={`relative grid w-full items-center gap-1.5 ${parentClassName}`}
    >
      {label && (
        <Label
          className="text-sm flex gap-2 items-center text-foreground"
          htmlFor={name}
        >
          {labelIcon}
          {label}
          {required && <span className="text-red-500 -translate-x-1"> * </span>}
        </Label>
      )}
      <div className="relative">
        <Input
          className={cn(isPassword && "pr-[38.5px]", className)}
          placeholder={placeholder}
          type={isPasswordField && showPassword ? "text" : type}
          name={name}
          required={required}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          onFocus={onFocus}
          disabled={disabled}
          readOnly={readonly}
          accept={accept}
          pattern={pattern}
          maxLength={maxLength}
          minLength={minLength}
          inputMode={inputMode}
        />
        {isPasswordField && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors"
          >
            {showPassword ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        )}
      </div>
    </div>
  );
}
