import { saveInvoiceToDatabase } from "@/actions/BillingActions";
import dbConnect from "@/lib/mongodb";
import Entreprise from "@/models/Entreprise";
import Plan from "@/models/Plan";
import Subscription from "@/models/Subscription";
import { NextRequest, NextResponse } from "next/server";
import <PERSON>e from "stripe";

interface PaymentMethodDetails {
  type: "card" | "bank_transfer" | "digital_wallet" | "other";
  brand?: string;
  last4?: string;
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

// Helper function to detect payment method details from Stripe charge
function detectPaymentMethodDetails(charge: any): PaymentMethodDetails {
  if (!charge?.payment_method_details) {
    return { type: "other", brand: "other" };
  }

  const pmd = charge.payment_method_details;

  // Card payments
  if (pmd.card) {
    return {
      type: "card",
      brand: pmd.card.brand || "unknown",
      last4: pmd.card.last4 || undefined,
    };
  }

  // European payment methods
  if (pmd.ideal) {
    return {
      type: "bank_transfer",
      brand: "ideal",
      last4: undefined,
    };
  }

  if (pmd.bancontact) {
    return {
      type: "bank_transfer",
      brand: "bancontact",
      last4: undefined,
    };
  }

  if (pmd.eps) {
    return {
      type: "bank_transfer",
      brand: "eps",
      last4: undefined,
    };
  }

  if (pmd.giropay) {
    return {
      type: "bank_transfer",
      brand: "giropay",
      last4: undefined,
    };
  }

  if (pmd.sofort) {
    return {
      type: "bank_transfer",
      brand: "sofort",
      last4: undefined,
    };
  }

  // SEPA payments
  if (pmd.sepa_debit) {
    return {
      type: "bank_transfer",
      brand: "sepa",
      last4: pmd.sepa_debit.last4 || undefined,
    };
  }

  // Google Pay
  if (pmd.google_pay) {
    return {
      type: "digital_wallet",
      brand: "google_pay",
      last4: undefined,
    };
  }

  // Link by Stripe
  if (pmd.link) {
    return {
      type: "digital_wallet",
      brand: "link",
      last4: undefined,
    };
  }

  // Bank transfers
  if (pmd.bank_transfer || pmd.ach_debit || pmd.ach_credit_transfer) {
    return {
      type: "bank_transfer",
      brand: "bank_transfer",
      last4: undefined,
    };
  }

  // Fallback for unknown types
  return {
    type: "other",
    brand: "other",
    last4: undefined,
  };
}

export async function POST(req: NextRequest) {
  const body = await req.text();
  const sig = req.headers.get("stripe-signature")!;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
  } catch (err: any) {
    console.log("Webhook signature verification failed.", err.message);
    return NextResponse.json(
      { error: "Webhook signature verification failed" },
      { status: 400 }
    );
  }

  // Handle the event
  switch (event.type) {
    case "checkout.session.completed":
      const session = event.data.object as Stripe.Checkout.Session;

      try {
        await dbConnect();

        console.log("Processing checkout session:", {
          session_id: session.id,
          customer: session.customer,
          payment_status: session.payment_status,
          mode: session.mode,
          amount_total: session.amount_total,
          metadata: session.metadata,
        });

        // Handle subscription checkout sessions
        if (session.mode === "subscription" && session.subscription) {
          console.log("Processing subscription checkout session:", {
            session_id: session.id,
            subscription_id: session.subscription,
            payment_status: session.payment_status,
          });

          const planId = session.metadata?.plan_id;
          const entrepriseId = session.metadata?.entreprise_id;
          const billingPeriod = session.metadata?.billing_period as
            | "monthly"
            | "yearly"
            | "weekly"
            | "daily";

          console.log("Subscription metadata:", {
            planId,
            entrepriseId,
            billingPeriod,
          });

          if (planId && entrepriseId && billingPeriod) {
            // Get the Stripe subscription details
            const stripeSubscription = (await stripe.subscriptions.retrieve(
              session.subscription as string
            )) as Stripe.Subscription;

            console.log("Retrieved Stripe subscription:", {
              id: stripeSubscription.id,
              status: stripeSubscription.status,
              current_period_start: (stripeSubscription as any)
                .current_period_start,
              current_period_end: (stripeSubscription as any)
                .current_period_end,
            });

            // Helper function to safely convert Unix timestamp to Date
            const safeTimestampToDate = (
              timestamp: any,
              fallback: Date
            ): Date => {
              if (typeof timestamp === "number" && timestamp > 0) {
                const date = new Date(timestamp * 1000);
                if (!isNaN(date.getTime())) {
                  return date;
                }
              }
              return fallback;
            };

            // Calculate default dates
            const now = new Date();
            const defaultEnd = new Date(
              now.getTime() + 30 * 24 * 60 * 60 * 1000
            );

            // Get plan details
            const plan = await Plan.findById(planId);
            if (!plan) {
              console.error(`Plan not found: ${planId}`);
              break;
            }

            // Get billing options for the specific billing period
            const billingOptionData = plan.billing_options?.[billingPeriod];
            if (!billingOptionData) {
              console.error(
                `Billing options not found for billing period: ${billingPeriod}`
              );
              break;
            }

            console.log(
              "Found plan:",
              plan.name,
              "Price:",
              billingOptionData.current_price,
              "Billing Period:",
              billingPeriod
            );

            // Create subscription in database
            const subscriptionData = {
              plan_id: planId,
              entreprise_id: entrepriseId,
              stripe_subscription_id: stripeSubscription.id,
              stripe_customer_id: session.customer,
              status: stripeSubscription.status,
              price_at_subscription: billingOptionData.current_price,
              currency: plan.currency,
              billing_period: billingPeriod,
              current_period_start: safeTimestampToDate(
                (stripeSubscription as any).current_period_start,
                now
              ),
              current_period_end: safeTimestampToDate(
                (stripeSubscription as any).current_period_end,
                defaultEnd
              ),
              trial_start: (stripeSubscription as any).trial_start
                ? safeTimestampToDate(
                    (stripeSubscription as any).trial_start,
                    now
                  )
                : undefined,
              trial_end: (stripeSubscription as any).trial_end
                ? safeTimestampToDate(
                    (stripeSubscription as any).trial_end,
                    defaultEnd
                  )
                : undefined,
            };

            console.log(
              "Creating/updating subscription with data:",
              subscriptionData
            );

            const updatedSubscription = await Subscription.findOneAndUpdate(
              { stripe_subscription_id: stripeSubscription.id },
              subscriptionData,
              { upsert: true, new: true }
            );

            console.log("Subscription created/updated:", {
              _id: updatedSubscription._id,
              status: updatedSubscription.status,
              stripe_subscription_id:
                updatedSubscription.stripe_subscription_id,
            });

            // Update enterprise current subscription if it's active
            if (stripeSubscription.status === "active") {
              console.log(
                `Setting enterprise ${entrepriseId} current_subscription to:`,
                updatedSubscription._id
              );

              await Entreprise.findByIdAndUpdate(entrepriseId, {
                current_subscription: updatedSubscription._id,
                $addToSet: { subscriptions: updatedSubscription._id },
              });

              console.log("Enterprise updated with current subscription");
            } else {
              // Still add to subscriptions array even if not active
              await Entreprise.findByIdAndUpdate(entrepriseId, {
                $addToSet: { subscriptions: updatedSubscription._id },
              });

              console.log(
                `Subscription status is ${stripeSubscription.status}, not setting as current subscription`
              );
            }

            console.log(
              `✅ Subscription created/updated via checkout for enterprise ${entrepriseId}: ${stripeSubscription.id} (${stripeSubscription.status})`
            );
          } else {
            console.error("❌ Missing subscription metadata:", {
              planId,
              entrepriseId,
              session_id: session.id,
              all_metadata: session.metadata,
            });
          }
        }
        // Handle payment checkout sessions (existing logic)
        else if (session.mode === "payment") {
          // For payment links, try to get metadata from different sources
          let amount = parseInt(session.metadata?.amount || "0");
          let entrepriseId = session.metadata?.entreprise_id;

          // If metadata is not available, try to get it from the customer
          if (!entrepriseId && session.customer) {
            try {
              const customer = await stripe.customers.retrieve(
                session.customer as string
              );
              if (customer && !customer.deleted && customer.metadata) {
                entrepriseId = customer.metadata.entreprise_id;
                console.log(
                  "Found enterprise ID from customer metadata:",
                  entrepriseId
                );
              }
            } catch (error) {
              console.error("Error retrieving customer:", error);
            }
          }

          // If still no amount, get it from the session total
          if (amount === 0 && session.amount_total) {
            amount = session.amount_total / 100; // Convert from cents
            console.log("Using amount from session total:", amount);
          }

          // Additional check: if we still don't have enterprise ID but have a customer,
          // look up the enterprise by stripe_customer_id
          if (!entrepriseId && session.customer) {
            try {
              const entreprise = await Entreprise.findOne({
                stripe_customer_id: session.customer,
              });
              if (entreprise) {
                entrepriseId = entreprise._id.toString();
                console.log(
                  "Found enterprise by customer ID lookup:",
                  entrepriseId
                );
              }
            } catch (error) {
              console.error(
                "Error looking up enterprise by customer ID:",
                error
              );
            }
          }

          if (amount > 0 && entrepriseId && session.payment_status === "paid") {
            // Payment model removed: skip payment creation and balance update
            console.log(
              `✅ Payment received via checkout session for enterprise ${entrepriseId}: +${amount}€`
            );
          } else {
            console.error("❌ Missing required data for purchase creation:", {
              amount,
              entrepriseId,
              payment_status: session.payment_status,
              session_id: session.id,
            });
          }
        }
      } catch (error) {
        console.error("Error processing checkout session webhook:", error);
      }
      break;

    // Subscription Events
    case "customer.subscription.created":
    case "customer.subscription.updated":
      const subscription = event.data.object as Stripe.Subscription;

      try {
        await dbConnect();

        const planId = subscription.metadata?.plan_id;
        const entrepriseId = subscription.metadata?.entreprise_id;
        const billingPeriod = subscription.metadata?.billing_period as
          | "monthly"
          | "yearly"
          | "weekly"
          | "daily";

        console.log(
          "-------------------- planId, entrepriseId, billingPeriod --------------------"
        );
        console.log(planId, entrepriseId, billingPeriod);

        if (planId && entrepriseId) {
          // Get plan details to get pricing information
          const plan = await Plan.findById(planId);
          if (!plan) {
            console.error(`Plan not found: ${planId}`);
            break;
          }

          // Get pricing data
          let priceAtSubscription = 0;
          const currency = plan.currency;

          if (billingPeriod && plan.billing_options) {
            const billingOptionData = plan.billing_options[billingPeriod];
            if (billingOptionData) {
              priceAtSubscription = billingOptionData.current_price;
            }
          }

          // Helper function to safely convert Unix timestamp to Date
          const safeTimestampToDate = (
            timestamp: any,
            fallback: Date
          ): Date => {
            if (typeof timestamp === "number" && timestamp > 0) {
              const date = new Date(timestamp * 1000);
              if (!isNaN(date.getTime())) {
                return date;
              }
            }
            return fallback;
          };

          const now = new Date();

          const subscriptionUpdateData: any = {
            status: subscription.status,
            current_period_start: safeTimestampToDate(
              (subscription as any).current_period_start,
              now
            ),
            current_period_end: safeTimestampToDate(
              (subscription as any).current_period_end,
              new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
            ),
            cancel_at_period_end: (subscription as any).cancel_at_period_end,
            canceled_at: (subscription as any).canceled_at
              ? safeTimestampToDate((subscription as any).canceled_at, now)
              : null,
            trial_start: (subscription as any).trial_start
              ? safeTimestampToDate((subscription as any).trial_start, now)
              : null,
            trial_end: (subscription as any).trial_end
              ? safeTimestampToDate((subscription as any).trial_end, now)
              : null,
            plan_link_code: subscription.metadata?.plan_link_code,
          };

          // If this is a new subscription (created event) or if we have complete data, set the full subscription data
          if (
            event.type === "customer.subscription.created" ||
            (billingPeriod && priceAtSubscription > 0)
          ) {
            subscriptionUpdateData.plan_id = planId;
            subscriptionUpdateData.entreprise_id = entrepriseId;
            subscriptionUpdateData.stripe_subscription_id = subscription.id;
            subscriptionUpdateData.currency = currency;

            if (billingPeriod) {
              subscriptionUpdateData.billing_period = billingPeriod;
            }

            if (priceAtSubscription > 0) {
              subscriptionUpdateData.price_at_subscription =
                priceAtSubscription;
            }
          }

          const updatedSubscription = await Subscription.findOneAndUpdate(
            { stripe_subscription_id: subscription.id },
            subscriptionUpdateData,
            { upsert: true, new: true }
          );

          // Update enterprise current subscription if it's active
          if (subscription.status === "active") {
            await Entreprise.findByIdAndUpdate(entrepriseId, {
              current_subscription: updatedSubscription._id,
              $addToSet: { subscriptions: updatedSubscription._id },
            });
          }

          console.log(
            `✅ Subscription ${subscription.status} for enterprise ${entrepriseId} (${event.type})`
          );
        }
      } catch (error) {
        console.error("Error processing subscription webhook:", error);
      }
      break;

    case "customer.subscription.deleted":
      const deletedSubscription = event.data.object as Stripe.Subscription;

      try {
        await dbConnect();

        await Subscription.findOneAndUpdate(
          { stripe_subscription_id: deletedSubscription.id },
          {
            status: "canceled",
            canceled_at: new Date(),
          }
        );

        // Remove current subscription from enterprise
        const entrepriseId = deletedSubscription.metadata?.entreprise_id;
        if (entrepriseId) {
          await Entreprise.findByIdAndUpdate(entrepriseId, {
            $unset: { current_subscription: 1 },
          });
        }

        console.log(`✅ Subscription canceled: ${deletedSubscription.id}`);
      } catch (error) {
        console.error("Error processing subscription deletion webhook:", error);
      }
      break;

    case "invoice.payment_succeeded":
      const invoice = event.data.object as Stripe.Invoice;

      try {
        await dbConnect();

        // Save invoice to database first
        if ((invoice as any).subscription) {
          const stripeSubscription = await stripe.subscriptions.retrieve(
            (invoice as any).subscription as string
          );
          const entrepriseId = stripeSubscription.metadata?.entreprise_id;
          const subscriptionId = await Subscription.findOne({
            stripe_subscription_id: stripeSubscription.id,
          }).select("_id");

          if (entrepriseId) {
            await saveInvoiceToDatabase({
              stripeInvoice: invoice,
              entrepriseId,
              subscriptionId: subscriptionId?._id?.toString(),
            });
            console.log(`✅ Invoice saved to database: ${invoice.id}`);
          }
        }

        // --- Handle credit topup ---
        if (invoice.metadata?.type === "credit_topup" && invoice.customer) {
          // Find entreprise by stripe_customer_id
          const entreprise = await Entreprise.findOne({
            stripe_customer_id: invoice.customer,
          });
          if (entreprise) {
            const amount = Number(invoice.metadata.amount) || 0;
            await Entreprise.findByIdAndUpdate(entreprise._id, {
              $inc: { balance: amount },
            });
            console.log(
              `✅ Credit topup: Increased balance of entreprise ${entreprise._id} by ${amount}€ (invoice: ${invoice.id})`
            );
          } else {
            console.error(
              `❌ Credit topup: Entreprise not found for customer ${invoice.customer}`
            );
          }
        }

        if (
          (invoice as any).subscription &&
          (invoice as any).billing_reason === "subscription_cycle"
        ) {
          const stripeSubscription = await stripe.subscriptions.retrieve(
            (invoice as any).subscription as string
          );
          const planId = stripeSubscription.metadata?.plan_id;
          const entrepriseId = stripeSubscription.metadata?.entreprise_id;

          if (planId && entrepriseId) {
            // Payment model removed: skip payment record creation for subscription
            console.log(
              `✅ Subscription payment received for entreprise ${entrepriseId} (invoice: ${invoice.id})`
            );
          }
        }
      } catch (error) {
        console.error("Error processing invoice payment webhook:", error);
      }
      break;

    case "invoice.payment_failed":
      const failedInvoice = event.data.object as Stripe.Invoice;

      try {
        await dbConnect();

        // Save/update invoice with failed payment status first
        if ((failedInvoice as any).subscription) {
          const stripeSubscription = await stripe.subscriptions.retrieve(
            (failedInvoice as any).subscription as string
          );
          const entrepriseId = stripeSubscription.metadata?.entreprise_id;
          const dbSubscription = await Subscription.findOne({
            stripe_subscription_id: stripeSubscription.id,
          }).select("_id");

          if (entrepriseId) {
            await saveInvoiceToDatabase({
              stripeInvoice: failedInvoice,
              entrepriseId,
              subscriptionId: dbSubscription?._id?.toString(),
            });
            console.log(
              `✅ Failed invoice saved to database: ${failedInvoice.id}`
            );
          }

          // Update subscription status
          await Subscription.findOneAndUpdate(
            {
              stripe_subscription_id: (failedInvoice as any)
                .subscription as string,
            },
            {
              status: "past_due",
            }
          );

          console.log(
            `❌ Subscription payment failed: ${
              (failedInvoice as any).subscription
            }`
          );
        }
      } catch (error) {
        console.error("Error processing failed invoice webhook:", error);
      }
      break;

    // Invoice created event
    case "invoice.created":
      const createdInvoice = event.data.object as Stripe.Invoice;

      try {
        await dbConnect();

        // Determine entreprise ID from customer or subscription
        let entrepriseId: string | undefined;
        let subscriptionId: string | undefined;

        if ((createdInvoice as any).subscription) {
          const stripeSubscription = await stripe.subscriptions.retrieve(
            (createdInvoice as any).subscription as string
          );
          entrepriseId = stripeSubscription.metadata?.entreprise_id;

          // Get our database subscription ID
          const dbSubscription = await Subscription.findOne({
            stripe_subscription_id: stripeSubscription.id,
          }).select("_id");

          subscriptionId = dbSubscription?._id?.toString();
        } else {
          // For non-subscription invoices, find entreprise by customer ID
          const entreprise = await Entreprise.findOne({
            stripe_customer_id: createdInvoice.customer,
          }).select("_id");
          entrepriseId = entreprise?._id?.toString();
        }

        if (entrepriseId) {
          await saveInvoiceToDatabase({
            stripeInvoice: createdInvoice,
            entrepriseId,
            subscriptionId,
          });
          console.log(`✅ Invoice created and saved: ${createdInvoice.id}`);
        }
      } catch (error) {
        console.error("Error processing invoice creation webhook:", error);
      }
      break;

    // Invoice updated event
    case "invoice.updated":
      const updatedInvoice = event.data.object as Stripe.Invoice;

      try {
        await dbConnect();

        // Determine entreprise ID from customer or subscription
        let entrepriseId: string | undefined;
        let subscriptionId: string | undefined;

        if ((updatedInvoice as any).subscription) {
          const stripeSubscription = await stripe.subscriptions.retrieve(
            (updatedInvoice as any).subscription as string
          );
          entrepriseId = stripeSubscription.metadata?.entreprise_id;

          // Get our database subscription ID
          const dbSubscription = await Subscription.findOne({
            stripe_subscription_id: stripeSubscription.id,
          }).select("_id");
          subscriptionId = dbSubscription?._id?.toString();
        } else {
          // For non-subscription invoices, find entreprise by customer ID
          const entreprise = await Entreprise.findOne({
            stripe_customer_id: updatedInvoice.customer,
          }).select("_id");
          entrepriseId = entreprise?._id?.toString();
        }

        if (entrepriseId) {
          await saveInvoiceToDatabase({
            stripeInvoice: updatedInvoice,
            entrepriseId,
            subscriptionId,
          });
          console.log(`✅ Invoice updated and saved: ${updatedInvoice.id}`);
        }
      } catch (error) {
        console.error("Error processing invoice update webhook:", error);
      }
      break;

    // Invoice finalized event (when draft becomes final)
    case "invoice.finalized":
      const finalizedInvoice = event.data.object as Stripe.Invoice;

      try {
        await dbConnect();

        // Determine entreprise ID from customer or subscription
        let entrepriseId: string | undefined;
        let subscriptionId: string | undefined;

        if ((finalizedInvoice as any).subscription) {
          const stripeSubscription = await stripe.subscriptions.retrieve(
            (finalizedInvoice as any).subscription as string
          );
          entrepriseId = stripeSubscription.metadata?.entreprise_id;

          // Get our database subscription ID
          const dbSubscription = await Subscription.findOne({
            stripe_subscription_id: stripeSubscription.id,
          }).select("_id");
          subscriptionId = dbSubscription?._id?.toString();
        } else {
          // For non-subscription invoices, find entreprise by customer ID
          const entreprise = await Entreprise.findOne({
            stripe_customer_id: finalizedInvoice.customer,
          }).select("_id");
          entrepriseId = entreprise?._id?.toString();
        }

        if (entrepriseId) {
          await saveInvoiceToDatabase({
            stripeInvoice: finalizedInvoice,
            entrepriseId,
            subscriptionId,
          });
          console.log(`✅ Invoice finalized and saved: ${finalizedInvoice.id}`);
        }
      } catch (error) {
        console.error("Error processing invoice finalization webhook:", error);
      }
      break;

    // Invoice voided event
    case "invoice.voided":
      const voidedInvoice = event.data.object as Stripe.Invoice;

      try {
        await dbConnect();

        // Determine entreprise ID from customer or subscription
        let entrepriseId: string | undefined;
        let subscriptionId: string | undefined;

        if ((voidedInvoice as any).subscription) {
          const stripeSubscription = await stripe.subscriptions.retrieve(
            (voidedInvoice as any).subscription as string
          );
          entrepriseId = stripeSubscription.metadata?.entreprise_id;

          // Get our database subscription ID
          const dbSubscription = await Subscription.findOne({
            stripe_subscription_id: stripeSubscription.id,
          }).select("_id");
          subscriptionId = dbSubscription?._id?.toString();
        } else {
          // For non-subscription invoices, find entreprise by customer ID
          const entreprise = await Entreprise.findOne({
            stripe_customer_id: voidedInvoice.customer,
          }).select("_id");
          entrepriseId = entreprise?._id?.toString();
        }

        if (entrepriseId) {
          await saveInvoiceToDatabase({
            stripeInvoice: voidedInvoice,
            entrepriseId,
            subscriptionId,
          });
          console.log(`✅ Invoice voided and saved: ${voidedInvoice.id}`);
        }
      } catch (error) {
        console.error("Error processing invoice void webhook:", error);
      }
      break;

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  return NextResponse.json({ received: true });
}
