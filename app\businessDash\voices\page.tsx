"use client";

import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  fetchVoices,
  Voice,
} from "@/redux/BusinessDashboard/subSlices/VoicesSlice";
import MainLoader from "@/components/Loaders/MainLoader";
import { handleCopyText } from "@/lib/Strings/Copy";
import { Copy, Pause, Play } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import CustomPagination from "@/components/pagination/CustomPagination";
import { usePagination } from "@/hooks/usePagination";

const VoiceCard = ({
  voice,
  isActive,
  onPlay,
}: {
  voice: Voice;
  isSelected?: boolean;
  isActive: boolean;
  onPlay: (ref: HTMLAudioElement) => void;
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const togglePlayback = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      onPlay(audio);
      audio.play().catch((err) => {
        console.error("Error playing audio:", err);
      });
      setIsPlaying(true);
    }
  };

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleEnded = () => setIsPlaying(false);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("ended", handleEnded);
    };
  }, []);

  useEffect(() => {
    if (!isActive && isPlaying) {
      audioRef.current?.pause();
      audioRef.current!.currentTime = 0;
      setIsPlaying(false);
    }
  }, [isActive, isPlaying]);

  const titleStyle =
    "w-fit bg-voxa-neutral-100 dark:bg-voxa-neutral-900 px-2 py-1 rounded-lg text-voxa-neutral-950 dark:text-voxa-neutral-200 text-xs sm:text-sm text-nowrap";

  return (
    <div className="bg-sidebar border-sidebar-border border relative w-full grid grid-cols-1 md:grid-cols-2 justify-between items-center gap-x-2.5 p-3.5 rounded-xl hover:shadow-lg transition-all duration-250 hover:scale-y-[1.03] hover:scale-x-[1.02]">
      <div className="mb-1 col-span-2 flex gap-2 items-center">
        <p className="text-voxa-neutral-500 dark:text-voxa-neutral-50 text-lg sm:text-xl font-bold">
          {voice.displayed_ai_voice.charAt(0).toUpperCase() +
            voice.displayed_ai_voice.slice(1)}
        </p>
        <p className={cn(titleStyle)}>
          {voice.gender.charAt(0).toUpperCase() + voice.gender.slice(1)}
        </p>
        <p className={cn(titleStyle)}>
          {voice.language.charAt(0).toUpperCase() + voice.language.slice(1)}
        </p>
      </div>

      <div className="col-span-2 flex flex-col gap-1">
        <p className="text-[16px] md:text-lg font-semibold text-voxa-teal-600 dark:text-voxa-teal-500  md:text-nowrap">
          {voice.price}
        </p>
        {voice.description === "Cloned voice" && (
          <p className="text-sm text-nowrap dark:text-voxa-neutral-200">
            <>
              <span className="font-bold">Cloning Price: </span>
              {voice.cloning_price}
            </>
          </p>
        )}
      </div>
      <div className="max-md:max-w-[90%] text-xs md:text-sm flex items-center gap-2 max-md:justify-between w-fit pl-1 rounded-md border border-voxa-neutral-300 dark:border-voxa-neutral-700">
        <p className="truncate">{voice._id}</p>
        <Button
          className="rounded-sm p-1 h-fit bg-voxa-neutral-600 dark:bg-voxa-neutral-300"
          onClick={() => handleCopyText(voice._id)}
        >
          <Copy className="scale-95" />
        </Button>
      </div>
      <Button
        className="justify-self-end text-base h-9 w-9 sm:w-24 max-md:mx-auto rounded-full gap-2 items-center bg-voxa-neutral-600 dark:bg-voxa-neutral-300"
        onClick={togglePlayback}
      >
        {isPlaying ? <Pause /> : <Play />}
        <p className="max-sm:hidden">{isPlaying ? "Pause" : "Play"}</p>
      </Button>
      {voice.available ? (
        <p
          className={cn(
            titleStyle,
            "w-fit py-0.5 font-medium absolute -top-2.5 right-3 rounded-full bg-voxa-teal-600 dark:bg-voxa-teal-600 text-white dark:text-white"
          )}
        >
          Available
        </p>
      ) : (
        <p
          className={cn(
            titleStyle,
            "w-fit py-0.5 font-medium absolute -top-2.5 right-3 rounded-full bg-voxa-neutral-600 dark:bg-voxa-neutral-600 text-white dark:text-white"
          )}
        >
          Coming Soon
        </p>
      )}
      <audio ref={audioRef} src={voice.url} />
    </div>
  );
};

export default function Voices() {
  const { t } = useTranslation("voices");
  const dispatch = useDispatch<AppDispatch>();
  const { voices, loading, selectedVoice } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardVoices
  );

  const [activeAudio, setActiveAudio] = useState<HTMLAudioElement | null>(null);
  const [activeVoiceId, setActiveVoiceId] = useState<string | null>(null);
  const itemsPerPage = 12;
  const {
    currentItems: currentVoices,
    currentPage,
    setCurrentPage,
  } = usePagination(voices, itemsPerPage);

  useEffect(() => {
    dispatch(fetchVoices());
  }, [dispatch]);

  const handlePlay = (audioElement: HTMLAudioElement, voiceId: string) => {
    if (activeAudio && activeAudio !== audioElement) {
      activeAudio.pause();
      activeAudio.currentTime = 0;
    }
    setActiveAudio(audioElement);
    setActiveVoiceId(voiceId);
  };

  return (
    <div className="relative rounded-2xl w-full flex flex-col gap-4">
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        {t("title")}
      </h1>
      {loading ? (
        <div className="absolute w-full flex justify-center items-center h-[calc(100vh-2rem)] sm:h-[calc(100vh-3rem)]">
          <MainLoader />
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-5">
            {currentVoices.map((voice, index) => (
              <VoiceCard
                key={voice._id || index}
                voice={voice}
                isSelected={selectedVoice?._id === voice._id}
                isActive={activeVoiceId === voice._id}
                onPlay={(audioRef) => handlePlay(audioRef, voice._id)}
              />
            ))}
          </div>
          <CustomPagination
            itemsPerPage={itemsPerPage}
            totalItems={voices.length}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
        </>
      )}
    </div>
  );
}
