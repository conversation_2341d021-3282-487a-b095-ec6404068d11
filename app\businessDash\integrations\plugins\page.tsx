"use client";

import chrome from "@/public/images/Icons/integrations/chrome.svg";
import firefox from "@/public/images/Icons/integrations/firefox.svg";
import safari from "@/public/images/Icons/integrations/safari.svg";
import edge from "@/public/images/Icons/integrations/edge.png";
import { useTranslation } from "react-i18next";
import {
  IntegrationCards,
  IntegrationCardsProps,
} from "@/components/Dashboard/Cards/IntegrationCard";

export default function Integrations() {
  const { t } = useTranslation("integrations");

  const plugins: IntegrationCardsProps[] = [
    {
      // title: t("browser.title"),
      items: [
        {
          icon: firefox,
          name: t("browser.items.0"),
          bgColor: "bg-orange-200",
          comingSoon: true,
          onClick: () => console.log("Connecting to Firefox"),
        },
        {
          icon: safari,
          name: t("browser.items.1"),
          bgColor: "bg-sky-200",
          comingSoon: true,
          onClick: () => console.log("Connecting to Safari"),
        },
        {
          icon: chrome,
          name: t("browser.items.2"),
          bgColor: "bg-yellow-200",
          comingSoon: true,
          onClick: () => console.log("Connecting to Google"),
        },
        {
          icon: edge,
          name: t("browser.items.2"),
          bgColor: "bg-[#96e7e3]",
          comingSoon: true,
          onClick: () => console.log("Connecting to Edge"),
        },
      ],
    },
  ];
  return (
    <div className="rounded-2xl w-full flex flex-col gap-4">
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        {t("browser.title")}
      </h1>
      {Object.values(plugins).map((section, index) => (
        <IntegrationCards
          key={index}
          // title={section.title}
          items={section.items}
        />
      ))}
    </div>
  );
}
