"use client";

import React, { useState } from "react";
import ManageNumbers from "../popups/ManageNumbers";
import { CreateGoalDialog } from "../dialogs/CreateNewGoal";
import GoalCard from "../Dashboard/Cards/GoalCard";
import PhoneCard from "../Dashboard/Cards/PhoneCard";
import CustomPagination from "@/components/pagination/CustomPagination";
import useSWR from "swr";
import { Skeleton } from "./skeleton";
import { AssistantWithDurations } from "@/actions/AssistantActions";
import {
  getGoalsByPhoneNumber,
  getGoalsCountByPhoneId,
} from "@/actions/GoalActions";
import { AnyGoal } from "@/types";
import { useTranslation } from "react-i18next";

interface OrganigrammeItemProps {
  assistant: AssistantWithDurations;
}

interface PhoneItemProps {
  phone: AssistantWithDurations["numbers"][number];
  index: number;
  assistant_id: string;
}

const itemsPerPage = 24;
const fetcher = async (phoneId: string, page: number) => {
  const res = await getGoalsByPhoneNumber(phoneId, page, itemsPerPage);
  if (!res.success) throw new Error(res.error || "Failed to fetch goals");
  return {
    goals: res.goals || [],
    totalGoals: res.totalPages ? res.totalPages * itemsPerPage : 0,
    currentPage: res.currentPage,
  };
};

const PhoneItem: React.FC<PhoneItemProps> = ({ phone, assistant_id }) => {
  const { t } = useTranslation("assistants");
  const [currentPage, setCurrentPage] = useState(1);
  const {
    data,
    isLoading: goalsLoading,
    error,
  } = useSWR(
    ["goals-by-phone", phone._id, currentPage],
    () => fetcher(phone._id, currentPage),
    {
      revalidateOnFocus: false,
      keepPreviousData: false,
    }
  );

  const { data: countData, isLoading: countLoading } = useSWR(
    ["goals-count-by-phone", phone._id],
    () => getGoalsCountByPhoneId(phone._id),
    { revalidateOnFocus: false }
  );

  const currentGoals = data?.goals || [];
  const totalGoals = countData?.count ?? 0;

  return (
    <div className="p-2 sm:p-3 flex flex-col items-center gap-3 rounded-xl bg-voxa-neutral-100/80 dark:bg-voxa-neutral-950 w-full">
      <div className="grid grid-cols-1 lg:grid-cols-3 max-lg:gap-y-2 lg:gap-x-2 items-center w-full">
        <PhoneCard phone={phone} />
        <CreateGoalDialog assistant_id={assistant_id} phone_id={phone._id} />
      </div>
      {/* Goals Section */}
      {goalsLoading || countLoading ? (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-2 w-full">
          {Array.from({ length: itemsPerPage }).map((_, idx) => (
            <Skeleton key={idx} className="h-9 w-full" />
          ))}
        </div>
      ) : error ? (
        <p className="text-center text-sm text-red-500">
          {t("organigramme.failedToLoadGoals")}
        </p>
      ) : totalGoals > 0 ? (
        <div className="w-full">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-2 w-full">
            {currentGoals.map((goal: AnyGoal, idx: number) => (
              <GoalCard key={idx} goal={goal} />
            ))}
          </div>
        </div>
      ) : (
        <p className="text-center text-sm">{t("organigramme.noGoalsFound")}</p>
      )}
      <CustomPagination
        className="-mt-1"
        itemsPerPage={itemsPerPage}
        totalItems={totalGoals}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
      />
    </div>
  );
};

const OrganigrammeItem: React.FC<OrganigrammeItemProps> = ({ assistant }) => {
  return (
    <div>
      <div className="max-w-screen-2xl mx-auto px-4 w-full flex gap-1 sm:gap-3 items-end justify-end">
        <p className="max-sm:w-full text-center h-full py-2 px-4 font-medium bg-voxa-neutral-50 dark:bg-voxa-neutral-900 rounded-t-lg text-sm">
          {assistant.name}
        </p>
        <ManageNumbers className="max-sm:hidden" />
      </div>
      <div className="max-w-screen-2xl mx-auto flex flex-col bg-voxa-neutral-50 dark:bg-voxa-neutral-900 w-full items-center p-4 gap-3 rounded-2xl">
        <ManageNumbers className="w-full sm:hidden rounded-lg" />
        <div className="w-full flex flex-col gap-4">
          {assistant.numbers.map((phone, index: number) => (
            <PhoneItem
              key={index}
              phone={phone}
              index={index}
              assistant_id={assistant._id?.toString()}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default OrganigrammeItem;
