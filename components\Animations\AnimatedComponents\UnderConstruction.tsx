"use client";

import <PERSON><PERSON> from "lottie-react";
import animationData from "../JSON/UnderConstruction2.json";
import { useEffect, useState } from "react";

const LottieAnimation = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null

  return (
    <div className="flex justify-center items-center w-full h-full">
      <Lottie animationData={animationData} loop className="w-[30rem] h-[30rem]" />
    </div>
  );
};

export default LottieAnimation;
