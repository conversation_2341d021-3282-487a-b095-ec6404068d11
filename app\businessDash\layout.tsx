"use client";

import BusinessAdminDashSidebar from "@/components/Sidebars/BusinessAdminDashSidebar";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { fetchEntreprise } from "@/redux/BusinessDashboard/subSlices/EntrepriseSlice";
import { AppDispatch } from "@/redux/store";
import clsx from "clsx";
import React, { useEffect } from "react";
import { useDispatch } from "react-redux";

export default function BuisnessDashLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [open, setOpen] = React.useState<boolean>(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("defaultOpen") !== "false";
    }
    return true;
  });
  React.useEffect(() => {
    localStorage.setItem("defaultOpen", open.toString());
  }, [open]);

  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    console.log("Business Dashboard Layout mounted");
    dispatch(
      fetchEntreprise([
        "name",
        "admin",
        "siret",
        "kbis",
        "sepa",
        "rib",
        "phone",
        "corpName",
        "country",
        "region",
        "street",
        "appartment",
        "legalRepresentantName",
        "legalRepresentantIdendity",
        "cpa",
        "field",
        "credit",
        "balance",
        "balanceAlertThreshold",
        "representantIsOwner",
        "current_subscription",
        "created_at",
      ])
    );
  }, [dispatch]);

  return (
    <SidebarProvider defaultOpen={open} open={open} onOpenChange={setOpen}>
      <BusinessAdminDashSidebar />
      <SidebarTrigger className="max-md:fixed top-3 sm:top-4 left-4 sm:left-8 z-50 md:hidden" />
      <SidebarInset
        className={clsx(
          "min-h-screen text-voxa-neutral-700 dark:text-voxa-neutral-200 py-4 sm:py-6 px-4 sm:px-8 md:px-10 lg:px-12 w-full"
        )}
      >
        {children}
      </SidebarInset>
    </SidebarProvider>
  );
}
