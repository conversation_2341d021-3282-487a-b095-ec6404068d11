import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils"; // Assuming you have a cn utility from shadcn/ui

export interface ExportDropdownItem {
  title: string;
  action?: () => Promise<void> | void;
  subItems?: ExportDropdownItem[];
}

interface ExportDropdownProps {
  items: ExportDropdownItem[];
  buttonText?: string;
  loadingText?: string;
  position?: "top-right" | "bottom-right" | "top-left" | "bottom-left";
  buttonClassName?: string;
  popoverClassName?: string;
  buttonVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  buttonSize?: "default" | "sm" | "lg" | "icon";
  className?: string; // Added className prop for the container
  isExporting?: boolean;
}

const ExportDropdown = ({
  items,
  buttonText = "Export",
  loadingText = "Exporting...",
  buttonClassName,
  popoverClassName,
  buttonVariant = "outline",
  buttonSize = "sm",
  className,
  isExporting = false,
}: ExportDropdownProps) => {
  const handleItemClick = async (item: ExportDropdownItem) => {
    if (!item.action) return;
    const analyticsExportElements =
      document.querySelectorAll(".analytics-export");
    const analyticsExportPopoverElements = document.querySelectorAll(
      ".analytics-export-popover"
    );
    try {
      analyticsExportElements.forEach((element) => {
        element.classList.add("hidden");
      });
      analyticsExportPopoverElements.forEach((element) => {
        element.classList.add("hidden");
      });
      await item.action();
    } catch (error) {
      console.error("Export action failed:", error);
    } finally {
      analyticsExportElements.forEach((element) => {
        element.classList.remove("hidden");
      });
      analyticsExportPopoverElements.forEach((element) => {
        element.classList.remove("hidden");
      });
    }
  };

  // Recursive render for subItems
  const renderMenuItems = (items: ExportDropdownItem[]) =>
    items.map((item, index) =>
      item.subItems && item.subItems.length > 0 ? (
        <DropdownMenuSub key={index}>
          <DropdownMenuSubTrigger
            className={cn(
              "w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-voxa-neutral-700 text-sm transition-colors",
              index === 0 && "first:rounded-t-sm",
              index === items.length - 1 && "last:rounded-b-sm"
            )}
            disabled={isExporting}
          >
            {item.title}
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            {renderMenuItems(item.subItems)}
          </DropdownMenuSubContent>
        </DropdownMenuSub>
      ) : (
        <DropdownMenuItem
          key={index}
          className={cn(
            "w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-voxa-neutral-700 text-sm transition-colors",
            "cursor-pointer", // Added cursor-pointer
            index === 0 && "first:rounded-t-sm",
            index === items.length - 1 && "last:rounded-b-sm"
          )}
          onClick={() => handleItemClick(item)}
          disabled={isExporting}
        >
          {item.title}
        </DropdownMenuItem>
      )
    );

  return (
    <div className={cn("analytics-export z-10 !mt-0", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            size={buttonSize}
            variant={buttonVariant}
            aria-haspopup="menu"
            disabled={isExporting}
            className={cn(
              "bg-white dark:bg-voxa-neutral-800 hover:bg-gray-100 dark:hover:bg-voxa-neutral-700 py-1",
              buttonClassName
            )}
          >
            {isExporting ? loadingText : buttonText}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className={cn(
            "w-44 p-0 dark:bg-voxa-neutral-800 border dark:border-voxa-neutral-700 overflow-hidden analytics-export-popover",
            popoverClassName
          )}
        >
          {renderMenuItems(items)}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default ExportDropdown;
