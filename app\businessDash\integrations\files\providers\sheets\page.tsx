'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function GoogleSheetsPage() {
  const [rows, setRows] = useState<string[][] | null>(null);
  const [error, setError] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  const fetchSheets = async () => {
    try {
      setLoading(true);
      setError(null);

      const res = await fetch('/api/sheets/data');
      if (res.status !== 200) {
        setError(res.status);
        return;
      }

      const data = await res.json();
      setRows(data);
    } catch (err) {
      setRows(null);
      setError(500);
    } finally {
      setLoading(false);
    }
  };

  const disconnect = async () => {
    await fetch('/api/auth/google/logout', { method: 'POST' });
    setRows(null);
    setError(null);
    setLoading(false);
    router.refresh();
  };

  useEffect(() => {
    fetchSheets();
  }, []);

  // Not authorized
  if (error === 401) {
    return (
      <div className="p-10">
        <button
          className="px-4 py-2 bg-green-600 text-white rounded"
          onClick={() => (window.location.href = '/api/auth/google/sheets')}
        >
          Connect Google Sheets
        </button>
      </div>
    );
  }

  // Loading state
  if (loading) {
    return <p className="p-10 text-gray-500">Chargement des données Sheets…</p>;
  }

  // No rows
  if (!rows || rows.length === 0) {
    return (
      <div className="p-10">
        <p className="text-gray-500">Aucune donnée trouvée dans la feuille.</p>
        <button className="mt-4 text-sm text-blue-600 hover:underline" onClick={fetchSheets}>
          ↻ Réessayer
        </button>
        <button className="block mt-6 text-sm text-red-600 hover:underline" onClick={disconnect}>
          ⎋ Se déconnecter
        </button>
      </div>
    );
  }

  // Connected + data view
  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-start justify-between">
        <h1 className="text-2xl font-semibold mb-4">Données Google Sheets</h1>
        <button className="text-sm text-red-600 hover:underline" onClick={disconnect}>
          ⎋ Se déconnecter
        </button>
      </div>

      <table className="w-full text-sm text-left text-gray-600 border border-gray-200 bg-white shadow">
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr key={rowIndex} className="border-b">
              {row.map((cell, colIndex) => (
                <td key={colIndex} className="px-4 py-2">
                  {cell}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      <div className="mt-8 flex items-center gap-4">
        <button className="text-sm text-gray-500 hover:underline" onClick={fetchSheets}>
          ↻ Rafraîchir
        </button>
      </div>
    </div>
  );
}
