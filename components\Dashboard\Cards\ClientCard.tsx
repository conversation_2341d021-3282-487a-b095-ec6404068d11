"use client";

import {
  convertSecondsToMinutes,
  getTimeFromTimestamp,
} from "@/lib/Strings/DateFormat";
import {
  ArrowDownLeft,
  ArrowUpRight,
  DownloadIcon,
  ForwardIcon,
  MessageSquareReplyIcon,
  MessageSquareShareIcon,
  PhoneIncomingIcon,
  PhoneMissedIcon,
  PhoneOutgoingIcon,
  RedoDotIcon,
  ScrollText,
  Text,
  VoicemailIcon,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTransition } from "react";
import {
  handleOpenCallDetails,
  handleTranscriptionOpen,
  setCallTranscriptOpen,
  setIsHistoryVisible,
  setIsTranscriptVisible,
  setNotesOpen,
} from "@/redux/BusinessDashboard/subSlices/RootSlice";
import { useDispatch, useSelector } from "react-redux";
import { getPresignedUrl } from "@/actions/S3Audio";
import { IoLogoWhatsapp } from "react-icons/io";
import { RootState, AppDispatch } from "@/redux/store";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";
import { useTranslation } from "react-i18next";
import GoogleMeetIcon from "@/public/images/Icons/google_meet.svg";
import Image from "next/image";
import { cn } from "@/lib/utils";

export default function ClientCard({ call }: { call: any }) {
  const { t, i18n } = useTranslation("callLogs");
  const dispatch = useDispatch<AppDispatch>();
  const { callDetails, callTranscriptOpen, NotesOpen } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardRoot
  );
  const [isPending, startTransition] = useTransition();

  const handleDownloadRecording = async () => {
    startTransition(async () => {
      const key = call.RecordingURL.split(
        "callbot-voice-recordings.s3.eu-west-3.amazonaws.com/"
      )[1];
      if (!key) {
        console.error("Failed to extract key from URL");
        return;
      }
      const signedUrl = await getPresignedUrl(key);
      if (!signedUrl) {
        console.error("Failed to get signed URL");
        return;
      }
      const a = document.createElement("a");
      a.href = signedUrl;
      a.download = key.split("/").pop() || "audio.mp3";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    });
  };

  const openCallNotes = () => {
    dispatch(setIsTranscriptVisible(false));
    setTimeout(() => {
      dispatch(setCallTranscriptOpen(false));
    }, 301);
    dispatch(handleOpenCallDetails(call));
  };

  const openCallTranscript = () => {
    dispatch(setIsHistoryVisible(false));
    setTimeout(() => {
      dispatch(setNotesOpen(false));
    }, 301);
    dispatch(handleTranscriptionOpen(call));
  };

  return (
    <div className="relative text-xs md:text-sm">
      {call.answered_and_vm_detected ? (
        <div className="absolute -translate-y-2 left-2 z-10 bg-black/80 dark:bg-black  text-voxa-teal-600 font-semibold px-2 py-0.5 rounded-full shadow-sm">
          vm dropped
        </div>
      ) : call.missed_and_vm_dropped ? (
        <div className="absolute -translate-y-2 left-2 z-10 bg-black/80 dark:bg-black  text-voxa-teal-600 font-semibold px-2 py-0.5 rounded-full shadow-sm">
          vm dropped
        </div>
      ) : (
        ""
      )}
      <div
        key={call._id}
        onClick={openCallNotes}
        className={cn(
          "w-full rounded-xl border-[1.5px] items-center relative transition-colors cursor-pointer grid grid-cols-2 lg:flex lg:justify-between gap-y-3 px-0.5 sm:px-2 lg:px-4 py-3 bg-sidebar border-sidebar-border",
          {
            "hover:bg-gradient-to-tr from-orange-700/20 to-orange-500/10":
              call.type?.toLowerCase() === "meet",
            "hover:bg-gradient-to-tr from-teal-700/20 to-teal-500/10":
              call.type?.toLowerCase() === "sms" ||
              call.type?.toLowerCase() === "whatsapp",
            "hover:bg-gradient-to-tr from-purple-700/20 to-purple-500/10":
              call.is_transfer,
            "hover:bg-gradient-to-tr from-indigo-700/20 to-indigo-500/10":
              call.Direction === "inbound",
            "hover:bg-gradient-to-tr from-indigo-500/20 to-indigo-500/10":
              call.is_voicemail_drop === true ||
              call.voicemailDetected === true,
            "hover:bg-gradient-to-tr from-red-700/20 to-red-500/10":
              call.CallStatus === "missed" ||
              call.CallStatus === "no-answer" ||
              call.CallDuration == 0,
            "hover:bg-gradient-to-tr from-green-700/20 to-green-500/10": !(
              call.type?.toLowerCase() === "sms" ||
              call.type?.toLowerCase() === "whatsapp" ||
              call.is_transfer ||
              call.Direction === "inbound" ||
              call.is_voicemail_drop === true ||
              call.voicemailDetected === true ||
              call.CallStatus === "missed" ||
              call.CallStatus === "no-answer" ||
              call.CallDuration == 0
            ),
            "bg-voxa-neutral-100 dark:bg-voxa-neutral-900 border-foreground dark:border-voxa-neutral-400":
              callDetails?.CallSid === call.CallSid &&
              (NotesOpen || callTranscriptOpen),
          }
        )}
      >
        {/* Tag indicators at the bottom */}
        {call.tags && call.tags.length > 0 && (
          <div className="w-full absolute bottom-[0.25px] lg:left-2.5 flex max-lg:justify-center h-1.5">
            {call.tags.map((tag: any, index: number) => (
              <Tooltip key={index}>
                <TooltipTrigger>
                  <div
                    className={cn(
                      "border-[1.5px] border-l-0 border-b-0 border-sidebar-border w-10 sm:w-12 h-1.5",
                      index === 0 && "rounded-l-full border-l-[1.5px]",
                      index === call.tags.length - 1 && "rounded-r-full",
                      {
                        "border-foreground dark:border-voxa-neutral-100":
                          callDetails?.CallSid === call.CallSid &&
                          (NotesOpen || callTranscriptOpen),
                      }
                    )}
                    style={{ backgroundColor: tag.color }}
                  />
                </TooltipTrigger>
                <TooltipContent>{tag.name}</TooltipContent>
              </Tooltip>
            ))}
          </div>
        )}

        {(call.is_voicemail_drop === true ||
          call.voicemailDetected === true) && (
          <div className="absolute -translate-y-9 left-1 z-10 bg-black/80 dark:bg-black  text-voxa-teal-600 text-xs font-semibold px-2 py-0.5 rounded-full shadow-sm">
            {call.is_voicemail_drop === true
              ? t("stats.dropped")
              : t("stats.detected")}
          </div>
        )}
        <div className="w-full flex gap-1 sm:gap-2 items-center flex-row justify-center lg:justify-start">
          {call.is_voicemail_drop === true ||
          call.voicemailDetected === true ? (
            <div className="bg-indigo-500/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
              <VoicemailIcon className="text-indigo-500 w-5 h-5 mr-px" />
            </div>
          ) : call.type?.toLowerCase() === "meet" ? (
            <div className="bg-orange-500/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
              <Image
                src={GoogleMeetIcon}
                alt="Google Meet Icon"
                className="w-5 h-5 mr-px"
              />
            </div>
          ) : call.type?.toLowerCase() === "sms" ? (
            call.Direction?.toLowerCase() === "outbound_api" ? (
              <div className="bg-teal-700/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
                <MessageSquareShareIcon className="text-teal-500 w-5 h-5 mr-px" />
              </div>
            ) : call.Direction?.toLowerCase() === "inbound" ? (
              <div className="bg-teal-700/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
                <MessageSquareReplyIcon className="text-teal-500 w-5 h-5 mr-px" />
              </div>
            ) : (
              ""
            )
          ) : call.type?.toLowerCase() === "whatsapp" ? (
            call.Direction?.toLowerCase() === "outbound_api" ? (
              <div className="relative w-fit h-fit">
                <div className="bg-teal-700/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
                  <IoLogoWhatsapp className="text-teal-500 w-5 h-5 mr-px" />
                </div>
                <ArrowUpRight
                  className="absolute -top-1 right-0 text-teal-500 w-4 h-4"
                  strokeWidth={3}
                />
              </div>
            ) : call.Direction?.toLowerCase() === "inbound" ? (
              <div className="relative w-fit h-fit">
                <div className="bg-teal-700/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
                  <IoLogoWhatsapp className="text-teal-500 w-5 h-5 mr-px" />
                </div>
                <ArrowDownLeft
                  className="absolute -top-1 right-0 text-teal-500 w-4 h-4"
                  strokeWidth={3}
                />
              </div>
            ) : (
              ""
            )
          ) : call.is_transfer ? (
            <div className="bg-purple-500/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
              <RedoDotIcon className="text-purple-500 w-5 h-5 mr-px" />
            </div>
          ) : call.Direction === "inbound" ? (
            <div className="bg-indigo-500/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
              <PhoneIncomingIcon className="text-indigo-500 w-5 h-5 mr-px" />
            </div>
          ) : call.CallStatus === "missed" ||
            call.CallStatus === "no-answer" ||
            call.CallDuration == 0 ? (
            <div className="bg-red-500/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
              <PhoneMissedIcon className="text-red-500 w-5 h-5 mr-px" />
            </div>
          ) : (
            <div className="bg-green-500/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
              <PhoneOutgoingIcon className="text-green-500 w-5 h-5 mr-px" />
            </div>
          )}
          <div className="flex gap-1 sm:gap-2 items-center flex-col">
            {call.client_name && (
              <p className="w-full text-black/80 dark:text-voxa-neutral-50 truncate">
                {call.client_name}
              </p>
            )}
            <p
              className={`w-full text-black/60 dark:text-voxa-neutral-200 text-nowrap max-sm:text-[11px]`}
            >
              {phoneNumberFormat(call.To, call.country)}
            </p>
          </div>
        </div>
        <div className="w-full flex gap-1 sm:gap-2 items-center flex-col justify-center text-center">
          <p className="text-black/80 dark:text-voxa-neutral-50 font-semibold">
            {getTimeFromTimestamp(call.TimeStamp, i18n.language)
              .split(",")
              .slice(0, 3)
              .join(", ")}
          </p>
          <p className="text-black/60 dark:text-voxa-neutral-200 max-sm:text-[11px]">
            {getTimeFromTimestamp(call.TimeStamp).split(",")[3]}
            {", "}
            {convertSecondsToMinutes(
              call.recording_duration || call.CallDuration
            )}
          </p>
        </div>
        <div className="w-full flex gap-1 sm:gap-2 items-center justify-center lg:justify-end text-center lg:text-end">
          {call.assistant_name && (
            <p className="text-lg text-black/80 dark:text-voxa-neutral-50">
              {call.assistant_name}
            </p>
          )}
          <p className={`text-black/60 dark:text-voxa-neutral-200 w-full`}>
            {phoneNumberFormat(call.From, call.country)}
          </p>
        </div>
        <div className="w-full flex gap-0.5 items-center flex-row justify-center lg:justify-end">
          {call.who_hang_up?.includes("Transfer to") ? (
            <Tooltip>
              <TooltipTrigger
                className="group size-8 flex items-center justify-center rounded-full hover:bg-blue-400/20 transition-colors p-1.5"
                asChild
                onClick={(e) => e.stopPropagation()}
              >
                <div>
                  <ForwardIcon className="size-5 text-blue-400" />
                </div>
              </TooltipTrigger>
              <TooltipContent className="fill-blue-400 bg-blue-400">
                <p>Call Transferred</p>
              </TooltipContent>
            </Tooltip>
          ) : (
            <div className="size-7" />
          )}

          {call.RecordingURL ? (
            <Tooltip>
              <TooltipTrigger
                className="group size-8 flex items-center justify-center rounded-full hover:bg-purple-400/20 transition-colors p-1.5"
                asChild
                aria-disabled={isPending}
                onClick={(e) => {
                  e.stopPropagation();
                  if (!isPending) handleDownloadRecording();
                }}
              >
                <div className="flex items-center justify-center">
                  <DownloadIcon
                    className={`size-6 ${
                      isPending
                        ? "text-purple-300 cursor-not-allowed"
                        : "text-purple-400 cursor-pointer"
                    }`}
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent className="fill-purple-400 bg-purple-400">
                <p>{isPending ? "Downloading..." : "Download Recording"}</p>
              </TooltipContent>
            </Tooltip>
          ) : (
            <div className="size-7" />
          )}

          {call.transcript &&
          call.transcript.length !== 0 &&
          call.CallDuration !== 0 &&
          call.CallStatus.toLowerCase() !== "missed" &&
          call.CallStatus.toLowerCase() !== "no-answer" ? (
            <Tooltip>
              <TooltipTrigger
                className="group size-8 flex items-center justify-center rounded-full hover:bg-orange-400/20 transition-colors p-1.5"
                asChild
                onClick={(e) => {
                  e.stopPropagation();
                  openCallTranscript();
                }}
              >
                <div className="flex items-center justify-center">
                  <ScrollText className="size-6 text-orange-400" />
                </div>
              </TooltipTrigger>
              <TooltipContent className="fill-orange-400 bg-orange-400">
                <p>Open Transcript</p>
              </TooltipContent>
            </Tooltip>
          ) : (
            <div className="size-7" />
          )}

          <Tooltip>
            <TooltipTrigger
              className="group size-8 flex items-center justify-center rounded-full hover:bg-gray-400/20 transition-colors p-1.5"
              asChild
            >
              <Text className="size-5 text-gray-400" />
            </TooltipTrigger>
            <TooltipContent>
              {call.type?.toLowerCase() === "sms" ? (
                <p>View SMS</p>
              ) : (
                <p>Call Details</p>
              )}
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </div>
  );
}
