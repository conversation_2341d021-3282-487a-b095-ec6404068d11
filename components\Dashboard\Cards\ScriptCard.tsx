import { Calendar, FileText, <PERSON><PERSON>en, Trash2 } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";

type ScriptCardProps = {
  script: any;
  date: string;
  t: (key: string) => string;
  handleDeleteButton: (id: any) => void;
  handleUpdateButton: (id: any) => void;
  handleClickEyeIcon: (content: any) => void;
};

const ScriptCard: React.FC<ScriptCardProps> = ({
  script,
  date,
  t,
  handleUpdateButton,
  handleDeleteButton,
  handleClickEyeIcon,
}) => {
  return (
    <div className="h-full relative group bg-sidebar border-[1.5px] border-sidebar-border rounded-xl flex flex-col gap-2.5 transition-all duration-300 cursor-pointer hover:scale-102 backdrop-blur min-w-0 p-4 pr-12">
      <div className="h-full px-2 py-2.5 absolute top-0 right-0 flex flex-col justify-center">
        {/* Edit button */}
        <button
          className="text-voxa-neutral-600 transition-all pointer-events-auto z-50 w-[34px] h-[34px] p-2 pb-1.5 rounded-t-full flex items-center justify-center border border-b-0 border-sidebar-border group-hover:bg-voxa-neutral-50 group-hover:dark:bg-voxa-neutral-950 hover:scale-105 duration-200"
          onClick={() => handleUpdateButton(script.id)}
        >
          <SquarePen />
        </button>
        {/* Delete button */}
        <button className="text-red-600 transition-all pointer-events-auto z-50 w-[34px] h-[34px] p-2 pt-1.5 rounded-b-full flex items-center justify-center border border-sidebar-border group-hover:bg-voxa-neutral-50 group-hover:dark:bg-voxa-neutral-950 hover:scale-105  duration-200">
          <Trash2 onClick={() => handleDeleteButton(script.id)} />
        </button>
      </div>

      {/* Header Section with Icon */}
      <div
        className="text-voxa-teal-500 text-start flex items-center gap-1 text-lg"
        style={{ fontWeight: 700 }}
      >
        <FileText className="size-[18px]" />
        <p className="truncate">{script.name}</p>
      </div>

      <div className="w-full flex max-sm:flex-col sm:items-center gap-2 sm:gap-1.5 text-medium">
        {/* Uploaded At */}
        <p className="text-nowrap flex gap-1 w-fit text-sm text-voxa-neutral-700 dark:text-voxa-neutral-200">
          <Calendar className="dark:text-voxa-neutral-50 size-[18px]" />
          {date}
        </p>{" "}
        {/* Tags */}
        <div className="max-sm:pl-8 z-50 w-full h-full flex gap-0.5 justify-center sm:justify-end">
          {script.tags.map((tag: any, idx: number) => (
            <Tooltip key={idx}>
              <TooltipTrigger asChild>
                <button
                  className="w-4 h-4 rounded-full border-2 group-hover:border-sidebar-border flex items-center justify-center transition-all duration-150"
                  style={{ background: tag.color }}
                />
              </TooltipTrigger>
              <TooltipContent sideOffset={-4} className="font-medium border-2">
                {tag.name || "No name"}
              </TooltipContent>
            </Tooltip>
          ))}
        </div>
      </div>
      {/* Hover Content */}
      <div className="absolute inset-0 bg-gradient-to-t from-voxa-neutral-500/60 dark:from-voxa-neutral-800/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-xl flex items-start justify-center pointer-events-none backdrop-blur-sm h-full">
        <Tooltip>
          <TooltipTrigger asChild className="mx-auto flex items-center gap-2">
            <button
              onClick={() => handleClickEyeIcon(script.content)}
              className="mt-3 p-2 rounded-full shadow-md transition-all text-voxa-neutral-600 dark:text-voxa-neutral-200 border-2 border-sidebar-border font-semibold text-xs pointer-events-auto group-hover:bg-voxa-neutral-50 group-hover:dark:bg-voxa-neutral-950 hover:scale-103"
            >
              {t("page.viewContent")}
            </button>
          </TooltipTrigger>
          <TooltipContent className="font-medium border-2">
            {script.name}
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
};

export default ScriptCard;
