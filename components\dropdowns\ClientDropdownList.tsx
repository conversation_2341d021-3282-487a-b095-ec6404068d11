import {
  // ReceiptText,
  // Power,
  // GitCommitVertical,
  // Co<PERSON>,
  Ellipsis,
  Goal,
} from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState } from "react";
import { toast } from "sonner";
import {
  AddClientToGoal,
  getAllEntrepriseGoals,
  getClientGoalName,
} from "@/actions/ClientsActions";
// import { set } from "mongoose";
import CircularLoaderSmall from "../Loaders/CircularLoaderSmall";

export function ClientDropDownList({ clientID }: { clientID: string }) {
  const [clientGoal, setClientGoal] = useState<any>(null);
  const [EntrepriseGoals, setEntrepriseGoals] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  const getClientGoal = async () => {
    try {
      const response = await getClientGoalName(clientID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      if (response.goal) {
        setClientGoal(response.goal);
      }
      if (response.error && response.error === "No Goal") {
        setClientGoal("No Goal");
        const EntrepriseGoalsResponse = await getAllEntrepriseGoals(clientID);
        if (!EntrepriseGoalsResponse.success) {
          toast.error(EntrepriseGoalsResponse.error);
          return;
        }
        setEntrepriseGoals(EntrepriseGoalsResponse.goals || []);
      }
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to get client goal");
    } finally {
      setLoading(false);
    }
  };

  const AddClientToEntrepriseGoal = async (goalID: string) => {
    try {
      setClientGoal("");
      setEntrepriseGoals([]);
      const response = await AddClientToGoal(clientID, goalID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("Client added to goal");
    } catch (err) {
      console.error(err);
      toast.error("Failed to add client to goal");
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild onPointerDown={getClientGoal}>
        <Ellipsis className="dark:text-voxa-neutral-200 cursor-pointer" />
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel>
          <span>Client Settings</span>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {clientGoal && (
          <DropdownMenuGroup>
            <DropdownMenuItem disabled={clientGoal === "No Goal"}>
              <Goal />
              <span className="text-black"> {clientGoal} </span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        )}
        {loading ? (
          <div className="w-full flex justify-center items-center">
            <CircularLoaderSmall />
          </div>
        ) : (
          EntrepriseGoals.length > 0 && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuLabel className="font-medium">
                <span>Add Client to Goal</span>
              </DropdownMenuLabel>
              {EntrepriseGoals.map((goal, index) => {
                return (
                  <DropdownMenuItem
                    key={index}
                    onClick={() => AddClientToEntrepriseGoal(goal._id)}
                  >
                    <span> {goal.name} </span>
                  </DropdownMenuItem>
                );
              })}
            </>
          )
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
