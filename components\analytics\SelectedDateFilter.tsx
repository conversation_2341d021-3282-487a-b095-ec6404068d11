// src/components/analytics/SelectedDateFilter.tsx
"use client";

import React from "react";
import AccessTimeRoundedIcon from "@mui/icons-material/AccessTimeRounded";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { parseISO, format } from "date-fns";

export default function SelectedDateFilter() {
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.analytics.filters
  );

  const formattedStart = startDate
    ? format(parseISO(startDate), "dd MMM yyyy")
    : "—";
  const formattedEnd = endDate ? format(parseISO(endDate), "dd MMM yyyy") : "—";

  // Determine if we should show "All Time" or the date range
  const displayText =
    !startDate && !endDate ? "All Time" : `${formattedStart} / ${formattedEnd}`;

  return (
    <div className="w-full sm:w-fit bg-white dark:bg-voxa-neutral-800 hover:bg-gray-100 dark:hover:bg-voxa-neutral-700 rounded-sm py-2 px-3 border dark:border-voxa-neutral-700 text-sm flex items-center gap-2">
      <AccessTimeRoundedIcon
        fontSize="small"
        className="text-voxa-neutral-500 dark:text-voxa-neutral-400"
      />
      <span className="text-sm text-voxa-neutral-600 dark:text-voxa-neutral-300">
        {displayText}
      </span>
    </div>
  );
}
