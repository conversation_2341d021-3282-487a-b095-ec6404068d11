"use server";

import authOptions from "@/lib/AuthOptions";
import dbConnect from "@/lib/mongodb";
import Entreprise from "@/models/Entreprise";
import { User } from "@/models/User";
import {
  ENTREPRISE_ALLOWED_FIELDS,
  ENTREPRISE_ALLOWED_FIELDS_TYPE,
} from "@/redux/BusinessDashboard/subSlices/EntrepriseSlice";
import { Entreprise as EntrepriseType } from "@/types";
import { getServerSession } from "next-auth";

export const getEntrepriseByAdminID = async (): Promise<{
  success: boolean;
  error?: string;
  entreprise?: any;
}> => {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }
    const adminID = session.user.id;
    await dbConnect();
    const entreprise = await Entreprise.findOne({ admin: adminID });
    if (!entreprise) {
      return { success: false, error: "Entreprise not found" };
    }
    return { success: true, entreprise: entreprise };
  } catch (e: any) {
    console.log(e.message);
    return { success: false, error: e.message };
  }
};

export async function getEntrepriseDetails() {
  try {
    const entreprise = await getEntrepriseByAdminID();
    const enterpriseDetails = {
      kbis: entreprise.entreprise.kbis,
      siret: entreprise.entreprise.siret,
      sepa: entreprise.entreprise.sepa,
      rib: entreprise.entreprise.rib,
      phone: entreprise.entreprise.phone,
      corname: entreprise.entreprise.corpName,
      country: entreprise.entreprise.country,
      city: entreprise.entreprise.region,
      street: entreprise.entreprise.street,
      appnb: entreprise.entreprise.appartment,
      name: entreprise.entreprise.legalRepresentantName,
      cin: entreprise.entreprise.legalRepresentantIdendity,
      poa: entreprise.entreprise.cpa,
      isOwner: entreprise.entreprise.representantIsOwner,
    };
    return { success: true, entreprise: enterpriseDetails };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function getEntrepriseAgents() {
  try {
    const entreprise = await getEntrepriseByAdminID();
    const agents = await User.find({
      entreprise: entreprise.entreprise._id,
      role: "ENTREPRISE_AGENT",
    });
    const formattedAgents = agents.map((agent: any) => {
      return {
        _id: agent._id.toString(),
        name: agent.name,
        email: agent.email,
        phone: agent.phone,
        status: agent.status,
        createdAt: agent.createdAt,
      };
    });
    return { success: true, agents: formattedAgents };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export const udpateSiretAndAdresse = async ({
  adresse,
  siret,
}: {
  adresse: string;
  siret: string;
}) => {
  try {
    const response = await getEntrepriseByAdminID();
    if (!response.success) {
      return {
        success: false,
        error: response.error || "Utilisateur non connecté",
      };
    }

    const entreprise_id = response.entreprise._id;

    const existing = await Entreprise.findOne({
      siret,
      _id: { $ne: entreprise_id },
    });

    if (existing) {
      return {
        success: false,
        error: "Ce numéro SIRET est déjà utilisé par une autre entreprise.",
      };
    }

    const updatedEntreprise = await Entreprise.findByIdAndUpdate(
      entreprise_id,
      { siret, appartment: adresse },
      { new: true }
    );

    if (!updatedEntreprise) {
      return {
        success: false,
        error: "Entreprise introuvable",
      };
    }

    return {
      success: true,
    };
  } catch (err: any) {
    return {
      success: false,
      error: err.message || "Une erreur est survenue",
    };
  }
};

// Fetch entreprise by admin ID, with specified fields
export const getEntrepriseByAdminWithFields = async (
  fields: ENTREPRISE_ALLOWED_FIELDS_TYPE[] | undefined = Array.from(
    ENTREPRISE_ALLOWED_FIELDS
  )
): Promise<{
  success: boolean;
  error?: string;
  data?: Partial<EntrepriseType>;
}> => {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }
    const adminID = session.user.id;
    await dbConnect();

    // Filter requested fields to only allowed attributes
    const selectedFields = fields?.filter((f) =>
      ENTREPRISE_ALLOWED_FIELDS.includes(f)
    );
    if (selectedFields.length === 0) {
      return { success: false, error: "No valid fields requested" };
    }

    const entreprise = await Entreprise.findOne({ admin: adminID }).select(
      selectedFields.join(" ")
    );
    if (!entreprise) {
      return { success: false, error: "Entreprise not found" };
    }

    const data = JSON.parse(JSON.stringify(entreprise));

    return { success: true, data };
  } catch (e: any) {
    console.log(e.message);
    return { success: false, error: e.message };
  }
};
