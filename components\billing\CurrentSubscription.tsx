"use client";

import use<PERSON><PERSON> from "swr";
import {
  cancelSubscription,
  getCurrentSubscription,
  reactivateSubscription,
} from "@/actions/BillingActions";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plan, Subscription } from "@/types";
import {
  AlertCircle,
  Calendar,
  Check,
  Crown,
  Eye,
  RefreshCw,
  X,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { renderTemplate } from "@/lib/renderTemplate";
import { featurePeriodMap } from "@/lib/billingUtils";

interface CurrentSubscriptionProps {
  className?: string;
}

export default function CurrentSubscription({
  className,
}: CurrentSubscriptionProps) {
  // Remove local subscription/isLoading/isRefreshing state, use S<PERSON> instead
  const { data, error, isLoading, mutate, isValidating } = useSWR(
    "current-subscription",
    getCurrentSubscription
  );

  const [isProcessingAction, setIsProcessingAction] = useState(false);

  // subscription comes from SWR data
  const subscription = data?.success ? data.data : null;

  // Refresh handler using SWR mutate
  const handleRefresh = async () => {
    await mutate();
  };

  const handleCancelSubscription = async () => {
    if (!subscription) return;

    try {
      setIsProcessingAction(true);
      const response = await cancelSubscription(subscription._id);
      if (response.success) {
        toast.success(
          "Renewal canceled. Your subscription will end after the current period."
        );
        await mutate(); // Refresh SWR cache
      } else {
        toast.error(response.error || "Failed to cancel subscription");
      }
    } catch (error: any) {
      console.error("Error canceling subscription:", error);
      toast.error("Failed to cancel subscription");
    } finally {
      setIsProcessingAction(false);
    }
  };

  const handleReactivateSubscription = async () => {
    if (!subscription) return;

    try {
      setIsProcessingAction(true);
      const response = await reactivateSubscription(subscription._id);
      if (response.success) {
        toast.success("Renewal resumed. Your subscription will continue.");
        await mutate(); // Refresh SWR cache
      } else {
        toast.error(response.error || "Failed to reactivate subscription");
      }
    } catch (error: any) {
      console.error("Error reactivating subscription:", error);
      toast.error("Failed to reactivate subscription");
    } finally {
      setIsProcessingAction(false);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    const symbol = currency === "eur" ? "€" : "$";
    return `${symbol}${price}`;
  };

  const formatDate = (date: string | Date) => {
    if (typeof date === "string") {
      return new Date(date).toLocaleDateString();
    }
    return date.toLocaleDateString();
  };

  const formatBillingPeriod = (period: string) => {
    const periodText =
      period === "monthly"
        ? "month"
        : period === "yearly"
        ? "year"
        : period === "weekly"
        ? "week"
        : "day";
    return periodText;
  };

  // Helper function to get current subscription pricing
  const getSubscriptionPricing = () => {
    if (!subscription || !plan) return null;

    // Get pricing from subscription's billing period
    const billingPeriod = subscription.billing_period;
    if (!billingPeriod || !plan.billing_options) {
      // Fallback to price_at_subscription if available
      return {
        current_price: subscription.price_at_subscription || 0,
        billing_period: billingPeriod || "monthly",
      };
    }

    const billingOption =
      plan.billing_options[billingPeriod as keyof typeof plan.billing_options];
    if (billingOption) {
      return {
        current_price: billingOption.current_price,
        billing_period: billingPeriod,
      };
    }

    // Fallback to subscription's stored price
    return {
      current_price: subscription.price_at_subscription || 0,
      billing_period: billingPeriod || "monthly",
    };
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: "default" as const, label: "Active", icon: Crown },
      trialing: {
        variant: "secondary" as const,
        label: "Trial",
        icon: Calendar,
      },
      past_due: {
        variant: "destructive" as const,
        label: "Past Due",
        icon: AlertCircle,
      },
      canceled: {
        variant: "outline" as const,
        label: "Canceled",
        icon: AlertCircle,
      },
      incomplete: {
        variant: "secondary" as const,
        label: "Incomplete",
        icon: AlertCircle,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
      icon: AlertCircle,
    };

    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  if (isLoading && !subscription) {
    return (
      <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
        <div className="flex items-center justify-center gap-2 text-muted-foreground">
          <RefreshCw className="w-4 h-4 animate-spin" />
          Loading subscription details...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="w-5 h-5" />
            Current Subscription
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-muted-foreground">
            <Crown className="w-12 h-12 mx-auto mb-3 opacity-30" />
            <p className="text-lg font-medium">Failed to load subscription</p>
            <p className="text-sm">
              {error.message || "Please try again later."}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="w-5 h-5" />
            Current Subscription
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-muted-foreground">
            <Crown className="w-12 h-12 mx-auto mb-3 opacity-30" />
            <p className="text-lg font-medium">No Active Subscription</p>
            <p className="text-sm">
              Subscribe to a plan to access premium features
            </p>
            {/* <Button className="mt-4 bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700">
              Browse Plans
            </Button> */}
          </div>
        </CardContent>
      </Card>
    );
  }

  const plan = subscription?.plan_id as Plan;
  const subscriptionPricing = getSubscriptionPricing();

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Crown className="w-5 h-5" />
            Current Subscription
          </CardTitle>
          <div className="flex items-center gap-2">
            {getStatusBadge(subscription.status)}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isValidating}
              className="h-8 w-8 p-0"
            >
              <RefreshCw
                className={`w-3 h-3 ${isValidating ? "animate-spin" : ""}`}
              />
            </Button>
          </div>
        </div>
      </CardHeader>

      {/* Refresh Indicator */}
      {isValidating && (
        <div className="px-4 pb-2">
          <div className="flex items-center gap-2 text-xs text-muted-foreground bg-muted/50 rounded px-2 py-1">
            <RefreshCw className="w-3 h-3 animate-spin" />
            Refreshing subscription details...
          </div>
        </div>
      )}

      <CardContent className="space-y-3">
        {/* Plan Details - More Compact */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground">
              {plan.name}
            </h3>
            <p className="text-sm text-muted-foreground">{plan.description}</p>
          </div>
          <div className="text-right">
            <div className="font-semibold text-voxa-teal-600">
              {subscriptionPricing &&
                formatPrice(subscriptionPricing.current_price, plan.currency!)}
              <span className="text-sm text-muted-foreground ml-1">
                /
                {subscriptionPricing &&
                  formatBillingPeriod(subscriptionPricing.billing_period)}
              </span>
            </div>
          </div>
        </div>

        {/* Billing Info with Cancel Button */}
        <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
          <div className="flex items-center gap-3">
            <Calendar className="w-4 h-4 text-voxa-teal-600" />
            <div>
              <div className="text-sm font-medium">Next billing:</div>
              <div className="text-xs text-muted-foreground">
                {formatDate(subscription.current_period_end)}
              </div>
            </div>
          </div>

          {/* Cancel/Reactivate Button */}
          {subscription.cancel_at_period_end ? (
            <Button
              variant="default"
              size="sm"
              onClick={handleReactivateSubscription}
              disabled={isProcessingAction}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isProcessingAction ? "Processing..." : "Resume Renewal"}
            </Button>
          ) : (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleCancelSubscription}
              disabled={isProcessingAction}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <X className="w-3 h-3 mr-1" />
              {isProcessingAction ? "Processing..." : "Cancel Renewal"}
            </Button>
          )}
        </div>

        {/* Trial Warning */}
        {subscription.trial_end &&
          new Date(subscription.trial_end) > new Date() && (
            <div className="flex items-center gap-2 p-2 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
              <Calendar className="w-4 h-4 text-amber-600" />
              <div className="text-sm">
                <span className="font-medium text-amber-800 dark:text-amber-200">
                  Trial ends on {formatDate(subscription.trial_end)}
                </span>
              </div>
            </div>
          )}

        {/* Cancellation Warning */}
        {subscription.cancel_at_period_end && (
          <div className="flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <AlertCircle className="w-4 h-4 text-red-600" />
            <div className="text-sm">
              <span className="font-medium text-red-800 dark:text-red-200">
                Subscription will cancel on{" "}
                {formatDate(subscription.current_period_end)}
              </span>
            </div>
          </div>
        )}

        {/* Plan Features Accordion */}
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem
            value="features"
            className="border rounded-lg bg-card data-[state=open]:shadow-sm transition-all duration-200"
          >
            <AccordionTrigger className="hover:no-underline p-3 rounded-md border border-dashed border-muted-foreground/30 hover:border-muted-foreground/50 hover:bg-muted transition-colors [&[data-state=open]>div>svg]:rotate-180">
              <span className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <Eye className="w-4 h-4" />
                Plan Features ({plan.features?.length})
              </span>
            </AccordionTrigger>

            <AccordionContent className="px-3 pb-3">
              <div className="space-y-2 pt-2">
                {plan.features?.map((feature, index) => {
                  // Prepare variables for template rendering
                  const variables = {
                    value: feature.value || "",
                    months:
                      featurePeriodMap[subscription.billing_period]?.months ??
                      "",
                    days:
                      featurePeriodMap[subscription.billing_period]?.days ?? "",
                  };
                  return (
                    <div
                      key={index}
                      className="flex items-center gap-3 text-sm"
                    >
                      <div
                        className={`w-5 h-5 rounded-full flex items-center justify-center ${
                          feature.included
                            ? "bg-voxa-teal-100 dark:bg-voxa-teal-900/30"
                            : "bg-muted"
                        }`}
                      >
                        <Check
                          className={`w-3 h-3 ${
                            feature.included
                              ? "text-voxa-teal-600 dark:text-voxa-teal-400"
                              : "text-muted-foreground"
                          }`}
                        />
                      </div>
                      <span
                        className={`text-sm flex-1 ${
                          feature.included
                            ? "text-foreground"
                            : "text-muted-foreground"
                        }`}
                      >
                        {renderTemplate(feature.description, variables)}
                      </span>
                      {feature.note && (
                        <div className="text-amber-600 dark:text-amber-400 text-xs italic">
                          {feature.note}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  );
}
