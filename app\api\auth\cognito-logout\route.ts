import { NextResponse } from "next/server";

export async function GET() {
  const COGNITO_DOMAIN = process.env.COGNITO_DOMAIN;
  const CLIENT_ID = process.env.COGNITO_CLIENT_ID;
  const LOGOUT_REDIRECT_URI = process.env.COGNITO_LOGOUT_REDIRECT_URI;

  if (!COGNITO_DOMAIN || !CLIENT_ID || !LOGOUT_REDIRECT_URI) {
    return NextResponse.json({ error: "Missing Cognito env variables." }, { status: 500 });
  }

  const logoutUrl = `https://${COGNITO_DOMAIN}/logout?client_id=${CLIENT_ID}&logout_uri=${encodeURIComponent(LOGOUT_REDIRECT_URI)}`;

  return NextResponse.redirect(logoutUrl);
}
