{"yourAssistants": "Your Assistants", "searchGoalsPlaceholder": "Search Goals by ID and Name", "search": "Search", "searching": "Searching..", "clear": "Clear", "searchResultsFor": "Search Results for \"{{searchTerm}}\"", "foundGoals": "Found {{count}} goal", "errorLoadingGoals": "Error loading goals: {{message}}", "noGoalsFound": "No goals found matching your search.", "failedToLoadAssistants": "Failed to load assistants.", "noAssistantsFound": "No assistants found.", "meet": {"addClientToMeet": "Add Client to Meet", "allFieldsRequired": "All fields are required", "country": "Country", "phoneNumber": "Phone Number", "googleMeetPin": "Google Meet PIN", "addingClient": "Adding Client", "addClient": "Add Client"}, "import": {"selectCsvOrTextFile": "Please select a CSV or Text file", "selectCountry": "Please select a country before uploading the file.", "fillAllFields": "Please fill all fields", "selectPhoneColumn": "Please select a column for the phone numbers.", "importContacts": "Import Contacts", "country": "Country", "dontSaveInDb": "Don't Save in clients database.", "saveInDb": "Save in clients database.", "csvColumnsInfo": "The csv file should contain the following columns: phone number, name", "selectPhoneColumnInfo": "Select which column contains the phone numbers you want to call", "invalidNumbersBelow": "Below are the numbers that are not valid.", "allNumbersValid": "All numbers are valid", "duplicatesBelow": "Below are the numbers that already exist in the database.", "noDuplicates": "No duplicate numbers", "uploadingContacts": "Uploading Contacts", "upload": "Upload"}, "group": {"groupRequired": "Group is required", "goalIdRequired": "GoalID is required", "addGroupToGoal": "Add Group to Goal", "searchGroup": "Search Group", "enterFirstLetters": "Enter first letters...", "selectGroup": "Select Group", "selectAGroup": "Select a group", "addingToGoal": "Adding to Goal", "addToGoal": "Add to Goal"}, "existingClient": {"clientRequired": "Client is required", "goalIdRequired": "GoalID is required", "addExistingClient": "Add Existing Client", "searchByName": "Search by Name", "enterName": "Enter name", "searchByNumber": "Search by Number", "enterNumber": "Enter number", "selectClient": "Select Client", "clientsFound": "{{count}} clients found", "searchByNameAbove": "Search by name above", "noClientsFound": "No clients found", "noAttachedGoals": "No attached goals", "addingToGoal": "Adding to Goal", "addToGoal": "Add to Goal"}, "addContact": {"requiredFields": "Phone number and GoalID and country are required", "addNewContact": "Add new Contact", "name": "Name", "country": "Country", "phoneNumber": "Phone Number", "messageDrop": "Message Drop", "optional": "Optional", "dontSaveInDb": "Don't Save in clients database.", "saveInDb": "Save in clients database.", "addingContact": "Adding Contact", "addContact": "Add Contact"}, "organigramme": {"failedToLoadGoals": "Failed to load goals.", "noGoalsFound": "No goals found"}, "manageNumbers": {"manageNumbers": "Manage Numbers", "addExistingNumber": "Add an existing number to {{botName}}.", "add": "Add", "noActiveNumbers": "No active numbers found.", "requestNewNumber": "Request a new number.", "submitting": "submitting...", "submitRequest": "Submit request"}, "phone": {"calls": "CALLS", "only": "ONLY", "sms": "SMS", "whatsapp": "WHATSAPP", "status": {"active": "Active", "inactive": "Inactive"}}, "createGoal": {"outboundCalling": "Outbound Calling", "googleMeet": "Google Meet", "googleMeetIntegrationInfo": "Google Meet Integration Info", "multiTranslation": "Multi-directional translation", "smsWhatsappNotifications": "SMS/WhatsApp Notifications", "comingSoon": "Coming Soon", "createGoal": "Create Goal", "chooseHowToCreate": "Choose how you want to create a goal:", "chooseFromTemplate": "Choose from template"}, "goalCard": {"googleMeetIconAlt": "Google Meet Icon", "outgoingCall": "Outgoing Call", "incomingCall": "Incoming Call"}, "pagination": {"previous": "Previous", "next": "Next"}, "countriesSelect": {"selectCountry": "Select a Country"}, "select": {"selectAnOption": "Select an option", "searchPlaceholder": "Search...", "noResultsFound": "No results found."}, "goalDropdown": {"goalOptions": "Goal Options", "started": "Started", "notStarted": "Not Started", "pending": "Pending", "paused": "Paused", "stopped": "Stopped", "completed": "Completed", "start": "Start", "restart": "<PERSON><PERSON>", "resume": "Resume", "pause": "Pause", "stop": "Stop", "settings": "Settings", "clients": "Clients", "addClient": "Add Client", "addClients": "Add Clients", "addExistingClient": "Add Existing Client", "oneClient": "One client", "importList": "Import List", "addExistingGroup": "Add Existing Group", "tags": "Tags", "noTags": "No tags for this Goal"}, "createTag": {"fillAllFields": "Please fill all fields", "failedToCreate": "Failed to create tag", "createTag": "Create Tag", "createNewTag": "Create new Tag", "tagName": "Tag Name", "namePlaceholder": "Name", "tagColor": "Tag Color", "cancel": "Cancel", "creatingTag": "Creating Tag"}, "addTags": {"addTags": "Add Tags", "addTagsToGoal": "Add Tags to Goal", "selectTags": "Select Tags", "noAvailableTags": "No available tags", "selectATag": "Select a tag", "removeTag": "Remove {{tag}}", "cancel": "Cancel", "addingTags": "Adding Tags"}, "duplicateGoal": {"duplicateGoal": "Duplicate Goal", "confirmation": "Are you sure you want to duplicate this goal?", "cancel": "Cancel", "success": "Goal duplicated successfully!", "errorDuplicating": "Error duplicating goal: {{error}}"}, "deleteGoal": {"archiveGoal": "Archive Goal", "confirmation": "Are you sure you want to archive this goal?", "cancel": "Cancel", "success": "Goal archived successfully", "failed": "Failed to delete goal"}, "addAssistant": {"requestNewAssistant": "Request new assistant", "nameMinLength": "Assistant name must be at least 3 characters long.", "createdAfterApproval": "The assistant is created after request approval (24h).", "assistantName": "Assistant Name", "namePlaceholder": "Name", "submitting": "submitting...", "submitRequest": "Submit request"}, "pipWidget": {"liveStats": "📊 Live Stats", "time": "TIME", "value": "VALUE", "openPip": "Open PiP", "closePip": "Close PiP"}, "meetGoalClients": {"goalsClients": "Goal's Clients", "noClientsFound": "No clients found for this goal.", "dialInNumber": "Dial-in Number"}, "createEditGoal": {"header": {"editTitle": "Update Goal", "createTitle": "Create New Goal", "googleMeetInfo": "Google Meet Integration – Documentation", "transferOn": "Transfer ON", "transferOff": "Transfer OFF"}, "stats": {"totalDuration": "Total Duration", "allCalls": "All Calls", "calledClients": "Called Clients", "missedCalls": "Missed Calls", "answeredCalls": "Answered Calls", "totalCost": "Total Cost"}, "topSection": {"goalName": "Goal Name: ", "goalID": "Goal ID: ", "goalClients": "Goal Clients"}, "details": {"section": "Goal Details:", "goalName": "Goal Name", "goalNamePlaceholder": "Enter goal name", "goalContext": "Goal Context", "goalContextPlaceholder": "e.g., Selling Computers", "targetCountry": "Target Country", "incomingOutgoingSwitch": "Incoming/Outgoing Calls Selection", "incoming": "Incoming Calls", "outgoing": "Outgoing Calls", "incomingInfo": "Incoming calls are calls that are made by clients to your assistant.", "outgoingOnlyInfo": "You can only have outgoing call goals now. You already have an incoming call goal."}, "forwarding": {"section": "Forward To Ringover Groups/Members:", "atLeastOne": "At least one is required", "groupId": "Group ID", "groupIdPlaceholder": "Select Group ID", "taNumbers": "TA Numbers", "taNumbersPlaceholder": "Select TA Number"}, "prompt": {"section": "Conversation Prompt:", "viewScript": "<PERSON>", "fromDatabase": "From database", "fromComputer": "From computer", "uploadPrompt": "Upload Prompt", "downloadExample": "Download Example", "viewFile": "View File", "notMuted": "Not Muted", "muted": "Muted", "aiVoiceEnabled": "AI Voice Enabled", "aiVoiceDisabled": "AI Voice Disabled"}, "advanced": {"section": "Advanced Settings", "durationBetweenCalls": "Duration Between Multiple Calls", "durationBetweenCallsPlaceholder": "Enter Duration in Seconds", "ringingDuration": "Ringing Duration", "ringingDurationPlaceholder": "Enter Ringing Duration in Seconds", "messagesDrop": "Messages Drop:", "noMessage": "No Message", "sms": "SMS", "whatsapp": "Whatsapp", "messagesDropInfo": "Configure the messages sent to your clients in case of missed calls (optional)", "smsLimitInfo": "A standard SMS allows 160 characters per message. Using emojis or Unicode reduces the limit to 70 characters per message.", "voicemailDrop": "Voicemail Drop:", "noVoicemail": "No Voicemail", "text": "Text", "audio": "Audio", "voicemailDropTextInfo": "Configure what the AI agent will say in case of voice mail detection", "voicemailDropAudioInfo": "Record or upload an audio from your local machine.", "voicemailCharLimitInfo": "A voicemail with 700 characters is roughly equal to 1 minute of audio. The duration may vary slightly depending on speaking speed and pauses.", "humanIntroduction": "Human Introduction:", "noIntroduction": "No Introduction", "humanIntroTextInfo": "Configure what to say as an introduction using a human recording.", "numberOfRetries": "Number of retries:", "pronounceClientName": "Pronounce client name:", "pronounceClientNameInfo": "Choose whether you want to mention client name or not.", "enabled": "Enabled", "disabled": "Disabled", "pronounceClientHonorific": "Pronounce client honorific (AI):", "pronounceClientHonorificInfo": "Choose whether you want to mention client honorific (Mr./Mrs.) or not.", "messagePlaceholder": "Enter your message here...", "charactersLabel": "characters"}, "aiVoice": {"section": "Preferred AI Voice Parameters:", "description": "Choose from the lists below, what AI voice you want to use for both genders (Male/Female).", "male": "Male", "assistantNameForMale": "Assistant Name for Male", "assistantNameForMalePlaceholder": "Eg: <PERSON>", "voiceId": "Voice ID", "voiceIdPlaceholder": "Select voice ID", "female": "Female", "assistantNameForFemale": "Assistant Name for Female", "assistantNameForFemalePlaceholder": "Eg: <PERSON>"}, "submit": {"createGoal": "Create Goal", "creatingGoal": "Creating Goal", "editGoal": "Update Goal", "editingGoal": "Updating Goal"}, "rangesSlider": {"selectTimeSlots": "Select Time Slots", "totalHours": "{{hours}} Hours", "selectUpTo": "Select up to 3 time slots for your bot's availability. Each slot is fixed", "addSlot": "Add Slot", "slot": "Slot {{index}}: {{start}} - {{end}}", "removeSlot": "Remove Slot", "doubleClickToAdd": "Double click on the track to add a new Slot."}, "audioRecorder": {"startRecording": "Start Recording", "pause": "Pause", "resume": "Resume", "uploadAudio": "Upload Audio File", "recording": "Recording...", "seconds": "{{seconds}}s"}, "scriptCard": {"usedByGoals": "{{count}} Goals", "uploadedAt": "Uploaded at", "view": "View", "selected": "Selected", "noName": "No name"}}}