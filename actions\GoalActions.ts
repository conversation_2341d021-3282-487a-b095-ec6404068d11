"use server";

import dbConnect from "@/lib/mongodb";
import { getEntrepriseByAdminID } from "./Entreprise";
import Goal, {
  DemarchageGoal,
  GoogleMeetGoal,
  MultiTranslationGoal,
} from "@/models/Goal";
import mongoose, { PipelineStage } from "mongoose";
import Phone from "../models/PhoneNumbers";
import Client from "@/models/Client";
import Assistant from "@/models/Assistant";
import { setCurrentGoalStatus } from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import {
  uploadAudioToS3,
  uploadHumanIntroductionAudio,
} from "./VoicemailFilesAWS";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";
import { CountryCode, isValidPhoneNumber } from "libphonenumber-js";
import { AnyGoal, PhoneNumber } from "@/types";
import Entreprise from "@/models/Entreprise";

//Demarchage Template
export async function CreateDemarchageGoal({
  name,
  assistantID,
  prompt,
  country,
  numberID,
  IncomingSelected,
  goalContext,
  groupid,
  retry_count,
  ranges,
  ForwardToNumber,
  incoming_call_transfer_on,
  messages_on_missed_call,
  message_on_missed_call_content,
  voicemail_drop_type,
  voicemail_drop_content,
  goal_assistant_name_for_male,
  goal_assistant_name_for_female,
  audio_blob,
  audio_duration,
  pronounce_client_name_enabled,
  pronounce_client_honorific_enabled,
  duration_between_calls,
  ringing_duration,
  male_voice,
  female_voice,
  human_introduction_audio_blob,
  human_introduction_audio_duration,
  human_introduction_enabled,
  entreprise_id
}: {
  name: string;
  assistantID: string;
  prompt: string;
  country: string;
  numberID: string;
  IncomingSelected: boolean;
  goalContext: string;
  groupid: string;
  retry_count: number;
  ranges: [number, number][];
  ForwardToNumber: string;
  incoming_call_transfer_on: boolean;
  messages_on_missed_call: string;
  message_on_missed_call_content: string;
  voicemail_drop_content: string;
  goal_assistant_name_for_male: string;
  goal_assistant_name_for_female: string;
  pronounce_client_name_enabled: boolean;
  pronounce_client_honorific_enabled: boolean;
  duration_between_calls: string;
  ringing_duration: string;
  male_voice: {
    id: string;
    provider: string;
  };
  female_voice: {
    id: string;
    provider: string;
  };
  // voicemail_drop audio
  voicemail_drop_type: string;
  audio_blob: Blob | null;
  audio_duration: number | null;
  // human_introduction audio
  human_introduction_enabled: string;
  human_introduction_audio_blob: Blob | null;
  human_introduction_audio_duration: number | null;
  entreprise_id?: string
}): Promise<{ success: boolean; error?: string }> {
  try {
    let EntrepriseID: any;
    if(!entreprise_id){
      const entrepriseResponse = await getEntrepriseByAdminID();
      if (!entrepriseResponse.success) {
        return { success: false, error: entrepriseResponse.error };
      }
      EntrepriseID = entrepriseResponse.entreprise._id;
    }else{
      EntrepriseID = new mongoose.Types.ObjectId(entreprise_id);
    }

    const assistantObjectId = new mongoose.Types.ObjectId(assistantID);
    const phoneObjectId = new mongoose.Types.ObjectId(numberID);

    const number = await Phone.findById(phoneObjectId);
    if (!number) {
      return { success: false, error: "Phone number not found" };
    }
    // voicemail_drop audio
    let audioUrl = null;
    if (audio_blob && voicemail_drop_type === "AUDIO") {
      const uploadResponse = await uploadAudioToS3(audio_blob);
      if (!uploadResponse.success) {
        return { success: false, error: uploadResponse.error };
      }
      audioUrl = uploadResponse.url;
    }

    // human_introduction audio
    let humanIntroductionAudioUrl = null;
    if (
      human_introduction_audio_blob &&
      human_introduction_enabled === "AUDIO"
    ) {
      const uploadResponse = await uploadHumanIntroductionAudio(
        human_introduction_audio_blob
      );
      if (!uploadResponse.success) {
        return { success: false, error: uploadResponse.error };
      }
      humanIntroductionAudioUrl = uploadResponse.url;
    }

    await dbConnect();
    const availability = ranges.map(([start, end]) => ({
      start_time: new Date(start).toISOString(),
      end_time: new Date(end).toISOString(),
    }));

    let male_voice_objectId;
    let female_voice_objectId;

    if (male_voice.id) {
      male_voice_objectId = new mongoose.Types.ObjectId(male_voice.id);
    }
    if (female_voice.id) {
      female_voice_objectId = new mongoose.Types.ObjectId(female_voice.id);
    }

    const newGoal = await DemarchageGoal.create({
      name: name,
      assistant: assistantObjectId,
      prompt: prompt,
      country: country,
      entreprise: EntrepriseID,
      phoneNumber: phoneObjectId,
      assistant_number: number.number,
      GoalContext: goalContext,
      goalType: IncomingSelected ? "INCOMING" : "OUTGOING",
      forwarded_to_group_id: groupid,
      forwarded_to_number: ForwardToNumber,
      availability,
      retry_count: retry_count,
      avoid_script_and_direct_transfer: incoming_call_transfer_on,
      messages_on_missed_call: messages_on_missed_call,
      message_on_missed_call_content: message_on_missed_call_content,
      voicemail_drop_content: voicemail_drop_content,
      ai_name_male: goal_assistant_name_for_male,
      ai_name_female: goal_assistant_name_for_female,
      is_pronounce_client_name_enabled: pronounce_client_name_enabled,
      is_pronounce_client_honorific_enabled: pronounce_client_honorific_enabled,
      duration_between_calls: parseInt(duration_between_calls) || 30,
      ringing_duration: parseInt(ringing_duration) || 30,
      male_voice: {
        id: male_voice_objectId,
        provider: male_voice.provider,
      },
      female_voice: {
        id: female_voice_objectId,
        provider: female_voice.provider,
      },
      // voicemail_drop audio
      voicemail_drop_type: voicemail_drop_type,
      voicemail_drop_audio_url: audioUrl,
      voicemail_drop_audio_duration: audio_duration,
      // human_introduction audio
      human_introduction_enabled:
        human_introduction_enabled === "AUDIO" ? true : false,
      human_introduction_url: humanIntroductionAudioUrl,
      human_introduction_audio_duration,
    });

    if (!newGoal) {
      return { success: false, error: "Failed to create goal" };
    }

    const updatedPhone = await Phone.findByIdAndUpdate(
      phoneObjectId,
      { $addToSet: { goals: newGoal._id } },
      { new: true }
    );

    if (!updatedPhone) {
      return { success: false, error: "Failed to update phone with goal" };
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
export async function GetDemarchageGoal(
  goalID: string
): Promise<{ success: boolean; goal?: any; error?: string }> {
  try {
    await dbConnect();
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    const goal = await Goal.findById(goalObjectId).populate("conversations");
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    let totalDurationSeconds = 0;
    let missedCalls = 0;
    let answeredCalls = 0;
    const calledClients = goal.conversations.length;
    const TotalClients = goal.clients.length;

    if (Array.isArray(goal.conversations)) {
      totalDurationSeconds = goal.conversations.reduce(
        (acc: number, conversation: any) => {
          if (conversation.duration === "0") {
            missedCalls++;
          } else {
            answeredCalls++;
          }
          return acc + Number(conversation.duration) || 0;
        },
        0
      );
    }

    const totalMinutes = Math.ceil(totalDurationSeconds / 60);
    const TotalCost = totalMinutes * 0.22; //0.11 *2

    const hours = Math.floor(totalDurationSeconds / 3600);
    const minutes = Math.floor((totalDurationSeconds % 3600) / 60);
    const seconds = totalDurationSeconds % 60;

    const formattedTime = [
      hours.toString().padStart(2, "0"),
      minutes.toString().padStart(2, "0"),
      seconds.toString().padStart(2, "0"),
    ].join(":");

    const ranges = goal.availability.map((range: any) => [
      range.start_time,
      range.end_time,
    ]);
    const FormattedGoal = {
      name: goal.name,
      goalType: goal.goalType,
      prompt: goal.prompt,
      country: goal.country,
      goalContext: goal.GoalContext,
      totalDuration: formattedTime,
      conversations: goal.conversations.length,
      missedCalls: missedCalls,
      answeredCalls: answeredCalls,
      calledClients: calledClients,
      TotalClients: TotalClients,
      TotalCost: TotalCost.toFixed(2),
      forwarded_to_group_id: goal.forwarded_to_group_id,
      forwarded_to_number: goal.forwarded_to_number,
      availability: ranges.length > 0 ? ranges : null,
      retry_count: goal.retry_count,
      avoid_script_and_direct_transfer: goal.avoid_script_and_direct_transfer,
      messages_on_missed_call: goal.messages_on_missed_call,
      message_on_missed_call_content: goal.message_on_missed_call_content,
      voicemail_drop_content: goal.voicemail_drop_content,
      ai_name_male: goal.ai_name_male,
      ai_name_female: goal.ai_name_female,
      pronounce_client_name_enabled: goal.is_pronounce_client_name_enabled,
      pronounce_client_honorific_enabled:
        goal.is_pronounce_client_honorific_enabled,
      duration_between_calls: goal.duration_between_calls,
      ringing_duration: goal.ringing_duration,
      male_voice: {
        id: (goal.male_voice.id || "").toString(),
        provider: goal.male_voice.provider,
      },
      female_voice: {
        id: (goal.female_voice.id || "").toString(),
        provider: goal.female_voice.provider,
      },
      updated_at:
        goal.updated_at?.toISOString() || goal.created_at?.toISOString(),
      template_type: goal.goal_template_type || "DEMARCHAGE",
      // voicemail_drop audio
      voicemail_drop_type: goal.voicemail_drop_type,
      voicemail_drop_audio_url: goal.voicemail_drop_audio_url,
      voicemail_drop_audio_duration: goal.voicemail_drop_audio_duration,
      // human_introduction audio
      human_introduction_enabled:
        goal.human_introduction_enabled === true ? "AUDIO" : "NONE",
      human_introduction_url: goal.human_introduction_url,
      human_introduction_audio_duration: goal.human_introduction_audio_duration,
    };

    return { success: true, goal: FormattedGoal };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
export async function UpdateDemarchageGoal({
  goalID,
  name,
  prompt,
  country,
  goalContext,
  groupid,
  forwardTo,
  ranges,
  retry_count,
  messages_on_missed_call,
  message_on_missed_call_content,
  voicemail_drop_type,
  voicemail_drop_content,
  goal_assistant_name_for_male,
  goal_assistant_name_for_female,
  audio_blob,
  audio_duration,
  pronounce_client_name_enabled,
  pronounce_client_honorific_enabled,
  duration_between_calls,
  ringing_duration,
  male_voice,
  female_voice,
  human_introduction_audio_blob,
  human_introduction_audio_duration,
  human_introduction_enabled,
}: {
  goalID: string;
  name: string;
  prompt: string;
  country: string;
  goalContext: string;
  groupid: string;
  forwardTo: string;
  ranges: [number, number][];
  retry_count: number;
  messages_on_missed_call: string;
  message_on_missed_call_content: string;
  voicemail_drop_content: string;
  goal_assistant_name_for_male: string;
  goal_assistant_name_for_female: string;
  pronounce_client_name_enabled: boolean;
  pronounce_client_honorific_enabled: boolean;
  duration_between_calls: string;
  ringing_duration: string;
  male_voice: {
    id: string;
    provider: string;
  };
  female_voice: {
    id: string;
    provider: string;
  };
  // voicemail_drop audio
  voicemail_drop_type: string;
  audio_blob: Blob | null;
  audio_duration: number | null;
  // human_introduction audio
  human_introduction_enabled: string;
  human_introduction_audio_blob: Blob | null;
  human_introduction_audio_duration: number | null;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();

    const availability = ranges.map(([start, end]) => ({
      start_time: new Date(start).toISOString(),
      end_time: new Date(end).toISOString(),
    }));
    // voicemail_drop audio
    let audioUrl = null;
    if (audio_blob && voicemail_drop_type === "AUDIO") {
      const uploadResponse = await uploadAudioToS3(audio_blob);
      if (!uploadResponse.success) {
        return { success: false, error: uploadResponse.error };
      }
      audioUrl = uploadResponse.url;
    }

    // human_introduction audio
    let humanIntroductionAudioUrl = null;
    if (
      human_introduction_audio_blob &&
      human_introduction_enabled === "AUDIO"
    ) {
      const uploadResponse = await uploadHumanIntroductionAudio(
        human_introduction_audio_blob
      );
      if (!uploadResponse.success) {
        return { success: false, error: uploadResponse.error };
      }
      humanIntroductionAudioUrl = uploadResponse.url;
    }

    const currentUtcTime = Date.now();
    const goal_updated_at = new Date(currentUtcTime).toISOString();

    let male_voice_objectId;
    let female_voice_objectId;

    if (male_voice.id) {
      male_voice_objectId = new mongoose.Types.ObjectId(male_voice.id);
    }
    if (female_voice.id) {
      female_voice_objectId = new mongoose.Types.ObjectId(female_voice.id);
    }

    const updatedGoal = await DemarchageGoal.findByIdAndUpdate(
      goalObjectId,
      {
        name,
        prompt,
        country,
        GoalContext: goalContext,
        forwarded_to_group_id: groupid,
        forwarded_to_number: forwardTo,
        availability,
        retry_count: retry_count,
        messages_on_missed_call: messages_on_missed_call,
        message_on_missed_call_content: message_on_missed_call_content,
        voicemail_drop_content: voicemail_drop_content,
        is_pronounce_client_name_enabled: pronounce_client_name_enabled,
        is_pronounce_client_honorific_enabled:
          pronounce_client_honorific_enabled,
        duration_between_calls: parseInt(duration_between_calls) || 30,
        ringing_duration: parseInt(ringing_duration) || 30,
        ai_name_male: goal_assistant_name_for_male,
        ai_name_female: goal_assistant_name_for_female,
        updated_at: goal_updated_at,
        male_voice: {
          id: male_voice_objectId,
          provider: male_voice.provider,
        },
        female_voice: {
          id: female_voice_objectId,
          provider: female_voice.provider,
        },
        // voicemail_drop audio
        voicemail_drop_type: voicemail_drop_type,
        voicemail_drop_audio_url: audioUrl,
        voicemail_drop_audio_duration: audio_duration,
        // human_introduction audio
        human_introduction_enabled:
          human_introduction_enabled === "AUDIO" ? true : false,
        human_introduction_url: humanIntroductionAudioUrl,
        human_introduction_audio_duration,
      },
      { new: true }
    );

    if (!updatedGoal) {
      return { success: false, error: "Failed to update goal" };
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

//Meet Template
export async function CreateMeetGoal({
  name,
  country,
  assistantID,
  numberID,
  goalContext,
  enable_mute,
  enable_ai_voice,
  script_content,
  ai_name_male,
  ai_name_female,
  male_voice,
  female_voice,
  crmID,
  crmToken,
}: {
  name: string;
  country: string;
  assistantID: string;
  numberID: string;
  goalContext: string;
  enable_mute: boolean;
  enable_ai_voice: boolean;
  script_content: string;
  ai_name_male: string;
  ai_name_female: string;
  male_voice: any;
  female_voice: any;
  crmID?: string;
  crmToken?: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const EntrepriseID = entrepriseResponse.entreprise._id;
    const assistantObjectId = new mongoose.Types.ObjectId(assistantID);
    const phoneObjectId = new mongoose.Types.ObjectId(numberID);
    let male_voice_objectId;
    let female_voice_objectId;

    if (male_voice.id) {
      male_voice_objectId = new mongoose.Types.ObjectId(male_voice.id);
    }
    if (female_voice.id) {
      female_voice_objectId = new mongoose.Types.ObjectId(female_voice.id);
    }

    const phone = await Phone.findById(phoneObjectId);
    if (!phone) {
      return { success: false, error: "Phone number not found" };
    }

    await dbConnect();
    const newMeetGoal = await GoogleMeetGoal.create({
      name: name,
      assistant: assistantObjectId,
      country: country,
      entreprise: EntrepriseID,
      phoneNumber: phoneObjectId,
      assistant_number: phone.number,
      GoalContext: goalContext,
      crm_id: crmID,
      crm_token: crmToken,
      enable_mute: enable_mute,
      enable_AI_voice: enable_ai_voice,
      prompt: script_content,
      ai_name_male,
      ai_name_female,
      male_voice: {
        id: male_voice_objectId,
        provider: male_voice.provider,
      },
      female_voice: {
        id: female_voice_objectId,
        provider: female_voice.provider,
      },
    });
    if (!newMeetGoal) {
      return { success: false, error: "Failed to create meet goal" };
    }
    const updatedPhone = await Phone.findByIdAndUpdate(
      phoneObjectId,
      { $addToSet: { goals: newMeetGoal._id } },
      { new: true }
    );

    if (!updatedPhone) {
      return { success: false, error: "Failed to update phone with goal" };
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
export async function GetMeetGoal(
  goalID: string
): Promise<{ success: boolean; goal?: any; error?: string }> {
  try {
    await dbConnect();
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    const goal = await GoogleMeetGoal.findById(goalObjectId);
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    const formattedGoal = {
      name: goal.name,
      country: goal.country,
      goalContext: goal.GoalContext,
      updated_at:
        goal.updated_at?.toISOString() || goal.created_at?.toISOString(),
      template_type: goal.goal_template_type || "GOOGLE_MEET",
      crm_id: "",
      crm_token: "",
      male_voice: {
        id: goal.male_voice.id?.toString(),
        provider: goal.male_voice.provider,
      },
      female_voice: {
        id: goal.female_voice.id?.toString(),
        provider: goal.female_voice.provider,
      },
      ai_name_female: goal.ai_name_female,
      ai_name_male: goal.ai_name_female,
      prompt: goal.prompt,
      enable_mute: goal.enable_mute,
      enable_ai_voice: goal.enable_AI_voice,
    };
    return { success: true, goal: formattedGoal };
  } catch (err: any) {
    console.error(err.message);
    return { success: false, error: err.message };
  }
}
export async function UpdateMeetGoal({
  goalID,
  name,
  country,
  goalContext,
  enable_mute,
  enable_ai_voice,
  script_content,
  ai_name_male,
  ai_name_female,
  male_voice,
  female_voice,
  crmID,
  crmToken,
}: {
  goalID: string;
  name: string;
  country: string;
  goalContext: string;
  enable_mute: boolean;
  enable_ai_voice: boolean;
  script_content: string;
  ai_name_male: string;
  ai_name_female: string;
  male_voice: any;
  female_voice: any;
  crmID?: string;
  crmToken?: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();
    let male_voice_objectId;
    let female_voice_objectId;

    if (male_voice.id) {
      male_voice_objectId = new mongoose.Types.ObjectId(male_voice.id);
    }
    if (female_voice.id) {
      female_voice_objectId = new mongoose.Types.ObjectId(female_voice.id);
    }
    const updatedGoal = await GoogleMeetGoal.findByIdAndUpdate(
      goalObjectId,
      {
        name,
        country,
        GoalContext: goalContext,
        crm_id: crmID,
        crm_token: crmToken,
        enable_mute,
        enable_AI_voice: enable_ai_voice,
        prompt: script_content,
        ai_name_male,
        ai_name_female,
        male_voice: {
          id: male_voice_objectId,
          provider: male_voice.provider,
        },
        female_voice: {
          id: female_voice_objectId,
          provider: female_voice.provider,
        },
      },
      { new: true }
    );
    if (!updatedGoal) {
      return { success: false, error: "Failed to update meet goal" };
    }
    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

//Multi Translation Template
export async function CreateMultiTranslationGoal({
  name,
  assistantID,
  country,
  numberID,
  goalContext,
  retry_count,
  ranges,
  duration_between_calls,
  ringing_duration,
}: {
  name: string;
  assistantID: string;
  country: string;
  numberID: string;
  goalContext: string;
  retry_count: number;
  ranges: [number, number][];
  duration_between_calls: string;
  ringing_duration: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const EntrepriseID = entrepriseResponse.entreprise._id;
    const assistantObjectId = new mongoose.Types.ObjectId(assistantID);
    const phoneObjectId = new mongoose.Types.ObjectId(numberID);

    const number = await Phone.findById(phoneObjectId);
    if (!number) {
      return { success: false, error: "Phone number not found" };
    }

    await dbConnect();
    const availability = ranges.map(([start, end]) => ({
      start_time: new Date(start).toISOString(),
      end_time: new Date(end).toISOString(),
    }));

    const newGoal = await MultiTranslationGoal.create({
      name: name,
      assistant: assistantObjectId,
      country: country,
      entreprise: EntrepriseID,
      phoneNumber: phoneObjectId,
      assistant_number: number.number,
      GoalContext: goalContext,
      availability,
      retry_count: retry_count,
      duration_between_calls: parseInt(duration_between_calls) || 30,
      ringing_duration: parseInt(ringing_duration) || 30,
    });

    if (!newGoal) {
      return { success: false, error: "Failed to create goal" };
    }

    const updatedPhone = await Phone.findByIdAndUpdate(
      phoneObjectId,
      { $addToSet: { goals: newGoal._id } },
      { new: true }
    );

    if (!updatedPhone) {
      return { success: false, error: "Failed to update phone with goal" };
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
export async function GetMultiTranslationGoal(
  goalID: string
): Promise<{ success: boolean; goal?: any; error?: string }> {
  try {
    await dbConnect();
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    const goal = await Goal.findById(goalObjectId).populate("conversations");
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    let totalDurationSeconds = 0;
    let missedCalls = 0;
    let answeredCalls = 0;
    const calledClients = goal.conversations.length;
    const TotalClients = goal.clients.length;

    if (Array.isArray(goal.conversations)) {
      totalDurationSeconds = goal.conversations.reduce(
        (acc: number, conversation: any) => {
          if (conversation.duration === "0") {
            missedCalls++;
          } else {
            answeredCalls++;
          }
          return acc + Number(conversation.duration) || 0;
        },
        0
      );
    }

    const totalMinutes = Math.ceil(totalDurationSeconds / 60);
    const TotalCost = totalMinutes * 0.22;

    const hours = Math.floor(totalDurationSeconds / 3600);
    const minutes = Math.floor((totalDurationSeconds % 3600) / 60);
    const seconds = totalDurationSeconds % 60;

    const formattedTime = [
      hours.toString().padStart(2, "0"),
      minutes.toString().padStart(2, "0"),
      seconds.toString().padStart(2, "0"),
    ].join(":");

    const ranges = goal.availability.map((range: any) => [
      range.start_time,
      range.end_time,
    ]);
    const FormattedGoal = {
      name: goal.name,
      country: goal.country,
      goalContext: goal.GoalContext,
      totalDuration: formattedTime,
      conversations: goal.conversations.length,
      missedCalls: missedCalls,
      answeredCalls: answeredCalls,
      calledClients: calledClients,
      TotalClients: TotalClients,
      TotalCost: TotalCost.toFixed(2),
      availability: ranges.length > 0 ? ranges : null,
      retry_count: goal.retry_count,
      duration_between_calls: goal.duration_between_calls,
      ringing_duration: goal.ringing_duration,
      updated_at:
        goal.updated_at?.toISOString() || goal.created_at?.toISOString(),
      template_type: goal.goal_template_type || "MULTI_TRANSLATION",
    };

    return { success: true, goal: FormattedGoal };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
export async function UpdateMultiTranslationGoal({
  goalID,
  name,
  country,
  goalContext,
  ranges,
  retry_count,
  duration_between_calls,
  ringing_duration,
}: {
  goalID: string;
  name: string;
  country: string;
  goalContext: string;
  ranges: [number, number][];
  retry_count: number;
  duration_between_calls: string;
  ringing_duration: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();

    const availability = ranges.map(([start, end]) => ({
      start_time: new Date(start).toISOString(),
      end_time: new Date(end).toISOString(),
    }));

    const currentUtcTime = Date.now();
    const goal_updated_at = new Date(currentUtcTime).toISOString();

    const updatedGoal = await MultiTranslationGoal.findByIdAndUpdate(
      goalObjectId,
      {
        name,
        country,
        GoalContext: goalContext,
        availability,
        retry_count: retry_count,
        duration_between_calls: parseInt(duration_between_calls) || 30,
        ringing_duration: parseInt(ringing_duration) || 30,
        updated_at: goal_updated_at,
      },
      { new: true }
    );

    if (!updatedGoal) {
      return { success: false, error: "Failed to update goal" };
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

// Other than templates
export const AddClientToGoogleMeet = async ({
  google_meet_dial_in_number,
  google_meet_pin,
  country,
  goal_id,
}: {
  google_meet_dial_in_number: string;
  google_meet_pin: string;
  country: string;
  goal_id: string;
}) => {
  try {
    const goal_object_id = new mongoose.Types.ObjectId(goal_id);
    const formattedPhone = phoneNumberFormat(
      google_meet_dial_in_number,
      country as CountryCode
    ).replace(/\s/g, "");
    if (!isValidPhoneNumber(formattedPhone)) {
      return {
        success: false,
        error: "Invalid phone number",
      };
    }
    const goalResponse = await Goal.findOneAndUpdate(
      {
        _id: goal_object_id,
        goal_template_type: "GOOGLE_MEET",
      },
      {
        $push: {
          clients: {
            google_meet_dial_in_number: formattedPhone,
            google_meet_pin,
          },
        },
      },
      { new: true }
    );

    if (!goalResponse) {
      return {
        success: false,
        error: "Goal not found or not of type GOOGLE_MEET",
      };
    }

    return {
      success: true,
    };
  } catch (err: any) {
    return {
      success: false,
      error: err.message,
    };
  }
};

export async function DeleteGoal(
  goalID: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();
    const goal = await Goal.findByIdAndDelete(goalObjectId);
    await Client.updateMany(
      { goals: goalObjectId },
      { $pull: { goals: goalObjectId } }
    );
    await Phone.updateMany(
      { goals: goalObjectId },
      { $pull: { goals: goalObjectId } }
    );
    await Assistant.updateMany(
      { goals: goalObjectId },
      { $pull: { goals: goalObjectId } }
    );

    if (!goal) {
      return { success: false, error: "Failed to delete goal" };
    }
    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function ArchiveGoal(
  goalID: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();
    const goal = await Goal.findById(goalObjectId);
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }
    if (goal.goalType === "INCOMING") {
      return { success: false, error: "Cannot archive an incoming goal" };
    }
    goal.goal_visibility = "HIDDEN";
    await goal.save();
    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function CheckPhoneHasIncomingCallsGoal(
  phoneID: string
): Promise<{ success: boolean; hasIncomingGoal?: boolean; error?: string }> {
  try {
    const phoneObjectId = new mongoose.Types.ObjectId(phoneID);
    await dbConnect();
    const goal = await Goal.findOne({
      phoneNumber: phoneObjectId,
      goalType: "INCOMING",
    });
    if (!goal) {
      return { success: true, hasIncomingGoal: false };
    }
    return { success: true, hasIncomingGoal: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function getClientsByStatus(
  goalID: string,
  status:
    | "ANSWERED"
    | "CALLED"
    | "NOT_CALLED"
    | "MISSED"
    | "VOICEMAIL_DETECTED"
    | "FAILED"
): Promise<{ success: boolean; clients?: any; error?: string }> {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();

    // Find the goal and its clients
    const goal = await Goal.findById(goalObjectId).populate(
      "clients.client_id"
    );

    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    // Filter clients based on the provided status
    const filteredClients = goal.clients
      .filter((clientEntry: any) => clientEntry.status === status)
      .map((clientEntry: any) => {
        const client = clientEntry.client_id;
        return {
          name: client?.name || "",
          phone: client?.phone || "",
          country: client?.country || "",
        };
      });
    return { success: true, clients: filteredClients };
  } catch (err: any) {
    console.error(err.message);
    return { success: false, error: err.message };
  }
}

export async function updateGoalAvailability(
  goalID: string,
  ranges: [number, number][]
): Promise<{ success: boolean; error?: string }> {
  try {
    // Map the ranges into the availability format
    const availability = ranges.map(([start, end]) => ({
      start_time: start,
      end_time: end,
    }));

    // Update the goal's availability field
    const updatedGoal = await Goal.findByIdAndUpdate(
      goalID,
      { availability }, // Update the availability field
      { new: true } // Return the updated document
    );

    if (!updatedGoal) {
      return { success: false, error: "Goal not found" };
    }

    // Return success if the update is successful
    return { success: true };
  } catch (error: any) {
    // Handle any errors
    console.error(error);
    return { success: false, error: error.message };
  }
}

export const StopGoal = async (
  goalID: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();
    const goal = await Goal.findByIdAndUpdate(
      goalObjectId,
      { status: "STOPPED" },
      { new: true }
    );
    if (!goal) {
      return { success: false, error: "Failed to stop goal" };
    }
    setCurrentGoalStatus("STOPPED");
    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export const PauseGoal = async (
  goalID: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();
    const goal = await Goal.findByIdAndUpdate(
      goalObjectId,
      { status: "PAUSED" },
      { new: true }
    );
    if (!goal) {
      return { success: false, error: "Failed to stop goal" };
    }
    setCurrentGoalStatus("PAUSED");
    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
};

export async function UpdateIncomingGoalTransfer(
  goalID: string,
  status: boolean
): Promise<{ success: boolean; error?: string }> {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();
    const goal = await Goal.findByIdAndUpdate(
      goalObjectId,
      { avoid_script_and_direct_transfer: status },
      { new: true }
    );
    if (!goal) {
      return {
        success: false,
        error: `Failed to update goal transfer to ${status}`,
      };
    }
    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function GetGoalAnsweredConversations(goalID: string) {
  try {
    await dbConnect();
    const goalObjectId = new mongoose.Types.ObjectId(goalID);

    const goalConversations = await Goal.findById(goalObjectId).populate(
      "answered_conversations_ids"
    );

    if (!goalConversations) {
      return { success: false, error: "Failed to get answered conversations" };
    }

    const answeredConversations =
      goalConversations.answered_conversations_ids.map((conversation: any) => {
        const conversationDetails = conversation || {};
        return {
          _id: conversationDetails._id?.toString() || "",
          callSid: conversationDetails.sid || "N/A",
          callDuration: conversationDetails.duration || 0,
          timestamp: conversationDetails.initiated_time || "N/A",
          client_name: conversationDetails.to_name || "No Name",
        };
      });

    return { success: true, answeredConversations };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function getVoiceMailDetectedGoalConversations(goalID: string) {
  try {
    await dbConnect();
    const goalObjectId = new mongoose.Types.ObjectId(goalID);

    const goalConversations = await Goal.findById(goalObjectId).populate(
      "voicemail_detected_conversations_ids"
    );

    if (!goalConversations) {
      return {
        success: false,
        error: "Failed to get voicemail detected conversations",
      };
    }

    const voicemailDetectedConversations =
      goalConversations.voicemail_detected_conversations_ids.map(
        (conversation: any) => {
          const conversationDetails = conversation || {};
          return {
            _id: conversationDetails._id?.toString() || "",
            callSid: conversationDetails.sid || "N/A",
            callDuration: conversationDetails.duration || 0,
            timestamp: conversationDetails.initiated_time || "N/A",
            client_name: conversationDetails.to_name || "No Name",
          };
        }
      );

    return { success: true, voicemailDetectedConversations };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function GetGoalOtherConversationNotAnswered(
  goalID: string,
  conversationType: "MISSED" | "FAILED"
) {
  try {
    await dbConnect();
    const goalObjectId = new mongoose.Types.ObjectId(goalID);

    const fieldName = `${conversationType.toLowerCase()}_conversations_ids`;
    const goalConversations = await Goal.findById(goalObjectId).populate(
      fieldName
    );

    if (!goalConversations) {
      return {
        success: false,
        error: `Failed to get ${conversationType.toLowerCase()} conversations`,
      };
    }

    const otherConversations = (goalConversations as any)[fieldName].map(
      (conversation: any) => {
        return {
          _id: conversation._id?.toString() || "",
          callSid: conversation.sid || "N/A",
          callDuration: conversation.duration || 0,
          timestamp: conversation.initiated_time || "N/A",
          client_name: conversation.to_name || "No Name",
        };
      }
    );

    return { success: true, otherConversations };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function getIncallConversations(goalID: string) {
  try {
    await dbConnect();
    const goalObjectID = new mongoose.Types.ObjectId(goalID);

    const goalConversations = await Goal.findById(goalObjectID).populate(
      "incall_conversations_ids"
    );
    if (!goalConversations) {
      return { success: false, error: "Failed to get answered conversations" };
    }

    const filteredIncallConversations =
      goalConversations.incall_conversations_ids.map((conversation: any) => {
        const conversationDetails = conversation || {};
        return {
          _id: conversationDetails._id
            ? conversationDetails._id.toString()
            : "",
          callSid: conversationDetails.sid || "N/A",
          callDuration: conversationDetails.duration || "0",
          timestamp: conversationDetails.initiated_time || "N/A",
          client_name: conversationDetails.to_name || "No Name",
        };
      });

    return {
      success: true,
      incallConversations: filteredIncallConversations || [],
    };
  } catch (err: any) {
    console.error("Error fetching incall conversations:", err.message);
    return { success: false, error: err.message };
  }
}

export async function GetCurrentGoalStatus(
  goalID: string
): Promise<{ success: boolean; status?: string; error?: string }> {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();
    const goal = await Goal.findById(goalObjectId).select("status");
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }
    return { success: true, status: goal.status };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function getGoalsByPhoneNumber(
  phoneId: string,
  page: number = 1,
  limit: number = 10
): Promise<{
  success: boolean;
  goals?: AnyGoal[];
  totalPages?: number;
  currentPage?: number;
  error?: string;
}> {
  try {
    await dbConnect();

    // First, get the phone with populated goals to count actual valid goals
    const phoneWithAllGoals = await Phone.findById(phoneId).populate({
      path: "goals",
      select: "_id", // Only select ID for counting
    });

    if (!phoneWithAllGoals) {
      return { success: false, error: "Phone number not found" };
    }

    const totalGoals = phoneWithAllGoals.goals?.length;
    const totalPages = Math.ceil(totalGoals / limit);
    const skip = (page - 1) * limit;

    // Now get the paginated goals with all required fields
    const phoneWithGoals = await Phone.findById(phoneId, "goals")
      .populate({
        path: "goals",
        match: { goal_visibility: "VISIBLE" },
        select:
          "_id name country goal_template_type template_type goalType status tags",
        options: {
          sort: { _id: 1 }, // Sort by _id descending
          skip: skip,
          limit: limit,
        },
        populate: {
          path: "tags",
          select: "name color -_id",
        },
      })
      .lean<PhoneNumber>();

    if (!phoneWithGoals || !phoneWithGoals.goals) {
      return {
        success: false,
        error: "Could not retrieve goals for phone number",
      };
    }

    phoneWithGoals.goals.forEach((goal) => {
      const goalObj = goal as AnyGoal;
      // Convert ObjectId to string for easier handling in the frontend
      goalObj._id = goalObj._id?.toString();
    });

    return {
      success: true,
      goals: phoneWithGoals.goals as AnyGoal[],
      totalPages,
      currentPage: page,
    };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function DuplicateGoal(goalID: string): Promise<{
  success: boolean;
  error?: string;
  callback_url?: string;
}> {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    await dbConnect();

    const goal = await Goal.findById(goalObjectId).populate("conversations");
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }

    const { _id, __v, ...goalData } = goal.toObject();

    const newGoal = await Goal.create({
      ...goalData,
      name: `${goal.name} - Copy`,
    });

    const phoneObjectId = goal.phoneNumber;

    const updatedPhone = await Phone.findByIdAndUpdate(
      phoneObjectId,
      { $addToSet: { goals: newGoal._id } },
      { new: true }
    );

    if (!updatedPhone) {
      return { success: false, error: "Failed to update phone with goal" };
    }

    return {
      success: true,
      callback_url:
        newGoal.goal_template_type === "DEMARCHAGE"
          ? `/businessDash/assistants/Goals/${newGoal.name}/editGoal/demarchage_goal?goal=${goalID}&goalName=${newGoal.name}`
          : `/businessDash/assistants/Goals/${newGoal.name}/editGoal/meet_goal?goal=${goalID}&goalName=${newGoal.name}`,
    };
  } catch (err: any) {
    console.error(err);
    return { success: false, error: err.message };
  }
}

export async function checkGoalExists(goalID: string) {
  try {
    const goalObjectId = new mongoose.Types.ObjectId(goalID);
    console.error("goalObjectId", goalObjectId);
    await dbConnect();
    const goal = await Goal.findOne({
      _id: goalObjectId,
      goal_visibility: "VISIBLE",
    });
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }
    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export const GetGoalTemplateType = async (
  goalID: string
): Promise<{ success: boolean; error?: string; templateType?: string }> => {
  try {
    await dbConnect();
    const goalObjectID = new mongoose.Types.ObjectId(goalID);
    const goal = await Goal.findById(goalObjectID).select("goal_template_type");
    if (!goal) {
      return { success: false, error: "Goal not found" };
    }
    return { success: true, templateType: goal.goal_template_type.toString() };
  } catch (err: any) {
    console.error("Error fetching goal template type:", err);
    return { success: false, error: err.message };
  }
};

export async function getFilteredGoals({
  searchTerm = "",
  page = 1,
  limit = 10,
}: {
  searchTerm?: string;
  page?: number;
  limit?: number;
}): Promise<{
  success: boolean;
  goals?: AnyGoal[];
  totalPages?: number;
  currentPage?: number;
  error?: string;
}> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const entrepriseID = entrepriseResponse.entreprise._id;
    await dbConnect();

    const searchCondition = searchTerm
      ? {
          $or: [
            { "goals.name": { $regex: searchTerm, $options: "i" } },
            {
              "goals._id": mongoose.Types.ObjectId.isValid(searchTerm)
                ? new mongoose.Types.ObjectId(searchTerm)
                : null,
            },
          ],
        }
      : {};

    const countPipeline: PipelineStage[] = [
      { $match: { _id: entrepriseID } },
      {
        $lookup: {
          from: "assistants",
          localField: "assistants",
          foreignField: "_id",
          as: "assistants",
        },
      },
      { $unwind: "$assistants" },
      {
        $lookup: {
          from: "phones",
          localField: "assistants.numbers",
          foreignField: "_id",
          as: "numbers",
        },
      },
      { $unwind: "$numbers" },
      {
        $lookup: {
          from: "goals",
          localField: "numbers.goals",
          foreignField: "_id",
          as: "goals",
        },
      },
      { $unwind: "$goals" },
      {
        $match: {
          "goals.goal_visibility": "VISIBLE",
          ...searchCondition,
        },
      },
      { $count: "total" },
    ];

    const countResult = await Entreprise.aggregate(countPipeline);
    const totalGoals = countResult.length > 0 ? countResult[0].total : 0;
    const totalPages = Math.ceil(totalGoals / limit);

    const pipeline: PipelineStage[] = [
      { $match: { _id: entrepriseID } },
      {
        $lookup: {
          from: "assistants",
          localField: "assistants",
          foreignField: "_id",
          as: "assistants",
        },
      },
      { $unwind: "$assistants" },
      {
        $lookup: {
          from: "phones",
          localField: "assistants.numbers",
          foreignField: "_id",
          as: "numbers",
        },
      },
      { $unwind: "$numbers" },
      {
        $lookup: {
          from: "goals",
          localField: "numbers.goals",
          foreignField: "_id",
          as: "goals",
        },
      },
      { $unwind: "$goals" },
      {
        $lookup: {
          from: "tags",
          localField: "goals.tags",
          foreignField: "_id",
          as: "goalTags",
        },
      },
      {
        $match: {
          "goals.goal_visibility": "VISIBLE",
          ...searchCondition,
        },
      },
      { $sort: { "goals.created_at": -1 } },
      { $skip: (page - 1) * limit },
      { $limit: limit },
      {
        $lookup: {
          from: "conversations",
          localField: "goals.conversations",
          foreignField: "_id",
          as: "conversationData",
        },
      },
      {
        $addFields: {
          totalDuration: {
            $sum: {
              $map: {
                input: "$conversationData",
                as: "conv",
                in: { $ifNull: [{ $toDouble: "$$conv.duration" }, 0] },
              },
            },
          },
        },
      },
      {
        $project: {
          _id: "$goals._id",
          name: "$goals.name",
          country: "$goals.country",
          goalType: "$goals.goalType",
          status: "$goals.status",
          goal_template_type: "$goals.goal_template_type",
          is_debug: "$goals.is_debug",
          duration: "$totalDuration",
          assistant: {
            _id: "$assistants._id",
            name: "$assistants.name",
          },
          phone: {
            _id: "$numbers._id",
            number: "$numbers.number",
            country: "$numbers.country",
          },
          tags: "$goalTags",
          created_at: "$goals.created_at",
        },
      },
    ];

    const goals = await Entreprise.aggregate(pipeline);

    const formattedGoals = goals.map((goal) => ({
      ...goal,
      _id: goal._id.toString(),
      assistant: goal.assistant && {
        ...goal.assistant,
        _id: goal.assistant._id ? goal.assistant._id.toString() : null,
      },
      phone: goal.phone && {
        ...goal.phone,
        _id: goal.phone._id ? goal.phone._id.toString() : null,
      },
      tags: Array.isArray(goal.tags)
        ? goal.tags.map((tag: any) => ({
            ...tag,
            _id: tag._id ? tag._id.toString() : null,
          }))
        : [],
    }));

    return {
      success: true,
      goals: formattedGoals,
      totalPages,
      currentPage: page,
    };
  } catch (err: any) {
    console.error("Error fetching filtered goals:", err);
    return { success: false, error: err.message };
  }
}

export const addTagToGoal = async (goalID: string, tagID: string): Promise<{success: boolean, error?: string, tags?: any[]}> => {
    try {
        await dbConnect();
        const goal = await Goal.findById(goalID);
        if (!goal) {
            return { success: false, error: "Goal not found" };
        }
        if (!goal.tags.map((t: any) => t.toString()).includes(tagID)) {
            goal.tags.push(tagID);
            await goal.save();
        }
        await goal.populate('tags');
        const formattedTags = (goal.tags || []).map((tag: any) => ({
            _id: tag._id.toString(),
            name: tag.name,
            color: tag.color,
        }));
        return { success: true, tags: formattedTags };
    } catch (e: any) {
        console.log("Error in addTagToGoal:", e.message);
        return { success: false, error: e.message };
    }
};

export const addTagsToGoal = async (goalID: string, tagIDs: string[]): Promise<{success: boolean, error?: string, tags?: any[]}> => {
    try {
        await dbConnect();
        const goal = await Goal.findById(goalID);
        if (!goal) {
            return { success: false, error: "Goal not found" };
        }
        const currentTagIds = goal.tags.map((t: any) => t.toString());
        let added = false;
        for (const tagID of tagIDs) {
            if (!currentTagIds.includes(tagID)) {
                goal.tags.push(tagID);
                added = true;
            }
        }
        if (added) {
            await goal.save();
        }
        await goal.populate('tags');
        const formattedTags = (goal.tags || []).map((tag: any) => ({
            _id: tag._id.toString(),
            name: tag.name,
            color: tag.color,
        }));
        return { success: true, tags: formattedTags };
    } catch (e: any) {
        console.log("Error in addTagsToGoal:", e.message);
        return { success: false, error: e.message };
    }
};

export const removeTagFromGoal = async (goalID: string, tagID: string): Promise<{success: boolean, error?: string, tags?: any[]}> => {
    try {
        await dbConnect();
        const goal = await Goal.findById(goalID);
        if (!goal) {
            return { success: false, error: "Goal not found" };
        }
        goal.tags = goal.tags.filter((t: any) => t.toString() !== tagID);
        await goal.save();
        await goal.populate('tags');
        const formattedTags = (goal.tags || []).map((tag: any) => ({
            _id: tag._id.toString(),
            name: tag.name,
            color: tag.color,
        }));
        return { success: true, tags: formattedTags };
    } catch (e: any) {
        console.log("Error in removeTagFromGoal:", e.message);
        return { success: false, error: e.message };
    }
};

export async function getGoalsCountByPhoneId(
  phoneId: string
): Promise<{ success: boolean; count?: number; error?: string }> {
  try {
    await dbConnect();
    const phone = await Phone.findById(phoneId).populate({
      path: "goals",
      match: { goal_visibility: "VISIBLE" },
      select: "_id",
    });
    if (!phone) {
      return { success: false, error: "Phone number not found" };
    }
    const count = phone.goals ? phone.goals.length : 0;
    return { success: true, count };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
