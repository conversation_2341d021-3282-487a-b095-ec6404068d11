"use client";

import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import RingoverIcon from "@/public/images/Icons/ringover-seeklogo.png";
import office from "@/public/images/Icons/integrations/office.svg";
import slack from "@/public/images/Icons/integrations/slack.svg";
import zoho from "@/public/images/Icons/integrations/zoho.svg";
import google_sheets from "@/public/images/Icons/integrations/google_sheets.svg";
import google_calendar from "@/public/images/Icons/integrations/google_calendar.svg";
import hubspot from "@/public/images/Icons/integrations/hubspot.svg";
import google_contacts from "@/public/images/Icons/integrations/google_contacts.svg";
import pipedrive from "@/public/images/Icons/integrations/pipedrive.svg";
import copper from "@/public/images/Icons/integrations/copper.svg";
import salesforce from "@/public/images/Icons/integrations/salesforce.svg";
import gorgias from "@/public/images/Icons/integrations/gorgias.svg";
import close from "@/public/images/Icons/integrations/close.png";
import karlia from "@/public/images/Icons/integrations/karlia.svg";
import sellsy from "@/public/images/Icons/integrations/sellsy.svg";
import odoo from "@/public/images/Icons/integrations/odoo.svg";
import freshsales from "@/public/images/Icons/integrations/freshsales.svg";
import {
  IntegrationCards,
  IntegrationCardsProps,
} from "@/components/Dashboard/Cards/IntegrationCard";
import Link from "next/link";
import { toast } from "sonner";

const url = "https://crm.echoparrot.com";

export default function Integrations() {
  const { t } = useTranslation("integrations");
  const router = useRouter();
  const integrations: IntegrationCardsProps[] = [
    {
      title: t("productivity.title"),
      items: [
        {
          icon: RingoverIcon,
          name: t("productivity.items.0"),
          bgColor: "bg-teal-200",
          onClick: () => router.push("/businessDash/agents"),
        },
        {
          icon: google_calendar,
          name: t("productivity.items.3"),
          bgColor: "bg-blue-200",
          comingSoon: false,
          onClick: () => router.push("/businessDash/integrations/productivity/providers/google_calendar"),
        },
        {
          icon: office,
          name: t("productivity.items.1"),
          bgColor: "bg-orange-200",
          comingSoon: true,
          onClick: () => toast.info("Coming soon"),
        },
        {
          icon: slack,
          name: t("productivity.items.2"),
          bgColor: "bg-blue-200",
          comingSoon: true,
          onClick: () => toast.info("Coming soon"),
        },
      ],
    },
    {
      title: t("files.title"),
      items: [
        {
          icon: google_sheets,
          name: t("files.items.0"),
          bgColor: "bg-green-200",
          onClick: () => router.push("/businessDash/integrations/files/providers/sheets"),
        },
      ],
    },
    {
      title: t("crm.title"),
      items: [
        {
          icon: zoho,
          name: t("crm.items.0"),
          bgColor: "bg-red-200",
          onClick: () => window.open(`${url}/loginZoho`),
        },
        {
          icon: hubspot,
          name: t("crm.items.1"),
          bgColor: "bg-orange-200",
          onClick: () => window.open(`${url}/contactsHubspot`),
        },
        {
          icon: google_contacts,
          name: t("crm.items.2"),
          bgColor: "bg-blue-400",
          onClick: () => window.open(`${url}/contactsGoogle`),
        },
        {
          icon: pipedrive,
          name: t("crm.items.3"),
          bgColor: "bg-pink-200",
          onClick: () => window.open(`${url}/contactPipedrive`),
        },
        {
          icon: copper,
          name: t("crm.items.4"),
          bgColor: "bg-amber-500",
          onClick: () => window.open(`${url}/CopperContactsPage`),
        },
        {
          icon: salesforce,
          name: t("crm.items.5"),
          bgColor: "bg-sky-200",
          onClick: () => window.open(`${url}/contactSalesforce`),
        },
        {
          icon: gorgias,
          name: t("crm.items.6"),
          bgColor: "bg-yellow-200",
          onClick: () => window.open(`${url}/gorgias`),
        },
        {
          icon: close,
          name: t("crm.items.7"),
          bgColor: "bg-green-200",
          onClick: () => window.open(`${url}/contactsClose`),
        },
        {
          icon: freshsales,
          name: t("crm.items.8"),
          bgColor: "bg-orange-300",
          onClick: () => window.open(`${url}/freshsales`),
        },
        {
          icon: karlia,
          name: t("crm.items.9"),
          bgColor: "bg-indigo-800",
          onClick: () => window.open(`${url}/karlia`),
        },
        {
          icon: sellsy,
          name: t("crm.items.10"),
          bgColor: "bg-neutral-200",
          onClick: () => window.open(`${url}/sellsy`),
        },
        {
          icon: odoo,
          name: t("crm.items.11"),
          bgColor: "bg-fuchsia-300",
          onClick: () => window.open(`${url}/odoo`),
        }, 
      ],
    },
  ];
  return (
    <div className="rounded-2xl w-full flex flex-col gap-4">
      <div className="flex gap-4 max-md:flex-col items-center justify-between">
        <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
          {t("title")}
        </h1>
        <Link
          href={`${url}`}
          className="text-white max-sm:w-full text-center text-nowrap bg-voxa-teal-600 hover:bg-voxa-teal-600/80 active:bg-voxa-teal-500 transition-all font-medium w-fit rounded-md px-4 py-1.5 sm:py-2 place-self-end"
        >
          Visit CRMs
        </Link>
      </div>
      {Object.values(integrations).map((section, index) => (
        <IntegrationCards
          key={index}
          title={section.title}
          items={section.items}
        />
      ))}
    </div>
  );
}
