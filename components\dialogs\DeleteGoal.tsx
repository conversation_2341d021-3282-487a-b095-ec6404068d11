import {
  ArchiveGoal,
  // , DeleteGoal
} from "@/actions/GoalActions";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ArchiveIcon } from "lucide-react";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

export function DeleteGoalDialog({ goalID }: { goalID: string }) {
  const { t } = useTranslation("assistants");
  const ArchiveGoalByID = async () => {
    try {
      const response = await ArchiveGoal(goalID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success(t("deleteGoal.success"));
      window.location.reload();
    } catch (err) {
      console.error(err);
      toast.error(t("deleteGoal.failed"));
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild className="-my-1.5 -mx-2 h-8">
        <Button
          variant="default"
          className="w-[calc(100%+16px)] px-6 justify-start bg-red-500/80 hover:bg-red-500/60 active:bg-red-600 shadow-none text-sm group"
        >
          <ArchiveIcon className="text-white" />
          <span className="text-white">{t("deleteGoal.archiveGoal")}</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-96">
        <DialogHeader>
          <DialogTitle>{t("deleteGoal.archiveGoal")}</DialogTitle>
        </DialogHeader>
        <DialogDescription className="my-3">
          {t("deleteGoal.confirmation")}
        </DialogDescription>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="submit"
              className="hover:text-white hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950"
            >
              {t("deleteGoal.cancel")}
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              onClick={ArchiveGoalByID}
              className="text-white bg-red-500 hover:bg-red-600 active:bg-red-500/70"
            >
              {t("deleteGoal.archiveGoal")}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
