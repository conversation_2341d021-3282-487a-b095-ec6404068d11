"use client";

import {
  createPlan,
  deletePlan,
  getAllPlanLinksForAdmin,
  getAllPlansForAdmin,
  togglePlanVisibility,
  updatePlan,
  type CreatePlanData,
} from "@/actions/BillingActions";
import CreatePlanDialog from "@/components/admin/CreatePlanDialog";
import PlanLinksList from "@/components/admin/PlanLinksList";
import PlansList from "@/components/admin/PlansList";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plan } from "@/types";
import { Plus, RefreshCw } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import useSWR from "swr";

const fetchPlans = async () => {
  const response = await getAllPlansForAdmin();
  if (!response.success) {
    throw new Error(response.error || "Failed to load plans");
  }
  return response.data || [];
};

export default function AdminPage() {
  // Dialog states
  const [isCreatingPlan, setIsCreatingPlan] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingPlan, setEditingPlan] = useState<Plan | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);

  // SWR for plans
  const {
    data: plans = [],
    isLoading: isLoadingPlans,
    error: plansError,
    mutate: mutatePlans,
  } = useSWR("admin-plans", fetchPlans);

  const handleCreatePlan = async (planData: CreatePlanData) => {
    try {
      setIsCreatingPlan(true);
      const response = await createPlan(planData);

      if (response.success) {
        toast.success("Plan created successfully!");
        setShowCreateDialog(false);
        await mutatePlans(); // Reload plans
      } else {
        toast.error(response.error || "Failed to create plan");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to create plan");
    } finally {
      setIsCreatingPlan(false);
    }
  };

  const handleEditPlan = (plan: Plan) => {
    setEditingPlan(plan);
    setShowEditDialog(true);
  };

  const handleUpdatePlan = async (planData: CreatePlanData, planId: string) => {
    try {
      setIsCreatingPlan(true);
      const response = await updatePlan(planId, planData);

      if (response.success) {
        toast.success("Plan updated successfully!");
        setShowEditDialog(false);
        setEditingPlan(null);
        await mutatePlans(); // Reload plans
      } else {
        toast.error(response.error || "Failed to update plan");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to update plan");
    } finally {
      setIsCreatingPlan(false);
    }
  };

  const handleSubmitPlan = async (
    planData: CreatePlanData,
    planId?: string
  ) => {
    if (planId) {
      await handleUpdatePlan(planData, planId);
    } else {
      await handleCreatePlan(planData);
    }
  };

  const handleDeletePlan = async (planId: string) => {
    try {
      const response = await deletePlan(planId);

      if (response.success) {
        toast.success("Plan deleted successfully!");
        await mutatePlans(); // Reload plans
      } else {
        toast.error(response.error || "Failed to delete plan");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to delete plan");
    }
  };

  const handleToggleVisibility = async (planId: string, visible: boolean) => {
    try {
      const response = await togglePlanVisibility(planId, visible);

      if (response.success) {
        toast.success(`Plan ${visible ? "shown" : "hidden"} successfully!`);
        await mutatePlans(); // Reload plans
      } else {
        toast.error(response.error || "Failed to toggle plan visibility");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to toggle plan visibility");
    }
  };

  return (
    <div className="w-full flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center flex-wrap gap-2">
        <div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
            Subscription Management
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage subscription plans and pricing settings
          </p>
        </div>

        <Button
          onClick={() => setShowCreateDialog(true)}
          className="bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 hover:from-voxa-teal-700 hover:to-voxa-teal-800 text-white ms-auto"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Plan
        </Button>
      </div>

      {/* Plan Form Dialog - Create */}
      <CreatePlanDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSubmit={handleSubmitPlan}
        isLoading={isCreatingPlan}
        isEditMode={false}
      />

      {/* Plan Form Dialog - Edit */}
      <CreatePlanDialog
        open={showEditDialog}
        onOpenChange={(open) => {
          setShowEditDialog(open);
          if (!open) {
            setEditingPlan(null);
          }
        }}
        onSubmit={handleSubmitPlan}
        isLoading={isCreatingPlan}
        editPlan={editingPlan}
        isEditMode={true}
      />

      {/* Subscription Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Subscription Settings</CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Manage your subscription plans and pricing
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => mutatePlans()}
              disabled={isLoadingPlans}
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${
                  isLoadingPlans ? "animate-spin" : ""
                }`}
              />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <PlansList
            plans={plans}
            onEdit={handleEditPlan}
            onDelete={handleDeletePlan}
            onToggleVisibility={handleToggleVisibility}
            isLoading={isLoadingPlans}
          />
        </CardContent>
      </Card>

      {/* PlanLinks List Table */}
      <PlanLinksList />
    </div>
  );
}
