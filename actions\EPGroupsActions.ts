"use server"
import Group from "@/models/Groups"
import mongoose from "mongoose"

export async function GetGroupClients(groupID: string): Promise<{success: boolean, error?: string, clients?: any[]}> {
    try {
        console.log(groupID)
        const groupObjectID = new mongoose.Types.ObjectId(groupID)
        const groupClients = await Group.findOne({_id: groupObjectID}).populate("members")
        if(!groupClients) return {success: false, error: "Group not found"}
        const filteredClients = groupClients.members.map((client: any) => {
            return {
                _id: client._id?.toString(),
                name: client.name,
                phone: client.phone
            }
        })
        // Sort clients alphabetically by name
        const sortedClients = filteredClients.sort((a: any, b: any) => {
            if (!a.name) return 1;
            if (!b.name) return -1;
    
            return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
        });
        
        return {success: true, clients: sortedClients}
    } catch (error: any) {
        return {success: false, error: error.message}
    }
}