import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useRouter } from "next/navigation";
import { Info } from "lucide-react";
import { useTranslation } from "react-i18next";
import Link from "next/link";
import { UnderConstructionDialog } from "./UnderConstructionDialog";
import { useState } from "react";

export function CreateGoalDialog({
  assistant_id,
  phone_id,
}: {
  assistant_id: string;
  phone_id: string;
}) {
  const { t } = useTranslation("assistants");
  const router = useRouter();
  const [showUnderConstruction, setShowUnderConstruction] = useState(false);

  const handleTemplateClick = (template: any, index: number) => {
    if (index === 3) {
      setShowUnderConstruction(true);
    } else {
      router.push(template.value);
    }
  };

  const templates = [
    {
      value: `/businessDash/assistants/Goals/CreateEdit/create?templateType=DEMARCHAGE&assistantID=${assistant_id}&phoneID=${phone_id}`,
      label: [t("createGoal.outboundCalling")],
    },
    {
      value: `/businessDash/assistants/Goals/CreateEdit/create?templateType=GOOGLE_MEET&assistantID=${assistant_id}&phoneID=${phone_id}`,
      label: [
        <div className="flex items-center" key="meet">
          <span>{t("createGoal.googleMeet")}</span>
          <Link
            href="https://business-files-bucket.s3.eu-west-3.amazonaws.com/BusinessFiles/Google+Meet+Integration.pdf"
            target="_blank"
            rel="noopener noreferrer"
            className="ml-2 text-voxa-teal-600 hover:text-voxa-teal-500"
            title={t("createGoal.googleMeetIntegrationInfo")}
            onClick={(e) => e.stopPropagation()}
          >
            <Info className="w-4 h-4" />
          </Link>
        </div>,
      ],
    },
    {
      value: `/businessDash/assistants/Goals/CreateEdit/create?templateType=MULTI_TRANSLATION&assistantID=${assistant_id}&phoneID=${phone_id}`,
      label: [t("createGoal.multiTranslation")],
    },
    {
      value: `/businessDash/assistants/Goals/CreateEdit/create?templateType=SMS_WHATSAPP&assistantID=${assistant_id}&phoneID=${phone_id}`,
      label: [
        <div
          className="w-full justify-between flex items-center gap-1.5"
          key="sms"
        >
          <p>{t("createGoal.smsWhatsappNotifications")}</p>
          <p className="text-xs text-orange-600 ms-auto">
            {t("createGoal.comingSoon")}
          </p>
        </div>,
      ],
    },
  ];
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="font-semibold px-4 bg-voxa-teal-600 hover:bg-voxa-teal-500 dark:text-voxa-neutral-50">
          {t("createGoal.createGoal")}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] shadow-none">
        <DialogHeader>
          <DialogTitle>{t("createGoal.createGoal")}</DialogTitle>
          <DialogDescription className="py-4 text-center text-foreground">
            {t("createGoal.chooseHowToCreate")}
          </DialogDescription>
          <div className="grid grid-cols-1 gap-3">
            {templates.map((template, index) => (
              <div
                key={index}
                onClick={() => handleTemplateClick(template, index)}
                className="p-2.5 rounded-lg border border-border hover:border-voxa-teal-500 bg-card hover:bg-accent/50 cursor-pointer transition-all duration-200 text-sm"
              >
                <div className="flex items-center justify-between">
                  {template.label[0]}
                </div>
              </div>
            ))}
          </div>
        </DialogHeader>
      </DialogContent>
      {showUnderConstruction && (
        <UnderConstructionDialog
          isOpen={showUnderConstruction}
          setIsOpen={setShowUnderConstruction}
        />
      )}
    </Dialog>
  );
}
