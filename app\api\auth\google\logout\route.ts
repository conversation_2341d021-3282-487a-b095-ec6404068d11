import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export const runtime = "nodejs";

export async function POST() {
  const tokenStr = (await cookies()).get("google_tokens")?.value;

  if (tokenStr) {
    const { access_token } = JSON.parse(tokenStr);
    if (access_token) {
      try {
        // Appel REST pour révoquer le token côté Google
        await fetch("https://oauth2.googleapis.com/revoke", {
          method: "POST",
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          body: new URLSearchParams({ token: access_token }).toString(),
        });
      } catch {
        /* silence : le token est peut‑être déjà expiré */
      }
    }
  }

  // • suppression du cookie
  (await cookies()).delete("google_tokens");
  return new NextResponse(null, { status: 204 });
}
