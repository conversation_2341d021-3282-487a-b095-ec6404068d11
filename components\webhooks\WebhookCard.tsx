"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";

interface WebhookCardProps {
  webhook: {
    event?: string;
    method?: string;
    url?: string;
  };
  onUrlChange?: (event: string, url: string) => void;
  isValidUrl?: boolean;
}

const WebhookCard: React.FC<WebhookCardProps> = ({
  webhook,
  onUrlChange,
  isValidUrl = true,
}) => {
  const [url, setUrl] = useState(webhook.url || "");

  useEffect(() => {
    setUrl(webhook.url || "");
  }, [webhook]);

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setUrl(newUrl);
    if (onUrlChange && webhook.event) {
      onUrlChange(webhook.event, newUrl);
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-[220px_1fr] p-1.5 gap-2 bg-voxa-neutral-50 dark:text-voxa-neutral-300 dark:bg-voxa-neutral-900/80 rounded-md items-start justify-between w-full">
      <div className="flex gap-2 items-center min-w-0 self-start pt-2">
        <span
          className={`text-xs font-medium uppercase px-2 py-1 rounded ${
            webhook.method === "POST"
              ? "bg-blue-500 text-white"
              : "bg-gray-500 text-white"
          }`}
        >
          {webhook.method}
        </span>
        <span
          title={webhook.event || ""}
          className="block text-sm font-semibold truncate min-w-0"
        >
          {webhook.event}
        </span>
      </div>
      <div className="flex flex-col w-full">
        <Input
          placeholder="https://example.com/webhook"
          value={url}
          onChange={handleUrlChange}
          className={`w-full ${
            !isValidUrl ? "border-red-500 focus-visible:ring-red-500" : ""
          }`}
        />
        {!isValidUrl && url && (
          <p className="text-red-500 text-xs mt-1">
            Please enter a valid URL (e.g., https://example.com/webhook)
          </p>
        )}
      </div>
    </div>
  );
};

export default WebhookCard;
