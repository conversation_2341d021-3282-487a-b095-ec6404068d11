"use server"

import dbConnect from "@/lib/mongodb";
import { getEntrepriseByAdminID } from "./Entreprise";
import Phone from "@/models/PhoneNumbers";
import Entreprise from "@/models/Entreprise";
import Assistant from "@/models/Assistant";
import mongoose from "mongoose";

export async function CreateNumber(country: string): Promise<{success: boolean, error?: string}>{
    try{
        const entrepriseResponse = await getEntrepriseByAdminID()
        if(!entrepriseResponse.success){
            return {success: false, error: entrepriseResponse.error}
        }
        await dbConnect()

        const entrepriseID = entrepriseResponse.entreprise._id

        const phone = await Phone.create({
            country,
            entreprise_id: entrepriseID,
        })
        if(!phone){
            return {success: false, error: "Failed to create phone number"}
        }

        const entreprise = await Entreprise.findById(entrepriseID)
        if(!entreprise){
            return {success: false, error: "Entreprise not found"}
        }

        if(!entreprise.numbers){
            entreprise.phone = []
        }
        entreprise.numbers.push(phone._id)

        await entreprise.save()

        return {success: true}
    }catch(e: any){
        return {success: false, error: e.message}
    }
}

export async function getEntrepriseNumbers(): Promise<{success: boolean, error?: string, numbers?: any[]}>{
    try{
        const entrepriseResponse = await getEntrepriseByAdminID()
        if(!entrepriseResponse.success){
            return {success: false, error: entrepriseResponse.error}
        }

        const entrepriseID = entrepriseResponse.entreprise._id
        await dbConnect()

        const entreprise = await Entreprise.findById(entrepriseID).populate('numbers')
        if(!entreprise){
            return {success: false, error: "Entreprise not found"}
        }

        if(!Array.isArray(entreprise.numbers)){
            return {success: false, error: "No numbers found"}
        }

        const formattedNumbers = entreprise.numbers
        .filter((phone: any) => phone.status === "ACTIVE" && !phone.assistant)
        .map((phone: any) => ({
            id: phone._id.toString(),
            number: phone.number,
            country: phone.country,
            status: phone.status
        }))

        console.log(formattedNumbers)

        return {success: true, numbers: formattedNumbers}
    }catch(err: any){
        return {success: false, error: err.message}
    }
}

export async function addNumberToAssistant(
    phoneID: string,
    assistantID: string
): Promise<{ success: boolean; error?: string }> {
    try {
        await dbConnect();
        console.log({phoneID, assistantID});
        const phoneObjectId = new mongoose.Types.ObjectId(phoneID);
        const assistantObjectId = new mongoose.Types.ObjectId(assistantID);

        console.log(phoneObjectId, assistantObjectId);

        const phone = await Phone.findById(phoneObjectId);
        if (!phone) {
            return { success: false, error: "Phone number not found" };
        }

        const updatedPhone = await Phone.findByIdAndUpdate(
            phoneObjectId,
            { assistant: assistantObjectId },
            { new: true }
        );
        console.log(updatedPhone);
        if (!updatedPhone) {
            return { success: false, error: "Failed to update phone number" };
        }

        const updatedAssistant = await Assistant.findByIdAndUpdate(
            assistantObjectId,
            { $addToSet: { numbers: phoneObjectId } }, 
            { new: true }
        );
        if (!updatedAssistant) {
            return { success: false, error: "Failed to update assistant" };
        }

        return { success: true };
    } catch (err: any) {
        return { success: false, error: err.message };
    }
}
