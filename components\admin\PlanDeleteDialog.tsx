"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface PlanDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirmDelete: () => void;
  onHideInstead?: () => void;
  planName: string;
  subscriptionCount: number;
  isDeleting: boolean;
}

export default function PlanDeleteDialog({
  isOpen,
  onClose,
  onConfirmDelete,
  onHideInstead,
  planName,
  subscriptionCount,
  isDeleting,
}: PlanDeleteDialogProps) {
  const canDelete = subscriptionCount === 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Plan</DialogTitle>
        </DialogHeader>
        <DialogDescription>
          {canDelete ? (
            <>
              Are you sure you want to permanently delete the plan {`"`}
              {planName}
              {`"`}? This action cannot be undone.
            </>
          ) : (
            <>
              The plan {`"`}
              {planName}
              {`"`} has {subscriptionCount} existing subscription
              {subscriptionCount !== 1 ? "s" : ""}. You cannot delete a plan
              with existing subscriptions. Please hide the plan instead or wait
              for all subscriptions to be cancelled.
            </>
          )}
        </DialogDescription>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {canDelete ? (
            <Button
              variant="destructive"
              onClick={onConfirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  Deleting...
                  <div className="ml-2 animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </>
              ) : (
                "Delete Plan"
              )}
            </Button>
          ) : (
            onHideInstead && (
              <Button
                variant="secondary"
                onClick={onHideInstead}
                disabled={isDeleting}
              >
                Hide Plan Instead
              </Button>
            )
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
