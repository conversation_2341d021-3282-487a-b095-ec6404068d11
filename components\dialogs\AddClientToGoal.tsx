import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { AppDispatch, RootState } from "@/redux/store";
import { useDispatch, useSelector } from "react-redux";
import {
  addClientToGoal,
  setAddClientToGoalOpen,
} from "@/redux/BusinessDashboard/subSlices/ClientsSlice";
import { toast } from "sonner";

export function AddClientToGoalDialog({ goal }: { goal: any }) {
  const { clientDetails, addClientToGoalOpen, clientSelectedGoal, loading } =
    useSelector(
      (state: RootState) => state.businessDashboard.businessDashboardClients
    );
  const dispatch = useDispatch<AppDispatch>();

  const handleAddClientToGoal = async () => {
    if (!clientSelectedGoal) return toast.error("No goal selected!");
    await dispatch(
      addClientToGoal({ clientID: clientDetails._id, goal: clientSelectedGoal })
    );
  };

  return (
    <Dialog
      open={addClientToGoalOpen}
      onOpenChange={(open) =>
        dispatch(setAddClientToGoalOpen({ open, goal: open ? goal : null }))
      }
    >
      <DialogTrigger asChild className="h-8">
        <Button className="text-bg-voxa-neutral-800 bg-voxa-neutral-100 dark:bg-voxa-neutral-700 hover:text-white hover:dark:bg-voxa-neutral-500 px-2 w-full rounded-full mx-2 flex flex-col">
          <p className="font-medium text-xs">{goal.name}</p>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] shadow-none">
        <DialogHeader>
          <DialogTitle>Add Client to Goal</DialogTitle>
          <DialogDescription className="text-center text-foreground">
            Are you sure you want to add{" "}
            <span className="font-semibold">{clientDetails.name}</span> to{" "}
            <span className="font-semibold">
              {clientSelectedGoal?.name || "this goal"}
            </span>
            ?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button className="hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-500 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950">
              Cancel
            </Button>
          </DialogClose>
          <Button
            className="bg-voxa-teal-600 hover:bg-voxa-teal-500 text-voxa-neutral-50"
            onClick={handleAddClientToGoal}
            disabled={loading.addLoading}
          >
            {loading.addLoading ? "Adding to goal..." : "Add Client"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
