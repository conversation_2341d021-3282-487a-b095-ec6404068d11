"use server";

import { getServerSession } from "next-auth";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import authOptions from "@/lib/AuthOptions";
import { BusinessFilesS3 } from "@/lib/S3Client";
import {
  GetObjectCommand,
  HeadObjectCommand,
  PutObjectCommand,
} from "@aws-sdk/client-s3";

export async function GetSignedURL(filename: string) {
  const session = await getServerSession(authOptions);
  if (!session) return { failure: "You must be signed in to upload files" };

  const command = new PutObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME!,
    Key: `BusinessFiles/${session.user.name?.replaceAll(
      " ",
      "_"
    )}_${filename.replaceAll(" ", "_")}_${Date.now()}`,
    ContentType: "image/jpeg",
  });

  const url = await getSignedUrl(BusinessFilesS3, command, { expiresIn: 60 });

  return { success: { url: url } };
}

export async function GetRagFilesSignedUrl(filename: string, name: string) {
  const session = await getServerSession(authOptions);
  if (!session) return { failure: "You must be signed in to upload files" };

  const key = `RagFiles/${session.user.name?.replaceAll(
    " ",
    "_"
  )}_${filename.replaceAll(" ", "_")}_${Date.now()}_${name.replaceAll(
    " ",
    "_"
  )}`;

  const command = new PutObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME!,
    Key: key,
    ContentType: "application/pdf",
  });

  try {
    const url = await getSignedUrl(BusinessFilesS3, command, { expiresIn: 60 });
    return { success: { url: url } };
  } catch (error) {
    console.error("Error generating signed URL:", error);
    return { failure: "Failed to generate signed URL" };
  }
}

function extractKeyFromUrl(url: string) {
  const urlObj = new URL(url);
  return urlObj.pathname.slice(1);
}

export async function getBusinessFilesPresignedUrl(url: string) {
  const key = extractKeyFromUrl(url);
  console.log("Key:", key);
  if (!key) {
    throw new Error("File name is required.");
  }

  try {
    // ✅ Check if file exists using HeadObjectCommand
    await BusinessFilesS3.send(
      new HeadObjectCommand({ Bucket: process.env.AWS_BUCKET_NAME!, Key: key })
    );

    // ✅ If it exists, generate the presigned URL
    const command = new GetObjectCommand({
      Bucket: process.env.AWS_BUCKET_NAME!,
      Key: key,
      ResponseContentDisposition: "attachment",
    });

    return await getSignedUrl(BusinessFilesS3, command, { expiresIn: 3600 });
  } catch (error: any) {
    if (error.name === "NotFound" || error.$metadata?.httpStatusCode === 404) {
      return null; // File not found
    }
    console.error("S3 Error:", error);
    throw new Error("Failed to fetch file.");
  }
}
