"use client";

import { useState, useEffect, useCallback } from "react";
import { GetConversationsByToNumber } from "@/actions/ConversationActions";
import { toast } from "sonner";
import { getTimeFromTimestamp } from "@/lib/Strings/DateFormat";
import {
  PhoneMissedIcon,
  PhoneOutgoingIcon,
  Loader2,
  ChevronsDown,
  VoicemailIcon,
  PhoneIncomingIcon,
  RedoDotIcon,
  ArrowDownLeft,
  ArrowUpRight,
  MessageSquareReplyIcon,
  MessageSquareShareIcon,
  Download,
} from "lucide-react";
import CircularLoaderSmall from "../Loaders/CircularLoaderSmall";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";
import { useTranslation } from "react-i18next";
import CustomButton from "../CustomFormItems/Button";
import { IoLogoWhatsapp } from "react-icons/io";
import { Button } from "../ui/button";
import { Card, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "../ui/card";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipTrigger } from "../ui/tooltip";

export default function NumberHistory({
  conversationID,
  to,
}: {
  conversationID: string;
  to: string;
}) {
  const { t, i18n } = useTranslation("callLogs");
  const [conversations, setConversations] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [page, setPage] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [loadedCount, setLoadedCount] = useState<number>(0);
  const [exporting, setExporting] = useState<boolean>(false);
  const LIMIT = 10;

  const fetchConversations = useCallback(
    async (skip: number = 0, append: boolean = false) => {
      try {
        if (skip === 0) {
          setLoading(true);
        } else {
          setLoadingMore(true);
        }
        const response = await GetConversationsByToNumber(
          conversationID,
          to,
          LIMIT,
          skip
        );
        if (response.success && response.conversations) {
          if (append) {
            setConversations((prev) => [
              ...prev,
              ...(response.conversations || []),
            ]);
          } else {
            setConversations(response.conversations || []);
          }
          setTotalCount(response.totalCount || 0);
          setLoadedCount(skip + (response.conversations?.length || 0));
          setHasMore(
            (response.totalCount || 0) >
              skip + (response.conversations?.length || 0)
          );
        } else if (response.error) {
          toast.error(response.error);
        }
      } catch (error) {
        console.error("Error fetching conversations:", error);
        toast.error("Failed to fetch conversation history");
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [to, conversationID]
  );

  const loadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    fetchConversations(nextPage * LIMIT, true);
  };

  useEffect(() => {
    setPage(0);
    setHasMore(true);
    setLoadedCount(0);
    fetchConversations(0, false);
  }, [to, fetchConversations]);

  const handleExportConversations = async () => {
    try {
      setExporting(true);
      const response = await GetConversationsByToNumber(conversationID, to);

      if (!response.success || !response.conversations) {
        throw new Error(response.error || "Failed to export conversations");
      }
      const csvData: any[] = response.conversations.map((conv: any) => ({
        To: conv.To ?? "",
        From: conv.From ?? "",
        Status: conv.CallStatus ?? "",
        CallSid: conv.CallSid ?? "",
        TimeStamp: conv.TimeStamp ?? "",
        Duration: conv.CallDuration ?? "",
        Type: conv.Type ?? "",
        Direction: conv.Direction ?? "",
      }));

      const headers = [
        "To",
        "From",
        "Status",
        "SID",
        "Date",
        "Duration",
        "Type",
        "Direction",
      ];

      const escapeCsvValue = (value: any) => {
        if (value === null || value === undefined) {
          return "";
        }
        const stringValue = String(value);
        if (
          stringValue.includes('"') ||
          stringValue.includes(",") ||
          stringValue.includes("\n")
        ) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      };

      const csvString = [
        headers.join(","),
        ...csvData.map((row) =>
          [
            row.To,
            row.From,
            row.Status,
            row.CallSid,
            row.TimeStamp,
            row.Duration,
            row.Type,
            row.Direction,
          ]
            .map(escapeCsvValue)
            .join(",")
        ),
      ].join("\n");

      const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", `conversations_${to}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success("Conversations exported successfully");
    } catch (error: any) {
      console.error("Error exporting conversations:", error);
      toast.error("Failed to export conversations");
    } finally {
      setExporting(false);
    }
  };

  return (
    <Card className="relative bg-voxa-neutral-100 dark:bg-voxa-neutral-950">
      {!loading && (
        <Tooltip>
          <TooltipTrigger asChild className="absolute top-3 left-3">
            <Button
              onClick={handleExportConversations}
              variant="outlined"
              className="hover:bg-voxa-neutral-50 dark:hover:bg-voxa-neutral-800 h-9 w-9 rounded-full bg-white dark:bg-black/40 dark:border-voxa-neutral-700"
            >
              {exporting ? <Loader2 className="animate-spin" /> : <Download />}
            </Button>
          </TooltipTrigger>
          <TooltipContent className="bg-white fill-white dark:fill-sidebar dark:bg-sidebar">
            {t("numberHistory.exportConversations")}
          </TooltipContent>
        </Tooltip>
      )}
      <CardHeader>
        <CardTitle className="text-voxa-teal-600 text-xl text-center pb-1">
          {t("notesTabs.conversationHistory")}
        </CardTitle>
      </CardHeader>
      <CardContent className="w-full px-1.5 sm:px-2 pb-3 pt-0">
        <div className="w-full flex flex-col space-y-2 items-center">
          {!loading && (
            <span className="w-full text-center text-sm font-medium dark:text-gray-200">
              {loadedCount} / {totalCount}{" "}
              {t("numberHistory.conversationsFound")}
            </span>
          )}
          {loading ? (
            <div className="min-h-96 flex justify-center w-full">
              <CircularLoaderSmall />
            </div>
          ) : (
            conversations.map((conv, index) => (
              <div key={index}>
                {conversations.length === 0 ? (
                  <div className="text-center dark:text-gray-300">
                    {t("numberHistory.noConversationHistory")}
                  </div>
                ) : (
                  <div
                    key={index}
                    className="w-full sm:w-full bg-voxa-neutral-50 dark:bg-voxa-neutral-900 rounded-xl hover:bg-voxa-neutral-200/60 dark:hover:bg-voxa-neutral-800 transition-colors flex rtl:flex-grow rtl:flex-row-reverse rtl:text-right gap-1.5 p-2 items-center dark:text-voxa-neutral-200 text-[10px] sm:text-[13px] font-medium"
                  >
                    {conv.is_voicemail_drop === true ||
                    conv.voicemailDetected === true ? (
                      <div className="bg-indigo-500/20 rounded-full min-h-10 min-w-10 flex items-center justify-center">
                        <VoicemailIcon className="text-indigo-500 size-5 sm:size-[22px]" />
                      </div>
                    ) : conv.Type?.toLowerCase() === "sms" ? (
                      conv.Direction?.toLowerCase() === "outbound_api" ? (
                        <div className="bg-teal-700/20 rounded-full min-h-10 min-w-10 flex items-center justify-center">
                          <MessageSquareShareIcon className="text-teal-500 size-5 sm:size-[22px]" />
                        </div>
                      ) : conv.Direction?.toLowerCase() === "inbound" ? (
                        <div className="bg-teal-700/20 rounded-full min-h-10 min-w-10 flex items-center justify-center">
                          <MessageSquareReplyIcon className="text-teal-500 size-5 sm:size-[22px]" />
                        </div>
                      ) : (
                        ""
                      )
                    ) : conv.Type?.toLowerCase() === "whatsapp" ? (
                      conv.Direction?.toLowerCase() === "outbound_api" ? (
                        <div className="relative w-fit h-fit">
                          <div className="bg-teal-700/20 rounded-full min-h-10 min-w-10 flex items-center justify-center">
                            <IoLogoWhatsapp className="text-teal-500 size-5 sm:size-[22px]" />
                          </div>
                          <ArrowUpRight
                            className="absolute -top-1 right-0 text-teal-500 w-4 h-4"
                            strokeWidth={3}
                          />
                        </div>
                      ) : conv.Direction?.toLowerCase() === "inbound" ? (
                        <div className="relative w-fit h-fit">
                          <div className="bg-teal-700/20 rounded-full min-h-10 min-w-10 flex items-center justify-center">
                            <IoLogoWhatsapp className="text-teal-500 size-5 sm:size-[22px]" />
                          </div>
                          <ArrowDownLeft
                            className="absolute -top-1 right-0 text-teal-500 w-4 h-4"
                            strokeWidth={3}
                          />
                        </div>
                      ) : (
                        ""
                      )
                    ) : conv.is_transfer ? (
                      <div className="bg-purple-500/20 rounded-full min-h-10 min-w-10 flex items-center justify-center">
                        <RedoDotIcon className="text-purple-500 size-5 sm:size-[22px]" />
                      </div>
                    ) : conv.Direction === "inbound" ? (
                      <div className="bg-indigo-500/20 rounded-full min-h-10 min-w-10 flex items-center justify-center">
                        <PhoneIncomingIcon className="text-indigo-500 size-5 sm:size-[22px]" />
                      </div>
                    ) : conv.CallStatus === "missed" ||
                      conv.CallStatus === "no-answer" ||
                      conv.CallDuration == 0 ? (
                      <div className="bg-red-500/20 rounded-full min-h-10 min-w-10 flex items-center justify-center">
                        <PhoneMissedIcon className="text-red-500 size-5 sm:size-[22px]" />
                      </div>
                    ) : (
                      <div className="bg-green-500/20 rounded-full min-h-10 min-w-10 flex items-center justify-center">
                        <PhoneOutgoingIcon className="text-green-500 size-5 sm:size-[22px]" />
                      </div>
                    )}
                    <div className="w-full sm:space-y-[1.5px]">
                      <div className="flex justify-between rtl:flex-grow rtl:flex-row-reverse">
                        <p className="flex font-semibold text-voxa-neutral-600 dark:text-voxa-neutral-400 rtl:flex-grow rtl:flex-row-reverse text-nowrap gap-0.5">
                          {t("numberHistory.sid")}:
                        </p>
                        <p className="max-sm:truncate rtl:truncate max-sm:max-w-[200px] rtl:sm:max-w-[220px] ml-0.5">
                          {conv.CallSid}
                        </p>
                      </div>
                      <div className="w-full flex gap-2 justify-between rtl:sm:gap-6">
                        <div className="flex gap-1 rtl:flex-grow rtl:flex-row-reverse">
                          <div className="flex font-semibold text-voxa-neutral-600 dark:text-voxa-neutral-400 rtl:flex-grow rtl:flex-row-reverse">
                            <p>{t("numberHistory.status")}</p>
                            <p>:</p>
                          </div>
                          <p className="w-full flex justify-start rtl:justify-end">
                            {conv.CallStatus}
                          </p>
                        </div>
                        <div className="flex gap-1 rtl:flex-grow rtl:flex-row-reverse">
                          <div className="sm:pl-4 flex font-semibold text-voxa-neutral-600 dark:text-voxa-neutral-400 rtl:flex-grow rtl:flex-row-reverse">
                            {t("numberHistory.from")}:
                          </div>
                          <p className="w-full flex">
                            {conv.From && phoneNumberFormat(conv.From)}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-1 items-center">
                        <p className="font-semibold text-voxa-neutral-600 dark:text-voxa-neutral-400">
                          {t("numberHistory.date")}:
                        </p>
                        <p>
                          {getTimeFromTimestamp(conv.TimeStamp, i18n.language)}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
          {/* Load More Button */}
          {!loading && hasMore && (
            <CustomButton
              props={{
                value: t("buttons.loadMore"),
                onClick: loadMore,
                className:
                  "mx-auto hover:bg-voxa-teal-500 bg-voxa-teal-600 dark:bg-voxa-teal-600 dark:hover:bg-voxa-teal-500 w-fit self-center mt-3 py-2 px-4 rounded-md font-medium flex justify-center items-center gap-1",
                icon: loadingMore ? (
                  <Loader2 className="animate-spin" />
                ) : (
                  <ChevronsDown />
                ),
                loading: loadingMore,
              }}
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
}
