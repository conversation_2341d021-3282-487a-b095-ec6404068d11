"use client";

import ExportDropdown, {
  ExportDropdownItem,
} from "@/components/analytics/ExportDropdown";
import CircularLoaderSmall from "@/components/Loaders/CircularLoaderSmall";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  exportChartAsImage,
  exportChartAsPDF,
} from "@/lib/analytics/chartExport";
import { CountryCode } from "@/lib/countries";
import {
  convertCountriesToUppercase,
  TimelineStatsEntry,
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { store } from "@/redux/store";
import { useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { CartesianGrid, Line, LineChart, <PERSON>A<PERSON>s, <PERSON>Axis } from "recharts";
import { useTheme } from "next-themes";
import useSWR from "swr";

// 1. Define a ChartConfig mapping each series key to a label and color.
//    This will drive both the line colors and the legend labels.
const chartConfig = {
  missed: {
    label: "Missed",
    color: "#e76e50", // red-ish
  },
  answered: {
    label: "Answered",
    color: "#2a9d90", // teal-ish
  },
} satisfies ChartConfig;

type TimePeriod = "day" | "week" | "month" | "weekday" | "hour";

// Format date string based on time period
const formatDate = (dateStr: string, timePeriod: TimePeriod) => {
  if (!dateStr) return "";

  // For weekday, just return the day name
  if (timePeriod === "weekday") return dateStr;

  // For hour, return the hour range
  if (timePeriod === "hour") return dateStr;

  // For day, format as D MMM
  const date = new Date(dateStr);
  const day = date.getDate();
  const month = date.toLocaleString("en-US", { month: "short" }).toLowerCase();

  if (timePeriod === "day") return `${day} ${month}`;

  // For week and month, return as is (already formatted by the server action)
  return dateStr;
};

interface CallStatusByTimeLineChartProps {
  timelinePeriod?: TimePeriod;
  isAnimationActive?: boolean;
}

// SWR fetcher for timeline stats
const fetchTimelineStatsSWR = async (
  period: TimePeriod
): Promise<TimelineStatsEntry[]> => {
  const filters = store.getState().analytics.filters;
  const {
    startDate,
    endDate,
    conversationTypes,
    countries,
    callDuration,
    ringingTime,
  } = filters;

  const { getNumberOfCallsByTimePeriod } = await import(
    "@/actions/AnalyticsActions"
  );
  const res = await getNumberOfCallsByTimePeriod(
    period,
    startDate ? new Date(startDate) : undefined,
    endDate ? new Date(endDate) : undefined,
    conversationTypes,
    countries.length
      ? convertCountriesToUppercase(countries as CountryCode[])
      : countries,
    callDuration,
    ringingTime
  );

  if (!res.success) {
    throw new Error(res.error || "Failed to fetch timeline stats");
  }
  if (!("data" in res)) {
    throw new Error("No data returned from getNumberOfCallsByTimePeriod");
  }
  return res.data as TimelineStatsEntry[];
};

export default function CallStatusByTimeLineChart({
  timelinePeriod: timelinePeriodProp,
  isAnimationActive = true,
  setLoading,
}: CallStatusByTimeLineChartProps & {
  setLoading?: (loading: boolean) => void;
}) {
  const { theme, systemTheme } = useTheme();
  const isDarkMode =
    theme === "dark" || (theme === "system" && systemTheme === "dark");

  // Get filters and timelinePeriod from Redux for dependency tracking
  const filters = useSelector((state: any) => state.analytics.filters);
  const timelinePeriodRedux = useSelector(
    (state: any) => state.analytics.timelinePeriod
  );
  const [timelinePeriodState, setTimelinePeriodState] = useState<TimePeriod>(
    timelinePeriodProp ?? timelinePeriodRedux
  );
  const timelinePeriod = timelinePeriodProp ?? timelinePeriodState;

  const chartRef = useRef<HTMLDivElement>(null);

  // Use SWR to fetch timeline stats
  const {
    data: timelineStats,
    isLoading: timelineLoading,
    error,
  } = useSWR<TimelineStatsEntry[]>(
    ["timelineStats", timelinePeriod, filters],
    () => fetchTimelineStatsSWR(timelinePeriod as TimePeriod),
    {
      revalidateOnFocus: false,
      dedupingInterval: 10000,
    }
  );

  // Call setLoading when loading state changes
  useEffect(() => {
    if (setLoading) setLoading(timelineLoading);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timelineLoading]);

  // Memoize the data so we don't recreate the array on every render.
  const chartData = useMemo(() => {
    return (timelineStats || []).map((entry) => ({
      date: entry.date,
      missed: entry.missed,
      answered: entry.answered,
    }));
  }, [timelineStats]);

  // Check if we have data to display
  const hasData = useMemo(() => {
    return chartData.some((entry) => entry.missed > 0 || entry.answered > 0);
  }, [chartData]);

  // Get chart title based on time period
  const getChartTitle = () => {
    switch (timelinePeriod) {
      case "day":
        return "Daily Calls";
      case "week":
        return "Weekly Calls";
      case "month":
        return "Monthly Calls";
      case "weekday":
        return "Calls by Weekday";
      case "hour":
        return "Hourly Calls";
      default:
        return "Calls (Missed vs Answered)";
    }
  };

  // Define export items for the dropdown
  const exportItems: ExportDropdownItem[] = [
    {
      title: "Export as PNG",
      action: async () => {
        try {
          await exportChartAsImage(
            chartRef.current,
            "png",
            `call-timeline-${timelinePeriod}-chart`
          );
        } finally {
        }
      },
    },
    {
      title: "Export as SVG",
      action: async () => {
        try {
          await exportChartAsImage(
            chartRef.current,
            "svg",
            `call-timeline-${timelinePeriod}-chart`
          );
        } finally {
        }
      },
    },
    {
      title: "Export as PDF",
      action: async () => {
        try {
          await exportChartAsPDF(
            chartRef.current,
            `call-timeline-${timelinePeriod}-chart`
          );
        } finally {
        }
      },
    },
  ];

  if (timelineLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="dark:text-voxa-neutral-50">
            {getChartTitle()} (Missed vs Answered)
          </CardTitle>
          <CardDescription className="dark:text-voxa-neutral-200">
            Loading data...
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[300px]">
          <CircularLoaderSmall />
        </CardContent>
      </Card>
    );
  }

  return (
    <div ref={chartRef} className="flex w-full">
      <Card className="bg-voxa-neutral-50 dark:bg-voxa-neutral-900 dark:border-voxa-neutral-700 transition-all duration-150 w-full">
        <CardHeader className="pb-0">
          <div className="flex flex-row items-start justify-between">
            <div>
              <CardTitle className="dark:text-voxa-neutral-50">
                {getChartTitle()} (Missed vs Answered)
              </CardTitle>
            </div>

            <ExportDropdown
              items={exportItems}
              buttonText="Export"
              loadingText="Exporting..."
              className="mt-0"
            />
          </div>

          <Tabs
            value={timelinePeriod}
            // Only allow changing period if not controlled by prop
            onValueChange={
              timelinePeriodProp
                ? undefined
                : (value: string) => setTimelinePeriodState(value as TimePeriod)
            }
            className="mt-2"
          >
            <TabsList className="flex flex-wrap h-auto dark:bg-transparent">
              <TabsTrigger
                value="day"
                className="text-sm w-fit grow dark:data-[state=active]:bg-voxa-neutral-800"
              >
                Daily
              </TabsTrigger>
              <TabsTrigger
                value="week"
                className="text-sm w-fit grow  dark:data-[state=active]:bg-voxa-neutral-800"
              >
                Weekly
              </TabsTrigger>
              <TabsTrigger
                value="month"
                className="text-sm w-fit grow  dark:data-[state=active]:bg-voxa-neutral-800"
              >
                Monthly
              </TabsTrigger>
              <TabsTrigger
                value="weekday"
                className="text-sm w-fit grow  dark:data-[state=active]:bg-voxa-neutral-800"
              >
                By Weekday
              </TabsTrigger>
              <TabsTrigger
                value="hour"
                className="text-sm w-fit grow  dark:data-[state=active]:bg-voxa-neutral-800"
              >
                Hourly
              </TabsTrigger>
              <div></div>
            </TabsList>
          </Tabs>
        </CardHeader>

        <CardContent className="flex flex-col gap-6 px-0">
          {/* Chart Container */}
          <ChartContainer config={chartConfig} className="w-full max-h-[300px]">
            {error ? (
              <div className="w-full h-[300px] flex flex-col items-center justify-center text-red-500">
                Error loading timeline stats
              </div>
            ) : !hasData ? (
              <div className="w-full h-[300px] flex flex-col items-center justify-center">
                <div className="relative w-full h-[250px] flex items-center justify-center">
                  {/* Horizontal axis line */}
                  <div className="absolute bottom-0 w-[90%] h-[1px] bg-gray-300 dark:bg-gray-700"></div>

                  {/* Vertical axis line */}
                  <div className="absolute left-[5%] h-full w-[1px] bg-gray-300 dark:bg-gray-700"></div>

                  {/* Dashed horizontal guidelines */}
                  <div className="absolute top-[25%] w-[90%] h-[1px] border-t border-dashed border-gray-300 dark:border-gray-700"></div>
                  <div className="absolute top-[50%] w-[90%] h-[1px] border-t border-dashed border-gray-300 dark:border-gray-700"></div>
                  <div className="absolute top-[75%] w-[90%] h-[1px] border-t border-dashed border-gray-300 dark:border-gray-700"></div>

                  {/* Empty state message */}
                  <div className="text-center text-muted-foreground">
                    <p className="font-medium">No data available</p>
                    <p className="text-sm mt-1">
                      No calls recorded for this period
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <LineChart
                data={chartData}
                margin={{ top: 20, right: 20, left: 0, bottom: 5 }}
              >
                {/* Light gray grid lines */}
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke={isDarkMode ? "#444444" : "#e0e0e0"}
                />

                {/* X‐axis: date strings with custom formatter */}
                <XAxis
                  dataKey="date"
                  tick={{
                    fill: isDarkMode ? "#ffffff" : "#000000",
                  }}
                  tickLine={false}
                  axisLine={{
                    stroke: isDarkMode ? "#666666" : "#cccccc",
                  }}
                  interval={Math.floor(chartData.length / 8)} // show about 8 labels
                  tickFormatter={(value: string) =>
                    formatDate(value, timelinePeriod as TimePeriod)
                  }
                />

                {/* Y‐axis: numeric values */}
                <YAxis
                  tick={{
                    fill: isDarkMode ? "#ffffff" : "#000000",
                  }}
                  tickLine={false}
                  axisLine={{
                    stroke: isDarkMode ? "#666666" : "#cccccc",
                  }}
                />

                {/* Tooltip (styled via shadcn) */}
                <ChartTooltip content={<ChartTooltipContent />} />

                {/* Line for "missed" */}
                <Line
                  type="monotone"
                  dataKey="missed"
                  stroke={chartConfig.missed.color}
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                  isAnimationActive={isAnimationActive}
                />

                {/* Line for "answered" */}
                <Line
                  type="monotone"
                  dataKey="answered"
                  stroke={chartConfig.answered.color}
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                  isAnimationActive={isAnimationActive}
                />
              </LineChart>
            )}
          </ChartContainer>

          {/* Legend - always display regardless of data */}
          <div className="flex flex-row justify-center gap-8 text-sm">
            {Object.entries(chartConfig).map(([key, { label, color }]) => (
              <div
                key={key}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                }}
              >
                <span
                  style={{
                    width: "12px",
                    height: "12px",
                    borderRadius: "50%",
                    backgroundColor: color,
                  }}
                />
                <span style={{ color: isDarkMode ? "#ffffff" : "#000000" }}>
                  {label}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
