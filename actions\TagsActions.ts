"use server"

import dbConnect from "@/lib/mongodb";
import Tag from "@/models/Tag";
import { getEntrepriseByAdminID } from "./Entreprise";
import Entreprise from "@/models/Entreprise"
import Goal from "@/models/Goal"

export const getTags = async (): Promise<{success: boolean, error?: string, tags?: any[]}> => {
    try {
        const entrepriseResponse = await getEntrepriseByAdminID()
        if (!entrepriseResponse.success) {
            return { success: false, error: entrepriseResponse.error }
        }

        const EntrepriseID = entrepriseResponse.entreprise._id
        console.log(EntrepriseID)
        await dbConnect()

        const entreprise = await Entreprise.findById(EntrepriseID).populate('tags')
        if (!entreprise) {
            return { success: false, error: "Entreprise not found" }
        }

        if (!Array.isArray(entreprise.tags)) {
            return { success: false, error: "No tags found." }
        }

        const formattedTags = entreprise.tags.map((tag: any) => ({
            _id: tag._id.toString(),
            name: tag.name,
            color: tag.color,
        }))

        return { success: true, tags: formattedTags }

    } catch (e: any) {
        console.log("Error in getTags:", e.message)
        return { success: false, error: e.message }
    }
};

export const CreateNewTag = async (name: string, color: string): Promise<{success: boolean, error?: string, tag?: any}> => {
    try {
        const entrepriseResponse = await getEntrepriseByAdminID();
        if (!entrepriseResponse.success) {
            return { success: false, error: entrepriseResponse.error };
        }

        const EntrepriseID = entrepriseResponse.entreprise._id;

        await dbConnect();

        const tag = await Tag.create({ name, color });
        if (!tag) {
            return { success: false, error: "Failed to create tag" };
        }

        const entreprise = await Entreprise.findById(EntrepriseID);
        if (!entreprise) {
            return { success: false, error: "Entreprise not found" };
        }

        if (!entreprise.tags) {
            entreprise.tags = []
        }

        entreprise.tags.push(tag._id);
        await entreprise.save()

        const tagSimplefied = {
            _id: tag._id.toString(),
            name: tag.name,
            color: tag.color
        }
        return { success: true, tag: tagSimplefied };

    } catch (e: any) {
        console.log(e.message);
        return { success: false, error: e.message };
    }
}

export const getTagsNotInGoal = async (goalID: string): Promise<{success: boolean, error?: string, tags?: any[]}> => {
    try {
        const entrepriseResponse = await getEntrepriseByAdminID();
        if (!entrepriseResponse.success) {
            return { success: false, error: entrepriseResponse.error };
        }
        const EntrepriseID = entrepriseResponse.entreprise._id;
        await dbConnect();
        const entreprise = await Entreprise.findById(EntrepriseID).populate('tags');
        if (!entreprise) {
            return { success: false, error: "Entreprise not found" };
        }
        const allTags = Array.isArray(entreprise.tags) ? entreprise.tags : [];
        const goal = await Goal.findById(goalID).populate('tags');
        if (!goal) {
            return { success: false, error: "Goal not found" };
        }
        const goalTagIds = (goal.tags || []).map((tag: any) => tag._id.toString());
        const filteredTags = allTags.filter((tag: any) => !goalTagIds.includes(tag._id.toString()));
        const formattedTags = filteredTags.map((tag: any) => ({
            _id: tag._id.toString(),
            name: tag.name,
            color: tag.color,
        }));
        return { success: true, tags: formattedTags };
    } catch (e: any) {
        console.log("Error in getTagsNotInGoal:", e.message);
        return { success: false, error: e.message };
    }
};

export const addTagToGoal = async (goalID: string, tagID: string): Promise<{success: boolean, error?: string, tags?: any[]}> => {
    try {
        await dbConnect();
        const goal = await Goal.findById(goalID);
        if (!goal) {
            return { success: false, error: "Goal not found" };
        }
        if (!goal.tags.map((t: any) => t.toString()).includes(tagID)) {
            goal.tags.push(tagID);
            await goal.save();
        }
        await goal.populate('tags');
        const formattedTags = (goal.tags || []).map((tag: any) => ({
            _id: tag._id.toString(),
            name: tag.name,
            color: tag.color,
        }));
        return { success: true, tags: formattedTags };
    } catch (e: any) {
        console.log("Error in addTagToGoal:", e.message);
        return { success: false, error: e.message };
    }
};

export const addTagsToGoal = async (goalID: string, tagIDs: string[]): Promise<{success: boolean, error?: string, tags?: any[]}> => {
    try {
        await dbConnect();
        const goal = await Goal.findById(goalID);
        if (!goal) {
            return { success: false, error: "Goal not found" };
        }
        const currentTagIds = goal.tags.map((t: any) => t.toString());
        let added = false;
        for (const tagID of tagIDs) {
            if (!currentTagIds.includes(tagID)) {
                goal.tags.push(tagID);
                added = true;
            }
        }
        if (added) {
            await goal.save();
        }
        await goal.populate('tags');
        const formattedTags = (goal.tags || []).map((tag: any) => ({
            _id: tag._id.toString(),
            name: tag.name,
            color: tag.color,
        }));
        return { success: true, tags: formattedTags };
    } catch (e: any) {
        console.log("Error in addTagsToGoal:", e.message);
        return { success: false, error: e.message };
    }
};

export const RemoveTagFromGoal = async (goalID: string, tagID: string): Promise<{success: boolean, error?: string, tags?: any[]}> => {
    try {
        await dbConnect();
        const goal = await Goal.findById(goalID);
        if (!goal) {
            return { success: false, error: "Goal not found" };
        }
        goal.tags = goal.tags.filter((t: any) => t.toString() !== tagID);
        await goal.save();
        await goal.populate('tags');
        const formattedTags = (goal.tags || []).map((tag: any) => ({
            _id: tag._id.toString(),
            name: tag.name,
            color: tag.color,
        }));
        return { success: true, tags: formattedTags };
    } catch (e: any) {
        console.log("Error in removeTagFromGoal:", e.message);
        return { success: false, error: e.message };
    }
};

export const getGoalTags = async (goalID: string): Promise<{success: boolean, error?: string, tags?: any[]}> => {
    try {
        await dbConnect();
        const goal = await Goal.findById(goalID).populate('tags');
        if (!goal) {
            return { success: false, error: "Goal not found" };
        }
        const formattedTags = (goal.tags || []).map((tag: any) => ({
            _id: tag._id.toString(),
            name: tag.name,
            color: tag.color,
        }));
        return { success: true, tags: formattedTags };
    } catch (e: any) {
        console.log("Error in getGoalTags:", e.message);
        return { success: false, error: e.message };
    }
};