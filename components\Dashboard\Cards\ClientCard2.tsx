import React from "react";
import { Bookmark, Phone, Sparkle, UserRoundX, Users2Icon } from "lucide-react";
import * as Flags from "country-flag-icons/react/3x2";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  handleOpenCallDetails,
  toggleClientFavorite,
  toggleClientBlacklist,
} from "@/redux/BusinessDashboard/subSlices/ClientsSlice";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";
import { ClientType } from "@/types/Models";
import { CountryCode } from "libphonenumber-js";
import clsx from "clsx";

function highlightMatch(text: string, pattern: string) {
  if (!pattern) return text;
  const textNoSpaces = text.replace(/\s+/g, "");
  const patternNoSpaces = pattern.replace(/\s+/g, "");
  if (!patternNoSpaces) return text;

  const regex = new RegExp(
    patternNoSpaces.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
    "gi"
  );
  let match;
  const matchRanges: [number, number][] = [];
  while ((match = regex.exec(textNoSpaces)) !== null) {
    matchRanges.push([match.index, match.index + match[0].length]);
    if (regex.lastIndex === match.index) regex.lastIndex++;
  }
  if (!matchRanges.length) return text;

  const result = [];
  let textIdx = 0;
  let noSpaceIdx = 0;
  let matchIdx = 0;
  while (textIdx < text.length) {
    if (text[textIdx] === " ") {
      result.push(<React.Fragment key={textIdx}> </React.Fragment>);
      textIdx++;
      continue;
    }
    let inMatch = false;
    if (matchIdx < matchRanges.length) {
      const [start, end] = matchRanges[matchIdx];
      if (noSpaceIdx >= start && noSpaceIdx < end) inMatch = true;
      if (noSpaceIdx === end) matchIdx++;
    }
    if (inMatch) {
      result.push(
        <b key={textIdx} className="text-voxa-teal-700 dark:text-voxa-teal-400">
          {text[textIdx]}
        </b>
      );
    } else {
      result.push(
        <React.Fragment key={textIdx}>{text[textIdx]}</React.Fragment>
      );
    }
    textIdx++;
    noSpaceIdx++;
  }
  return result;
}

export default function ClientCard({ client }: { client: ClientType }) {
  const dispatch = useDispatch<AppDispatch>();
  const FlagComponent = Flags[client.country as keyof typeof Flags];
  const { clientDetails, pattern, clientDetailsOpen } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardClients
  );

  return (
    <div
      onClick={() => dispatch(handleOpenCallDetails({ bool: true, client }))}
      className={clsx(
        "relative rounded-lg border w-full sm:h-12 grid grid-cols-1 sm:grid-cols-[6fr_4fr_2fr] gap-2 sm:gap-4 items-center hover:bg-voxa-neutral-100 dark:hover:bg-voxa-neutral-900 cursor-pointer transition-all duration-200 p-1",
        clientDetails?._id === client._id && clientDetailsOpen
          ? "bg-voxa-neutral-100 dark:bg-voxa-neutral-900 border-foreground"
          : "bg-sidebar border-sidebar-border"
      )}
    >
      <div className="max-sm:mx-auto flex gap-1.5 items-center overflow-hidden">
        {FlagComponent ? (
          <FlagComponent className="w-4 sm:w-5 h-3 sm:h-4 overflow-visible" />
        ) : (
          <span className="text-base overflow-visible">🌎</span>
        )}
        <span className="dark:text-voxa-neutral-50 text-xs sm:text-sm truncate">
          {highlightMatch(client.name as string, pattern)}
        </span>
      </div>
      <div className="max-sm:mx-auto w-32 flex gap-1 sm:gap-2 items-center sm:justify-start">
        <Phone className="dark:text-voxa-neutral-200 w-3.5 h-3.5" />
        <span className="dark:text-voxa-neutral-50 text-xs text-nowrap">
          {highlightMatch(
            phoneNumberFormat(
              client.phone as string,
              client.country as CountryCode
            ),
            pattern
          )}
        </span>
      </div>
      <div className="flex items-center justify-center sm:justify-end">
        <Tooltip>
          <TooltipTrigger asChild className="h-full rounded-full">
            <Button
              variant="ghost"
              className="w-8 h-8"
              onClick={(e) => e.stopPropagation()}
            >
              <Users2Icon className="h-3 w-3" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <div className="flex flex-col gap-1">
              {Array.isArray(client.groups) && client.groups.length > 0 ? (
                client.groups.map(
                  (group: { name: string; _id: string }, index: number) => (
                    <p key={index} className="flex items-center gap-1">
                      <Sparkle className="fill-voxa-teal-600 text-voxa-teal-600 size-2.5" />
                      {group.name}
                    </p>
                  )
                )
              ) : (
                <p>No groups found for this client</p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
        <BlacklistToggle
          clientID={client._id as string}
          isBlacklisted={client.isBlacklisted as boolean}
        />
        <FavoriteToggle
          clientID={client._id as string}
          isFavorited={client.isFavorited as boolean}
        />
      </div>
    </div>
  );
}

export function FavoriteToggle({
  clientID,
  isFavorited,
  className,
}: {
  clientID: string;
  isFavorited: boolean;
  className?: string;
}) {
  const dispatch = useDispatch<AppDispatch>();

  const handleToggleFavorite = async (e: React.MouseEvent) => {
    e.stopPropagation();

    await dispatch(toggleClientFavorite({ clientID: clientID }));
  };
  return (
    <Tooltip>
      <TooltipTrigger asChild className={clsx(className)}>
        <Button
          onClick={handleToggleFavorite}
          className={`w-8 h-8 rounded-full hover:text-yellow-500 ${
            isFavorited ? "text-yellow-500" : ""
          }`}
          variant="ghost"
        >
          <Bookmark
            className={`h-3 w-3 hover:fill-yellow-400 transition-all duration-150 ${
              isFavorited ? "fill-yellow-500" : "fill-none"
            }`}
          />
        </Button>
      </TooltipTrigger>
      <TooltipContent>Toggle Favorite</TooltipContent>
    </Tooltip>
  );
}

export function BlacklistToggle({
  clientID,
  isBlacklisted,
  className,
}: {
  clientID: string;
  isBlacklisted: boolean;
  className?: string;
}) {
  const dispatch = useDispatch<AppDispatch>();

  const handleToggleBlacklist = async (e: React.MouseEvent) => {
    e.stopPropagation();
    await dispatch(toggleClientBlacklist({ clientID: clientID as string }));
  };
  return (
    <Tooltip>
      <TooltipTrigger asChild className={clsx(className)}>
        <Button
          onClick={handleToggleBlacklist}
          className={`w-8 h-8 rounded-full hover:text-red-500 ${
            isBlacklisted ? "text-red-500" : ""
          }`}
          variant="ghost"
        >
          <UserRoundX
            className={`h-3 w-3 hover:fill-red-400 transition-all duration-150 ${
              isBlacklisted ? "fill-red-500" : "fill-none"
            }`}
          />
        </Button>
      </TooltipTrigger>
      <TooltipContent>Toggle Blacklist</TooltipContent>
    </Tooltip>
  );
}
