import mongoose from "mongoose";

const SubscriptionSchema = new mongoose.Schema(
  {
    // Plan reference
    plan_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plan",
    },

    // Enterprise reference
    entreprise_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Entreprise",
    },

    // Stripe subscription data
    stripe_subscription_id: {
      type: String,
      unique: true,
      sparse: true,
    },
    stripe_customer_id: {
      type: String,
    },

    // Subscription details
    status: {
      type: String,
      enum: [
        "active",
        "past_due",
        "unpaid",
        "canceled",
        "incomplete",
        "incomplete_expired",
        "trialing",
        "paused",
      ],
      default: "incomplete",
    },

    // Pricing snapshot (in case plan prices change)
    price_at_subscription: {
      type: Number,
    },
    currency: {
      type: String,
      default: "eur",
    },
    billing_period: {
      type: String,
      enum: ["monthly", "yearly", "weekly", "daily"],
    },

    // Subscription periods
    current_period_start: {
      type: Date,
    },
    current_period_end: {
      type: Date,
    },

    // Trial information
    trial_start: {
      type: Date,
    },
    trial_end: {
      type: Date,
    },

    // Cancellation
    cancel_at_period_end: {
      type: Boolean,
      default: false,
    },
    canceled_at: {
      type: Date,
    },
    cancellation_reason: {
      type: String,
    },

    // Usage tracking (for features like calls, etc.)
    usage: {
      type: Map,
      of: Number,
      default: new Map(),
    },

    // Unique code for the subscription
    plan_link_code: {
      type: String,
      unique: true,
    },

    // Payment history for this subscription
    payments: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Payment",
      },
    ],

    // Metadata
    metadata: {
      type: Map,
      of: String,
      default: new Map(),
    },

    // Timestamps
    created_at: {
      type: Date,
      default: Date.now,
    },
    updated_at: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: { createdAt: "created_at", updatedAt: "updated_at" },
  }
);

const Subscription =
  mongoose.models.Subscription ||
  mongoose.model("Subscription", SubscriptionSchema);

export default Subscription;
