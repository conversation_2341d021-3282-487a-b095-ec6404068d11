import React from "react";
import { useTranslation } from "react-i18next";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  setIsPronounceClientNameEnabled,
  setIsPronounceClientHonorificEnabled,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";

export const PronounceClient: React.FC = () => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();

  const { isPronounceClientNameEnabled, isPronounceClientHonorificEnabled } =
    useSelector(
      (state: RootState) => state.businessDashboard.businessDashboardGoals
    );

  return (
    <>
      <div className="space-y-2">
        <p className="text-lg font-semibold">
          {t("createEditGoal.advanced.pronounceClientName")}
        </p>
        <p className="text-sm text-voxa-neutral-700">
          {t("createEditGoal.advanced.pronounceClientNameInfo")}
        </p>
        <div className="flex items-center space-x-3 mt-3">
          <Switch
            id="script"
            checked={isPronounceClientNameEnabled}
            onCheckedChange={(checked) =>
              dispatch(setIsPronounceClientNameEnabled(checked))
            }
          />
          <Label htmlFor="script">
            {isPronounceClientNameEnabled
              ? t("createEditGoal.advanced.enabled")
              : t("createEditGoal.advanced.disabled")}
          </Label>
        </div>
      </div>
      <div className="space-y-2">
        <p className="text-lg font-semibold text-nowrap">
          {t("createEditGoal.advanced.pronounceClientHonorific")}
        </p>
        <p className="text-voxa-neutral-700">
          {t("createEditGoal.advanced.pronounceClientHonorificInfo")}
        </p>
        <div className="flex items-center space-x-3 mt-3">
          <Switch
            id="script"
            checked={isPronounceClientHonorificEnabled}
            onCheckedChange={(checked) =>
              dispatch(setIsPronounceClientHonorificEnabled(checked))
            }
          />
          <Label htmlFor="script">
            {isPronounceClientHonorificEnabled
              ? t("createEditGoal.advanced.enabled")
              : t("createEditGoal.advanced.disabled")}
          </Label>
        </div>
      </div>
    </>
  );
};
