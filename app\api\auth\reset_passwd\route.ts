import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import dbConnect from "@/lib/mongodb";
import { EntrepriseAdmin } from "@/models/User";

const CHECK_KEY = "EcHoPaRroTWeBiNtErFaCe";

export async function POST(req: Request) {
  try {
    const { entrepriseEmail, newPasswd, key } = await req.json();

    await dbConnect();

    if (!entrepriseEmail || !newPasswd || !key)
      return NextResponse.json({ error: "Missing Fields" }, { status: 400 });
    if (key !== CHECK_KEY)
      return NextResponse.json({ error: "Not Allowed" }, { status: 403 });

    const entreprise = await EntrepriseAdmin.findOne({
      email: entrepriseEmail,
    });
    if (!entreprise)
      return NextResponse.json(
        { error: "Entreprise not found" },
        { status: 404 }
      );

    const hashedPassword = await bcrypt.hash(newPasswd, 10);
    entreprise.password = hashedPassword;
    const entrepriseAdminUpdate = await entreprise.save();
    if (!entrepriseAdminUpdate)
      return NextResponse.json(
        { error: "Error updating password" },
        { status: 500 }
      );
    return NextResponse.json(
      { message: "Password updated successfully" },
      { status: 200 }
    );
  } catch (err: any) {
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
}
