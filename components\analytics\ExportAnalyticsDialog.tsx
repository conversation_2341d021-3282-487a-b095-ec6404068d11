"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  resetExportedAnalytics,
  setExportedAnalytics,
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { AppDispatch, RootState } from "@/redux/store";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";

interface ExportAnalyticsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  // onExport?: (format: "png" | "svg" | "pdf") => Promise<void>;
  onExport?: (format: "png" | "svg" | "pdf") => void;
  onDialogClose?: () => void;
}

const CheckboxItem = ({
  id,
  label,
  checked,
  onCheckedChange,
}: {
  id: string;
  label: string;
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
}) => (
  <div
    className="flex items-center space-x-1.5 cursor-pointer"
    onClick={() => onCheckedChange(!checked)}
  >
    <div className="cursor-pointer">
      {checked ? (
        <CheckBoxIcon className="h-4 w-4 text-primary" />
      ) : (
        <CheckBoxOutlineBlankIcon className="h-4 w-4 text-muted-foreground" />
      )}
    </div>
    <Label
      htmlFor={id}
      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
    >
      {label}
    </Label>
  </div>
);

export default function ExportAnalyticsDialog({
  open,
  onOpenChange,
  onExport,
  onDialogClose,
}: ExportAnalyticsDialogProps) {
  const dispatch = useDispatch<AppDispatch>();
  const { exportedAnalytics } = useSelector(
    (state: RootState) => state.analytics
  );
  const [exportFormat, setExportFormat] = useState<"png" | "svg" | "pdf">(
    "png"
  );

  const handleToggle = (key: string, value: boolean) => {
    dispatch(setExportedAnalytics({ [key]: value }));
  };

  const handleNestedToggle = (
    parentKey: "callStatusByTimeLine" | "heatmap",
    childKey: string,
    value: boolean
  ) => {
    dispatch(
      setExportedAnalytics({
        [parentKey]: {
          ...exportedAnalytics[parentKey],
          [childKey]: value,
        },
      })
    );
  };

  const handleExport = async () => {
    if (!onExport) return;

    const analyticsExportElements =
      document.querySelectorAll(".analytics-export");
    const analyticsExportPopoverElements = document.querySelectorAll(
      ".analytics-export-popover"
    );
    try {
      analyticsExportElements.forEach((element) => {
        element.classList.add("hidden");
      });
      analyticsExportPopoverElements.forEach((element) => {
        element.classList.add("hidden");
      });
      await onExport(exportFormat);
    } catch (error) {
      console.error("Export action failed:", error);
    } finally {
      analyticsExportElements.forEach((element) => {
        element.classList.remove("hidden");
      });
      analyticsExportPopoverElements.forEach((element) => {
        element.classList.remove("hidden");
      });

      onOpenChange(false);
    }
  };

  // Helper to toggle all general analytics
  const handleToggleAllGeneral = (checked: boolean) => {
    dispatch(
      setExportedAnalytics({
        callStatusChart: checked,
        totalCallDurationChart: checked,
        conversationsTypesChart: checked,
        averageRingingTime: checked,
        averageCallDuration: checked,
        averageClientEngagement: checked,
        mostTalkativeSpeaker: checked,
        conversationsMap: checked,
      })
    );
  };

  // Helper to toggle all call status by time line
  const handleToggleAllCallStatus = (checked: boolean) => {
    // Ensure all required keys are present
    dispatch(
      setExportedAnalytics({
        callStatusByTimeLine: {
          day: checked,
          week: checked,
          month: checked,
          weekday: checked,
          hour: checked,
        },
      })
    );
  };

  // Helper to toggle all heatmap
  const handleToggleAllHeatmap = (checked: boolean) => {
    dispatch(
      setExportedAnalytics({
        heatmap: {
          total: checked,
          answered: checked,
          missed: checked,
        },
      })
    );
  };

  // Compute checked state for "toggle all" checkboxes
  const allGeneralChecked = [
    exportedAnalytics.callStatusChart,
    exportedAnalytics.totalCallDurationChart,
    exportedAnalytics.conversationsTypesChart,
    exportedAnalytics.averageRingingTime,
    exportedAnalytics.averageCallDuration,
    exportedAnalytics.averageClientEngagement,
    exportedAnalytics.mostTalkativeSpeaker,
    exportedAnalytics.conversationsMap,
  ].every(Boolean);

  const someGeneralChecked = [
    exportedAnalytics.callStatusChart,
    exportedAnalytics.totalCallDurationChart,
    exportedAnalytics.conversationsTypesChart,
    exportedAnalytics.averageRingingTime,
    exportedAnalytics.averageCallDuration,
    exportedAnalytics.averageClientEngagement,
    exportedAnalytics.mostTalkativeSpeaker,
    exportedAnalytics.conversationsMap,
  ].some(Boolean);

  const callStatusValues = Object.values(
    exportedAnalytics.callStatusByTimeLine
  );
  const allCallStatusChecked =
    callStatusValues.length > 0 && callStatusValues.every(Boolean);
  const someCallStatusChecked = callStatusValues.some(Boolean);

  const heatmapValues = Object.values(exportedAnalytics.heatmap);
  const allHeatmapChecked =
    heatmapValues.length > 0 && heatmapValues.every(Boolean);
  const someHeatmapChecked = heatmapValues.some(Boolean);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[600px]" onDialogClose={onDialogClose}>
        <DialogHeader>
          <DialogTitle>Custom Export</DialogTitle>
          <DialogDescription>
            Select the analytics you want to export.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4 max-h-[60vh] overflow-y-auto pr-2">
          <div>
            <div
              className="flex items-center justify-between font-semibold text-sm bg-muted p-2 rounded-md mb-3 cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700 duration-200"
              onClick={() => handleToggleAllGeneral(!allGeneralChecked)}
            >
              <span>General Analytics</span>
              <div className="flex items-center">
                {allGeneralChecked ? (
                  <CheckBoxIcon className="h-4 w-4 text-primary" />
                ) : (
                  <CheckBoxOutlineBlankIcon
                    className={`h-4 w-4 ${
                      someGeneralChecked
                        ? "text-primary/50"
                        : "text-muted-foreground"
                    }`}
                  />
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3 pl-2">
              <CheckboxItem
                id="callStatusChart"
                label="Call Status Chart"
                checked={exportedAnalytics.callStatusChart}
                onCheckedChange={(checked) =>
                  handleToggle("callStatusChart", checked as boolean)
                }
              />
              <CheckboxItem
                id="totalCallDurationChart"
                label="Total Call Duration Chart"
                checked={exportedAnalytics.totalCallDurationChart}
                onCheckedChange={(checked) =>
                  handleToggle("totalCallDurationChart", checked as boolean)
                }
              />
              <CheckboxItem
                id="conversationsTypesChart"
                label="Conversations Types Chart"
                checked={exportedAnalytics.conversationsTypesChart}
                onCheckedChange={(checked) =>
                  handleToggle("conversationsTypesChart", checked as boolean)
                }
              />
              <CheckboxItem
                id="averageRingingTime"
                label="Average Ringing Time"
                checked={exportedAnalytics.averageRingingTime}
                onCheckedChange={(checked) =>
                  handleToggle("averageRingingTime", checked as boolean)
                }
              />
              <CheckboxItem
                id="averageCallDuration"
                label="Average Call Duration"
                checked={exportedAnalytics.averageCallDuration}
                onCheckedChange={(checked) =>
                  handleToggle("averageCallDuration", checked as boolean)
                }
              />
              <CheckboxItem
                id="averageClientEngagement"
                label="Average Client Engagement"
                checked={exportedAnalytics.averageClientEngagement}
                onCheckedChange={(checked) =>
                  handleToggle("averageClientEngagement", checked as boolean)
                }
              />
              <CheckboxItem
                id="mostTalkativeSpeaker"
                label="Most Talkative Speaker"
                checked={exportedAnalytics.mostTalkativeSpeaker}
                onCheckedChange={(checked) =>
                  handleToggle("mostTalkativeSpeaker", checked as boolean)
                }
              />
              <CheckboxItem
                id="conversationsMap"
                label="Conversations Map"
                checked={exportedAnalytics.conversationsMap}
                onCheckedChange={(checked) =>
                  handleToggle("conversationsMap", checked as boolean)
                }
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3">
            <div className="flex flex-col">
              <div
                className="flex items-center justify-between font-semibold text-sm bg-muted p-2 rounded-md cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700 duration-200"
                onClick={() => handleToggleAllCallStatus(!allCallStatusChecked)}
              >
                <span>Call Status by Time Line</span>
                <div className="flex items-center">
                  {allCallStatusChecked ? (
                    <CheckBoxIcon className="h-4 w-4 text-primary" />
                  ) : (
                    <CheckBoxOutlineBlankIcon
                      className={`h-4 w-4 ${
                        someCallStatusChecked
                          ? "text-primary/50"
                          : "text-muted-foreground"
                      }`}
                    />
                  )}
                </div>
              </div>
              <div className="pl-4 flex flex-col gap-3 pt-3">
                {Object.entries(exportedAnalytics.callStatusByTimeLine).map(
                  ([key, value]) => (
                    <CheckboxItem
                      key={key}
                      id={`cst-${key}`}
                      label={key.charAt(0).toUpperCase() + key.slice(1)}
                      checked={value}
                      onCheckedChange={(checked) =>
                        handleNestedToggle(
                          "callStatusByTimeLine",
                          key,
                          checked as boolean
                        )
                      }
                    />
                  )
                )}
              </div>
            </div>

            <div className="flex flex-col">
              <div
                className="flex items-center justify-between font-semibold text-sm bg-muted p-2 rounded-md cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700 duration-200"
                onClick={() => handleToggleAllHeatmap(!allHeatmapChecked)}
              >
                <span>Heatmap</span>
                <div className="flex items-center">
                  {allHeatmapChecked ? (
                    <CheckBoxIcon className="h-4 w-4 text-primary" />
                  ) : (
                    <CheckBoxOutlineBlankIcon
                      className={`h-4 w-4 ${
                        someHeatmapChecked
                          ? "text-primary/50"
                          : "text-muted-foreground"
                      }`}
                    />
                  )}
                </div>
              </div>
              <div className="pl-4 flex flex-col gap-3 pt-3">
                {Object.entries(exportedAnalytics.heatmap).map(
                  ([key, value]) => (
                    <CheckboxItem
                      key={key}
                      id={`hm-${key}`}
                      label={key.charAt(0).toUpperCase() + key.slice(1)}
                      checked={value}
                      onCheckedChange={(checked) =>
                        handleNestedToggle("heatmap", key, checked as boolean)
                      }
                    />
                  )
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-3 mt-2 border-t pt-4">
          <h3 className="font-semibold text-sm bg-muted p-2 rounded-md">
            Export Format
          </h3>
          <RadioGroup
            value={exportFormat}
            onValueChange={(value) =>
              setExportFormat(value as "png" | "svg" | "pdf")
            }
            className="flex space-x-4 pl-2"
          >
            <div className="flex items-center">
              <RadioGroupItem value="png" id="format-png" />
              <Label htmlFor="format-png" className="cursor-pointer pl-2">
                PNG
              </Label>
            </div>
            <div className="flex items-center">
              <RadioGroupItem value="svg" id="format-svg" />
              <Label htmlFor="format-svg" className="cursor-pointer pl-2">
                SVG
              </Label>
            </div>
            <div className="flex items-center">
              <RadioGroupItem value="pdf" id="format-pdf" />
              <Label htmlFor="format-pdf" className="cursor-pointer pl-2">
                PDF
              </Label>
            </div>
          </RadioGroup>
        </div>

        <DialogFooter>
          <div className="flex gap-2">
            <Button
              variant="secondary"
              onClick={() => {
                dispatch(resetExportedAnalytics());
              }}
              className="bg-voxa-neutral-100 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-700 text-voxa-neutral-900 dark:text-voxa-neutral-100"
            >
              Reset
            </Button>
            <Button onClick={handleExport}>Export</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
