import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  setGroupSortBy,
  setGroupSortOrder,
  getGroups,
} from "@/redux/BusinessDashboard/subSlices/ClientsSlice";
import CustomSelect from "@/components/CustomFormItems/Select";
import { ArrowDownAZ, ArrowUpZA, Clock } from "lucide-react";

const GroupSortSelect = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { groupSortBy, groupSortOrder } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardClients
  );

  const getSortValue = () => {
    if (groupSortBy === "name" && groupSortOrder === "asc") return "name-asc";
    if (groupSortBy === "name" && groupSortOrder === "desc") return "name-desc";
    if (groupSortBy === "date" && groupSortOrder === "desc") return "newest";
    if (groupSortBy === "date" && groupSortOrder === "asc") return "oldest";
    return "newest";
  };

  const handleSortChange = (value: string) => {
    switch (value) {
      case "name-asc":
        dispatch(setGroupSortBy("name"));
        dispatch(setGroupSortOrder("asc"));
        break;
      case "name-desc":
        dispatch(setGroupSortBy("name"));
        dispatch(setGroupSortOrder("desc"));
        break;
      case "newest":
        dispatch(setGroupSortBy("date"));
        dispatch(setGroupSortOrder("desc"));
        break;
      case "oldest":
        dispatch(setGroupSortBy("date"));
        dispatch(setGroupSortOrder("asc"));
        break;
    }

    setTimeout(() => {
      dispatch(getGroups());
    }, 100);
  };

  const sortOptions = [
    {
      value: "newest",
      label: "Newest",
      icon: <Clock className="h-4 w-4 mr-2" />,
    },
    {
      value: "oldest",
      label: "Oldest",
      icon: <Clock className="h-4 w-4 mr-2" />,
    },
    {
      value: "name-asc",
      label: "Name A-Z",
      icon: <ArrowDownAZ className="h-4 w-4 mr-2" />,
    },
    {
      value: "name-desc",
      label: "Name Z-A",
      icon: <ArrowUpZA className="h-4 w-4 mr-2" />,
    },
  ];

  return (
    <CustomSelect
      placeholder="Sort by"
      value={getSortValue()}
      onValueChange={handleSortChange}
      items={sortOptions}
      className="w-[180px] text-sm text-black dark:text-white h-full"
      parentClassName="!w-fit ms-auto sm:ms-0 items-stretch"
    />
  );
};

export default GroupSortSelect;
