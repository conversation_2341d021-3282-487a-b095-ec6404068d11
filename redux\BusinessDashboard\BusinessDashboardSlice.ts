import { combineReducers } from "@reduxjs/toolkit";
import rootReducer from "./subSlices/RootSlice";
import clientsSlice from "./subSlices/ClientsSlice";
import assistantsSlice from "./subSlices/AssistantsSlice";
import goalsSlice from "./subSlices/GoalSlice";
import voicesSlice from "./subSlices/VoicesSlice";
import integrationSlice from "./subSlices/IntegrationSlice";
import billingSlice from "./subSlices/BillingSlice";
import entrepriseSlice from "./subSlices/EntrepriseSlice";

const businessDashboardReducer = combineReducers({
  businessDashboardRoot: rootReducer,
  businessDashboardClients: clientsSlice,
  businessDashboardAssistants: assistantsSlice,
  businessDashboardGoals: goalsSlice,
  businessDashboardVoices: voicesSlice,
  businessDashboardIntegration: integrationSlice,
  businessDashboardBilling: billingSlice,
  businessDashboardEntreprise: entrepriseSlice,
});

export default businessDashboardReducer;
export type BusinessDashboardState = ReturnType<
  typeof businessDashboardReducer
>;
