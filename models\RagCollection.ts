import mongoose from "mongoose";

const RagCollectionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  file: {
    type: String,
    required: true,
  },
  entreprise: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Entreprise",
  },
  created_at: {
    type: Date,
    default: Date.now,
  }
})

const RagCollection = mongoose.models.RagCollection || mongoose.model("RagCollection", RagCollectionSchema)

export default RagCollection