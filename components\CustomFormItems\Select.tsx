import { useState, ReactNode, useEffect } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover-dialog";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { useTranslation } from "react-i18next";

type StringNumber = string | number;

interface SelectProps {
  name?: string;
  label?: ReactNode | StringNumber | [StringNumber, StringNumber];
  placeholder?: string;
  required?: boolean;
  value?: string;
  defaultValue?: string;
  autoDefaultValue?: boolean;
  onValueChange?: (value: string) => void;
  disabled?: boolean;
  className?: string;
  parentClassName?: string;
  items?: Array<{
    value: string;
    label: string | number | [string | number, string | number] | ReactNode;
    searchText?: string;
    icon?: ReactNode;
  }>;
  searchable?: boolean;
  clearable?: boolean;
  itemLableDisplay?: boolean;
  menuClassName?: string;
  isDefaultOpen?: boolean;
}

export default function CustomSelect({
  name,
  label,
  placeholder,
  required,
  value,
  autoDefaultValue = false,
  onValueChange,
  disabled,
  className,
  parentClassName,
  items = [],
  searchable = false,
  menuClassName,
  isDefaultOpen
}: SelectProps) {
  const { t } = useTranslation("assistants");
  const [open, setOpen] = useState(isDefaultOpen || false);

  useEffect(() => {
    if (autoDefaultValue && !value && items.length > 0 && onValueChange) {
      onValueChange(items[0].value);
    }
  }, [autoDefaultValue, value, items, onValueChange]);

  const selectedItem = items.find((item) => item.value === value);

  const renderItemContent = (item: (typeof items)[0]) => {
    if (Array.isArray(item.label) && item.label.length === 2) {
      return (
        <div className="flex justify-between w-full max-sm:flex-col max-sm:items-start">
          <p className="font-medium">{item.label[0]}</p>
          <p className="max-sm:text-sm text-muted-foreground">
            {item.label[1]}
          </p>
        </div>
      );
    }
    return (
      <div className="w-full flex items-center gap-1.5">
        {item.icon && item.icon}
        <span className="grow"> {item.label}</span>
      </div>
    );
  };

  const renderButtonContent = () => {
    if (!selectedItem) {
      return placeholder || t("select.selectAnOption");
    }

    if (Array.isArray(selectedItem.label) && selectedItem.label.length === 2) {
      return (
        <div className="flex w-full items-center max-sm:flex-col max-sm:items-start sm:justify-between text-left">
          <p className="font-medium">{selectedItem.label[0]}</p>
          <p className="max-sm:text-sm sm:ml-auto text-muted-foreground">
            {selectedItem.label[1]}
          </p>
        </div>
      );
    }
    return (
      <div className="w-full flex items-center gap-1.5">
        {selectedItem.icon && selectedItem.icon}
        <span>{selectedItem.label}</span>
      </div>
    );
  };

  return (
    <div
      className={`relative grid w-full items-center gap-1.5 ${parentClassName}`}
    >
      {label && (
        <Label
          className="text-sm flex gap-2 items-center text-foreground"
          htmlFor={name}
        >
          {label}
          {required && <span className="text-red-500 -translate-x-1"> *</span>}
        </Label>
      )}
      <div className="relative">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger
            asChild
            className="bg-sidebar border border-sidebar-border"
          >
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              disabled={disabled}
              className={cn(
                "h-10 w-full justify-between",
                !value && "text-muted-foreground",
                selectedItem &&
                  Array.isArray(selectedItem.label) &&
                  selectedItem.label.length === 2 &&
                  "max-sm:h-[52px]",
                className
              )}
            >
              <div className="truncate flex-1 text-left">
                {renderButtonContent()}
              </div>
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className={cn(
              "w-[--radix-popover-trigger-width] p-0 rounded-lg",
              menuClassName
            )}
          >
            <Command
              filter={(value, search) => {
                const item = items.find((i) => i.value === value);
                if (!item) return 0;
                const searchableText =
                  item.searchText ||
                  (typeof item.label === "string"
                    ? item.label
                    : Array.isArray(item.label)
                    ? item.label.join(" ")
                    : "");
                return searchableText
                  .toLowerCase()
                  .includes(search.toLowerCase())
                  ? 1
                  : 0;
              }}
            >
              {searchable && (
                <CommandInput placeholder={t("select.searchPlaceholder")} />
              )}
              <CommandList>
                <CommandEmpty>{t("select.noResultsFound")}</CommandEmpty>
                <CommandGroup>
                  {items.map((item, index) => (
                    <CommandItem
                      key={index}
                      value={item.value}
                      className={`${
                        index === 0 ? "" : "cursor-pointer z-50 mt-0.5"
                      }`}
                      onSelect={(currentValue) => {
                        onValueChange?.(currentValue);
                        setOpen(false);
                      }}
                    >
                      {renderItemContent(item)}
                      <Check
                        className={cn(
                          "h-4 w-4",
                          value === item.value ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
