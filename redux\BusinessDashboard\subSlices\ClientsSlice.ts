import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { BusinessDashboardState } from "../BusinessDashboardSlice";
import {
  AddClientToGoal,
  CreateClient,
  DeleteClientCompletely,
  DeleteGroupFromDB,
  getAllEntrepriseGoals,
  getClientGoalNames,
  GetClientsByFirstLetter,
  GetGroups,
  removeClientFromGroupDB,
  SaveClients,
  ToggleClientBlacklist,
  ToggleClientFavorite,
  UpdateClientDetails,
  RemoveClientFromGoal,
  getAllClients,
} from "@/actions/ClientsActions";
import { toast } from "sonner";
import {
  CountryCode,
  formatNumber,
  isValidPhoneNumber,
} from "libphonenumber-js";
import { handleCloseCallDetails, setGoalID } from "./RootSlice";
import { ClientType } from "@/types/Models";

interface FileMetadata {
  name: string;
  type: string;
  size: number;
}

interface Loading {
  addLoading: boolean;
  updateLoading: boolean;
  deleteLoading: boolean;
}

interface ClientsRootState {
  clientDetails: any;
  loading: Loading;
  clientsLoading: boolean;
  groupsLoading: boolean;
  clientGoalsLoading: boolean;
  goalsLoading: boolean;
  clientDetailsOpen: boolean;
  addContact: boolean;
  importContacts: boolean;
  files: FileMetadata[];
  error: boolean;
  selectedLetter: string | null;
  clientsErrorMessage: string;
  pattern: string;
  groupPattern: string; // Add new state for group search
  groupSortBy: "name" | "date";
  groupSortOrder: "asc" | "desc";
  groups: any[];
  clients: ClientType[];
  Name: string;
  phoneNumber: string;
  country: CountryCode | "";
  clientGoals: any[];
  allGoals: any[];
  addClientToGoalOpen: boolean;
  clientSelectedGoal: any;
  importContactsDialogOpen: boolean;
}

const initialState: ClientsRootState = {
  loading: {
    addLoading: false,
    updateLoading: false,
    deleteLoading: false,
  } as Loading,
  clientGoalsLoading: false,
  clientsLoading: true,
  groupsLoading: true,
  goalsLoading: true,
  clientDetails: null,
  clientDetailsOpen: false,
  addContact: false,
  importContacts: false,
  files: [],
  error: false,
  selectedLetter: "favorites",
  clientsErrorMessage: "",
  pattern: "",
  groupPattern: "", // Initialize with empty string
  groupSortBy: "name",
  groupSortOrder: "asc",
  groups: [],
  clients: [],
  Name: "",
  phoneNumber: "",
  country: "",
  clientGoals: [],
  allGoals: [],
  addClientToGoalOpen: false,
  clientSelectedGoal: null,
  importContactsDialogOpen: false, // Initialize the new state
};

export const UploadClients = createAsyncThunk(
  "BusinessDashboardClients/UploadClients",
  async (
    { data, phoneColumn }: { data: any; phoneColumn: 0 | 1 },
    { dispatch, getState }
  ) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { country } = state.businessDashboard.businessDashboardClients;

      if (!data) return toast.error("Please select a file");

      const response = await SaveClients(data, country, phoneColumn);

      if (!response.success && response.error) {
        toast.error(response.error);
        return;
      }

      dispatch(setFiles([]));
      dispatch(setImportContacts(false));
      dispatch(setGoalID(""));
      toast.success("Contacts uploaded successfully");
      window.location.reload();
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const CreateNewClient = createAsyncThunk(
  "BusinessDashboardClients/CreateNewClient",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { Name, country, phoneNumber } =
        state.businessDashboard.businessDashboardClients;
      const formattedPhoneNumber = formatNumber(
        String(phoneNumber),
        country as CountryCode,
        "INTERNATIONAL"
      );
      if (!isValidPhoneNumber(formattedPhoneNumber)) {
        toast.error("Invalid phone number");
        return;
      }
      const response = await CreateClient(formattedPhoneNumber, country, Name);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      toast.success("User created successfully");
      dispatch(setName(""));
      dispatch(setNumber(""));
      dispatch(setCountry(""));
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const GetClients = createAsyncThunk(
  "BusinessDashboardClients/GetClients",
  async ({ letter }: { letter: string }, { dispatch }) => {
    try {
      dispatch(setClientsLoading(true));
      dispatch(setSelectedLetter(letter));
      dispatch(setPattern(""));

      const response = await GetClientsByFirstLetter(letter);

      if (!response.success && response.error) {
        dispatch(setClientsErrorMessage(response.error));
        dispatch(setClients([]));
      } else if (response.clients) {
        dispatch(setClientsErrorMessage(""));
        dispatch(setClients(response.clients));
      }
    } catch (err: any) {
      dispatch(setClientsErrorMessage(err.message));
    } finally {
      dispatch(setClientsLoading(false));
    }
  }
);

export const GetClientsByPattern = createAsyncThunk(
  "BusinessDashboardClients/GetClientsByPattern",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { pattern } = state.businessDashboard.businessDashboardClients;
      if (!pattern) return;
      dispatch(setClientsLoading(true));
      dispatch(setSelectedLetter(null));
      dispatch(setPattern(pattern));
      const response = await getAllClients(pattern);

      if (!response.success && response.error) {
        dispatch(setClientsErrorMessage(response.error));
        dispatch(setClients([]));
      } else if (response.clients) {
        dispatch(setClientsErrorMessage(""));
        dispatch(setClients(response.clients));
      }
    } catch (err: any) {
      dispatch(setClientsErrorMessage(err.message));
    } finally {
      dispatch(setClientsLoading(false));
    }
  }
);

export const toggleClientFavorite = createAsyncThunk(
  "BusinessDashboardClients/toggleClientFavorite",
  async ({ clientID }: { clientID: string }, { getState, dispatch }) => {
    const state = getState() as { businessDashboard: BusinessDashboardState };
    const { clients, selectedLetter, clientDetails } =
      state.businessDashboard.businessDashboardClients;

    const originalClients = clients.slice();
    const originalClientDetails = clientDetails ? { ...clientDetails } : null;
    let updatedClients;
    let updatedClientDetails = clientDetails;
    if (selectedLetter === "favorites") {
      const client = clients.find((c) => c._id === clientID);
      if (client) {
        if (client.isFavorited) {
          updatedClients = clients.filter((c) => c._id !== clientID);
          if (clientDetails && clientDetails._id === clientID) {
            updatedClientDetails = { ...clientDetails, isFavorited: false };
          }
        } else {
          updatedClients = clients.map((c) =>
            c._id === clientID ? { ...c, isFavorited: true } : c
          );
          if (clientDetails && clientDetails._id === clientID) {
            updatedClientDetails = { ...clientDetails, isFavorited: true };
          }
        }
      } else if (
        clientDetails &&
        clientDetails._id === clientID &&
        !clientDetails.isFavorited
      ) {
        updatedClients = [...clients, { ...clientDetails, isFavorited: true }];
        updatedClientDetails = { ...clientDetails, isFavorited: true };
      } else {
        updatedClients = clients;
      }
    } else {
      updatedClients = clients.map((c) =>
        c._id === clientID ? { ...c, isFavorited: !c.isFavorited } : c
      );
      if (clientDetails && clientDetails._id === clientID) {
        updatedClientDetails = {
          ...clientDetails,
          isFavorited: !clientDetails.isFavorited,
        };
      }
    }
    dispatch(setClients(updatedClients));
    if (updatedClientDetails !== clientDetails) {
      dispatch(setClientDetails(updatedClientDetails));
    }

    try {
      const response = await ToggleClientFavorite(clientID);
      if (!response.success) {
        dispatch(setClients(originalClients));
        if (originalClientDetails)
          dispatch(setClientDetails(originalClientDetails));
        toast.error(response.error || "Failed to toggle favorite status");
        return;
      }
      toast.success(
        response.isFavorited
          ? "Client added to favorites"
          : "Client removed from favorites"
      );
    } catch (err: any) {
      dispatch(setClients(originalClients));
      if (originalClientDetails)
        dispatch(setClientDetails(originalClientDetails));
      console.error(err);
      toast.error(`Failed to toggle favorite`);
    }
  }
);

export const toggleClientBlacklist = createAsyncThunk(
  "BusinessDashboardClients/toggleClientBlacklist",
  async ({ clientID }: { clientID: string }, { getState, dispatch }) => {
    const state = getState() as { businessDashboard: BusinessDashboardState };
    const { clients, selectedLetter, clientDetails } =
      state.businessDashboard.businessDashboardClients;

    const originalClients = clients.slice();
    const originalClientDetails = clientDetails ? { ...clientDetails } : null;
    let updatedClients;
    let updatedClientDetails = clientDetails;
    if (selectedLetter === "blacklist") {
      const client = clients.find((c) => c._id === clientID);
      if (client) {
        if (client.isBlacklisted) {
          updatedClients = clients.filter((c) => c._id !== clientID);
          if (clientDetails && clientDetails._id === clientID) {
            updatedClientDetails = { ...clientDetails, isBlacklisted: false };
          }
        } else {
          updatedClients = clients.map((c) =>
            c._id === clientID ? { ...c, isBlacklisted: true } : c
          );
          if (clientDetails && clientDetails._id === clientID) {
            updatedClientDetails = { ...clientDetails, isBlacklisted: true };
          }
        }
      } else if (
        clientDetails &&
        clientDetails._id === clientID &&
        !clientDetails.isBlacklisted
      ) {
        updatedClients = [
          ...clients,
          { ...clientDetails, isBlacklisted: true },
        ];
        updatedClientDetails = { ...clientDetails, isBlacklisted: true };
      } else {
        updatedClients = clients;
      }
    } else {
      updatedClients = clients.map((c) =>
        c._id === clientID ? { ...c, isBlacklisted: !c.isBlacklisted } : c
      );
      if (clientDetails && clientDetails._id === clientID) {
        updatedClientDetails = {
          ...clientDetails,
          isBlacklisted: !clientDetails.isBlacklisted,
        };
      }
    }
    dispatch(setClients(updatedClients));
    if (updatedClientDetails !== clientDetails) {
      dispatch(setClientDetails(updatedClientDetails));
    }

    try {
      const response = await ToggleClientBlacklist(clientID);
      if (!response.success) {
        dispatch(setClients(originalClients));
        if (originalClientDetails)
          dispatch(setClientDetails(originalClientDetails));
        toast.error(response.error || "Failed to toggle blacklist status");
        return;
      }
      toast.success(
        response.isBlacklisted
          ? "Client added to blacklist"
          : "Client removed from blacklist"
      );
    } catch (err: any) {
      dispatch(setClients(originalClients));
      if (originalClientDetails)
        dispatch(setClientDetails(originalClientDetails));
      console.error(err);
      toast.error(`Failed to toggle blacklist`);
    }
  }
);

export const getGroups = createAsyncThunk(
  "BusinessDashboardClients/getGroups",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { groupPattern, groupSortBy, groupSortOrder } =
        state.businessDashboard.businessDashboardClients;

      dispatch(setGroupsLoading(true));
      const response = await GetGroups(
        groupPattern,
        groupSortBy,
        groupSortOrder
      );
      if (!response.success && response.error) {
        toast.error(response.error);
        return;
      } else if (response.groups) {
        dispatch(setGroups(response.groups));
      }
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to get groups");
    } finally {
      dispatch(setGroupsLoading(false));
    }
  }
);

export const deleteGroup = createAsyncThunk(
  "BusinessDashboardClients/deleteGroup",
  async (groupID: string, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { groups } = state.businessDashboard.businessDashboardClients;
      const response = await DeleteGroupFromDB(groupID);
      if (!response.success && response.error) {
        toast.error(response.error);
        return;
      } else if (response.success) {
        toast.success("Group deleted successfully");
        dispatch(setGroups(groups.filter((group) => group._id !== groupID)));
      }
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to delete group");
    }
  }
);

export const GetClientGoals = createAsyncThunk(
  "BusinessDashboardClients/GetClientDetails",
  async (clientID: string, { dispatch }) => {
    try {
      dispatch(setClientGoalsLoading(true));
      const response = await getClientGoalNames(clientID);
      if (response.goals) {
        dispatch(setClientGoals(response.goals));
      }
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to get client goals");
    } finally {
      dispatch(setClientGoalsLoading(false));
    }
  }
);

export const removeClientFromGoal = createAsyncThunk(
  "BusinessDashboardClients/removeGoalFromClient",
  async (
    { clientID, goalID }: { clientID: string; goalID: string },
    { dispatch, getState }
  ) => {
    try {
      const response = await RemoveClientFromGoal(clientID, goalID);
      if (!response.success && response.error) {
        toast.error(response.error);
        return;
      }
      const state = getState() as { businessDashboard: BusinessDashboardState };
      const { clientGoals } = state.businessDashboard.businessDashboardClients;
      const updatedGoals = clientGoals.filter((g: any) => g._id !== goalID);
      dispatch(setClientGoals(updatedGoals));
      toast.success("Goal removed from client");
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const getAllExistingGoalsForEntreprise = createAsyncThunk(
  "BusinessDashboardClients/getAllExistingGoalsForEntreprise",
  async (clientID: string, { dispatch }) => {
    try {
      dispatch(setGoalsLoading(true));
      const response = await getAllEntrepriseGoals(clientID);
      if (!response.success && response.error) {
        toast.error(response.error);
        return;
      } else if (response.goals) {
        dispatch(setAllGoals(response.goals));
      }
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to get all goals");
    } finally {
      dispatch(setGoalsLoading(false));
    }
  }
);

export const removeClientFromGroup = createAsyncThunk(
  "BusinessDashboardClients/removeClientFromGroup",
  async ({ groupID, clientID }: { groupID: string; clientID: string }) => {
    try {
      const response = await removeClientFromGroupDB(clientID, groupID);
      if (!response.success && response.error) {
        throw new Error(response.error);
      }
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const deleteClient = createAsyncThunk(
  "BusinessDashboardClients/deleteClient",
  async (_, { dispatch, getState }) => {
    const state = getState() as { businessDashboard: BusinessDashboardState };
    const { clients, clientDetails, loading } =
      state.businessDashboard.businessDashboardClients;
    try {
      dispatch(setLoading({ ...loading, deleteLoading: true }));
      const response = await DeleteClientCompletely(clientDetails._id);
      if (!response.success && response.error) {
        toast.error(response.error);
        return;
      } else if (response.success) {
        dispatch(handleCloseCallDetails());
        dispatch(
          setClients(
            clients.filter(
              (client: ClientType) => client._id !== clientDetails._id
            )
          )
        );
        toast.success("Client deleted successfully");
      }
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to delete client");
    } finally {
      dispatch(setLoading({ ...loading, deleteLoading: false }));
    }
  }
);

export const updateClientDetails = createAsyncThunk(
  "BusinessDashboardClients/updateClientDetails",
  async (
    {
      clientID,
      name,
      phone,
      gender,
    }: { clientID: string; name: string; phone: string; gender: string },
    { dispatch, getState }
  ) => {
    const state = getState() as { businessDashboard: BusinessDashboardState };
    const { clientDetails, clients, loading } =
      state.businessDashboard.businessDashboardClients;
    try {
      dispatch(setLoading({ ...loading, updateLoading: true }));
      if (!phone) return toast.error("Name and phone are required");
      const formattedPhone = formatNumber(
        String(clientDetails.phone),
        clientDetails.country as CountryCode,
        "INTERNATIONAL"
      );
      if (!isValidPhoneNumber(formattedPhone)) {
        toast.error("Invalid phone number");
        return;
      }
      const response = await UpdateClientDetails(clientID, name, phone, gender);
      if (!response.success && response.error) {
        toast.error(response.error);
        return;
      } else if (response.success) {
        toast.success("Client updated successfully");
        dispatch(setClientDetails({ ...clientDetails, name, phone, gender }));
        const updatedClients = clients.map((client: ClientType) =>
          client._id === clientID ? { ...client, name, phone, gender } : client
        );
        dispatch(setClients(updatedClients));
      }
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      dispatch(setLoading({ ...loading, updateLoading: false }));
    }
  }
);

export const addClientToGoal = createAsyncThunk(
  "BusinessDashboardClients/addClientToGoal",
  async (
    { clientID, goal }: { clientID: string; goal: any },
    { dispatch, getState }
  ) => {
    const state = getState() as { businessDashboard: BusinessDashboardState };
    const { clientGoals, allGoals, loading } =
      state.businessDashboard.businessDashboardClients;
    try {
      dispatch(setLoading({ ...loading, addLoading: true }));
      if (!goal) return toast.error("Goal is required");
      dispatch(setAddClientToGoalOpen({ open: false }));
      const response = await AddClientToGoal(clientID, goal._id as string);
      if (!response.success && response.error) {
        toast.error(response.error);
        return;
      } else if (response.success) {
        toast.success("Client added to goal successfully");
        dispatch(setAllGoals(allGoals.filter((g: any) => g._id !== goal._id)));
        dispatch(setClientGoals([...clientGoals, goal]));
      }
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      dispatch(setLoading({ ...loading, addLoading: false }));
    }
  }
);

const clientsSlice = createSlice({
  name: "BusinessDashboardClients",
  initialState,
  reducers: {
    setClientDetails(state, action: PayloadAction<any>) {
      state.clientDetails = action.payload;
    },
    setLoading(state, action: PayloadAction<Loading>) {
      state.loading = action.payload;
    },
    setClientsLoading(state, action: PayloadAction<boolean>) {
      state.clientsLoading = action.payload;
    },
    setClientGoalsLoading(state, action: PayloadAction<boolean>) {
      state.clientGoalsLoading = action.payload;
    },
    setGoalsLoading(state, action: PayloadAction<boolean>) {
      state.goalsLoading = action.payload;
    },
    setGroupsLoading(state, action: PayloadAction<boolean>) {
      state.groupsLoading = action.payload;
    },
    setClientDetailsOpen(state, action: PayloadAction<boolean>) {
      state.clientDetailsOpen = action.payload;
    },
    handleCloseClientDetails(state) {
      state.clientDetails = null;
      state.clientDetailsOpen = false;
    },
    setAddContact(state, action: PayloadAction<boolean>) {
      state.addContact = action.payload;
    },
    setImportContacts(state, action: PayloadAction<boolean>) {
      state.importContacts = action.payload;
    },
    setFiles(state, action: PayloadAction<File[]>) {
      state.files = action.payload;
    },
    setError(state, action: PayloadAction<boolean>) {
      state.error = action.payload;
    },
    setSelectedLetter(state, action: PayloadAction<string | null>) {
      state.selectedLetter = action.payload;
    },
    setClients(state, action: PayloadAction<any>) {
      state.clients = action.payload;
    },
    setClientsErrorMessage(state, action: PayloadAction<string>) {
      state.clientsErrorMessage = action.payload;
    },
    setPattern(state, action: PayloadAction<string>) {
      state.pattern = action.payload;
    },
    setGroupPattern(state, action: PayloadAction<string>) {
      state.groupPattern = action.payload;
    },
    setGroupSortBy(state, action: PayloadAction<"name" | "date">) {
      state.groupSortBy = action.payload;
    },
    setGroupSortOrder(state, action: PayloadAction<"asc" | "desc">) {
      state.groupSortOrder = action.payload;
    },
    setGroups(state, action: PayloadAction<any[]>) {
      state.groups = action.payload;
    },
    handleOpenCallDetails(
      state,
      action: PayloadAction<{ bool: boolean; client?: any }>
    ) {
      if (action.payload.bool) {
        state.clientDetails = action.payload.client;
        state.clientDetailsOpen = true;
      } else {
        state.clientDetails = null;
        state.clientDetailsOpen = false;
      }
    },
    handleFileUpload: (state, action: PayloadAction<FileMetadata[]>) => {
      const files = action.payload;
      if (files.length !== 1) {
        state.error = true;
        return;
      }
      state.files = files;
      state.error = false;
    },
    setName(state, action: PayloadAction<string>) {
      state.Name = action.payload;
    },
    setNumber(state, action: PayloadAction<string>) {
      state.phoneNumber = action.payload;
    },
    setCountry(state, action: PayloadAction<CountryCode | "">) {
      state.country = action.payload;
    },
    setClientGoals(state, action: PayloadAction<any[]>) {
      state.clientGoals = action.payload;
    },
    setAllGoals(state, action: PayloadAction<any[]>) {
      state.allGoals = action.payload;
    },
    setAddClientToGoalOpen: (
      state,
      action: PayloadAction<{ open: boolean; goal?: any }>
    ) => {
      state.addClientToGoalOpen = action.payload.open;
      state.clientSelectedGoal = action.payload.goal || null;
    },
    setImportContactsDialogOpen(state, action: PayloadAction<boolean>) {
      state.importContactsDialogOpen = action.payload; // Action to toggle dialog state
    },
  },
});

export const {
  setClientDetails,
  setLoading,
  setClientsLoading,
  setGroupsLoading,
  setClientGoalsLoading,
  setGoalsLoading,
  setClientDetailsOpen,
  setAddContact,
  setImportContacts,
  setFiles,
  setError,
  setSelectedLetter,
  setClients,
  setClientsErrorMessage,
  setPattern,
  setGroupPattern,
  setGroupSortBy,
  setGroupSortOrder,
  setGroups,
  handleOpenCallDetails,
  handleFileUpload,
  setName,
  setNumber,
  setCountry,
  setClientGoals,
  setAllGoals,
  setAddClientToGoalOpen,
  setImportContactsDialogOpen,
  handleCloseClientDetails,
} = clientsSlice.actions;

export default clientsSlice.reducer;
