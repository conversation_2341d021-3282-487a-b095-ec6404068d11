import { cn } from "@/lib/utils";
import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";

interface CustomPaginationProps {
  itemsPerPage: number;
  totalItems: number;
  currentPage: number;
  className?: string;
  onPageChange: (page: number) => void;
}

const CustomPagination: React.FC<CustomPaginationProps> = ({
  itemsPerPage,
  totalItems,
  currentPage,
  onPageChange,
  className,
}) => {
  const { t } = useTranslation("assistants");
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  useEffect(() => {
    if (totalPages > 0 && currentPage > totalPages) {
      onPageChange(totalPages);
    }
  }, [totalItems, totalPages, currentPage, onPageChange]);

  if (totalPages <= 1) return null;

  const getPageNumbers = () => {
    const pages = [];
    for (let i = 1; i <= totalPages; i++) {
      if (i === 1 || i === totalPages || Math.abs(i - currentPage) <= 1) {
        pages.push(i);
      } else if (i === currentPage - 2 || i === currentPage + 2) {
        pages.push("ellipsis-" + i);
      }
    }
    return pages;
  };

  return (
    <div
      className={cn("flex justify-center items-center gap-1 mt-2", className)}
    >
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`px-3 py-1 rounded transition-all ${
          currentPage === 1
            ? "opacity-50 cursor-not-allowed"
            : "hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700"
        }`}
      >
        {t("pagination.previous")}
      </button>
      {getPageNumbers().map((page, idx) =>
        typeof page === "number" ? (
          <button
            key={page}
            onClick={() => onPageChange(page)}
            className={`px-3 py-1 rounded transition-all ${
              page === currentPage
                ? "bg-voxa-teal-600 text-white font-bold"
                : "hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700"
            }`}
          >
            {page}
          </button>
        ) : (
          <span key={page + idx} className="px-2 text-gray-400">
            ...
          </span>
        )
      )}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`px-3 py-1 rounded transition-all ${
          currentPage === totalPages
            ? "opacity-50 cursor-not-allowed"
            : "hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700"
        }`}
      >
        {t("pagination.next")}
      </button>
    </div>
  );
};

export default CustomPagination;
