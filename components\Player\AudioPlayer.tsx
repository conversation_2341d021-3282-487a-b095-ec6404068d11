"use client";

import { useState, useRef, useEffect, useTransition } from "react";
import WaveSurfer from "wavesurfer.js";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { RewindIcon, PlayIcon, PauseIcon, Download, Redo2 } from "lucide-react";
import { getPresignedUrl } from "@/actions/S3Audio";
import { useTheme } from "next-themes";
import CircularLoaderSmall from "../Loaders/CircularLoaderSmall";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

interface AudioPlayerProps {
  skip: number;
  type?: string;
  url?: string;
}
const AudioPlayer: React.FC<AudioPlayerProps> = ({ skip }) => {
  const { callDetails } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardRoot
  );
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [volume, setVolume] = useState(0.5);
  const [currentTime, setCurrentTime] = useState<number>(skip);
  const [duration, setDuration] = useState<number>(callDetails.CallDuration);
  const [loading, setLoading] = useState<boolean>(true);
  const waveformRef = useRef<HTMLDivElement>(null);
  const wavesurfer = useRef<WaveSurfer>(null);
  const [isPending, startTransition] = useTransition();

  const { theme, systemTheme } = useTheme();
  const isDarkMode =
    theme === "dark" || (theme === "system" && systemTheme === "dark");

  const { i18n } = useTranslation();
  const isRTL = i18n.language === "ar";

  useEffect(() => {
    if (!waveformRef.current) return;

    if (callDetails.CallDuration > 600) {
      if (wavesurfer.current) {
        wavesurfer.current.destroy();
        wavesurfer.current = null;
      }
      setLoading(false);
      return;
    }

    setIsPlaying(false);
    setLoading(true);

    const waveSurferOptions = (ref: any) => ({
      container: ref,
      waveColor: isDarkMode ? "#d0d0d2" : "#24242498",
      progressColor: "#01504C",
      cursorColor: "#01504C",
      barWidth: 3,
      barRadius: 30,
      height: 68,
      normalize: true,
    });

    const options = waveSurferOptions(waveformRef.current);
    wavesurfer.current = WaveSurfer.create(options);
    wavesurfer.current.load(callDetails.RecordingURL);

    wavesurfer.current.on("ready", function () {
      if (wavesurfer.current) {
        setDuration(wavesurfer.current.getDuration());
        const duration = wavesurfer.current.getDuration();
        if (Number.isFinite(duration) && duration > 0) {
          wavesurfer.current.seekTo(skip / duration);
        }
        setLoading(false);
      }
    });

    wavesurfer.current.on("audioprocess", function () {
      if (wavesurfer.current) {
        setCurrentTime(wavesurfer.current.getCurrentTime());
      }
    });

    wavesurfer.current.on("click", function () {
      if (wavesurfer.current) {
        setCurrentTime(wavesurfer.current.getCurrentTime());
      }
    });

    wavesurfer.current.on("finish", function () {
      setIsPlaying(false);
    });

    return () => {
      if (wavesurfer.current) {
        wavesurfer.current.destroy();
      }
    };
  }, [callDetails.RecordingURL, skip, isDarkMode, callDetails.CallDuration]);

  const handlePlayPause = () => {
    if (!wavesurfer.current) return;
    setIsPlaying(!isPlaying);
    wavesurfer.current.playPause();
  };

  const onVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { target } = e;
    const newVolume = +target.value;

    if (newVolume && wavesurfer.current) {
      setVolume(newVolume);
      wavesurfer.current.setVolume(newVolume);
    }
  };

  const handleSkip = () => {
    if (wavesurfer.current) {
      const newTime = Math.min(
        wavesurfer.current.getCurrentTime() + 2,
        wavesurfer.current.getDuration()
      );
      wavesurfer.current.seekTo(newTime / wavesurfer.current.getDuration());
      setCurrentTime(wavesurfer.current.getCurrentTime());
    }
  };

  const handleBack = () => {
    if (wavesurfer.current) {
      const newTime = Math.max(wavesurfer.current.getCurrentTime() - 2, 0);
      wavesurfer.current.seekTo(newTime / wavesurfer.current.getDuration());
      setCurrentTime(wavesurfer.current.getCurrentTime());
    }
  };

  const handleReset = () => {
    if (wavesurfer.current) {
      wavesurfer.current.seekTo(0);
      setCurrentTime(wavesurfer.current.getCurrentTime());
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  const handleDownloadRecording = async () => {
    startTransition(async () => {
      const key = callDetails.RecordingURL.split(
        "callbot-voice-recordings.s3.eu-west-3.amazonaws.com/"
      )[1];
      if (!key) {
        console.error("Failed to extract key from URL");
        return;
      }
      const signedUrl = await getPresignedUrl(key);
      if (!signedUrl) {
        console.error("Failed to get signed URL");
        return;
      }
      const a = document.createElement("a");
      a.href = signedUrl;
      a.download = key.split("/").pop() || "audio.mp3";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    });
  };

  return (
    <div className="max-w-md w-full rounded-xl">
      <Card className="bg-voxa-neutral-100 dark:bg-voxa-neutral-950">
        <CardContent className="flex flex-col items-center justify-center gap-2 px-4 pt-5 pb-3">
          <>
            <div className="w-full">
              <div className="h-16 w-full mb-2 relative">
                {loading && (
                  <div className="z-40 -mt-3.5 -mb-20 bg-voxa-neutral-100 dark:bg-voxa-neutral-950 absolute inset-0 flex items-center justify-center">
                    <CircularLoaderSmall />
                  </div>
                )}
                {callDetails.CallDuration > 600 && (
                  <div className="z-50 -mt-3.5 -mb-20 bg-voxa-neutral-100 dark:bg-voxa-neutral-950 absolute inset-0 flex flex-col gap-1.5 text-center text-sm items-center justify-center">
                    <p className="mt-4">
                      For performance, audio previews are disabled for files
                      longer than 10 minutes. Please download the audio to
                      listen.
                    </p>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="w-8 h-8 text-voxa-neutral-700 dark:text-voxa-neutral-100 dark:hover:bg-voxa-neutral-500"
                      aria-disabled={isPending}
                      onClick={() => {
                        if (!isPending) handleDownloadRecording();
                      }}
                    >
                      <Download className="w-6 h-6" />
                    </Button>
                  </div>
                )}
                <div ref={waveformRef} className="h-16 w-full" />
              </div>
              <div className="flex rtl:flex-grow rtl:flex-row-reverse justify-between text-sm text-voxa-neutral-600 dark:text-voxa-neutral-200 mt-1">
                <span>{formatTime(currentTime)}</span>
                {Number.isFinite(duration) && (
                  <span>{formatTime(duration)}</span>
                )}
              </div>
            </div>
            <div className="w-full flex rtl:flex-grow rtl:flex-row-reverse place-items-center items-center justify-between">
              <input
                type="range"
                id="volume"
                name="volume"
                min="0.01"
                max="1"
                step=".01"
                onChange={onVolumeChange}
                defaultValue={volume}
                className="w-[80px] rtl:rotate-180"
                style={{
                  background: `linear-gradient(to ${
                    isRTL ? "left" : "right"
                  }, #005f5a ${volume * 100}%, #c4c4c4 ${volume * 100}%)`,
                  accentColor: "#005f5a",
                }}
              />
              <div className="flex gap-2 rtl:flex-grow rtl:flex-row-reverse">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleBack}
                  className="text-voxa-neutral-700 dark:text-voxa-neutral-100 dark:hover:bg-voxa-neutral-500"
                >
                  <RewindIcon className="w-6 h-6" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handlePlayPause}
                  className="text-voxa-neutral-700 dark:text-voxa-neutral-100 dark:hover:bg-voxa-neutral-500"
                >
                  {isPlaying ? (
                    <PauseIcon className="w-6 h-6" />
                  ) : (
                    <PlayIcon className="w-6 h-6" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleSkip}
                  className="text-voxa-neutral-700 dark:text-voxa-neutral-100 dark:hover:bg-voxa-neutral-500"
                >
                  <RewindIcon className="rotate-180 w-6 h-6" />
                </Button>
              </div>
              <div className="flex gap-2 rtl:flex-grow rtl:flex-row-reverse">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleReset}
                  className="text-voxa-neutral-700 dark:text-voxa-neutral-100 dark:hover:bg-voxa-neutral-500"
                >
                  <Redo2 className="w-6 h-6" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-voxa-neutral-700 dark:text-voxa-neutral-100 dark:hover:bg-voxa-neutral-500"
                  aria-disabled={isPending}
                  onClick={() => {
                    if (!isPending) handleDownloadRecording();
                  }}
                >
                  <Download className="w-6 h-6" />
                </Button>
              </div>
            </div>
            {skip > 0 && (
              <span className="text-xs text-voxa-neutral-400">
                Skipped {skip} seconds, because this is a forwarded call
              </span>
            )}
          </>
        </CardContent>
      </Card>
    </div>
  );
};

export default AudioPlayer;
