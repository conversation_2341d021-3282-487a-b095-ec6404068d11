import parsePhoneNumberFromString, { CountryCode } from "libphonenumber-js";

export function GetTableData(
  file: File | null
): Promise<
  { firstValue: string; secondValue: string; messageDrop?: string }[]
> {
  if (!file) throw new Error("No file provided");

  const validTypes = ["text/csv", "text/plain"];
  if (!validTypes.includes(file.type)) {
    throw new Error("File must be a CSV or Text file");
  }

  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      try {
        const content = reader.result as string;

        const rows = content
          .split("\n")
          .map((row) => row.trim())
          .filter((row) => row !== "");

        const data = rows.map((row) => {
          const [col1, col2, col3] = row.split(",").map((cell) => cell.trim());
          const entry: {
            firstValue: string;
            secondValue: string;
            messageDrop?: string;
          } = {
            firstValue: col1 ?? "",
            secondValue: col2 ?? "",
          };

          if (col3 !== undefined) {
            entry.messageDrop = col3;
          }

          return entry;
        });

        resolve(data);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = (e) => {
      reject(e);
    };

    reader.readAsText(file);
  });
}

export function checkNumbersValidity(
  data: { firstValue: string; secondValue: string }[],
  phoneColumn: 0 | 1 | null,
  countryCode: string
): Promise<
  {
    number: string;
    username: string;
    type: "invalid" | "duplicate";
    index: number;
  }[]
> {
  return new Promise((resolve, reject) => {
    try {
      console.log(data);
      if (!data || data.length === 0) {
        throw new Error("No data provided");
      }

      const numberMap = new Map<string, string>();
      const warnings: {
        number: string;
        username: string;
        type: "invalid" | "duplicate";
        index: number;
      }[] = [];

      data.forEach((row, idx) => {
        const phoneRaw = phoneColumn === 0 ? row.firstValue : row.secondValue;
        const username = phoneColumn === 0 ? row.secondValue : row.firstValue;

        const number = phoneRaw.trim();

        // Validate number
        const phoneNumber = parsePhoneNumberFromString(
          number,
          countryCode as CountryCode
        );
        if (!phoneNumber || !phoneNumber.isValid()) {
          warnings.push({ number, username, type: "invalid", index: idx });
        }

        // Check for duplicates
        if (numberMap.has(number)) {
          warnings.push({ number, username, type: "duplicate", index: idx });
        } else {
          numberMap.set(number, username);
        }
      });

      resolve(warnings);
    } catch (error) {
      reject(error);
    }
  });
}
