"use client";

import { useState } from "react";
import { redirect, useRouter } from "next/navigation";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { udpateSiretAndAdresse } from "@/actions/Entreprise";

export default function OnboardingForm() {
  const [siret, setSiret] = useState("");
  const [adresse, setAdresse] = useState("");
  const [error, setError] = useState("");
  const [pending, setPending] = useState(false);
  const router = useRouter();
  const session = useSession();

  if( session.status === "unauthenticated") {
    return redirect("/");
  }

  const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      setError("");

      if (!/^\d{14}$/.test(siret)) {
        setError("Le SIRET doit contenir exactement 14 chiffres.");
        return;
      }

      if (!adresse.trim()) {
        setError("L'adresse est requise.");
        return;
      }

      setPending(true);

      const result = await udpateSiretAndAdresse({ siret, adresse });

      if (!result.success) {
        setError(result.error || "Une erreur est survenue.");
        setPending(false);
        return;
      }

      router.push("/businessDash");
      setPending(false);
    };
  return (
    <div className="w-full grid min-h-[100vh] xl:min-h-[100vh] px-6">
      <div className="flex items-center justify-center py-12 bg-secondary-600">
        <div className="mx-auto grid max-w-sm mt-20 px-2 w-full">
          <div className="grid gap-2 text-center">
            <h1 className="text-3xl font-bold">Complétez votre profil</h1>
            <p className="text-muted-foreground">Merci de renseigner les informations de votre entreprise</p>
          </div>
          <form onSubmit={handleSubmit} className="grid gap-3 mt-8">
            <div className="grid gap-2">
              <Label htmlFor="siret">Numéro SIRET <span className="text-xs text-voxa-neutral-400"> (14 Numéros) </span> </Label>
              <Input
                id="siret"
                type="text"
                name="siret"
                inputMode="numeric"
                pattern="\d{14}"
                maxLength={14}
                required
                className="border-secondary-900"
                value={siret}
                onChange={(e) => setSiret(e.target.value.replace(/\D/g, ""))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="adresse">Adresse</Label>
              <Input
                id="adresse"
                type="text"
                name="adresse"
                required
                className="border-secondary-900"
                value={adresse}
                onChange={(e) => setAdresse(e.target.value)}
              />
            </div>
            <Button type="submit" disabled={pending}>
              {pending ? "Envoi..." : "Valider"}
            </Button>
            {error && <p className="text-sm text-red-500">{error}</p>}
          </form>
        </div>
      </div>
    </div>
  );
}
