import React from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setAwsDialogOpen,
  setAwsCredentials,
  saveAwsCredentialsThunk,
  loadAwsCredentialsThunk,
} from "@/redux/BusinessDashboard/subSlices/IntegrationSlice";
import { RootState } from "@/redux/store";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import CustomInput from "@/components/CustomFormItems/CustomInput";
import { Button } from "@/components/ui/button";

const AwsCredentialsDialog: React.FC = () => {
  const dispatch = useDispatch();
  const { awsDialogOpen, awsCredentials, saving } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardIntegration
  );

  React.useEffect(() => {
    if (awsDialogOpen) {
      dispatch(loadAwsCredentialsThunk() as any);
    }
  }, [awsDialogOpen, dispatch]);

  const handleChange = (field: keyof typeof awsCredentials, value: string) => {
    dispatch(setAwsCredentials({ [field]: value }));
  };

  const handleSave = async () => {
    dispatch(saveAwsCredentialsThunk(awsCredentials) as any);
  };

  return (
    <Dialog
      open={awsDialogOpen}
      onOpenChange={(open) => dispatch(setAwsDialogOpen(open))}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Connect to Amazon Web Services</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-3 py-2">
          <CustomInput
            type="password"
            placeholder="AWS Access Key ID"
            label="AWS Access Key ID"
            value={awsCredentials.AWS_ACCESS_KEY_ID}
            onChange={(e) => handleChange("AWS_ACCESS_KEY_ID", e.target.value)}
            required
            isPassword
          />
          <CustomInput
            type="password"
            placeholder="AWS Secret Access Key"
            label="AWS Secret Access Key"
            value={awsCredentials.AWS_SECRET_ACCESS_KEY}
            required
            isPassword
            onChange={(e) =>
              handleChange("AWS_SECRET_ACCESS_KEY", e.target.value)
            }
          />
          <CustomInput
            type="text"
            placeholder="Region"
            label="AWS Region"
            value={awsCredentials.REGION}
            required
            isPassword
            onChange={(e) => handleChange("REGION", e.target.value)}
          />
          <CustomInput
            type="text"
            placeholder="Bucket Name"
            label="Bucket Name"
            value={awsCredentials.BUCKET_NAME}
            onChange={(e) => handleChange("BUCKET_NAME", e.target.value)}
            required
          />
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? "Saving..." : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AwsCredentialsDialog;
