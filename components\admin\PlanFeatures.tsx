"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/tooltip";
import { Eye, Info } from "lucide-react";

interface Feature {
  key: string;
  description: string;
  included: boolean;
  value?: string;
  note?: string;
}

interface PlanFeaturesProps {
  features: Feature[];
}

export default function PlanFeatures({ features }: PlanFeaturesProps) {
  if (features.length === 0) {
    return (
      <div className="space-y-3">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Eye className="w-4 h-4" />
          Features (0)
        </h4>
        <div className="border border-dashed border-voxa-neutral-300 dark:border-voxa-neutral-600 rounded-lg p-4 text-center text-muted-foreground">
          No features defined for this plan
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h4 className="font-medium text-foreground flex items-center gap-2">
        <Eye className="w-4 h-4" />
        Features ({features.length})
      </h4>
      <div className="border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg shadow-sm overflow-hidden">
        <div className="w-0 min-w-full overflow-x-auto">
          <table className="w-full min-w-[600px]">
            <thead className="bg-voxa-neutral-100 dark:bg-voxa-neutral-800">
              <tr>
                <th className="px-3 py-2 text-left text-xs font-medium">
                  Included
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium">
                  Feature
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium">
                  Value
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium">
                  Description
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium">
                  Note
                </th>
              </tr>
            </thead>
            <tbody>
              {features.map((feature, index) => (
                <tr
                  key={index}
                  className="border-t border-voxa-neutral-200 dark:border-voxa-neutral-800 hover:bg-voxa-neutral-50 dark:hover:bg-voxa-neutral-800 transition-colors"
                >
                  <td className="px-3 py-2 text-center">
                    <span
                      className={`text-lg font-bold ${
                        feature.included
                          ? "text-green-600 dark:text-green-400"
                          : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {feature.included ? "✓" : "✗"}
                    </span>
                  </td>
                  <td className="px-3 py-2">
                    <span className="text-sm font-medium">{feature.key}</span>
                  </td>
                  <td className="px-3 py-2">
                    <span className="text-sm">{feature.value || "—"}</span>
                  </td>
                  <td className="px-3 py-2">
                    <span className="text-sm text-muted-foreground">
                      {feature.description}
                    </span>
                  </td>
                  <td className="px-3 py-2">
                    {feature.note ? (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground cursor-help">
                              <Info className="w-3 h-3" />
                              <span className="max-w-20 truncate">
                                {feature.note}
                              </span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs">{feature.note}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ) : (
                      <span className="text-sm text-muted-foreground">—</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
