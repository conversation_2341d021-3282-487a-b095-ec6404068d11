"use client";

import { type LucideIcon } from "lucide-react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import clsx from "clsx";

export interface SidebarItemProps {
  icon: LucideIcon;
  title: string;
  url: string;
  alert: boolean;
}

export default function DashSidebarItem({ item }: { item: SidebarItemProps }) {
  const pathname = usePathname();

  return (
    <SidebarMenuItem key={item.title}>
      <SidebarMenuButton
        asChild
        className={clsx(
          "hover:bg-sidebar-borded min-w-9 min-h-9",
          pathname === item.url &&
            "border-2 border-voxa-teal-500 dark:border-voxa-teal-700"
        )}
        tooltip={item.title}
        isActive={pathname === item.url}
      >
        <Link href={item.url} className="relative">
          <item.icon
            className={clsx(
              "scale-125",
              pathname === item.url && "pr-0.5  rtl:pr-0 rtl:pl-0.5 scale-[1.4]"
            )}
          />
          <span>{item.title}</span>
          <SidebarMenuBadge className="absolute -right-2" />
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}
