"use client";

import ReactFlow, { Background, Controls, ReactFlowProvider } from "reactflow";
import "reactflow/dist/style.css";
import CustomNode from "./CustomNode";
import CustomEdge from "./CustomEdge";
import { cn } from "@/lib/utils";
import dagre from "dagre";

const nodeTypes = {
  custom: CustomNode,
};

const edgeTypes = {
  custom: CustomEdge,
};

const getLayoutedElements = (nodes: any[], edges: any[], direction = "TB") => {
  const dagreGraph = new dagre.graphlib.Graph();
  dagreGraph.setDefaultEdgeLabel(() => ({}));

  const isHorizontal = direction === "LR";
  dagreGraph.setGraph({
    rankdir: direction,
    ranksep: 200, // increase vertical spacing between nodes (columns in LR)
    nodesep: 100, // increase horizontal spacing between nodes (rows in LR)
  });

  nodes.forEach((node) => {
    dagreGraph.setNode(node.id, { width: 200, height: 200 });
  });

  edges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target);
  });

  dagre.layout(dagreGraph);

  nodes.forEach((node) => {
    const nodeWithPosition = dagreGraph.node(node.id);
    node.targetPosition = isHorizontal ? "left" : "top";
    node.sourcePosition = isHorizontal ? "right" : "bottom";

    // We are shifting the dagre node position (anchor=center center) to the top left
    // so it matches the React Flow node anchor point (top left).
    node.position = {
      x: nodeWithPosition.x - 200 / 2,
      y: nodeWithPosition.y - 200 / 2,
    };

    return node;
  });

  return { nodes, edges };
};

const TreeGraph = ({
  nodes,
  edges,
  className,
}: {
  nodes: any[];
  edges: any[];
  className?: string;
}) => {
  const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(
    nodes,
    edges,
    "LR"
  );

  const isHorizontal = true; // since direction is 'LR'
  const styledNodes = layoutedNodes.map((node) => ({
    ...node,
    type: "custom",
    sourcePosition: isHorizontal ? "right" : "bottom",
    targetPosition: isHorizontal ? "left" : "top",
  }));

  const styledEdges = layoutedEdges.map((edge) => ({
    ...edge,
    type: "custom",
  }));

  return (
    <div
      className={cn(
        "-my-8 -mx-4 sm:-mx-8 md:-mx-10 lg:-mx-12 w-[calc(100%+2rem)] sm:w-[calc(100%+4rem)] md:w-[calc(100%+5rem)] lg:w-[calc(100%+6rem)] h-[calc(100%+4rem)]",
        className
      )}
    >
      <ReactFlowProvider>
        <ReactFlow
          nodes={styledNodes}
          edges={styledEdges}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          minZoom={0.1}
        >
          <Background />
          <Controls />
        </ReactFlow>
      </ReactFlowProvider>
    </div>
  );
};

export default TreeGraph;
