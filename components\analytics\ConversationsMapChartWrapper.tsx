import ConversationsMapChart from "@/components/analytics/ConversationsMapChart";
import ExportDropdown, {
  ExportDropdownItem,
} from "@/components/analytics/ExportDropdown";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  exportChartAsImage,
  exportChartAsPDF,
} from "@/lib/analytics/chartExport";
import React, { useRef } from "react";

const ConversationsMapChartWrapper: React.FC<{
  setLoading?: (loading: boolean) => void;
}> = ({ setLoading }) => {
  const chartRef = useRef<HTMLDivElement>(null);

  // Define export items for the dropdown
  const exportItems: ExportDropdownItem[] = [
    {
      title: "Export as PNG",
      action: async () => {
        try {
          await exportChartAsImage(
            chartRef.current,
            "png",
            "conversations-map-chart"
          );
        } finally {
        }
      },
    },
    {
      title: "Export as SVG",
      action: async () => {
        try {
          await exportChartAsImage(
            chartRef.current,
            "svg",
            "conversations-map-chart"
          );
        } finally {
        }
      },
    },
    {
      title: "Export as PDF",
      action: async () => {
        try {
          await exportChartAsPDF(chartRef.current, "conversations-map-chart");
        } finally {
        }
      },
    },
  ];

  return (
    <div ref={chartRef} className="flex w-full">
      <Card className="bg-voxa-neutral-50 dark:bg-voxa-neutral-900 dark:border-voxa-neutral-700 transition-all duration-150 w-full">
        <CardHeader className="pb-0 flex flex-row items-start justify-between">
          <div>
            <CardTitle className="dark:text-voxa-neutral-50">
              Conversations Map Overview
            </CardTitle>
          </div>

          <ExportDropdown
            items={exportItems}
            buttonText="Export"
            loadingText="Exporting..."
            className="mt-0"
          />
        </CardHeader>
        <CardContent className="flex justify-center items-center">
          <ConversationsMapChart setLoading={setLoading} />
        </CardContent>
      </Card>
    </div>
  );
};

export default ConversationsMapChartWrapper;
