"use client";

import CustomButton from "@/components/CustomFormItems/Button";
import { Card, CardContent } from "@/components/ui/card";
import type { PaymentPackage } from "@/redux/BusinessDashboard/subSlices/BillingSlice";
import { CreditCard } from "lucide-react";
import React from "react";

interface PaymentPackagesGridProps {
  packages: PaymentPackage[];
  onSelectPackage: (amount: number) => void;
}

export default function PaymentPackagesGrid({
  packages,
  onSelectPackage,
}: PaymentPackagesGridProps) {
  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-xl font-semibold text-foreground/80">
        Add Credits to Your Balance
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {packages.map((pkg) => (
          <Card
            key={pkg.amount}
            className="relative transition-all duration-300 hover:shadow-xl cursor-pointer border-sidebar-border hover:border-voxa-teal-500/50 group hover:scale-[1.02] bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50"
            onClick={() => onSelectPackage(pkg.amount)}
          >
            <CardContent className="flex flex-col h-full text-center space-y-4 p-6 relative overflow-hidden">
              {/* Background decoration */}
              <div className="absolute inset-0 bg-gradient-to-br from-voxa-teal-50/20 to-transparent dark:from-voxa-teal-900/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              <div className="space-y-3 relative z-10">
                <div className="w-12 h-12 mx-auto bg-gradient-to-br from-voxa-teal-100 to-voxa-teal-200 dark:from-voxa-teal-900/30 dark:to-voxa-teal-800/50 rounded-full flex items-center justify-center group-hover:from-voxa-teal-200 group-hover:to-voxa-teal-300 dark:group-hover:from-voxa-teal-800/50 dark:group-hover:to-voxa-teal-700/60 transition-all duration-300 shadow-md group-hover:shadow-lg">
                  <CreditCard className="w-6 h-6 text-voxa-teal-600 dark:text-voxa-teal-400 group-hover:scale-110 transition-transform duration-300" />
                </div>
                <div className="text-4xl font-bold bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 bg-clip-text text-transparent group-hover:from-voxa-teal-700 group-hover:to-voxa-teal-800 transition-all duration-300">
                  {pkg.amount}€
                </div>
                <p className="text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">
                  Add {pkg.amount}€ credits to your balance
                </p>
              </div>
              <div className="grow"></div>
              <CustomButton
                props={{
                  value: "Select",
                  className:
                    "w-full relative z-10 !mt-0 bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 hover:from-voxa-teal-700 hover:to-voxa-teal-800 text-white font-medium transition-all duration-300 shadow-md hover:shadow-lg group-hover:scale-105",
                  onClick: (
                    e: React.MouseEvent<HTMLButtonElement, MouseEvent>
                  ) => {
                    e.stopPropagation();
                    onSelectPackage(pkg.amount);
                  },
                }}
              />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
