"use server"

import authOptions from "@/lib/AuthOptions"
import dbConnect from "@/lib/mongodb"
import Entreprise from "@/models/Entreprise"
import { getServerSession } from "next-auth"

export async function SaveBusinessDetails(data: any, cinUrl: string, poaUrl: string): Promise<{success: boolean, error?: string}> {
    try{
        const session = await getServerSession(authOptions)
        if(!session) return {success: false, error: "You must be logged in to perform this action"}
        await dbConnect()

        const existingEntreprise = await Entreprise.findOne({
            siret: data.siret,
            admin: { $ne: session.user.id }
        });
                if(existingEntreprise) return {success: false, error: "The provided SIRET already exists."}
        const EntrepriseAdmin = await Entreprise.findOne({ admin: session.user.id });
        const UpdatedEntreprise = await Entreprise.findOneAndUpdate(
            {
                admin: session.user.id
            },
            {
                siret: data.siret,
                kbis: data.kbis,
                sepa: data.sepa,
                rib: data.rib,
                phone: data.phone,
                corpName: data.corname,
                country: data.country,
                region: data.city,
                street: data.street,
                appartment: data.appnb,
                legalRepresentantName: data.name,
                legalRepresentantIdendity: cinUrl,
                cpa: poaUrl,
                representantIsOwner: data.isOwner,
            },
            {
                new: true
            }
        )
        if(!UpdatedEntreprise) return {success: false, error: "An error occured while updating your business details."}
        return {success: true}
    }catch(err: any){
        console.log(err)
        return {success: false, error: err.message}
    }
}