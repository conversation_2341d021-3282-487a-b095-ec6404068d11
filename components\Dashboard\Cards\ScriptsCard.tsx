import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { getTimeFromTimestamp } from "@/lib/Strings/DateFormat";
import {
  Calendar,
  CheckCircle2Icon,
  EyeIcon,
  FileText,
  GoalIcon,
} from "lucide-react";
import React from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  setScriptContent,
  setScriptContentOpen,
  setSelectedScript,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";
import ScriptContentPopup from "@/components/popups/EditScriptContent";

export type Tag = {
  name: string;
  color: string;
};

interface ScriptCard2Props {
  selected: boolean;
  script: {
    id: string;
    name: string;
    content: string;
    createdAt: number;
    usedBy: number;
    tags: Tag[];
  };
}

const ScriptsCard: React.FC<ScriptCard2Props> = ({ script, selected }) => {
  const { t } = useTranslation("assistants");
  const date = getTimeFromTimestamp(script.createdAt);
  const dispatch = useDispatch<AppDispatch>();
  const { selectedScript, scriptContentOpen, scriptContent } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );

  const handleClickEyeIcon = (content: string) => {
    dispatch(setScriptContent(content));
    dispatch(setScriptContentOpen(true));
  };

  const handleClickScriptCard = (script: any) => {
    dispatch(setScriptContent(script.content));
    dispatch(
      setSelectedScript(selectedScript === script.id ? null : script.id)
    );
  };
  return (
    <div
      className="relative p-4 bg-sidebar border border-sidebar-border w-full rounded-2xl shadow-lg space-y-3 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-800 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950 transition-all duration-150 cursor-pointer"
      onClick={() => handleClickScriptCard(script)}
    >
      {/* Header Section with Icon */}
      <div className="w-full flex justify-between items-center">
        {/* Left section */}
        <div className="flex items-center gap-1.5 flex-1 min-w-0">
          <FileText className="text-voxa-teal-600 w-5 h-5 shrink-0" />
          <Tooltip>
            <TooltipTrigger asChild>
              <p
                className="text-lg sm:text-xl font-bold text-voxa-teal-600 truncate cursor-pointer"
                tabIndex={0}
              >
                {script.name}
              </p>
            </TooltipTrigger>
            <TooltipContent>{script.name}</TooltipContent>
          </Tooltip>
        </div>
        {/* Right section */}
        <div className="flex gap-2 justify-between text-voxa-teal-600 shrink-0">
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              handleClickEyeIcon(script.content);
            }}
            title={t("createEditGoal.scriptCard.view")}
          >
            <EyeIcon className="hover:text-voxa-teal-500 active:text-voxa-teal-400" />
          </button>
          {selected && (
            <CheckCircle2Icon
              aria-label={t("createEditGoal.scriptCard.selected")}
            />
          )}
        </div>
      </div>

      {/* Uploaded At */}
      <div className="flex items-center gap-1.5">
        <Calendar className="dark:text-voxa-neutral-50 w-4 h-4" />
        <p className="text-sm dark:text-voxa-neutral-200">{date}</p>
      </div>
      {/* Used By */}
      <div className="flex items-center justify-between gap-2">
        <p className="flex gap-1.5 text-sm dark:text-voxa-neutral-50">
          <GoalIcon className="dark:text-voxa-neutral-200 w-[1.2rem] h-[1.2rem]" />
          {t("createEditGoal.scriptCard.usedByGoals", { count: script.usedBy })}
        </p>
        <div className="z-50 w-full h-full flex gap-0.5 justify-center sm:justify-end">
          {script.tags.map((tag: any, idx: number) => (
            <Tooltip key={idx}>
              <TooltipTrigger asChild>
                <button
                  className="w-4 h-4 rounded-full border-2 group-hover:border-sidebar-border flex items-center justify-center transition-all duration-150"
                  style={{ background: tag.color }}
                  title={tag.name || t("createEditGoal.scriptCard.noName")}
                />
              </TooltipTrigger>
              <TooltipContent sideOffset={-4} className="font-medium border-2">
                {tag.name || t("createEditGoal.scriptCard.noName")}
              </TooltipContent>
            </Tooltip>
          ))}
        </div>
      </div>

      {/* Script Popup */}
      {scriptContentOpen && selectedScript === script.id && (
        <ScriptContentPopup
          content={scriptContent}
          setScriptContentOpen={(value: boolean) =>
            dispatch(setScriptContentOpen(value))
          }
        />
      )}
    </div>
  );
};

export default ScriptsCard;
