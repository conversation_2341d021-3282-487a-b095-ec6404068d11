export function BusinessDashSettingsVerify(data: BusinessDetailsFormType): {success: boolean, error?: string} {
    console.log(`${data.isOwner} ${data.poa} HERE///////////////////////////////////`)
    if(!data.appnb || !data.cin || !data.corname || !data.country || !data.kbis || !data.name || !data.phone || !data.rib || !data.sepa || !data.siret || !data.street || !data.city) {
        return {success: false, error: "All fields are required"}
    }
    else if(!data.isOwner && data.poa === undefined){
        console.log(true)
        return {success: false, error: "Proof of address is required"}
    }
    else{
        return {success: true}
    }
}