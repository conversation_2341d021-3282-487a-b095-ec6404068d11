import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>it<PERSON>,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { useState } from "react";
import { Label } from "@radix-ui/react-label";
import { toast } from "sonner";
import ButtonLoader from "../Loaders/ButtonLoader";
import { TriangleAlert } from "lucide-react";

import {
  setAssistantName,
  requestNewAssistant,
} from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import { DialogTrigger } from "@radix-ui/react-dialog";
import { useTranslation } from "react-i18next";

export default function AddAssistant() {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const { assistantName } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardAssistants
  );

  const [loading, setLoading] = useState(false);

  const submitRequest = async () => {
    if (assistantName.length < 3) {
      return toast.error(t("addAssistant.nameMinLength"));
    }
    setLoading(true);
    await dispatch(requestNewAssistant());
    setLoading(false);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="bg-voxa-teal-600 hover:bg-voxa-teal-500 text-white">
          {t("addAssistant.requestNewAssistant")}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[400px] border-voxa-neutral-700">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle>{t("addAssistant.requestNewAssistant")}</DialogTitle>
        </DialogHeader>

        <div className="mt-8 flex gap-1 items-center text-orange-400">
          <TriangleAlert className="w-4 h-4" />
          <span className="text-xs">
            {t("addAssistant.createdAfterApproval")}
          </span>
        </div>

        <div className="flex flex-col gap-3">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="Name" className="text-sm">
              {t("addAssistant.assistantName")}
            </Label>
            <Input
              id="Name"
              value={assistantName}
              onChange={(e) => dispatch(setAssistantName(e.target.value))}
              placeholder={t("addAssistant.namePlaceholder")}
            />
          </div>
        </div>

        <DialogFooter className="mt-8">
          <Button
            onClick={submitRequest}
            disabled={loading}
            className={`
              w-full
              ${
                loading
                  ? "cursor-not-allowed bg-voxa-teal-400"
                  : "bg-voxa-teal-600 hover:bg-voxa-teal-500 text-voxa-neutral-50"
              }
            `}
          >
            {loading ? (
              <>
                {t("addAssistant.submitting")}
                <ButtonLoader />
              </>
            ) : (
              t("addAssistant.submitRequest")
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
