const localeMap: { [key: string]: string } = {
  en: "en-US",
  fr: "fr-FR",
  es: "es-ES",
  de: "de-DE",
  ar: "ar-TN",
};

export function getTimeFromTimestamp(timestamp: any, locale?: string): string {
  if (!timestamp) {
    return "N/A";
  }
  const date = new Date(timestamp);

  const targetLocale = locale ? localeMap[locale] || locale : "en-US";
  const dayOfWeek = new Intl.DateTimeFormat(targetLocale, {
    weekday: "short",
  }).format(date);
  const day = date.getDate();
  const month = new Intl.DateTimeFormat(targetLocale, { month: "long" }).format(
    date
  );
  const year = date.getFullYear();
  const formattedDay = `${day}`;
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");
  if (locale === "ar") {
    return `${dayOfWeek}، ${formattedDay} ${month} ${year}، ${hours}:${minutes}:${seconds}`;
  } else if (locale === "fr") {
    return `${dayOfWeek.slice(
      0,
      dayOfWeek.length - 1
    )}, ${formattedDay} ${month}, ${year}, ${hours}:${minutes}:${seconds}`;
  } else {
    return `${dayOfWeek}, ${formattedDay} ${month}, ${year}, ${hours}:${minutes}:${seconds}`;
  }
}

export function convertSecondsToMinutes(seconds: string): string {
  const totalSeconds = parseInt(seconds);
  const minutes = Math.floor(totalSeconds / 60);
  const remainingSeconds = totalSeconds % 60;
  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
}

const options: Intl.DateTimeFormatOptions = {
  weekday: "long",
  month: "long",
  day: "numeric",
};

export const getFormattedDate = (daysAgo: number, locale?: string) => {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  const targetLocale = locale ? localeMap[locale] || locale : "en-US";
  const formattedDate = date.toLocaleDateString(targetLocale, options);
  const parts = formattedDate.split(" ");
  const capitalizedParts = parts.map((part) => {
    if (/^\d+$/.test(part) || /^[.,]$/.test(part)) {
      return part;
    }
    return part.charAt(0).toUpperCase() + part.slice(1);
  });
  return capitalizedParts.join(" ");
};
