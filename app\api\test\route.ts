import { getEntrepriseByAdminID } from "@/actions/Entreprise";
import dbConnect from "@/lib/mongodb";
import Entreprise from "@/models/Entreprise";
import { NextResponse } from "next/server";

export async function GET() {
  await dbConnect();

  const entrepriseResponse = await getEntrepriseByAdminID();

  if (!entrepriseResponse.success) {
    return NextResponse.json(
      { success: false, error: entrepriseResponse.error },
      { status: 400 }
    );
  }

  const entrepriseID = entrepriseResponse.entreprise._id;

  await Entreprise.findOneAndUpdate(
    { _id: entrepriseID },
    { $inc: { balance: -100 } },
    { new: true }
  );

  return NextResponse.json({
    message: "Balance decreased by 100",
    entrepriseID,
  });
}
