// Additional features for saved payment methods

export interface PaymentMethodAnalytics {
  totalSpent: number;
  transactionCount: number;
  lastUsed: Date;
  averageAmount: number;
}

export interface RecurringPaymentSetup {
  amount: number;
  frequency: "monthly" | "weekly" | "daily";
  paymentMethodId: string;
  nextChargeDate: Date;
}

export interface PaymentHistoryItem {
  id: string;
  amount: number;
  currency: string;
  status:
    | "succeeded"
    | "processing"
    | "failed"
    | "canceled"
    | "requires_payment_method"
    | "requires_confirmation";
  created: number;
  payment_method: {
    id: string;
    type: string;
    card?: {
      brand: string;
      last4: string;
      exp_month: number;
      exp_year: number;
    };
  } | null;
  description?: string;
}

export interface PaymentHistoryResponse {
  success: boolean;
  error?: string;
  data?: PaymentHistoryItem[];
}

// Future enhancements we could implement:

/**
 * 1. PAYMENT ANALYTICS ✅ IMPLEMENTED
 * - Track spending per card
 * - Show transaction history ✅ DONE
 * - Display usage patterns
 */

/**
 * 2. RECURRING PAYMENTS
 * - Set up automatic top-ups
 * - Low balance auto-refill
 * - Subscription-style billing
 */

/**
 * 3. PAYMENT LIMITS
 * - Daily/monthly spending limits
 * - Card-specific limits
 * - Fraud protection
 */

/**
 * 4. NOTIFICATIONS
 * - Payment confirmations
 * - Card expiry reminders
 * - Failed payment alerts
 */

/**
 * 5. ADVANCED FEATURES
 * - Split payments across multiple cards
 * - Corporate card management
 * - Team member card access
 * - Approval workflows
 */
