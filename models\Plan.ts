import { Plan as PlanType } from "@/types";
import mongoose from "mongoose";

const PlanFeatureSchema = new mongoose.Schema({
  key: {
    type: String,
  },
  value: {
    type: String,
  },
  description: {
    type: String,
  },
  included: {
    type: Boolean,
    default: true,
  },
  note: {
    type: String,
  },
});

const PlanSchema = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    description: {
      type: String,
    },

    // Configuration options by billing period
    billing_options: {
      monthly: {
        original_price: {
          type: Number,
          min: 0,
        },
        current_price: {
          type: Number,
          min: 0,
        },
        stripe_price_id: {
          type: String,
        },
      },
      yearly: {
        original_price: {
          type: Number,
          min: 0,
        },
        current_price: {
          type: Number,
          min: 0,
        },
        stripe_price_id: {
          type: String,
        },
      },
      weekly: {
        original_price: {
          type: Number,
          min: 0,
        },
        current_price: {
          type: Number,
          min: 0,
        },
        stripe_price_id: {
          type: String,
        },
      },
      daily: {
        original_price: {
          type: Number,
          min: 0,
        },
        current_price: {
          type: Number,
          min: 0,
        },
        stripe_price_id: {
          type: String,
        },
      },
    },
    currency: {
      type: String,
      default: "eur",
      enum: ["eur", "usd"],
    },

    // Features
    features: [PlanFeatureSchema],

    // Stripe integration
    stripe_product_id: {
      type: String,
    },

    // Status
    visible: {
      type: Boolean,
      default: true,
    },

    // Most popular badge
    mostPopular: {
      type: Boolean,
      default: false,
    },

    // Order for display
    sort_order: {
      type: Number,
      default: 0,
    },

    // Timestamps
    created_at: {
      type: Date,
      default: Date.now,
    },
    updated_at: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: { createdAt: "created_at", updatedAt: "updated_at" },
  }
);

const Plan =
  mongoose.models.Plan || mongoose.model<PlanType>("Plan", PlanSchema);

export default Plan;
