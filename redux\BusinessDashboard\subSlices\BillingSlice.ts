import { createPaymentLink } from "@/actions/BillingActions";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { RefObject } from "react"; // <-- Add this import
import { toast } from "sonner";

export interface PaymentPackage {
  amount: number;
}

interface BillingState {
  // Balance management
  balanceLoading: boolean;

  // Payment flow
  selectedAmount: number | null;
  isConfirmDialogOpen: boolean;
  isProcessingPayment: boolean;

  // Payment packages
  paymentPackages: PaymentPackage[];

  // Payment history loading state (PaymentHistory component manages its own data)
  paymentHistoryLoading: boolean;

  // Add firstPlanLinkRef state
  firstPlanLinkRef?: RefObject<HTMLTableRowElement>;
}

const initialState: BillingState = {
  balanceLoading: true,
  selectedAmount: null,
  isConfirmDialogOpen: false,
  isProcessingPayment: false,
  paymentPackages: [
    { amount: 20 },
    { amount: 50 },
    { amount: 100 },
    { amount: 200 },
  ],
  paymentHistoryLoading: false,
  firstPlanLinkRef: undefined,
};

// Async thunks

export const processPayment = createAsyncThunk(
  "BusinessDashboardBilling/processPayment",
  async (amount: number, { dispatch }) => {
    try {
      dispatch(setIsProcessingPayment(true));
      const response = await createPaymentLink(amount);
      if (response.success && response.data?.url) {
        // Close dialog immediately
        dispatch(closeConfirmationDialog());
        // Small delay to let dialog close animation complete
        setTimeout(() => {
          window.location.href = response.data!.url;
        }, 300);
      } else {
        toast.error(response.error || "Failed to create payment session");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to create payment session");
    } finally {
      dispatch(setIsProcessingPayment(false));
    }
  }
);

// Slice definition
const BillingSlice = createSlice({
  name: "BusinessDashboardBilling",
  initialState,
  reducers: {
    setBalanceLoading: (state, action) => {
      state.balanceLoading = action.payload;
    },

    // Payment flow
    setSelectedAmount: (state, action) => {
      state.selectedAmount = action.payload;
    },
    setIsConfirmDialogOpen: (state, action) => {
      state.isConfirmDialogOpen = action.payload;
    },
    setIsProcessingPayment: (state, action) => {
      state.isProcessingPayment = action.payload;
    },

    // Combined actions
    openConfirmationDialog: (state, action) => {
      state.selectedAmount = action.payload;
      state.isConfirmDialogOpen = true;
    },
    closeConfirmationDialog: (state) => {
      state.isConfirmDialogOpen = false;
      state.isProcessingPayment = false;
    },
    resetSelectedAmount: (state) => {
      state.selectedAmount = null;
    },

    // Payment history
    setPaymentHistoryLoading: (state, action) => {
      state.paymentHistoryLoading = action.payload;
    },

    // Reset state
    resetBillingState: (state) => {
      state.selectedAmount = null;
      state.isConfirmDialogOpen = false;
      state.isProcessingPayment = false;
    },
  },
});

export const {
  setBalanceLoading,
  setSelectedAmount,
  setIsConfirmDialogOpen,
  setIsProcessingPayment,
  openConfirmationDialog,
  closeConfirmationDialog,
  resetSelectedAmount,
  setPaymentHistoryLoading,
  resetBillingState,
} = BillingSlice.actions;

export default BillingSlice.reducer;
