import { Badge } from "@/components/ui/badge";
import { Connection } from "@/types";
import { HelpCircle, Laptop, MapPin, Smartphone, Tablet } from "lucide-react";
import { useSession } from "next-auth/react"; // Added import
import { formatDistanceToNow } from "date-fns";

function DeviceIcon({ deviceType }: { deviceType: string }) {
  const lower = deviceType.toLowerCase();
  const commonClasses = "w-8 h-8"; // Restored to big icon

  if (lower.includes("desktop"))
    return <Laptop className={`${commonClasses} text-blue-500`} />;
  if (lower.includes("mobile"))
    return <Smartphone className={`${commonClasses} text-purple-500`} />;
  if (lower.includes("tablet"))
    return <Tablet className={`${commonClasses} text-green-500`} />;
  return <HelpCircle className={`${commonClasses} text-gray-400`} />;
}

export default function ConnectionCard({
  conn,
}: {
  conn: Connection;
  onRevoke: (id: string) => void;
}) {
  const { data: session } = useSession(); // Get session data
  const loginTime = conn.createdAt
    ? new Date(conn.createdAt).toLocaleString()
    : "—";
  const lastSeen = conn.lastSeen
    ? formatDistanceToNow(new Date(conn.lastSeen), { addSuffix: true })
    : "—";

  // Determine if this is the current session by comparing jti and sessionId
  const isCurrentSession =
    session?.sessionId &&
    conn.sessionId &&
    session?.sessionId === conn.sessionId;

  return (
    <div className="border rounded-lg p-2 flex sm:items-center flex-col sm:flex-row gap-4 sm:gap-6 bg-sidebar border-sidebar-border">
      {/* Device icon and online badge */}
      <div className="flex flex-col items-center">
        {/* Reduced margin from mr-4 to mr-3 */}
        <DeviceIcon deviceType={conn.device || "Unknown"} />
        {conn.online && (
          <Badge
            variant="outline"
            className="border-green-500 text-green-600 px-1 py-0 text-[8px] mt-0.5" /* Reduced py and mt */
          >
            Online
          </Badge>
        )}
      </div>
      {/* Details */}
      <div className="flex-1 space-y-0.5">
        {/* Reduced spacing from space-y-1 to space-y-0.5 */}
        <div className="flex items-center gap-1 flex-wrap">
          {/* Reduced gap from gap-2 to gap-1 */}
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            {/* Reduced from text-base to text-sm */}
            {conn.browser} on {conn.os}
          </h3>
          {isCurrentSession && (
            <Badge
              variant="outline"
              className="px-1.5 py-0 ms-auto sm:ms-0 text-[10px] font-semibold border-yellow-400 bg-yellow-50 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300"
            >
              Current
            </Badge>
          )}
        </div>
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-1">
          {" "}
          {/* Changed from text-sm to text-xs */}
          <MapPin className="w-3 h-3" /> {/* Reduced from w-4 h-4 */}
          <span>
            {conn.location || "Unknown location"}, {loginTime}
          </span>
        </div>
        {!conn.online && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {/* Changed from text-sm to text-xs */}
            Last seen: {lastSeen}
          </div>
        )}
        <div>
          {conn.active ? (
            <Badge
              variant="outline"
              className="border-blue-500 text-blue-600 text-[10px] py-0 px-1.5" /* Added py-0 and px-1.5 for smaller size */
            >
              Session valid
            </Badge>
          ) : (
            <Badge
              variant="outline"
              className="border-red-500 text-red-600 text-[10px] py-0 px-1.5" /* Added py-0 and px-1.5 for smaller size */
            >
              Session expired
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
}
