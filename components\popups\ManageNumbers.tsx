import ButtonLoader from "../Loaders/ButtonLoader";
import { CountriesSelect } from "../dropdowns/CountriesSelect";
import { useEffect, useState } from "react";
import { AlertOctagon } from "lucide-react";
import * as Flags from "country-flag-icons/react/3x2";
import {
  addNumber,
  createNumber,
  setNumberCountry,
} from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { CountryCode } from "libphonenumber-js";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Button } from "../ui/button";
import CircularLoaderSmall from "../Loaders/CircularLoaderSmall";
import { useTranslation } from "react-i18next";

export default function ManageNumbers({ className }: { className?: string }) {
  const { t } = useTranslation("assistants");
  const { assistantDetails, numbers, managenumbersLoading, NumberCountry } =
    useSelector(
      (state: RootState) => state.businessDashboard.businessDashboardAssistants
    );
  const botName = assistantDetails?.name || "";
  const dispatch = useDispatch<AppDispatch>();
  const [PhoneNumbers, setPhoneNumbers] = useState<any>([]);

  useEffect(() => {
    setPhoneNumbers(numbers);
  }, [managenumbersLoading, numbers]);

  const [loading, setLoading] = useState(false);
  const handleCreateNumber = async () => {
    setLoading(true);
    await dispatch(createNumber());
    setLoading(false);
  };

  const [adding, setAdding] = useState(false);
  const handleAddNumberClick = async (numberObject: any) => {
    setPhoneNumbers(
      PhoneNumbers.filter((number: any) => {
        console.log(number, numberObject);
        return number.number !== numberObject.number;
      })
    );
    setAdding(true);
    await dispatch(addNumber({ numberObject: numberObject }));
    setAdding(false);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <button
          className={cn(
            "text-sm h-full font-medium py-2 px-4 bg-voxa-neutral-500 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-900 transition-all duration-150 rounded-t-lg items-center justify-between text-white",
            className
          )}
        >
          {t("manageNumbers.manageNumbers")}
        </button>
      </DialogTrigger>
      <DialogContent className="p-5 border-voxa-neutral-700 rounded-md max-w-md w-full">
        <DialogHeader>
          <DialogTitle>{t("manageNumbers.manageNumbers")}</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-3 mt-4">
          <span>{t("manageNumbers.addExistingNumber", { botName })}</span>
          <div className="flex flex-col gap-2">
            {managenumbersLoading ? (
              <div className="w-full flex justify-center items-center">
                <CircularLoaderSmall />
              </div>
            ) : (
              <>
                {PhoneNumbers.length > 0 ? (
                  PhoneNumbers.map((number: any, index: any) => {
                    const FlagComponent =
                      Flags[number.country as keyof typeof Flags];
                    return (
                      <div
                        key={index}
                        className="flex justify-between items-center text-sm bg-voxa-neutral-300 dark:bg-voxa-neutral-900 text-voxa-neutral-200 hover:text-white font-medium p-2 rounded-md transition-all duration-200"
                      >
                        <div className="flex gap-1 items-center text-sm">
                          {FlagComponent ? (
                            <FlagComponent className="w-6 h-4 opacity-80 hover:opacity-100" />
                          ) : (
                            <span>🌍</span>
                          )}
                          <span className="font-medium">{NumberCountry}</span>
                        </div>
                        <span>{number.number}</span>
                        {adding ? (
                          <ButtonLoader />
                        ) : (
                          <span
                            className="text-sm font-semibold text-voxa-teal-600 hover:underline cursor-pointer"
                            onClick={() => handleAddNumberClick(number)}
                          >
                            {t("manageNumbers.add")}
                          </span>
                        )}
                      </div>
                    );
                  })
                ) : (
                  <span className="text-sm text-red-500 flex gap-1 items-center font-light">
                    <AlertOctagon className="w-4 h-4" />
                    {t("manageNumbers.noActiveNumbers")}
                  </span>
                )}
              </>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-3 mt-4">
          <span>{t("manageNumbers.requestNewNumber")}</span>
          <CountriesSelect
            selectCountry={(newCountry: "" | CountryCode) =>
              dispatch(setNumberCountry(newCountry))
            }
            country={NumberCountry}
          />
          <Button
            onClick={handleCreateNumber}
            disabled={loading}
            className={`${
              loading
                ? "cursor-not-allowed"
                : "bg-voxa-teal-600 hover:bg-voxa-teal-500"
            } text-voxa-neutral-50 mt-8`}
          >
            {loading ? (
              <>
                {t("manageNumbers.submitting")}
                <ButtonLoader />
              </>
            ) : (
              t("manageNumbers.submitRequest")
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
