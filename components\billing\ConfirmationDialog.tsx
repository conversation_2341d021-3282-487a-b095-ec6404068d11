"use client";

import {
  calculateTaxForAmount,
  getSavedCards,
  type SavedCard,
} from "@/actions/BillingActions";
import CustomButton from "@/components/CustomFormItems/Button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  getPaymentMethodDisplayName,
  getPaymentMethodIconPath,
} from "@/lib/billingUtils";
import { AppDispatch } from "@/redux/store";
import { ArrowLeft, Check, CreditCard, ExternalLink } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCloseComplete?: () => void; // Called after dialog close animation completes
  onPayWithCard: (cardId: string) => void;
  onPayWithLink: () => void;
  selectedAmount: number | null;
  isProcessingPayment: boolean;
}

export default function ConfirmationDialog({
  isOpen,
  onClose,
  onCloseComplete,
  onPayWithCard,
  onPayWithLink,
  selectedAmount,
  isProcessingPayment,
}: ConfirmationDialogProps) {
  const [savedCards, setSavedCards] = useState<SavedCard[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<string>("link"); // "link" or card ID
  const [loading, setLoading] = useState(false);
  const [showPaymentMethods, setShowPaymentMethods] = useState(false);
  const [taxInfo, setTaxInfo] = useState<{
    tax: number;
    total: number;
    currency: string;
  } | null>(null);
  const [taxLoading, setTaxLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadSavedCards();
      setShowPaymentMethods(false); // Reset to initial view when dialog opens
    }
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && selectedAmount) {
      fetchTax(selectedAmount);
    } else {
      setTaxInfo(null);
    }
  }, [isOpen, selectedAmount]);

  const loadSavedCards = async () => {
    try {
      setLoading(true);
      const response = await getSavedCards();
      if (response.success && response.data) {
        setSavedCards(response.data.cards);
        // Set default payment method if available
        const defaultCard = response.data.cards.find((card) => card.is_default);
        if (defaultCard) {
          setSelectedPaymentMethod(defaultCard.id);
        }
      }
    } catch (error) {
      console.error("Error loading saved cards:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTax = async (amount: number) => {
    setTaxLoading(true);
    try {
      const response = await calculateTaxForAmount(amount);
      if (response.success && response.data) {
        setTaxInfo({
          tax: response.data.tax,
          total: response.data.total,
          currency: response.data.currency,
        });
      } else {
        setTaxInfo(null);
      }
    } catch (e) {
      setTaxInfo(null);
    } finally {
      setTaxLoading(false);
    }
  };

  const handlePayment = () => {
    if (selectedPaymentMethod === "link") {
      onPayWithLink();
    } else {
      onPayWithCard(selectedPaymentMethod);
    }
  };

  const handleInitialPayClick = () => {
    setShowPaymentMethods(true);
  };

  const handleBackToSummary = () => {
    setShowPaymentMethods(false);
  };

  const getCardIcon = (brand: string) => {
    const iconPath = getPaymentMethodIconPath("card", brand);
    const altText = getPaymentMethodDisplayName("card", brand);

    return (
      <Image
        src={iconPath}
        alt={altText}
        width={24}
        height={16}
        className="border border-gray-200 dark:border-gray-700 rounded"
      />
    );
  };
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
      // Call onCloseComplete after dialog animation completes
      if (onCloseComplete) {
        setTimeout(() => {
          onCloseComplete();
        }, 300); // Match the dialog animation duration
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-lg border-0 shadow-2xl max-h-screen sm:max-h-[calc(100vh-2rem)] h-full">
        <div className="relative flex flex-col">
          {/* Header with gradient background */}
          <div className="bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 -m-6 mb-0 rounded-t-lg p-6 text-white">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <CreditCard className="w-5 h-5" />
                </div>
                Purchase Credits
              </DialogTitle>
              <DialogDescription className="text-white/90 mt-2">
                Review your purchase details below
              </DialogDescription>
            </DialogHeader>
          </div>

          {/* Content */}
          <div className="grow h-0 sm:px-6 py-6 space-y-6 overflow-auto">
            {!showPaymentMethods ? (
              <>
                {/* Amount showcase */}
                <div className="text-center py-4">
                  <div className="text-5xl font-bold text-voxa-teal-600 mb-2">
                    {selectedAmount || 0}€
                  </div>
                  <p className="text-muted-foreground">Credits to add</p>
                </div>

                {/* Balance breakdown */}
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-xl p-4 space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-muted-foreground">
                      Adding
                    </span>
                    <span className="font-semibold text-voxa-teal-600">
                      +{selectedAmount || 0}€
                    </span>
                  </div>
                  {/* Tax row */}
                  {taxLoading ? (
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-muted-foreground">
                        Tax
                      </span>
                      <span className="text-xs text-muted-foreground">
                        Calculating...
                      </span>
                    </div>
                  ) : taxInfo && taxInfo.tax > 0 ? (
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-muted-foreground">
                        Tax
                      </span>
                      <span className="font-semibold">
                        +{taxInfo.tax.toFixed(2)}€
                      </span>
                    </div>
                  ) : null}
                  {/* Total row */}
                  {taxInfo && (
                    <div className="flex justify-between items-center mt-2 border-t pt-3">
                      <span className="text-sm font-medium text-muted-foreground">
                        Total to pay
                      </span>
                      <span className="font-semibold text-voxa-teal-700">
                        {taxInfo.total.toFixed(2)}€
                      </span>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <>
                {/* Payment Method Selection */}
                <div className="flex flex-col space-y-4 h-full">
                  <div className="flex items-center gap-3">
                    <button
                      onClick={handleBackToSummary}
                      className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      disabled={isProcessingPayment}
                    >
                      <ArrowLeft className="w-4 h-4" />
                    </button>
                    <h4 className="font-medium text-lg">
                      Select Payment Method
                    </h4>
                  </div>

                  {loading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-voxa-teal-600"></div>
                    </div>
                  ) : (
                    <div className="grow bg-gray-50 dark:bg-gray-900 rounded-lg p-4 overflow-y-auto">
                      <div className="space-y-3">
                        {/* Payment Link Option */}
                        <div
                          className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                            selectedPaymentMethod === "link"
                              ? "border-voxa-teal-600 bg-voxa-teal-50 dark:bg-voxa-teal-950"
                              : "border-gray-200 dark:border-gray-700 hover:border-voxa-teal-300"
                          }`}
                          onClick={() => setSelectedPaymentMethod("link")}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                                <ExternalLink className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                              </div>
                              <div>
                                <div className="font-medium">
                                  Pay with checkout link
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  Redirect to secure Stripe checkout
                                </div>
                              </div>
                            </div>
                            {selectedPaymentMethod === "link" && (
                              <Check className="w-5 h-5 text-voxa-teal-600" />
                            )}
                          </div>
                        </div>
                        {/* Saved Cards */}
                        {savedCards.map((card) => (
                          <div
                            key={card.id}
                            className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                              selectedPaymentMethod === card.id
                                ? "border-voxa-teal-600 bg-voxa-teal-50 dark:bg-voxa-teal-950"
                                : "border-gray-200 dark:border-gray-700 hover:border-voxa-teal-300"
                            }`}
                            onClick={() => setSelectedPaymentMethod(card.id)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center">
                                  {getCardIcon(card.brand)}
                                </div>
                                <div>
                                  <div className="font-medium">
                                    {card.brand.toUpperCase()} •••• {card.last4}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    Expires {card.exp_month}/{card.exp_year}
                                  </div>
                                </div>
                                {card.is_default && (
                                  <Badge variant="outline" className="text-xs">
                                    Default
                                  </Badge>
                                )}
                              </div>
                              {selectedPaymentMethod === card.id && (
                                <Check className="w-5 h-5 text-voxa-teal-600" />
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>

          {/* Fixed footer with action buttons */}
          <div className="flex-shrink-0 px-6 pb-6 space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <CustomButton
                props={{
                  value: "Cancel",
                  className: `flex-1 h-11 border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:!bg-gray-100 dark:hover:!bg-gray-700 transition-all duration-200 ${
                    isProcessingPayment ? "opacity-50 cursor-not-allowed" : ""
                  }`,
                  onClick: isProcessingPayment
                    ? undefined
                    : () => handleOpenChange(false),
                }}
              />
              <CustomButton
                props={{
                  value: isProcessingPayment
                    ? "Processing..."
                    : taxLoading
                    ? "Calculating taxes..."
                    : !showPaymentMethods
                    ? `Pay ${taxInfo?.total || selectedAmount || 0}€`
                    : selectedPaymentMethod === "link"
                    ? `Pay ${taxInfo?.total || selectedAmount || 0}€ with Link`
                    : `Pay ${taxInfo?.total || selectedAmount || 0}€ with Card`,
                  className: `flex-1 h-11 bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 hover:from-voxa-teal-700 hover:to-voxa-teal-800 text-white font-semibold transition-all duration-200 shadow-lg hover:shadow-xl ${
                    isProcessingPayment || taxLoading
                      ? "opacity-75 cursor-not-allowed"
                      : ""
                  }`,
                  onClick:
                    isProcessingPayment || taxLoading
                      ? undefined
                      : !showPaymentMethods
                      ? handleInitialPayClick
                      : handlePayment,
                  disabled: isProcessingPayment || taxLoading,
                }}
              />
            </div>

            {/* Security note */}
            <div className="text-center">
              <p className="text-xs text-muted-foreground">
                🔒 Secure payment powered by Stripe
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
