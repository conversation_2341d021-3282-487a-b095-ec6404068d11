"use client";

import React, { useState } from "react";
import { Accordion } from "@/components/ui/accordion";
import { Plan } from "@/types";
import PlanItem from "./PlanItem";
import PlanDeleteDialog from "./PlanDeleteDialog";
import PlansEmptyState from "./PlansEmptyState";

interface PlansListProps {
  plans: Plan[];
  onEdit?: (plan: Plan) => void;
  onDelete?: (planId: string) => void;
  onToggleVisibility?: (planId: string, visible: boolean) => void;
  isLoading?: boolean;
}

export default function PlansList({
  plans,
  onEdit,
  onDelete,
  onToggleVisibility,
  isLoading = false,
}: PlansListProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [planToDelete, setPlanToDelete] = useState<{
    id: string;
    name: string;
    subscriptionCount: number;
  } | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const handleDeleteClick = (
    planId: string,
    planName: string,
    subscriptionCount: number = 0
  ) => {
    setPlanToDelete({ id: planId, name: planName, subscriptionCount });
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (planToDelete && onDelete) {
      setActionLoading(planToDelete.id);
      try {
        await onDelete(planToDelete.id);
      } finally {
        setActionLoading(null);
        setDeleteDialogOpen(false);
        setPlanToDelete(null);
      }
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setPlanToDelete(null);
  };

  const handleVisibilityToggle = async (planId: string, visible: boolean) => {
    if (onToggleVisibility) {
      setActionLoading(planId);
      try {
        await onToggleVisibility(planId, visible);
      } finally {
        setActionLoading(null);
      }
    }
  };

  if (isLoading && plans.length === 0) {
    return <PlansEmptyState isLoading={true} />;
  }

  if (plans.length === 0) {
    return <PlansEmptyState isLoading={false} />;
  }

  return (
    <>
      <Accordion type="multiple" className="space-y-4">
        {plans.map((plan) => (
          <PlanItem
            key={plan._id}
            plan={plan}
            onEdit={onEdit}
            onDelete={handleDeleteClick}
            onToggleVisibility={handleVisibilityToggle}
            actionLoading={actionLoading}
          />
        ))}
      </Accordion>

      {/* Delete Confirmation Dialog */}
      <PlanDeleteDialog
        isOpen={deleteDialogOpen}
        onClose={handleDeleteCancel}
        onConfirmDelete={handleDeleteConfirm}
        onHideInstead={
          planToDelete && onToggleVisibility
            ? () => {
                handleVisibilityToggle(planToDelete.id, false);
                handleDeleteCancel();
              }
            : undefined
        }
        planName={planToDelete?.name || ""}
        subscriptionCount={planToDelete?.subscriptionCount || 0}
        isDeleting={actionLoading === planToDelete?.id}
      />
    </>
  );
}
