import clsx from "clsx";
import SupportAgentRoundedIcon from "@mui/icons-material/SupportAgentRounded";

export default function CircularStat({
  percentage,
  className,
}: {
  percentage: number;
  className?: string;
  withText?: boolean;
}) {
  const radius = 46;
  const stroke = 8;
  const normalizedRadius = radius - stroke / 2;
  const circumference = 2 * Math.PI * normalizedRadius;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className={clsx("relative aspect-square", className)}>
      <svg viewBox="0 0 100 100" className="w-full h-full transform -rotate-90">
        <circle
          cx="50"
          cy="50"
          r={normalizedRadius}
          fill="transparent"
          stroke="#bbb"
          strokeWidth={stroke}
        />
        <circle
          cx="50"
          cy="50"
          r={normalizedRadius}
          fill="transparent"
          stroke="#3b82f6"
          strokeWidth={stroke}
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
        />
      </svg>
      <div
        className={clsx(
          "scale-50 absolute inset-0 flex items-center justify-center font-semibold ",
          percentage == 0 ? "text-[#aaa]" : "text-blue-500"
        )}
      >
        <SupportAgentRoundedIcon className="scale-110" />
      </div>
    </div>
  );
}
