import { NextResponse } from "next/server";
import { getPresignedUrl } from "@/actions/TwilioLogFiles"

const PREFIX = process.env.TWILIO_LOGS_PREFIX

export async function POST(req: Request) {
    try {
        const { key } = await req.json();

        if (!key) {
            return NextResponse.json({ error: "Missing file key" }, { status: 400 });
        }

        const url = await getPresignedUrl(`${PREFIX}${key}`);

        if (!url) {
            return NextResponse.json({ error: "File not found" }, { status: 404 });
        }

        return NextResponse.json({ url });

    } catch (error: any) {
        console.log(error)
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
}
