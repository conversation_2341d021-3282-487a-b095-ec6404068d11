import { cookies } from "next/headers";

export const runtime = "nodejs";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const fileId = searchParams.get("fileId");
  const mime   = searchParams.get("mime");

  if (!fileId) return new Response("fileId missing", { status: 400 });

  const tokenCookie = (await cookies()).get("google_tokens_drive")?.value;
  if (!tokenCookie) return new Response("Not authorized", { status: 401 });

  const { access_token } = JSON.parse(tokenCookie);

  const isGoogleDoc = mime?.startsWith("application/vnd.google-apps");

  const url = isGoogleDoc
    ? `https://www.googleapis.com/drive/v3/files/${fileId}/export?mimeType=${encodeURIComponent(
        mime === "application/vnd.google-apps.document" ? "application/pdf" :
        mime === "application/vnd.google-apps.spreadsheet" ? "text/csv" :
        "application/pdf"
      )}`
    : `https://www.googleapis.com/drive/v3/files/${fileId}?alt=media`;

  const res = await fetch(url, {
    headers: {
      Authorization: `Bearer ${access_token}`,
    },
  });


  if (!res.ok) {
    const errorText = await res.text();
    console.error("Download error:", errorText, "Status:", res.status);
    return new Response("Failed to download file", { status: res.status });
  }

  const buffer = Buffer.from(await res.arrayBuffer());

  return new Response(buffer, {
    headers: {
      "Content-Type": isGoogleDoc ? "application/pdf" : "application/octet-stream",
      "Content-Disposition": `attachment; filename="${fileId}${isGoogleDoc ? ".pdf" : ""}"`,
    },
  });
}
