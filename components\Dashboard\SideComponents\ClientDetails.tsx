"use client";

import { AddClientToGoalDialog } from "@/components/dialogs/AddClientToGoal";
import { Button } from "@/components/ui/button";
import { AppDispatch, RootState } from "@/redux/store";
import CloseRoundedIcon from "@mui/icons-material/CloseRounded";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteClient,
  getAllExistingGoalsForEntreprise,
  GetClientGoals,
  setClientDetailsOpen,
  setClientDetails,
  updateClientDetails,
  removeClientFromGoal,
} from "@/redux/BusinessDashboard/subSlices/ClientsSlice";
import { IoLogoWhatsapp } from "react-icons/io";
import CircularLoaderSmall from "@/components/Loaders/CircularLoaderSmall";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import CustomInput from "@/components/CustomFormItems/Input";
import CustomSelect from "@/components/CustomFormItems/Select";
import ButtonLoader from "@/components/Loaders/ButtonLoader";
import { BlacklistToggle, FavoriteToggle } from "../Cards/ClientCard2";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

export default function ClientDetails() {
  const {
    clientDetails,
    loading,
    clientGoals,
    allGoals,
    clientGoalsLoading,
    goalsLoading,
    clientDetailsOpen,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardClients
  );

  const dispatch = useDispatch<AppDispatch>();
  const [isVisible, setIsVisible] = useState(false);
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (clientDetailsOpen) {
      timeout = setTimeout(() => setIsVisible(true), 0.5);
    } else {
      setIsVisible(false);
    }
    return () => clearTimeout(timeout);
  }, [clientDetailsOpen]);
  useEffect(() => {
    const getGoals = async () => {
      await dispatch(GetClientGoals(clientDetails._id));
      await dispatch(getAllExistingGoalsForEntreprise(clientDetails._id));
    };
    if (clientDetails._id) {
      getGoals();
    }
  }, [clientDetails, dispatch]);

  const DeleteClient = async () => {
    await dispatch(deleteClient(clientDetails._id));
  };

  const UpdateClient = async () => {
    await dispatch(
      updateClientDetails({
        clientID: clientDetails._id,
        name: clientDetails.name,
        phone: clientDetails.phone,
        gender: clientDetails.gender,
      })
    );
  };

  const handleRemoveClientFromGoal = async (
    clientID: string,
    goalID: string
  ) => {
    await dispatch(
      removeClientFromGoal({
        clientID: clientID,
        goalID: goalID,
      })
    );
  };

  return (
    <div
      className={cn(
        "max-w-md w-full fixed -ml-[450px] h-full top-0 right-0 duration-300 transition-transform ease-in-out translate-x-[450px]",
        isVisible && "translate-x-0"
      )}
    >
      <div className="[direction:ltr] z-50 rounded-sm max-w-md w-full flex flex-col gap-4 h-screen items-center fixed right-0 rtl:right-auto rtl:left-0 top-0 backdrop-blur-sm bg-background/90">
        <div className="relative p-3 sm:p-5 pb-0 flex justify-between items-center w-full text-foreground/50 dark:text-voxa-neutral-50">
          <div className="absolute top-4 left-4 flex gap-1">
            <FavoriteToggle
              isFavorited={clientDetails.isFavorited}
              clientID={clientDetails._id}
              className="bg-sidebar"
            />
            <BlacklistToggle
              isBlacklisted={clientDetails.isBlacklisted}
              clientID={clientDetails._id}
              className="bg-sidebar"
            />
          </div>
          <h1 className="ml-6 w-full text-xl font-semibold text-center">
            Client Details
          </h1>
          <button
            onClick={() => {
              setIsVisible(false);
              setTimeout(() => {
                dispatch(setClientDetailsOpen(false));
              }, 300);
            }}
            className="hover:text-gray-400 transition-all duration-150"
          >
            <CloseRoundedIcon />
          </button>
        </div>
        <div className="space-y-5 pb-5 px-10 overflow-y-auto w-full">
          <div className="w-full flex flex-col gap-1 justify-center items-center">
            <div className="bg-voxa-neutral-400/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
              <IoLogoWhatsapp className="text-voxa-neutral-600 w-8 h-8 mr-px" />
            </div>
            <p className="dark:text-voxa-neutral-200 font-medium text-lg">
              {clientDetails.name}
            </p>
            <p className="text-voxa-neutral-700 font-medium text-sm">
              {phoneNumberFormat(clientDetails.phone, clientDetails.country)}
            </p>
            {clientDetails.group && (
              <div className="w-full flex justify-start p-2 flex-col font-semibold gap-1 items-start">
                <p>Linked to Group</p>
                <p className="w-full text-center text-voxa-neutral-700 font-medium text-sm">
                  {clientDetails.group.name
                    ? clientDetails.group.name
                    : "No group for this client"}
                </p>
              </div>
            )}
          </div>
          <Accordion
            collapsible
            type="single"
            defaultValue="item-1"
            className="w-full"
          >
            <AccordionItem value="item-1" className="w-full">
              <AccordionTrigger className="w-full font-semibold text-lg">
                Update Client Details
              </AccordionTrigger>
              <AccordionContent className="flex flex-col gap-4 w-full px-2">
                <CustomInput
                  props={{
                    label: "Name",
                    name: "name",
                    placeholder: "Name",
                    value: clientDetails.name,
                    onChange: (e) =>
                      dispatch(
                        setClientDetails({
                          ...clientDetails,
                          name: e.target.value,
                        })
                      ),
                  }}
                />
                <CustomInput
                  props={{
                    label: "Phone Number",
                    name: "phone",
                    placeholder: "Phone Number",
                    value: clientDetails.phone,
                    onChange: (e) =>
                      dispatch(
                        setClientDetails({
                          ...clientDetails,
                          phone: e.target.value,
                        })
                      ),
                  }}
                />
                <CustomSelect
                  autoDefaultValue
                  label="Gender"
                  value={clientDetails.gender}
                  onValueChange={(value) =>
                    dispatch(
                      setClientDetails({ ...clientDetails, gender: value })
                    )
                  }
                  placeholder="Select gender"
                  items={[
                    { value: "Male", label: "Male" },
                    { value: "Female", label: "Female" },
                  ]}
                />
                <Button
                  onClick={UpdateClient}
                  disabled={loading.updateLoading}
                  className={`mt-5 w-full rounded-full ${
                    loading.updateLoading
                      ? "cursor-not-allowed"
                      : "bg-voxa-teal-600 hover:bg-voxa-teal-500 text-voxa-neutral-50 "
                  }`}
                >
                  {loading.updateLoading ? (
                    <>
                      Updating Client
                      <ButtonLoader />
                    </>
                  ) : (
                    "Update Client Details"
                  )}
                </Button>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-2" className="w-full">
              <AccordionTrigger className="w-full font-semibold text-lg">
                Update Client Goals
              </AccordionTrigger>
              <AccordionContent className="w-full px-2">
                <p className="text-center mb-2 font-semibold text-lg text-black/60 dark:text-voxa-neutral-200">
                  Associated Goals
                </p>
                {clientGoalsLoading ? (
                  <div className="w-full flex justify-center items-center h-10">
                    <CircularLoaderSmall />
                  </div>
                ) : clientGoals.length > 0 ? (
                  <div className="flex gap-2 flex-wrap justify-center">
                    {clientGoals.map((goal, index) => (
                      <p
                        key={index}
                        className="flex justify-between items-center font-medium text-xs bg-voxa-neutral-500 dark:bg-voxa-neutral-400 text-background py-1 pl-2 pr-1 gap-1 rounded-full"
                      >
                        {goal.name}
                        <X
                          onClick={() =>
                            handleRemoveClientFromGoal(
                              clientDetails._id,
                              goal._id
                            )
                          }
                          className="size-3.5 cursor-pointer hover:text-background/80"
                        />
                      </p>
                    ))}
                  </div>
                ) : (
                  <p className="w-full text-center text-voxa-neutral-700 font-medium text-sm">
                    No goals set for {clientDetails.name}
                  </p>
                )}
                <p className="mt-4 mb-2 font-semibold text-lg text-center text-black/60 dark:text-voxa-neutral-200">
                  All Goals
                </p>
                {goalsLoading ? (
                  <div className="w-full flex justify-center items-center h-10">
                    <CircularLoaderSmall />
                  </div>
                ) : (
                  <div className="flex flex-col gap-1 w-full">
                    <div className="flex flex-wrap gap-1.5 justify-center">
                      {allGoals.map((goal, index) => (
                        <AddClientToGoalDialog key={index} goal={goal} />
                      ))}
                    </div>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          <Button
            onClick={DeleteClient}
            className={`mt-8 flex w-full justify-center items-center  rounded-full ${
              loading.deleteLoading
                ? "cursor-not-allowed"
                : "bg-red-500 hover:bg-red-600 text-voxa-neutral-50 "
            }`}
          >
            {loading.deleteLoading ? (
              <>
                Deleting Client
                <ButtonLoader />
              </>
            ) : (
              "Delete Client"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
