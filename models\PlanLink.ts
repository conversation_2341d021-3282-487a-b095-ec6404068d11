import mongoose from "mongoose";

const PlanLinkSchema = new mongoose.Schema(
  {
    code: {
      type: String,
      unique: true,
    },
    plan_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plan",
      required: true,
    },
    uses_left: {
      type: Number,
      default: null, // null means infinite usage
    },
  },
  {
    timestamps: true,
  }
);

const PlanLink =
  mongoose.models.PlanLink || mongoose.model("PlanLink", PlanLinkSchema);

export default PlanLink;
