"use client";

import { Tag as TagType } from "@/types";
import { useRouter } from "next/navigation";
import { DeleteGoalDialog } from "../dialogs/DeleteGoal";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { handleCopyText } from "@/lib/Strings/Copy";
import { useEffect, useState } from "react";
import { DuplicateGoalDialog } from "../dialogs/DuplicateGoal";
import { cn } from "@/lib/utils";
import CreateTag from "../popups/CreateTag";
import AddTagsToGoal from "@/components/popups/AddTagsToGoal";
import { getGoalTags } from "@/actions/TagsActions";
import CircularLoaderSmall from "../Loaders/CircularLoaderSmall";
import { useTranslation } from "react-i18next";
import AddClientToGoal from "@/components/popups/AddGoalContact";
import AddExistingClientToGoal from "@/components/popups/AddExistingClientToGoal";
import AddGroupToGoal from "@/components/popups/AddGroupToGoal";
import ImportClientsToAGoal from "@/components/popups/ImportClientsAndAddToGoal";
import {
  CloudUpload,
  PlusCircle,
  Settings,
  Power,
  UserPlus,
  Users,
  GitCommitVertical,
  Ellipsis,
  Tag,
  Copy,
  UsersIcon,
  CircleStop,
  PauseCircle,
  X,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  setCurrentGoalStatus,
  AddOneContact,
  HandleAddGroupToGoal,
  ImportClientsToGoal,
  startGoal,
  stopGoal,
  handleAddExistingClientToGoal,
  handleAddClientToMeetGoal,
  pauseGoal,
  fetchCurrentGoalStatus,
  removeTagFromGoalThunk,
} from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";

export function AssistantGoalDropdown({
  goalID,
  goalName,
  goalStatus,
  goalType,
  isOpen,
  setOpenGoalID,
  goal_template_type,
}: {
  goalID: string;
  goalName: string;
  goalStatus: string;
  goalType: string;
  isOpen: boolean;
  setOpenGoalID: any;
  goal_template_type: string;
}) {
  const { t } = useTranslation("assistants");
  const {
    GoalID,
    currentGoalStatus,
    addGroupToGoal,
    createClientAndAddToGoalOpen,
    addClientToGoal,
    addContactsToGoal,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardAssistants
  );
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [tags, setTags] = useState<TagType[]>([]);
  const [tagsLoading, setTagsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setTagsLoading(true);
      getGoalTags(goalID)
        .then((res) => {
          if (res.success) setTags(res.tags || []);
        })
        .finally(() => setTagsLoading(false));
    }
  }, [isOpen, goalID]);

  const refreshTags = () => {
    setTagsLoading(true);
    getGoalTags(goalID)
      .then((res) => {
        if (res.success) setTags(res.tags || []);
      })
      .finally(() => setTagsLoading(false));
  };

  const handleClick = async () => {
    if (!currentGoalStatus) dispatch(setCurrentGoalStatus(goalStatus));
    setOpenGoalID(goalID);
  };

  const handleStartGoal = async () => {
    setLoading(true);
    await dispatch(startGoal({ GoalID: goalID }));
    setLoading(false);
  };

  const handleStopGoal = async () => {
    setLoading(true);
    await dispatch(stopGoal({ GoalID: goalID }));
    setLoading(false);
  };

  const handlePauseGoal = async () => {
    setLoading(true);
    await dispatch(pauseGoal({ GoalID: goalID }));
    setLoading(false);
  };

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    if (!isOpen) return;

    if (currentGoalStatus === "STARTED") {
      console.log("Starting polling...");
      intervalId = setInterval(async () => {
        await dispatch(fetchCurrentGoalStatus({ goalID: goalID }));
      }, 10000);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
        console.log("Polling stopped.");
      }
    };
  }, [dispatch, currentGoalStatus, goalID, isOpen]);

  return (
    <>
      <DropdownMenu
        open={isOpen}
        onOpenChange={(open) => setOpenGoalID(open ? goalID : null)}
      >
        <DropdownMenuTrigger asChild onPointerDown={handleClick}>
          <Ellipsis
            className={cn(
              "w-6 h-6 p-0.5 hover:bg-voxa-neutral-400/10 dark:text-voxa-neutral-200 cursor-pointer rounded-full",
              isOpen && "bg-voxa-neutral-400/15"
            )}
          />
        </DropdownMenuTrigger>
        <DropdownMenuContent className="bg-[#fcfcfc] dark:bg-[#181818] shadow-lg rounded-xl min-w-40 max-w-[50vw]">
          {/* Goal ID */}
          <DropdownMenuLabel className="px-1">
            <span>{t("goalDropdown.goalOptions")}</span>
            <div className="flex gap-1 justify-between items-center mt-2">
              <span className="text-xs font-normal truncate">{goalID}</span>
              <Copy
                className="w-4 h-4 cursor-pointer -translate-y-px"
                onClick={() => handleCopyText(goalID)}
              />
            </div>
          </DropdownMenuLabel>
          {/* Start/Pause Goal */}
          {goal_template_type === "GOOGLE_MEET" && (
            <DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem disabled>
                <GitCommitVertical />
                <span>
                  {currentGoalStatus === "STARTED"
                    ? t("goalDropdown.started")
                    : currentGoalStatus === "PENDING"
                    ? t("goalDropdown.notStarted")
                    : currentGoalStatus === "PAUSED"
                    ? t("goalDropdown.paused")
                    : currentGoalStatus === "STOPPED"
                    ? t("goalDropdown.stopped")
                    : currentGoalStatus === "COMPLETED"
                    ? t("goalDropdown.completed")
                    : t("goalDropdown.notStarted")}
                </span>
              </DropdownMenuItem>
              {["PENDING", "STOPPED", "PAUSED", "COMPLETED"].includes(
                currentGoalStatus
              ) && (
                <DropdownMenuItem
                  onClick={handleStartGoal}
                  className={`${
                    loading
                      ? "bg-teal-500/80"
                      : "bg-teal-500 hover:bg-teal-500/70 active:bg-teal-500/80"
                  } text-white`}
                  disabled={loading || currentGoalStatus === "STARTED"}
                >
                  <Power />
                  <span>
                    {currentGoalStatus === "PENDING"
                      ? t("goalDropdown.start")
                      : ["STOPPED", "COMPLETED"].includes(currentGoalStatus)
                      ? t("goalDropdown.restart")
                      : t("goalDropdown.resume")}
                  </span>
                </DropdownMenuItem>
              )}
            </DropdownMenuGroup>
          )}
          {goalType === "OUTGOING" && (
            <DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem disabled>
                <GitCommitVertical />
                <span>
                  {currentGoalStatus === "STARTED"
                    ? t("goalDropdown.started")
                    : currentGoalStatus === "PENDING"
                    ? t("goalDropdown.pending")
                    : currentGoalStatus === "PAUSED"
                    ? t("goalDropdown.paused")
                    : currentGoalStatus === "STOPPED"
                    ? t("goalDropdown.stopped")
                    : currentGoalStatus === "COMPLETED"
                    ? t("goalDropdown.completed")
                    : t("goalDropdown.notStarted")}
                </span>
              </DropdownMenuItem>
              {["PENDING", "STOPPED", "PAUSED", "COMPLETED"].includes(
                currentGoalStatus
              ) && (
                <DropdownMenuItem
                  onClick={handleStartGoal}
                  className={`${
                    loading
                      ? "bg-teal-500/80"
                      : "bg-teal-500 hover:bg-teal-500/70 active:bg-teal-500/80"
                  } text-white`}
                  disabled={loading}
                >
                  <Power />
                  <span>
                    {currentGoalStatus === "PENDING"
                      ? t("goalDropdown.start")
                      : ["STOPPED", "COMPLETED"].includes(currentGoalStatus)
                      ? t("goalDropdown.restart")
                      : t("goalDropdown.resume")}
                  </span>
                </DropdownMenuItem>
              )}
              {currentGoalStatus === "STARTED" && (
                <>
                  <DropdownMenuItem
                    onClick={handlePauseGoal}
                    className={`${
                      loading ? "bg-gray-300" : "bg-gray-100"
                    } text-gray-600`}
                    disabled={loading}
                  >
                    <PauseCircle />
                    <span>{t("goalDropdown.pause")}</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleStopGoal}
                    className={`${
                      loading
                        ? "bg-orange-600"
                        : "bg-orange-500 hover:bg-orange-500/80 active:bg-orange-600"
                    } text-white`}
                    disabled={loading}
                  >
                    <CircleStop />
                    <span>{t("goalDropdown.stop")}</span>
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuGroup>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            {/* Settings */}
            <DropdownMenuItem
              onClick={() =>
                router.push(
                  `/businessDash/assistants/Goals/CreateEdit/${goalID}?templateType=${goal_template_type}`
                )
              }
            >
              <Settings />
              <span>{t("goalDropdown.settings")}</span>
            </DropdownMenuItem>
            {/* Clients */}
            {((goal_template_type === "DEMARCHAGE" &&
              goalType === "OUTGOING") ||
              goal_template_type !== "DEMARCHAGE") && (
              <DropdownMenuItem
                onClick={() =>
                  router.push(
                    `/businessDash/assistants/Goals/Clients/${goalID}?templateType=${goal_template_type}&goalName=${goalName}`
                  )
                }
              >
                <Users />
                <span>{t("goalDropdown.clients")}</span>
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            {/* Add Client(s) */}
            {goal_template_type === "GOOGLE_MEET" && (
              <DropdownMenuItem
                onClick={() =>
                  dispatch(
                    handleAddClientToMeetGoal({ goalID, goal_template_type })
                  )
                }
              >
                <UserPlus />
                <span>{t("goalDropdown.addClient")}</span>
              </DropdownMenuItem>
            )}
            {goalType === "OUTGOING" && (
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <UserPlus />
                  <span>{t("goalDropdown.addClients")}</span>
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent className="bg-[#fcfcfc] dark:bg-[#181818] shadow-lg rounded-xl">
                    <DropdownMenuItem
                      onClick={() =>
                        dispatch(handleAddExistingClientToGoal({ goalID }))
                      }
                    >
                      <PlusCircle />
                      <span>{t("goalDropdown.addExistingClient")}</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() =>
                        dispatch(AddOneContact({ goalID, goal_template_type }))
                      }
                    >
                      <PlusCircle />
                      <span>{t("goalDropdown.oneClient")}</span>
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />

                    <DropdownMenuItem
                      onClick={() => dispatch(ImportClientsToGoal({ goalID }))}
                    >
                      <CloudUpload />
                      <span>{t("goalDropdown.importList")}</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => dispatch(HandleAddGroupToGoal({ goalID }))}
                    >
                      <UsersIcon />
                      <span>{t("goalDropdown.addExistingGroup")}</span>
                    </DropdownMenuItem>
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>
            )}
            {/* Tags */}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <Tag />
                <span>{t("goalDropdown.tags")}</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent className="bg-[#fcfcfc] dark:bg-[#181818] shadow-lg rounded-xl">
                  {tagsLoading ? (
                    <div className="flex items-center justify-center">
                      <CircularLoaderSmall className="scale-75" />
                    </div>
                  ) : tags.length === 0 ? (
                    <DropdownMenuItem disabled className="text-gray-500">
                      {t("goalDropdown.noTags")}
                    </DropdownMenuItem>
                  ) : (
                    tags.map((tag: TagType, index: number) => (
                      <DropdownMenuItem
                        key={index}
                        onSelect={(e) => e.preventDefault()}
                        style={{
                          color: tag.color + "ee",
                          borderColor: tag.color + "ee",
                        }}
                        className="z-50 flex items-center border-2 justify-between gap-2 group"
                      >
                        <p>{tag.name}</p>
                        <div
                          className="rounded-full border p-0.5 w-5 h-5 flex items-center justify-center text-xs text-sidebar-border cursor-pointer"
                          style={{ borderColor: tag.color + "ee" }}
                          onClick={async () => {
                            await dispatch(
                              removeTagFromGoalThunk({
                                goalID,
                                tagID: (tag as any)._id || tag._id,
                              })
                            );
                            refreshTags();
                          }}
                        >
                          <X style={{ color: tag.color + "ee" }} />
                        </div>
                      </DropdownMenuItem>
                    ))
                  )}
                  <DropdownMenuSeparator />
                  <AddTagsToGoal goalID={goalID} onTagsAdded={refreshTags} />
                  <DropdownMenuSeparator />
                  <CreateTag />
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          {/* Duplicate Goal */}
          <DropdownMenuItem onClick={(e) => e.preventDefault()}>
            <DuplicateGoalDialog goalID={goalID} />
          </DropdownMenuItem>
          {/* Delete Goal */}
          <DropdownMenuItem
            onClick={(e) => e.preventDefault()}
            className="mt-1"
          >
            <DeleteGoalDialog goalID={goalID} />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {GoalID === goalID && (
        <>
          {createClientAndAddToGoalOpen && <AddClientToGoal />}
          {addClientToGoal && <AddExistingClientToGoal />}
          {addGroupToGoal && <AddGroupToGoal />}
          {addContactsToGoal && <ImportClientsToAGoal />}
        </>
      )}
    </>
  );
}
