"use client";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import {
  setFilterSidebarOpen,
  resetFilters,
  setFiltersFromInput, // <-- add this import
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { Button } from "@/components/ui/button";
import CircularLoaderSmall from "@/components/Loaders/CircularLoaderSmall";
import SearchRoundedIcon from "@mui/icons-material/SearchRounded";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import DateRangePresets from "@/components/analytics/filtersSidebar/DateRangePresets";
import DateRangePicker from "@/components/analytics/filtersSidebar/DateRangePicker";
import ConversationTypeFilters from "@/components/analytics/filtersSidebar/ConversationTypeFilters";
import CountrySelect from "@/components/analytics/filtersSidebar/CountrySelect";
import DurationFilters from "@/components/analytics/filtersSidebar/DurationFilters";

export default function FilterSidebar() {
  const { t } = useTranslation("analytics");
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const filterSidebarOpen = useSelector(
    (state: RootState) => state.analytics.filterSidebarOpen
  );

  const handleResetFilters = () => {
    dispatch(resetFilters());
  };

  const handleFilter = async () => {
    setLoading(true);
    try {
      dispatch(setFiltersFromInput()); // <-- copy filtersInput to filters
      dispatch(setFilterSidebarOpen(false));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Sheet
      open={filterSidebarOpen}
      onOpenChange={(open) => dispatch(setFilterSidebarOpen(open))}
    >
      <SheetContent
        className="flex flex-col gap-2 p-4 !max-w-[800px] w-full sm:!w-fit"
        side="right"
      >
        <SheetHeader>
          <SheetTitle>Filters</SheetTitle>
        </SheetHeader>

        <div className="grow overflow-y-auto">
          <div className="flex flex-wrap gap-8">
            <div className="grow">
              <ConversationTypeFilters />
            </div>
            <div className="grow">
              <h3 className="font-bold mb-2">Date Range</h3>
              <DateRangePresets />
              <DateRangePicker />
            </div>
          </div>
          <div className="px-1">
            <h3 className="font-bold mb-2">Country</h3>
            <CountrySelect />
          </div>
          <div className="grow px-1 py-1">
            <DurationFilters />
          </div>
        </div>

        <SheetFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            onClick={handleResetFilters}
            variant="outline"
            className="w-full"
          >
            Reset Filters
          </Button>
          <Button
            onClick={handleFilter}
            className="w-full flex justify-center items-center gap-2 bg-foreground/70"
            disabled={loading}
          >
            {loading ? (
              <>
                <CircularLoaderSmall />
                <span>{t("filters.searching")}</span>
              </>
            ) : (
              <>
                <SearchRoundedIcon />
                <span>{t("filters.search")}</span>
              </>
            )}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
