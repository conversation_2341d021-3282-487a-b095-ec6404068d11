/**
 * Utility function to get the payment method icon path based on the type and brand
 */
export const getPaymentMethodIconPath = (
  type: string,
  brand?: string
): string => {
  // Handle card payments
  if (type === "card" && brand) {
    return getCardIconPath(brand);
  }

  // Handle other payment methods based on brand or type
  const methodKey = (brand || type).toLowerCase();

  switch (methodKey) {
    // Cards
    case "visa":
      return "/images/Icons/payment/visa.svg";
    case "mastercard":
      return "/images/Icons/payment/mastercard.svg";
    case "amex":
    case "american_express":
      return "/images/Icons/payment/american-express.svg";
    case "discover":
      return "/images/Icons/payment/discover.svg";
    case "diners":
    case "diners_club":
      return "/images/Icons/payment/diners.svg";
    case "jcb":
      return "/images/Icons/payment/jcb.svg";
    case "unionpay":
      return "/images/Icons/payment/unionpay.svg";

    // European payment methods
    case "ideal":
      return "/images/Icons/payment/ideal.svg";
    case "bancontact":
      return "/images/Icons/payment/bancontact.svg";
    case "eps":
      return "/images/Icons/payment/eps.svg";
    case "google_pay":
    case "google-pay":
      return "/images/Icons/payment/google-pay.svg";
    case "link":
      return "/images/Icons/payment/link.svg";

    // Bank transfers and SEPA
    case "sepa":
    case "sepa_debit":
      return "/images/Icons/payment/sepa.svg";

    // Other methods that now have specific icons
    case "giropay":
      return "/images/Icons/payment/giropay.svg";
    case "sofort":
      return "/images/Icons/payment/sofort.svg";
    case "other":
    default:
      return "/images/Icons/payment/generic-card.svg";
  }
};

/**
 * Legacy function for card-specific icon paths (kept for backwards compatibility)
 */
export const getCardIconPath = (brand: string): string => {
  switch (brand.toLowerCase()) {
    case "visa":
      return "/images/Icons/payment/visa.svg";
    case "mastercard":
      return "/images/Icons/payment/mastercard.svg";
    case "amex":
    case "american_express":
      return "/images/Icons/payment/american-express.svg";
    case "discover":
      return "/images/Icons/payment/discover.svg";
    case "diners":
    case "diners_club":
      return "/images/Icons/payment/diners.svg";
    case "jcb":
      return "/images/Icons/payment/jcb.svg";
    case "unionpay":
      return "/images/Icons/payment/unionpay.svg";
    default:
      return "/images/Icons/payment/generic-card.svg";
  }
};

/**
 * Utility function to get the proper display name for payment methods
 */
export const getPaymentMethodDisplayName = (
  type: string,
  brand?: string
): string => {
  // Handle card payments
  if (type === "card" && brand) {
    return getCardBrandName(brand);
  }

  // Handle other payment methods
  const methodKey = (brand || type).toLowerCase();

  switch (methodKey) {
    // Cards
    case "visa":
      return "Visa";
    case "mastercard":
      return "Mastercard";
    case "amex":
    case "american_express":
      return "American Express";
    case "discover":
      return "Discover";
    case "diners":
    case "diners_club":
      return "Diners Club";
    case "jcb":
      return "JCB";
    case "unionpay":
      return "UnionPay";

    // European payment methods
    case "ideal":
      return "iDEAL";
    case "bancontact":
      return "Bancontact";
    case "eps":
      return "EPS";
    case "google_pay":
    case "google-pay":
      return "Google Pay";
    case "link":
      return "Link";
    case "sepa":
    case "sepa_debit":
      return "SEPA Direct Debit";
    case "bank_transfer":
      return "Bank Transfer";

    // Default cases
    case "other":
      return "Other";
    default:
      return type.charAt(0).toUpperCase() + type.slice(1);
  }
};

/**
 * Legacy function for card-specific display names (kept for backwards compatibility)
 */
export const getCardBrandName = (brand: string): string => {
  switch (brand.toLowerCase()) {
    case "visa":
      return "Visa";
    case "mastercard":
      return "Mastercard";
    case "amex":
    case "american_express":
      return "American Express";
    case "discover":
      return "Discover";
    case "diners":
    case "diners_club":
      return "Diners Club";
    case "jcb":
      return "JCB";
    case "unionpay":
      return "UnionPay";
    default:
      return "Credit Card";
  }
};

export const formatBillingPeriod = (period: string) => {
  const periodText =
    period === "monthly"
      ? "month"
      : period === "yearly"
      ? "year"
      : period === "weekly"
      ? "week"
      : "day";
  return `per ${periodText}`;
};

export const featurePeriodMap: Record<
  string,
  { months: number; days: number }
> = {
  monthly: { months: 1, days: 30 },
  yearly: { months: 12, days: 365 },
  weekly: { months: 0.25, days: 7 },
  daily: { months: 0.033, days: 1 },
};
