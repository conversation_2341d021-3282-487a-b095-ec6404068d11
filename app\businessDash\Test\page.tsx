import clsx from "clsx";

const Button = ({
  className,
  children,
}: {
  className?: string;
  children?: React.ReactNode;
}) => (
  <p
    className={clsx(
      "text-center font-semibold text-background rounded-md p-2",
      className
    )}
  >
    {children}
  </p>
);

const Text = ({
  className,
  children,
}: {
  className?: string;
  children?: React.ReactNode;
}) => <h1 className={clsx("mx-auto font-semibold", className)}>{children}</h1>;

export default function Test() {
  return (
    <div className="bg-transparent">
      <div className="mb-2 grid grid-cols-4 gap-x-12 gap-y-2">
        <Button className=" bg-voxa-neutral-950">Test 1</Button>
        <Button className="bg-accent">Test 2</Button>
        <Button className="bg-background">Background</Button>
        <Button className="bg-foreground">Foreground</Button>
        <Text className="text-voxa-neutral-300  ">Test 1</Text>
        <Text className="text-red-600">Test 2</Text>
        <Text className="text-background">Background</Text>
        <Text className="text-foreground">Foreground</Text>
      </div>
      <div className="grid grid-cols-11 gap-2">
        <Button className="bg-voxa-neutral-50">n-050</Button>
        <Button className="bg-voxa-neutral-100">n-100</Button>
        <Button className="bg-voxa-neutral-200">n-200</Button>
        <Button className="bg-voxa-neutral-300">n-300</Button>
        <Button className="bg-voxa-neutral-400">n-400</Button>
        <Button className="bg-voxa-neutral-500">n-500</Button>
        <Button className="bg-voxa-neutral-600">n-600</Button>
        <Button className="bg-voxa-neutral-700">n-700</Button>
        <Button className="bg-voxa-neutral-800">n-800</Button>
        <Button className="bg-voxa-neutral-900">n-900</Button>
        <Button className="bg-voxa-neutral-950">n-950</Button>
        <Text className="text-voxa-neutral-50">n-050</Text>
        <Text className="text-voxa-neutral-100">n-100</Text>
        <Text className="text-voxa-neutral-200">n-200</Text>
        <Text className="text-voxa-neutral-300">n-300</Text>
        <Text className="text-voxa-neutral-400">n-400</Text>
        <Text className="text-voxa-neutral-500">n-500</Text>
        <Text className="text-voxa-neutral-600">n-600</Text>
        <Text className="text-voxa-neutral-700">n-700</Text>
        <Text className="text-voxa-neutral-800">n-800</Text>
        <Text className="text-voxa-neutral-900">n-900</Text>
        <Text className="text-voxa-neutral-950">n-950</Text>
        <Button className="bg-voxa-teal-50">t-050</Button>
        <Button className="bg-voxa-teal-100">t-100</Button>
        <Button className="bg-voxa-teal-200">t-200</Button>
        <Button className="bg-voxa-teal-300">t-300</Button>
        <Button className="bg-voxa-teal-400">t-400</Button>
        <Button className="bg-voxa-teal-500">t-500</Button>
        <Button className="bg-voxa-teal-600">t-600</Button>
        <Button className="bg-voxa-teal-700">t-700</Button>
        <Button className="bg-voxa-teal-800">t-800</Button>
        <Button className="bg-voxa-teal-900">t-900</Button>
        <Button className="bg-voxa-teal-950">t-950</Button>
        <Text className="text-voxa-teal-50">t-050</Text>
        <Text className="text-voxa-teal-100">t-100</Text>
        <Text className="text-voxa-teal-200">t-200</Text>
        <Text className="text-voxa-teal-300">t-300</Text>
        <Text className="text-voxa-teal-400">t-400</Text>
        <Text className="text-voxa-teal-500">t-500</Text>
        <Text className="text-voxa-teal-600">t-600</Text>
        <Text className="text-voxa-teal-700">t-700</Text>
        <Text className="text-voxa-teal-800">t-800</Text>
        <Text className="text-voxa-teal-900">t-900</Text>
        <Text className="text-voxa-teal-950">t-950</Text>
        <Button className="bg-voxa-cryan-50">c-050</Button>
        <Button className="bg-voxa-cryan-100">c-100</Button>
        <Button className="bg-voxa-cryan-200">c-200</Button>
        <Button className="bg-voxa-cryan-300">c-300</Button>
        <Button className="bg-voxa-cryan-400">c-400</Button>
        <Button className="bg-voxa-cryan-500">c-500</Button>
        <Button className="bg-voxa-cryan-600">c-600</Button>
        <Button className="bg-voxa-cryan-700">c-700</Button>
        <Button className="bg-voxa-cryan-800">c-800</Button>
        <Button className="bg-voxa-cryan-900">c-900</Button>
        <Button className="bg-voxa-cryan-950">c-950</Button>
        <Text className="text-voxa-cryan-50">c-050</Text>
        <Text className="text-voxa-cryan-100">c-100</Text>
        <Text className="text-voxa-cryan-200">c-200</Text>
        <Text className="text-voxa-cryan-300">c-300</Text>
        <Text className="text-voxa-cryan-400">c-400</Text>
        <Text className="text-voxa-cryan-500">c-500</Text>
        <Text className="text-voxa-cryan-600">c-600</Text>
        <Text className="text-voxa-cryan-700">c-700</Text>
        <Text className="text-voxa-cryan-800">c-800</Text>
        <Text className="text-voxa-cryan-900">c-900</Text>
        <Text className="text-voxa-cryan-950">c-950</Text>
        <Button className="bg-voxa-stone-50">s-050</Button>
        <Button className="bg-voxa-stone-100">s-100</Button>
        <Button className="bg-voxa-stone-200">s-200</Button>
        <Button className="bg-voxa-stone-300">s-300</Button>
        <Button className="bg-voxa-stone-400">s-400</Button>
        <Button className="bg-voxa-stone-500">s-500</Button>
        <Button className="bg-voxa-stone-600">s-600</Button>
        <Button className="bg-voxa-stone-700">s-700</Button>
        <Button className="bg-voxa-stone-800">s-800</Button>
        <Button className="bg-voxa-stone-900">s-900</Button>
        <Button className="bg-voxa-stone-950">s-950</Button>
        <Text className="text-voxa-stone-50">s-050</Text>
        <Text className="text-voxa-stone-100">s-100</Text>
        <Text className="text-voxa-stone-200">s-200</Text>
        <Text className="text-voxa-stone-300">s-300</Text>
        <Text className="text-voxa-stone-400">s-400</Text>
        <Text className="text-voxa-stone-500">s-500</Text>
        <Text className="text-voxa-stone-600">s-600</Text>
        <Text className="text-voxa-stone-700">s-700</Text>
        <Text className="text-voxa-stone-800">s-800</Text>
        <Text className="text-voxa-stone-900">s-900</Text>
        <Text className="text-voxa-stone-950">s-950</Text>
      </div>
    </div>
  );
}
