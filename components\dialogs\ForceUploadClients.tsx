import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import ButtonLoader from "../Loaders/ButtonLoader";

export function ForceUploadClients({
  handleUpload,
  invalid_numbers,
  duplicate_numbers,
  files,
  error,
  Loading,
  valid_rows,
  country,
}: {
  handleUpload: any;
  invalid_numbers: number;
  duplicate_numbers: number;
  files: any;
  error: any;
  Loading: any;
  valid_rows: any;
  country: any;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          disabled={files.length === 0 || error || Loading || !country}
          className={`${
            Loading
              ? "cursor-not-allowed"
              : "bg-voxa-teal-600 hover:bg-voxa-teal-500"
          } text-voxa-neutral-50 flex justify-center items-center gap-2 ${
            files.length === 0 || error
              ? "cursor-not-allowed"
              : "cursor-pointer"
          }`}
        >
          {Loading ? (
            <>
              Uploading Contacts
              <ButtonLoader />
            </>
          ) : (
            "Upload"
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] border-voxa-neutral-700">
        <DialogHeader>
          <DialogTitle>Save Numbers</DialogTitle>
          <DialogDescription>
            Are you sure you want to save the following numbers?
            {(invalid_numbers > 0 || duplicate_numbers > 0) && (
              <>
                {invalid_numbers > 0 && (
                  <>
                    We will delete{" "}
                    <span className="font-medium text-orange-500">
                      {invalid_numbers} invalid number
                      {invalid_numbers > 1 ? "s" : ""}
                    </span>
                    {duplicate_numbers > 0 && " and "}
                  </>
                )}
                {duplicate_numbers > 0 && (
                  <>
                    override{" "}
                    <span className="font-medium text-yellow-500">
                      {duplicate_numbers} duplicate number
                      {duplicate_numbers > 1 ? "s" : ""}
                    </span>
                  </>
                )}
                .
              </>
            )}{" "}
            , you currently have{" "}
            <span className="text-green-500">
              {valid_rows >= 0 ? valid_rows : 0} valid numbers
            </span>
            . This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="submit"
              className="hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950"
            >
              Cancel
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              onClick={handleUpload}
              className="bg-voxa-teal-600 hover:bg-voxa-teal-500 "
              disabled={files.length === 0 || error || Loading || !country}
            >
              Save Numbers
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
