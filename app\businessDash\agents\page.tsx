"use client";

import { handleCopyText } from "@/lib/Strings/Copy";
import { CopyIcon, SearchIcon } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import CustomInput from "@/components/CustomFormItems/Input";
import CustomButton from "@/components/CustomFormItems/Button";
import {
  GetEntrepriseRingoverParams,
  UpdateRingoverAPIKeyBaseUrls,
} from "@/actions/CRMActions";
import { Button } from "@/components/ui/button";
import MainLoader from "@/components/Loaders/MainLoader";
import CustomPagination from "@/components/pagination/CustomPagination";
import { usePagination } from "@/hooks/usePagination";

export default function BusinessDashHome() {
  const [apiKey, setApiKey] = useState<string>("");
  const [baseUrl, setBaseUrl] = useState<string>("");
  const [groups, setGroups] = useState<any[]>([]);
  const [filteredGroups, setFilteredGroups] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [updating, setUpdating] = useState<boolean>(false);
  const [fromGetFunction, setFromGetFunction] = useState<boolean>(false);
  const [fromUpdateFunction, setFromUpdateFunction] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const itemsPerPage = 12;

  const { currentItems, currentPage, setCurrentPage } = usePagination(
    filteredGroups,
    itemsPerPage
  );

  useEffect(() => {
    getRingoverParams();
  }, []);

  useEffect(() => {
    const getGroups = async () => {
      try {
        const response = await fetch(`${baseUrl}/groups`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: apiKey,
          },
        });
        if (response.status !== 200) throw new Error("Failed to fetch groups");
        toast.success("Groups fetched successfully");
        const data = await response.json();
        setGroups(data.list);
        setFilteredGroups(data.list);
      } catch (err: any) {
        toast.error(err.message);
      } finally {
        setLoading(false);
      }
    };
    if (baseUrl && apiKey) {
      getGroups();
    }
  }, [fromGetFunction, fromUpdateFunction, apiKey, baseUrl]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchTerm.trim() === "") {
        setFilteredGroups(groups);
      } else {
        const filtered = groups.filter((group) =>
          group.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setFilteredGroups(filtered);
      }
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm, groups]);

  const updateAPIKeyAndBaseUrls = async () => {
    try {
      if (!apiKey || !baseUrl) throw new Error("Please fill in all fields");
      setUpdating(true);
      const response = await UpdateRingoverAPIKeyBaseUrls(apiKey, baseUrl);
      if (!response.success) toast.error(response.error);
      toast.success("API Key and Base Urls updated successfully");
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setUpdating(false);
      setFromUpdateFunction(true);
    }
  };

  const getRingoverParams = async () => {
    try {
      const response = await GetEntrepriseRingoverParams();
      if (!response.success) return;
      if (response.apiKey) setApiKey(response.apiKey);
      if (response.baseUrl) setBaseUrl(response.baseUrl);
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setFromGetFunction(true);
    }
  };

  const handleSearch = () => {
    if (searchTerm.trim() === "") {
      setFilteredGroups(groups);
    } else {
      const filtered = groups.filter((group) =>
        group.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredGroups(filtered);
    }
  };

  return (
    <div className="relative w-full flex flex-col gap-4">
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        Ringover Groups
      </h1>
      <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 w-full items-end">
        <CustomInput
          props={{
            name: "apiKey",
            label: "Ringover API Key",
            placeholder: "Enter your Ringover API Key",
            className: "pr-10",
            value: apiKey,
            onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
              setApiKey(e.target.value),
            isPassword: true,
            type: "password",
          }}
        />
        <CustomInput
          props={{
            name: "baseURL",
            label: "Base URL",
            placeholder: "Enter your Base Url for the API",
            value: baseUrl,
            onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
              setBaseUrl(e.target.value),
          }}
        />
        <CustomButton
          props={{
            loading: updating,
            className: "sm:col-span-2 lg:col-span-1 h-10 rounded-lg",
            value: "Update API Key and Base Urls",
            onClick: updateAPIKeyAndBaseUrls,
          }}
        />
      </div>
      <div className="-mt-2 w-full md:max-w-md flex items-end">
        <CustomInput
          props={{
            name: "search",
            label: "Search by Group Name",
            placeholder: "Search...",
            className: "rounded-md rounded-r-none",
            value: searchTerm,
            onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchTerm(e.target.value),
          }}
        />
        <Button
          onClick={handleSearch}
          className="h-10 w-10 bg-voxa-teal-600 hover:bg-voxa-teal-500 font-semibold transition-all duration-200 rounded-md rounded-l-none"
        >
          <SearchIcon className="scale-125 text-white w-5 h-5" />
        </Button>
      </div>
      {!baseUrl || !apiKey ? (
        <div className="bg-voxa-primary-light p-5 rounded-xl flex items-center gap-2">
          <p className="w-full text-center text-voxa-primary-dark font-semibold">
            You need to set your Ringover API Key and Base Urls in order to
            fetch groups and users
          </p>
        </div>
      ) : loading ? (
        <div className="absolute w-full grid sm:col-span-2 md:col-span-3 lg:col-span-4 justify-center items-center h-[calc(100vh-2rem)] sm:h-[calc(100vh-3rem)]">
          <MainLoader />
        </div>
      ) : filteredGroups.length > 0 ? (
        <>
          <div className="grid sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4">
            {currentItems.map((group) => (
              <Link
                href={`/businessDash/agents/${group.name}?groupName=${group.name}&usersbaseurl=${baseUrl}/groups/${group.group_id}`}
                key={group.group_id}
                className="bg-sidebar border border-sidebar-border p-4 rounded-xl flex flex-col gap-1 transition duration-300 ease-in-out transform hover:scale-104 cursor-pointer hover:shadow-lg"
              >
                <div className="w-full flex justify-between items-center">
                  <h2 className="text-lg font-medium">{group.name}</h2>
                  <div className="flex items-center gap-2 z-10 relative">
                    <span className="text-voxa-neutral-700 text-sm font-semibold">
                      {group.group_id.toString().substring(0, 5)}
                    </span>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleCopyText(group.group_id);
                      }}
                    >
                      <CopyIcon className="w-4 h-4 dark:text-voxa-neutral-200 hover:text-white transition-colors" />
                    </button>
                  </div>
                </div>
                <p className="text-sm text-foreground/70">
                  Users: {group.total_users_count}
                </p>
                <p
                  className={`text-sm font-semibold ${
                    group.is_jumper ? "text-green-400" : "text-red-400"
                  }`}
                >
                  {group.is_jumper ? "Jumper Enabled" : "Jumper Disabled"}
                </p>
              </Link>
            ))}
          </div>
          <CustomPagination
            itemsPerPage={itemsPerPage}
            totalItems={filteredGroups.length}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
        </>
      ) : (
        <div className="w-full flex justify-center items-center">
          <p className="dark:text-voxa-neutral-200">No groups found</p>
        </div>
      )}
    </div>
  );
}
