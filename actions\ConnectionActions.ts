"use server";

import authOptions from "@/lib/AuthOptions";
import dbConnect from "@/lib/mongodb";
import Connection from "@/models/Connection";
import { User } from "@/models/User";
import { Connection as ConnectionType, User as UserType } from "@/types";
import mongoose from "mongoose";
import { getServerSession } from "next-auth";
import { headers } from "next/headers";
import { userAgent } from "next/server";
import jwt from "jsonwebtoken";

export async function CreateConnection(
  userId: string,
  sessionId: string
): Promise<{
  success: boolean;
  data?: ConnectionType;
  error?: string;
}> {
  try {
    await dbConnect();

    if (!userId || !sessionId) {
      return { success: false, error: "User ID and session ID are required" };
    }

    // Validate user exists
    const user = await User.findById(userId);
    if (!user) {
      return { success: false, error: "User not found" };
    }

    // Get user-agent and IP from headers
    const headersList = await headers();
    const ua = userAgent({ headers: headersList });
    const userAgentString = headersList.get("user-agent") || "";
    const ip =
      headersList.get("x-forwarded-for") ||
      headersList.get("x-real-ip") ||
      "127.0.0.1";

    // Fetch location from IP
    let location = "Unknown";
    try {
      const response = await fetch(
        `http://ip-api.com/json/${ip.split(",")[0]}`
      );
      if (response.ok) {
        const data = await response.json();
        if (data.status === "success" && data.city && data.country) {
          location = `${data.city}, ${data.country}`;
        }
      }
    } catch (error) {
      console.error("Failed to fetch location from IP:", error);
    }

    // Determine device type
    let deviceType = "Unknown";

    // If device info exists, use it
    if (ua.device.model || ua.device.vendor || ua.device.type) {
      deviceType =
        ua.device.model || ua.device.vendor || ua.device.type || "Unknown";
    }
    // Infer device type from OS when device type is undefined
    else if (ua.os.name) {
      const osName = ua.os.name.toLowerCase();
      if (
        osName.includes("windows") ||
        osName.includes("mac") ||
        osName.includes("linux")
      ) {
        deviceType = "Desktop";
      } else if (
        osName.includes("android") ||
        osName.includes("ios") ||
        osName.includes("iphone")
      ) {
        deviceType = "Mobile";
      } else if (osName.includes("ipad")) {
        deviceType = "Tablet";
      }
    }

    // Create new connection
    const newConnection = await Connection.create({
      user: userId,
      ip: ip.split(",")[0], // Take first IP if multiple are provided
      userAgent: userAgentString,
      device: deviceType,
      browser: `${ua.browser.name || "Unknown"} ${ua.browser.version || ""}`,
      os: `${ua.os.name || "Unknown"} ${ua.os.version || ""}`,
      location,
      sessionId, // Store the session ID
    });

    // Add connection to user's connections array
    await User.findByIdAndUpdate(
      userId,
      { $push: { connections: newConnection._id } },
      { new: true }
    );

    const connectionWithStringId: ConnectionType = {
      ...newConnection.toObject(),
      _id: newConnection._id.toString(),
      user: newConnection.user.toString(),
    };

    return { success: true, data: connectionWithStringId };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function GetConnectionById(connectionId: string): Promise<{
  success: boolean;
  data?: ConnectionType;
  error?: string;
}> {
  try {
    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(connectionId)) {
      return { success: false, error: "Invalid connection ID format" };
    }

    const connection = await Connection.findById(connectionId).populate(
      "user",
      "name lastname email"
    );

    if (!connection) {
      return { success: false, error: "Connection not found" };
    }

    const connectionWithStringIds: ConnectionType = {
      ...connection.toObject(),
      _id: connection._id.toString(),
      user: (connection.user as UserType)._id.toString(),
    };

    return { success: true, data: connectionWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function GetConnectionsByUserId(userId: string): Promise<{
  success: boolean;
  data?: ConnectionType[];
  totalCount?: number;
  error?: string;
}> {
  try {
    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return { success: false, error: "Invalid user ID format" };
    }

    // Fetch user with only the connections array, then populate connections (no skip/limit)
    const user = await User.findById(userId)
      .select("connections")
      .populate({
        path: "connections",
        options: {
          sort: { createdAt: -1 },
        },
      });

    if (!user) {
      return { success: false, error: "User not found" };
    }

    // Filter out null connections (in case of deleted connections)
    const connections = (user.connections || []).filter((conn: any) => conn);

    const connectionsWithStringIds: ConnectionType[] = connections.map(
      (connection: any) => ({
        ...connection.toObject(),
        _id: connection._id.toString(),
        user: connection.user.toString(),
      })
    );

    return {
      success: true,
      data: connectionsWithStringIds,
      totalCount: user.connections.length,
    };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function GetAllConnections(
  limit?: number,
  skip?: number,
  activeOnly?: boolean
): Promise<{
  success: boolean;
  data?: ConnectionType[];
  totalCount?: number;
  error?: string;
}> {
  try {
    await dbConnect();

    const query = activeOnly ? { active: true } : {};

    // Get total count
    const totalCount = await Connection.countDocuments(query);

    // Build connections query
    let connectionsQuery = Connection.find(query)
      .populate("user", "name lastname email")
      .sort({ createdAt: -1 });

    if (typeof skip !== "undefined") {
      connectionsQuery = connectionsQuery.skip(skip);
    }

    if (typeof limit !== "undefined") {
      connectionsQuery = connectionsQuery.limit(limit);
    }

    const connections = await connectionsQuery;

    const connectionsWithStringIds: ConnectionType[] = connections.map(
      (connection) => ({
        ...connection.toObject(),
        _id: connection._id.toString(),
        user: (connection.user as UserType)._id.toString(),
      })
    );

    return {
      success: true,
      data: connectionsWithStringIds,
      totalCount,
    };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function UpdateConnection(
  connectionId: string,
  updateData: {
    lastSeen?: Date;
    active?: boolean;
    device?: string;
    browser?: string;
    os?: string;
  }
): Promise<{
  success: boolean;
  data?: ConnectionType;
  error?: string;
}> {
  try {
    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(connectionId)) {
      return { success: false, error: "Invalid connection ID format" };
    }

    const updatedConnection = await Connection.findByIdAndUpdate(
      connectionId,
      { $set: updateData },
      { new: true }
    );

    if (!updatedConnection) {
      return { success: false, error: "Connection not found" };
    }

    const connectionWithStringIds: ConnectionType = {
      ...updatedConnection.toObject(),
      _id: updatedConnection._id.toString(),
      user: updatedConnection.user.toString(),
    };

    return { success: true, data: connectionWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

// Add this new function to get current user's connections
export async function GetCurrentUserConnections(): Promise<{
  success: boolean;
  data?: ConnectionType[];
  totalCount?: number;
  error?: string;
}> {
  try {
    await dbConnect();

    // Get current user from session
    const session = await getServerSession(authOptions);
    if (!session || !session.user || !session.user.id) {
      return { success: false, error: "User not authenticated" };
    }

    const userId = session.user.id;

    // Use the existing function with the userId from session
    return GetConnectionsByUserId(userId);
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

// Update EndConnection to work with current user's session when connectionId isn't provided
export async function EndConnection(connectionId?: string): Promise<{
  success: boolean;
  data?: ConnectionType;
  error?: string;
}> {
  try {
    await dbConnect();

    // If no connectionId is provided, end the current user's active connection
    if (!connectionId) {
      const session = await getServerSession(authOptions);
      if (!session || !session.user || !session.user.id) {
        return { success: false, error: "User not authenticated" };
      }

      // Get the most recent active connection for this user
      const activeConnections = await Connection.find({
        user: session.user.id,
        active: true,
      })
        .sort({ lastSeen: -1 })
        .limit(1);

      if (activeConnections.length === 0) {
        return { success: false, error: "No active connection found" };
      }

      connectionId = activeConnections[0]._id.toString();
    } else if (!mongoose.Types.ObjectId.isValid(connectionId)) {
      return { success: false, error: "Invalid connection ID format" };
    }

    // Verify connection ownership if user is not an admin
    const session = await getServerSession(authOptions);
    if (session && session.user && session.user.role !== "ADMIN") {
      const connection = await Connection.findById(connectionId);
      if (!connection || connection.user.toString() !== session.user.id) {
        return {
          success: false,
          error: "Unauthorized to end this connection",
        };
      }
    }

    const updatedConnection = await Connection.findByIdAndUpdate(
      connectionId,
      {
        $set: {
          endedAt: new Date(),
          active: false,
        },
      },
      { new: true }
    );

    if (!updatedConnection) {
      return { success: false, error: "Connection not found" };
    }

    const connectionWithStringIds: ConnectionType = {
      ...updatedConnection.toObject(),
      _id: updatedConnection._id.toString(),
      user: updatedConnection.user.toString(),
    };

    return { success: true, data: connectionWithStringIds };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

// Enhance UpdateLastSeen to verify connection ownership
export async function UpdateLastSeen(connectionId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(connectionId)) {
      return { success: false, error: "Invalid connection ID format" };
    }

    // Verify connection ownership if user is not an admin
    const session = await getServerSession(authOptions);
    if (session && session.user && session.user.role !== "ADMIN") {
      const connection = await Connection.findById(connectionId);
      if (!connection || connection.user.toString() !== session.user.id) {
        return {
          success: false,
          error: "Unauthorized to update this connection",
        };
      }
    }

    await Connection.findByIdAndUpdate(
      connectionId,
      { $set: { lastSeen: new Date() } },
      { new: true }
    );

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

// Ends the current active connection for the logged-in user, using session jti if available
export async function EndCurrentConnection(): Promise<{
  success: boolean;
  data?: ConnectionType;
  error?: string;
}> {
  try {
    await dbConnect();

    const session = await getServerSession(authOptions);
    if (!session || !session.user || !session.user.id) {
      return { success: false, error: "User not authenticated" };
    }

    // Use sessionId from session to find the current connection
    const sessionId: string | undefined = (session as any).sessionId;

    let connection = null;
    if (sessionId) {
      connection = await Connection.findOne({
        user: session.user.id,
        sessionId: sessionId,
        active: true,
      });
    }

    // Fallback: find the most recent active connection for this user
    if (!connection) {
      connection = await Connection.findOne({
        user: session.user.id,
        active: true,
      }).sort({ lastSeen: -1 });
    }

    if (!connection) {
      return { success: false, error: "No active connection found" };
    }

    // End the connection
    connection.endedAt = new Date();
    connection.active = false;
    await connection.save();

    const connectionWithStringIds: ConnectionType = {
      ...connection.toObject(),
      _id: connection._id.toString(),
      user: connection.user.toString(),
    };

    // return { success: true, data: connectionWithStringIds };
    return { success: true, data: {} as ConnectionType };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
