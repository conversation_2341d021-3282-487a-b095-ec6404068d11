"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  CreditCard,
  Plus,
  RefreshCw,
  Star,
  Trash2,
  MoreVertical,
} from "lucide-react";
import { toast } from "sonner";
import Image from "next/image";
import {
  getSavedCards,
  deleteCard,
  setDefaultCard,
  SavedCard,
} from "@/actions/BillingActions";
import {
  getPaymentMethodDisplayName,
  getPaymentMethodIconPath,
} from "@/lib/billingUtils";
import AddCardDialog from "@/components/billing/AddCardDialog";

export default function SavedCards() {
  const [cards, setCards] = useState<SavedCard[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [cardToDelete, setCardToDelete] = useState<{
    id: string;
    brand: string;
    last4: string;
  } | null>(null);
  const [addCardDialogOpen, setAddCardDialogOpen] = useState(false);

  const getPaymentMethodIcon = (brand: string) => {
    const iconClass = "border border-gray-200 dark:border-gray-700 rounded";
    const iconPath = getPaymentMethodIconPath("card", brand);
    const altText = getPaymentMethodDisplayName("card", brand);

    return (
      <Image
        src={iconPath}
        alt={altText}
        width={32}
        height={20}
        className={iconClass}
      />
    );
  };

  const formatExpiry = (month: number, year: number) => {
    return `${month.toString().padStart(2, "0")}/${year.toString().slice(-2)}`;
  };

  const getFundingType = (funding: string) => {
    switch (funding) {
      case "credit":
        return "Credit";
      case "debit":
        return "Debit";
      case "prepaid":
        return "Prepaid";
      default:
        return "Card";
    }
  };

  const fetchCards = async () => {
    try {
      const result = await getSavedCards();
      if (!result.success) {
        toast.error(result.error);
        return;
      }
      setCards(result.data?.cards || []);
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch saved cards");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCards();
  }, []);

  const handleSetDefault = async (cardId: string) => {
    setActionLoading(cardId);
    try {
      const result = await setDefaultCard(cardId);
      if (!result.success) {
        toast.error(result.error);
        return;
      }

      toast.success("Default card updated successfully");
      // Refresh the cards list
      await fetchCards();
    } catch (error: any) {
      toast.error(error.message || "Failed to set default card");
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async (cardId: string) => {
    setActionLoading(cardId);
    try {
      const result = await deleteCard(cardId);
      if (!result.success) {
        toast.error(result.error);
        return;
      }

      toast.success("Card deleted successfully");
      setCards(cards.filter((card) => card.id !== cardId));
    } catch (error: any) {
      toast.error(error.message || "Failed to delete card");
    } finally {
      setActionLoading(null);
      setDeleteDialogOpen(false);
      setCardToDelete(null);
    }
  };

  const handleDeleteClick = (card: SavedCard) => {
    setCardToDelete({
      id: card.id,
      brand: card.brand,
      last4: card.last4,
    });
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (cardToDelete) {
      await handleDelete(cardToDelete.id);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setCardToDelete(null);
  };

  const handleAddCardSuccess = () => {
    setAddCardDialogOpen(false);
    fetchCards();
  };

  if (loading) {
    return (
      <div className="w-full">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem
            value="saved-cards"
            className="!border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg overflow-hidden"
          >
            <AccordionTrigger className="py-5 px-4 font-medium text-base bg-voxa-neutral-50 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700 transition-colors flex items-center">
              <div className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-voxa-teal-500 dark:text-voxa-teal-400" />
                <span>Saved Cards</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-4">
              <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                <div className="flex items-center justify-center gap-2 text-muted-foreground">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Loading cards...
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    );
  }

  return (
    <>
      <div className="w-full">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem
            value="saved-cards"
            className="!border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg overflow-hidden"
          >
            <AccordionTrigger className="py-5 px-4 font-medium text-base bg-voxa-neutral-50 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700 transition-colors flex items-center">
              <div className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-voxa-teal-500 dark:text-voxa-teal-400" />
                <span>Saved Cards</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-4">
              <div className="mt-4 mb-4 w-full">
                <div className="flex justify-between items-center mb-4">
                  <p className="text-sm text-muted-foreground">
                    Manage your saved payment methods
                  </p>
                  <Button
                    onClick={() => setAddCardDialogOpen(true)}
                    className="bg-voxa-teal-600 hover:bg-voxa-teal-700 text-white"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Card
                  </Button>
                </div>

                {/* Security note */}
                <div className="text-center dark:border-gray-800 mb-4">
                  <p className="text-xs text-muted-foreground flex items-center justify-center gap-1">
                    🔒{" "}
                    <span>
                      Your cards are only stored securely in Stripe. Echoparrot
                      never sees or stores your card details.
                    </span>
                  </p>
                </div>

                {cards.length === 0 ? (
                  <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                    <CreditCard className="w-10 h-10 mx-auto mb-4 text-voxa-neutral-300 dark:text-voxa-neutral-600" />
                    <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 mb-2">
                      No saved cards yet
                    </p>
                    <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 text-sm">
                      Add your first card to get started
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {cards.map((card) => (
                      <div
                        key={card.id}
                        className="relative bg-gradient-to-br from-voxa-neutral-50 to-voxa-neutral-100 dark:from-voxa-neutral-800 dark:to-voxa-neutral-900 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-xl p-4 hover:shadow-lg transition-all duration-300 hover:scale-105"
                      >
                        {/* Top Row - Brand Logo and Actions */}
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex-shrink-0">
                            {getPaymentMethodIcon(card.brand)}
                          </div>

                          <div className="flex items-center gap-2">
                            {/* Small Loading Indicator */}
                            {actionLoading === card.id && (
                              <div className="flex items-center gap-2 bg-voxa-teal-50 dark:bg-voxa-teal-900/20 border border-voxa-teal-200 dark:border-voxa-teal-800 rounded-full px-2 py-1">
                                <div className="animate-spin rounded-full h-3 w-3 border border-voxa-teal-200 dark:border-voxa-teal-800 border-t-voxa-teal-600 dark:border-t-voxa-teal-400"></div>
                                <span className="text-xs text-voxa-teal-600 dark:text-voxa-teal-400">
                                  Setting...
                                </span>
                              </div>
                            )}

                            {card.is_default && (
                              <Badge className="bg-voxa-teal-100 hover:bg-voxa-teal-200 text-voxa-teal-800 dark:bg-voxa-teal-900 dark:text-voxa-teal-100 text-xs">
                                <Star className="w-3 h-3 mr-1" />
                                Default
                              </Badge>
                            )}

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 hover:bg-voxa-neutral-100 dark:hover:bg-voxa-neutral-700"
                                  disabled={actionLoading === card.id}
                                >
                                  <MoreVertical className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-48">
                                {!card.is_default && (
                                  <DropdownMenuItem
                                    onClick={() => handleSetDefault(card.id)}
                                    disabled={actionLoading === card.id}
                                    className="cursor-pointer"
                                  >
                                    {actionLoading === card.id ? (
                                      <>
                                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-voxa-teal-600 mr-2"></div>
                                        Setting as default...
                                      </>
                                    ) : (
                                      <>
                                        <Star className="w-4 h-4 mr-2" />
                                        Set as default
                                      </>
                                    )}
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuItem
                                  onClick={() => handleDeleteClick(card)}
                                  disabled={actionLoading === card.id}
                                  className="cursor-pointer text-red-600 focus:text-red-600 dark:text-red-400 dark:focus:text-red-400"
                                >
                                  {actionLoading === card.id ? (
                                    <>
                                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600 mr-2"></div>
                                      Deleting...
                                    </>
                                  ) : (
                                    <>
                                      <Trash2 className="w-4 h-4 mr-2" />
                                      Delete card
                                    </>
                                  )}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>

                        {/* Card Number - Center */}
                        <div className="mb-4">
                          <div className="text-sm xl:text-lg font-mono tracking-wider text-voxa-neutral-900 dark:text-voxa-neutral-100">
                            •••• •••• •••• {card.last4}
                          </div>
                        </div>

                        {/* Bottom Row - Card Details */}
                        <div className="flex justify-between items-end">
                          <div className="flex-1">
                            <div className="text-xs uppercase tracking-wide mb-1 text-voxa-neutral-500 dark:text-voxa-neutral-400">
                              {getFundingType(card.funding)} Card
                            </div>
                            <div className="text-sm font-medium text-voxa-neutral-700 dark:text-voxa-neutral-300">
                              {getPaymentMethodDisplayName("card", card.brand)}
                            </div>
                          </div>

                          <div className="text-right">
                            <div className="text-xs uppercase tracking-wide mb-1 text-voxa-neutral-500 dark:text-voxa-neutral-400">
                              Expires
                            </div>
                            <div className="text-sm font-mono text-voxa-neutral-700 dark:text-voxa-neutral-300">
                              {formatExpiry(card.exp_month, card.exp_year)}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      {/* Add Card Dialog */}
      <AddCardDialog
        open={addCardDialogOpen}
        onClose={() => setAddCardDialogOpen(false)}
        onSuccess={handleAddCardSuccess}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Card</DialogTitle>
          </DialogHeader>
          <DialogDescription>
            Are you sure you want to delete this card ending in{" "}
            <strong>{cardToDelete?.last4}</strong>? This action cannot be undone
            and the card will be permanently removed from your account.
          </DialogDescription>
          <DialogFooter>
            <Button variant="outline" onClick={handleDeleteCancel}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={actionLoading === cardToDelete?.id}
            >
              {actionLoading === cardToDelete?.id ? (
                <>
                  Deleting...
                  <div className="ml-2 animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </>
              ) : (
                "Delete Card"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
