"use server";

import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { BusinessFilesS3 } from "@/lib/S3Client";
import {
  GetObjectCommand,
  HeadObjectCommand,
  PutObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import dbConnect from "@/lib/mongodb";
import Document from "@/models/Document";
import Entreprise from "@/models/Entreprise";
import {
  Entreprise as EntrepriseType,
  Document as DocumentType,
} from "@/types";
import { getEntrepriseByAdminID } from "@/actions/Entreprise";

export async function GetDocumentSignedURL(filename: string, fileType: string) {
  await dbConnect();

  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success) {
    return { success: false, error: entrepriseResponse.error };
  }

  const key = `Documents/${
    entrepriseResponse.entreprise._id
  }_${filename.replaceAll(" ", "_")}_${Date.now()}`;

  const command = new PutObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME!,
    Key: key,
    ContentType: fileType,
  });

  try {
    const url = await getSignedUrl(BusinessFilesS3, command, { expiresIn: 60 });
    return { success: true, data: { url: url, key: key } };
  } catch (error) {
    console.error("Error generating signed URL:", error);
    return { success: false, error: "Failed to generate signed URL" };
  }
}

export async function SaveDocumentDetails(
  documentData: {
    name: string;
    originalName: string;
    size: number;
    type: string;
    category: string;
  },
  url: string
) {
  try {
    await dbConnect();

    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const document = new Document({
      ...documentData,
      url: url,
      entreprise_id: entrepriseResponse.entreprise._id,
    });

    await document.save();

    // Add document to entreprise
    entrepriseResponse.entreprise.documents.push(document._id);
    await entrepriseResponse.entreprise.save();

    return { success: true, data: "Document uploaded successfully" };
  } catch (error: any) {
    console.error("Error saving document:", error);
    return {
      success: false,
      error: error.message || "Failed to save document",
    };
  }
}

export async function GetEntrepriseDocuments() {
  try {
    await dbConnect();

    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const entreprise = await Entreprise.findOne(
      {
        _id: entrepriseResponse.entreprise._id,
      },
      "documents"
    )
      .populate({
        path: "documents",
      })
      .lean<EntrepriseType>();

    if (!entreprise) return { success: false, error: "Business not found" };

    if (entreprise.documents) {
      entreprise.documents.forEach((doc: any) => {
        doc._id = doc._id.toString();
        doc.entreprise_id = entreprise._id.toString();
      });
    }

    return { success: true, data: entreprise.documents || [] };
  } catch (error: any) {
    console.error("Error fetching documents:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch documents",
    };
  }
}

export async function DeleteDocument(documentId: string) {
  try {
    await dbConnect();

    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const document = await Document.findOne({
      _id: documentId,
      entreprise_id: entrepriseResponse.entreprise._id,
    });

    if (!document) return { success: false, error: "Document not found" };

    // Delete from S3
    try {
      const key = extractKeyFromUrl(document.url);
      if (key) {
        const deleteCommand = new DeleteObjectCommand({
          Bucket: process.env.AWS_BUCKET_NAME!,
          Key: key,
        });
        await BusinessFilesS3.send(deleteCommand);
      }
    } catch (s3Error) {
      console.error("Error deleting from S3:", s3Error);
      // Continue with database deletion even if S3 deletion fails
    }

    // Remove from entreprise documents array
    entrepriseResponse.entreprise.documents =
      entrepriseResponse.entreprise.documents.filter(
        (doc: any) => doc.toString() !== documentId
      );
    await entrepriseResponse.entreprise.save();

    // Delete document from database
    await Document.findByIdAndDelete(documentId);

    return { success: true, data: "Document deleted successfully" };
  } catch (error: any) {
    console.error("Error deleting document:", error);
    return {
      success: false,
      error: error.message || "Failed to delete document",
    };
  }
}

function extractKeyFromUrl(url: string) {
  try {
    const urlObj = new URL(url);
    return urlObj.pathname.slice(1);
  } catch (error) {
    console.error("Error extracting key from URL:", error);
    return null;
  }
}

export async function GetDocumentDownloadUrl(documentId: string) {
  try {
    await dbConnect();

    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const document = await Document.findOne({
      _id: documentId,
      entreprise_id: entrepriseResponse.entreprise._id,
    });

    if (!document) return { success: false, error: "Document not found" };

    const key = extractKeyFromUrl(document.url);
    if (!key) return { success: false, error: "Invalid document URL" };

    // Check if file exists
    try {
      await BusinessFilesS3.send(
        new HeadObjectCommand({
          Bucket: process.env.AWS_BUCKET_NAME!,
          Key: key,
        })
      );
    } catch (error: any) {
      if (
        error.name === "NotFound" ||
        error.$metadata?.httpStatusCode === 404
      ) {
        return { success: false, error: "File not found in storage" };
      }
      throw error;
    }

    // Generate download URL
    const command = new GetObjectCommand({
      Bucket: process.env.AWS_BUCKET_NAME!,
      Key: key,
      ResponseContentDisposition: `attachment; filename="${document.originalName}"`,
    });

    const downloadUrl = await getSignedUrl(BusinessFilesS3, command, {
      expiresIn: 3600,
    });

    return { success: true, data: downloadUrl };
  } catch (error: any) {
    console.error("Error generating download URL:", error);
    return {
      success: false,
      error: error.message || "Failed to generate download URL",
    };
  }
}
