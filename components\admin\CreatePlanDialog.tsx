"use client";

import type { CreatePlanData } from "@/actions/BillingActions";
import { getAllPlansForAdmin } from "@/actions/BillingActions";
import CustomButton from "@/components/CustomFormItems/Button";
import CustomInput from "@/components/CustomFormItems/Input";
import CustomSelect from "@/components/CustomFormItems/Select";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  deletePlanDraft,
  getPlanDraftById,
  getPlanDraftsSync,
  savePlanDraft,
  type PlanDraft,
} from "@/lib/planDrafts";
import type { Plan, PlanFeature } from "@/types";
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  Check,
  Edit,
  FileText,
  GripVertical,
  Info,
  Plus,
  Save,
  Trash2,
  X,
} from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface PlanFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (planData: CreatePlanData, planId?: string) => Promise<void>;
  isLoading?: boolean;
  // Edit mode props
  editPlan?: Plan | null;
  isEditMode?: boolean;
}

// Sortable Feature Row Component
interface SortableFeatureRowProps {
  feature: PlanFeature & { id: string };
  index: number;
  onRemove: (index: number) => void;
  onEdit: (index: number) => void;
  isBeingEdited?: boolean;
}

function SortableFeatureRow({
  feature,
  index,
  onRemove,
  onEdit,
  isBeingEdited = false,
}: SortableFeatureRowProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: feature.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? undefined : transition, // Disable transition during drag
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <tr
      ref={setNodeRef}
      style={style}
      className={`border-t border-voxa-neutral-200 dark:border-voxa-neutral-800 hover:bg-voxa-neutral-50 dark:hover:bg-voxa-neutral-800 transition-colors ${
        isDragging ? "bg-voxa-neutral-100 dark:bg-voxa-neutral-700" : ""
      } ${
        isBeingEdited
          ? "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700"
          : ""
      }`}
    >
      <td className="px-3 py-2">
        <div className="flex items-center gap-2">
          <button
            type="button"
            className="cursor-grab active:cursor-grabbing text-voxa-neutral-400 hover:text-voxa-neutral-600 dark:text-voxa-neutral-500 dark:hover:text-voxa-neutral-300"
            {...attributes}
            {...listeners}
          >
            <GripVertical className="w-4 h-4" />
          </button>
        </div>
      </td>
      <td className="px-3 py-2 text-center">
        <span
          className={`text-lg font-bold ${
            feature.included
              ? "text-green-600 dark:text-green-400"
              : "text-red-600 dark:text-red-400"
          }`}
        >
          {feature.included ? "✓" : "✗"}
        </span>
      </td>
      <td className="px-3 py-2">
        <span className="font-medium text-sm">{feature.key}</span>
      </td>
      <td className="px-3 py-2">
        <span className="text-sm">{feature.value || "—"}</span>
      </td>
      <td className="px-3 py-2">
        <span className="text-sm text-muted-foreground">
          {feature.description}
        </span>
      </td>
      <td className="px-3 py-2">
        {feature.note ? (
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Info className="w-3 h-3" />
            <span>{feature.note}</span>
          </div>
        ) : (
          <span className="text-sm text-muted-foreground">—</span>
        )}
      </td>
      <td className="px-3 py-2 text-center">
        <div className="flex items-center gap-1">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => onEdit(index)}
            disabled={isBeingEdited}
            className={`h-7 w-7 p-0 ${
              isBeingEdited
                ? "text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/40"
                : "text-blue-500 hover:text-blue-700"
            }`}
          >
            <Edit className="w-3 h-3" />
          </Button>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => onRemove(index)}
            className="text-red-500 hover:text-red-700 h-7 w-7 p-0"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      </td>
    </tr>
  );
}

export default function CreatePlanDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
  editPlan = null,
  isEditMode = false,
}: PlanFormProps) {
  const [formData, setFormData] = useState<CreatePlanData>({
    name: "",
    description: "",
    billing_options: {
      monthly: {
        original_price: 0,
        current_price: 0,
      },
      yearly: {
        original_price: 0,
        current_price: 0,
      },
      weekly: {
        original_price: 0,
        current_price: 0,
      },
      daily: {
        original_price: 0,
        current_price: 0,
      },
    },
    currency: "eur",
    features: [],
    visible: true,
    mostPopular: false,
    sort_order: 0,
  });

  const [originalPriceInputs, setOriginalPriceInputs] = useState<
    Record<string, string>
  >({
    monthly: "",
    yearly: "",
    weekly: "",
    daily: "",
  });
  const [currentPriceInputs, setCurrentPriceInputs] = useState<
    Record<string, string>
  >({
    monthly: "",
    yearly: "",
    weekly: "",
    daily: "",
  });
  const [sortOrderInput, setSortOrderInput] = useState<string>("");
  const [activeBillingTab, setActiveBillingTab] = useState<string>("monthly");

  // Draft-related state
  const [drafts, setDrafts] = useState<PlanDraft[]>([]);
  const [plans, setPlans] = useState<Plan[]>([]);
  const [selectedDraftOption, setSelectedDraftOption] = useState<string>("new");
  const [currentDraftId, setCurrentDraftId] = useState<string | null>(null);

  const [newFeature, setNewFeature] = useState<PlanFeature>({
    key: "",
    value: "",
    description: "",
    included: true,
    note: "",
  });

  // Edit feature state
  const [editingFeatureIndex, setEditingFeatureIndex] = useState<number | null>(
    null
  );
  const [isFeatureEditMode, setIsFeatureEditMode] = useState(false);

  // Ref for smooth scrolling to feature form
  const featureFormRef = useRef<HTMLDivElement>(null);

  // DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require dragging 8px before activating
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load drafts and plans when dialog opens
  useEffect(() => {
    if (open) {
      const existingDrafts = getPlanDraftsSync();
      setDrafts(existingDrafts);

      // Load existing plans
      const loadPlans = async () => {
        try {
          const response = await getAllPlansForAdmin();
          if (response.success) {
            setPlans(response.data || []);
          }
        } catch (error) {
          console.error("Failed to load plans:", error);
        }
      };

      loadPlans();
    }
  }, [open]);

  // Initialize form for edit mode
  useEffect(() => {
    if (open && isEditMode && editPlan) {
      const featuresWithIds = editPlan.features?.map((feature, index) => ({
        ...feature,
        id: (feature as any).id || `edit-feature-${index}-${Date.now()}`,
      }));

      setFormData({
        name: editPlan.name || "",
        description: editPlan.description || "",
        billing_options: editPlan.billing_options || {
          monthly: { original_price: 0, current_price: 0 },
          yearly: { original_price: 0, current_price: 0 },
          weekly: { original_price: 0, current_price: 0 },
          daily: { original_price: 0, current_price: 0 },
        },
        currency: editPlan.currency || "eur",
        features: featuresWithIds || [],
        visible: editPlan.visible !== undefined ? editPlan.visible : true,
        mostPopular: editPlan.mostPopular || false,
        sort_order: editPlan.sort_order !== undefined ? editPlan.sort_order : 0,
      });

      // Set price inputs for each billing period
      const newOriginalInputs: Record<string, string> = {};
      const newCurrentInputs: Record<string, string> = {};

      Object.entries(editPlan.billing_options || {}).forEach(
        ([period, data]) => {
          if (data) {
            newOriginalInputs[period] = data.original_price?.toString() || "";
            newCurrentInputs[period] = data.current_price?.toString() || "";
          }
        }
      );

      setOriginalPriceInputs(newOriginalInputs);
      setCurrentPriceInputs(newCurrentInputs);
      setSortOrderInput(editPlan.sort_order?.toString() || "");

      // Reset draft-related state when editing an existing plan
      setSelectedDraftOption("new");
      setCurrentDraftId(null);
    }
  }, [open, isEditMode, editPlan]);

  // Reset form when dialog closes
  useEffect(() => {
    if (!open) {
      resetForm();
      setSelectedDraftOption("new");
      setCurrentDraftId(null);
      setActiveBillingTab("monthly");
    }
  }, [open]);

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      billing_options: {
        monthly: {
          original_price: 0,
          current_price: 0,
        },
        yearly: {
          original_price: 0,
          current_price: 0,
        },
        weekly: {
          original_price: 0,
          current_price: 0,
        },
        daily: {
          original_price: 0,
          current_price: 0,
        },
      },
      currency: "eur",
      features: [],
      visible: true,
      mostPopular: false,
      sort_order: 0,
    });
    setOriginalPriceInputs({
      monthly: "",
      yearly: "",
      weekly: "",
      daily: "",
    });
    setCurrentPriceInputs({
      monthly: "",
      yearly: "",
      weekly: "",
      daily: "",
    });
    setSortOrderInput("");
    setActiveBillingTab("monthly");
    setNewFeature({
      key: "",
      value: "",
      description: "",
      included: true,
      note: "",
    });
    setEditingFeatureIndex(null);
    setIsFeatureEditMode(false);
  };

  const loadDraft = (draftId: string) => {
    const draft = getPlanDraftById(draftId);
    if (draft) {
      const featuresWithIds = draft.features.map((feature, index) => ({
        ...feature,
        id: (feature as any).id || `draft-feature-${index}-${Date.now()}`,
      }));

      // Handle both old and new draft formats
      const billingOptions = (draft as any).billing_options || {
        monthly: {
          original_price: (draft as any).original_price || 0,
          current_price: (draft as any).current_price || 0,
        },
        yearly: { original_price: 0, current_price: 0 },
        weekly: { original_price: 0, current_price: 0 },
        daily: { original_price: 0, current_price: 0 },
      };

      setFormData({
        name: draft.name,
        description: draft.description,
        billing_options: billingOptions,
        currency: draft.currency,
        features: featuresWithIds,
        visible: draft.visible,
        mostPopular: draft.mostPopular || false,
        sort_order: draft.sort_order,
      });

      // Set price inputs for each billing period
      const newOriginalInputs: Record<string, string> = {};
      const newCurrentInputs: Record<string, string> = {};

      Object.entries(billingOptions).forEach(([period, data]) => {
        if (
          data &&
          typeof data === "object" &&
          "original_price" in data &&
          "current_price" in data
        ) {
          newOriginalInputs[period] =
            (data as any).original_price?.toString() || "";
          newCurrentInputs[period] =
            (data as any).current_price?.toString() || "";
        }
      });

      setOriginalPriceInputs(newOriginalInputs);
      setCurrentPriceInputs(newCurrentInputs);
      setSortOrderInput(draft.sort_order.toString());
      setCurrentDraftId(draftId);
      toast.success("Draft loaded successfully");
    } else {
      toast.error("Draft not found");
    }
  };

  const loadPlan = (planId: string) => {
    const plan = plans.find((p) => p._id === planId);
    if (plan) {
      const featuresWithIds = plan.features?.map((feature, index) => ({
        ...feature,
        id: (feature as any).id || `plan-feature-${index}-${Date.now()}`,
      }));

      // Handle both old and new plan formats
      const billingOptions = plan.billing_options || {
        monthly: {
          original_price: (plan as any).original_price || 0,
          current_price: (plan as any).current_price || 0,
        },
        yearly: { original_price: 0, current_price: 0 },
        weekly: { original_price: 0, current_price: 0 },
        daily: { original_price: 0, current_price: 0 },
      };

      setFormData({
        name: `${plan.name} (Copy)`,
        description: plan.description || "",
        billing_options: billingOptions || "",
        currency: plan.currency || "eur",
        features: featuresWithIds || [],
        visible: true, // New copies should be visible by default
        mostPopular: false, // New copies should not be marked as most popular by default
        sort_order: plan.sort_order !== undefined ? plan.sort_order : 0,
      });

      // Set price inputs for each billing period
      const newOriginalInputs: Record<string, string> = {};
      const newCurrentInputs: Record<string, string> = {};

      Object.entries(billingOptions).forEach(([period, data]) => {
        if (
          data &&
          typeof data === "object" &&
          "original_price" in data &&
          "current_price" in data
        ) {
          newOriginalInputs[period] =
            (data as any).original_price?.toString() || "";
          newCurrentInputs[period] =
            (data as any).current_price?.toString() || "";
        }
      });

      setOriginalPriceInputs(newOriginalInputs);
      setCurrentPriceInputs(newCurrentInputs);
      setSortOrderInput(plan.sort_order?.toString() || "");
      setCurrentDraftId(null); // This is not a draft
      toast.success("Plan copied successfully");
    } else {
      toast.error("Plan not found");
    }
  };

  const handleDraftOptionChange = (value: string) => {
    setSelectedDraftOption(value);
    if (value === "new") {
      resetForm();
      setCurrentDraftId(null);
      setActiveBillingTab("monthly");
    } else if (value.startsWith("draft_")) {
      const draftId = value.replace("draft_", "");
      loadDraft(draftId);
    } else if (value.startsWith("plan_")) {
      const planId = value.replace("plan_", "");
      loadPlan(planId);
    }
  };

  const handleSaveAsDraft = () => {
    if (!formData.name) {
      toast.error("Please enter a plan name before saving as draft");
      return;
    }

    const result = savePlanDraft(formData, currentDraftId || undefined);
    if (result.success) {
      toast.success(
        `Draft ${currentDraftId ? "updated" : "saved"} successfully`
      );
      setCurrentDraftId(result.data._id);
      // Refresh drafts list
      const updatedDrafts = getPlanDraftsSync();
      setDrafts(updatedDrafts);
      // Update selected option if we just created a new draft
      if (!currentDraftId) {
        setSelectedDraftOption(`draft_${result.data._id}`);
      }
    } else {
      toast.error(result.error || "Failed to save draft");
    }
  };

  const handleDeleteDraft = (draftId: string) => {
    const result = deletePlanDraft(draftId);
    if (result.success) {
      toast.success("Draft deleted successfully");
      // Refresh drafts list
      const updatedDrafts = getPlanDraftsSync();
      setDrafts(updatedDrafts);
      // Reset form if we deleted the currently loaded draft
      if (currentDraftId === draftId) {
        resetForm();
        setSelectedDraftOption("new");
        setCurrentDraftId(null);
      }
    } else {
      toast.error(result.error || "Failed to delete draft");
    }
  };

  const canDeleteSelected = () => {
    return selectedDraftOption.startsWith("draft_");
  };

  const hasPricingConfigured = (period: string) => {
    const options =
      formData.billing_options[period as keyof typeof formData.billing_options];
    return options && (options.current_price > 0 || options.original_price > 0);
  };

  const getBillingPeriodLabel = (period: string) => {
    const labels: Record<string, string> = {
      monthly: "Monthly",
      yearly: "Yearly",
      weekly: "Weekly",
      daily: "Daily",
    };
    return labels[period] || period;
  };

  const handleInputChange = (field: keyof CreatePlanData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFeatureChange = (field: keyof PlanFeature, value: any) => {
    setNewFeature((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const addFeature = () => {
    if (!newFeature.key || !newFeature.description) {
      toast.error("Please fill in Feature Key and Description");
      return;
    }

    if (isFeatureEditMode && editingFeatureIndex !== null) {
      // Update existing feature
      setFormData((prev) => {
        const updatedFeatures = [...prev.features];
        const existingFeature = updatedFeatures[editingFeatureIndex];
        updatedFeatures[editingFeatureIndex] = {
          ...newFeature,
          ...(existingFeature &&
          typeof existingFeature === "object" &&
          "id" in existingFeature
            ? { id: (existingFeature as any).id }
            : {}),
        };
        return {
          ...prev,
          features: updatedFeatures,
        };
      });

      // Reset edit mode
      setIsFeatureEditMode(false);
      setEditingFeatureIndex(null);
      toast.success("Feature updated successfully");
    } else {
      // Add new feature
      const featureWithId = {
        ...newFeature,
        id: `feature-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      setFormData((prev) => ({
        ...prev,
        features: [...prev.features, featureWithId],
      }));

      toast.success("Feature added successfully");
    }

    // Reset form
    setNewFeature({
      key: "",
      value: "",
      description: "",
      included: true,
      note: "",
    });
  };

  const editFeature = (index: number) => {
    const feature = formData.features[index];
    setNewFeature({
      key: feature.key,
      value: feature.value || "",
      description: feature.description,
      included: feature.included,
      note: feature.note || "",
    });
    setEditingFeatureIndex(index);
    setIsFeatureEditMode(true);

    // Smooth scroll to the feature form
    setTimeout(() => {
      featureFormRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }, 100); // Small delay to ensure state updates are applied
  };

  const cancelEdit = () => {
    setNewFeature({
      key: "",
      value: "",
      description: "",
      included: true,
      note: "",
    });
    setEditingFeatureIndex(null);
    setIsFeatureEditMode(false);
  };

  const removeFeature = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setFormData((prev) => {
        const featuresWithIds = prev.features.map((feature, index) => ({
          ...feature,
          id: (feature as any).id || `feature-${index}-${Date.now()}`,
        }));

        const oldIndex = featuresWithIds.findIndex((f) => f.id === active.id);
        const newIndex = featuresWithIds.findIndex((f) => f.id === over.id);

        return {
          ...prev,
          features: arrayMove(featuresWithIds, oldIndex, newIndex),
        };
      });
    }
  };

  // Handle form submission and automatic draft cleanup
  // If a plan is successfully created from a draft, the draft will be automatically deleted
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.description) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Validate that at least one billing period has a price > 0
    const hasValidPrice = Object.values(formData.billing_options).some(
      (options) => options && options.current_price > 0
    );

    if (!hasValidPrice) {
      toast.error(
        "At least one billing period must have a current price greater than 0"
      );
      return;
    }

    if (formData.features.length === 0) {
      toast.error("Please add at least one feature");
      return;
    }

    try {
      if (isEditMode && editPlan) {
        await onSubmit(formData, editPlan._id);
      } else {
        await onSubmit(formData);
      }

      // If we successfully created a plan and we're working from a draft, delete the draft
      // Only apply draft cleanup logic in create mode
      if (!isEditMode && currentDraftId) {
        const deleteResult = deletePlanDraft(currentDraftId);
        if (deleteResult.success) {
          toast.success("Plan created and draft removed successfully!");
          // Refresh drafts list
          const updatedDrafts = getPlanDraftsSync();
          setDrafts(updatedDrafts);
          // Reset draft-related state
          setCurrentDraftId(null);
          setSelectedDraftOption("new");
        } else {
          // Plan was created but draft deletion failed - not critical
          console.warn(
            "Failed to delete draft after plan creation:",
            deleteResult.error
          );
        }
      }
    } catch (error) {
      // onSubmit will handle error display, so we don't need to do anything here
      throw error;
    }
  };

  const currencyOptions = [
    { value: "eur", label: "EUR (€)" },
    { value: "usd", label: "USD ($)" },
  ];

  const draftOptions = [
    { value: "new", label: "🚀 Create from scratch" },
    ...(plans.length > 0
      ? plans.map((plan) => ({
          value: `plan_${plan._id}`,
          label: `📋 Copy from: ${plan.name}`,
        }))
      : []),
    ...(drafts.length > 0
      ? drafts.map((draft) => ({
          value: `draft_${draft._id}`,
          label: `📝 Continue draft: ${draft.name || "Untitled"} (${new Date(
            draft.updated_at
          ).toLocaleDateString()})`,
        }))
      : []),
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Edit Plan" : "Create New Plan"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 mt-4">
          {/* Draft/Plan Selection - only show in create mode */}
          {!isEditMode && (drafts.length > 0 || plans.length > 0) && (
            <div className="space-y-3 p-4 border border-dashed rounded-lg bg-muted/20">
              <div className="flex items-center gap-2">
                <FileText className="w-4 h-4 text-muted-foreground" />
                <Label className="text-sm font-medium">Start from</Label>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex-1">
                  <CustomSelect
                    value={selectedDraftOption}
                    onValueChange={handleDraftOptionChange}
                    items={draftOptions}
                    placeholder="Select option"
                  />
                </div>
                {canDeleteSelected() && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const draftId = selectedDraftOption.replace("draft_", "");
                      handleDeleteDraft(draftId);
                    }}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CustomInput
              props={{
                label: "Plan Name",
                value: formData.name,
                onChange: (e) => handleInputChange("name", e.target.value),
                placeholder: "e.g. Professional Plan",
                required: true,
              }}
            />
            <CustomInput
              props={{
                label: "Sort Order",
                type: "number",
                value: sortOrderInput,
                onChange: (e) => {
                  setSortOrderInput(e.target.value);
                  handleInputChange(
                    "sort_order",
                    parseInt(e.target.value) || 0
                  );
                },
                placeholder: "0",
              }}
            />
          </div>

          <CustomInput
            props={{
              label: "Description",
              value: formData.description,
              onChange: (e) => handleInputChange("description", e.target.value),
              placeholder: "Describe what this plan offers...",
              required: true,
            }}
          />

          {/* Billing Options */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold text-foreground/80">
                Billing Options
              </h3>
              <div className="grid w-full max-w-xs items-center gap-1.5 ml-auto">
                <Label className="text-sm font-medium">Currency</Label>
                <CustomSelect
                  value={formData.currency}
                  onValueChange={(value) =>
                    handleInputChange("currency", value)
                  }
                  items={currencyOptions}
                  placeholder="Select currency"
                />
              </div>
            </div>

            <Tabs
              value={activeBillingTab}
              onValueChange={setActiveBillingTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-4">
                {Object.keys(formData.billing_options).map((period) => (
                  <TabsTrigger key={period} value={period} className="relative">
                    <div className="flex items-center gap-2">
                      <span>{getBillingPeriodLabel(period)}</span>
                      {hasPricingConfigured(period) ? (
                        <Check className="w-3 h-3 text-green-500" />
                      ) : (
                        <X className="w-3 h-3 text-gray-400" />
                      )}
                    </div>
                  </TabsTrigger>
                ))}
              </TabsList>

              {Object.entries(formData.billing_options).map(([period]) => (
                <TabsContent key={period} value={period} className="mt-4">
                  <div className="border border-dashed border-voxa-neutral-300 dark:border-voxa-neutral-600 rounded-lg p-4 bg-voxa-neutral-50/50 dark:bg-voxa-neutral-800/50">
                    <div className="flex items-center gap-2 mb-4">
                      <h4 className="font-medium text-base capitalize">
                        {period} Billing Configuration
                      </h4>
                      {hasPricingConfigured(period) && (
                        <div className="flex items-center gap-1 text-green-600 text-sm">
                          <Check className="w-4 h-4" />
                          <span>Configured</span>
                        </div>
                      )}
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <CustomInput
                        props={{
                          label: "Original Price",
                          type: "number",
                          value: originalPriceInputs[period] || "",
                          onChange: (e) => {
                            const newInputs = { ...originalPriceInputs };
                            newInputs[period] = e.target.value;
                            setOriginalPriceInputs(newInputs);

                            const newBillingOptions = {
                              ...formData.billing_options,
                            };
                            if (
                              !newBillingOptions[
                                period as keyof typeof newBillingOptions
                              ]
                            ) {
                              newBillingOptions[
                                period as keyof typeof newBillingOptions
                              ] = {
                                original_price: 0,
                                current_price: 0,
                              };
                            }
                            newBillingOptions[
                              period as keyof typeof newBillingOptions
                            ]!.original_price = parseFloat(e.target.value) || 0;
                            handleInputChange(
                              "billing_options",
                              newBillingOptions
                            );
                          },
                          placeholder: "99.99",
                          inputMode: "decimal",
                        }}
                      />
                      <CustomInput
                        props={{
                          label: "Current Price",
                          type: "number",
                          value: currentPriceInputs[period] || "",
                          onChange: (e) => {
                            const newInputs = { ...currentPriceInputs };
                            newInputs[period] = e.target.value;
                            setCurrentPriceInputs(newInputs);

                            const newBillingOptions = {
                              ...formData.billing_options,
                            };
                            if (
                              !newBillingOptions[
                                period as keyof typeof newBillingOptions
                              ]
                            ) {
                              newBillingOptions[
                                period as keyof typeof newBillingOptions
                              ] = {
                                original_price: 0,
                                current_price: 0,
                              };
                            }
                            newBillingOptions[
                              period as keyof typeof newBillingOptions
                            ]!.current_price = parseFloat(e.target.value) || 0;
                            handleInputChange(
                              "billing_options",
                              newBillingOptions
                            );
                          },
                          placeholder: "79.99",
                          inputMode: "decimal",
                        }}
                      />
                    </div>
                    <div className="mt-3 text-sm text-muted-foreground">
                      {hasPricingConfigured(period) ? (
                        <div className="flex items-center gap-2 text-green-600">
                          <Check className="w-4 h-4" />
                          <span>
                            This billing period is configured and will be
                            available to customers
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2 text-gray-500">
                          <X className="w-4 h-4" />
                          <span>
                            Leave empty if you don{"'"}t want to offer {period}{" "}
                            billing
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </div>

          {/* Features Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground/80">
              Plan Features
            </h3>

            {/* Existing Features Table */}
            {formData.features.length > 0 && (
              <div className="border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg shadow-sm overflow-hidden">
                <div className="bg-voxa-neutral-50 dark:bg-voxa-neutral-900 border-b border-voxa-neutral-200 dark:border-voxa-neutral-700 px-4 py-2">
                  <h4 className="font-medium text-sm">Current Features</h4>
                </div>
                <div className="w-0 min-w-full overflow-x-auto">
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                  >
                    <SortableContext
                      items={formData.features.map(
                        (feature, index) =>
                          (feature as any).id || `feature-${index}-fallback`
                      )}
                      strategy={verticalListSortingStrategy}
                    >
                      <table className="w-full min-w-[700px]">
                        <thead className="bg-voxa-neutral-100 dark:bg-voxa-neutral-800">
                          <tr>
                            <th className="px-3 py-2 text-left text-xs font-medium w-12">
                              Order
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium">
                              Included
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium">
                              Feature
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium">
                              Value
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium">
                              Description
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium">
                              Note
                            </th>
                            <th className="px-3 py-2 text-center text-xs font-medium">
                              Actions
                            </th>
                          </tr>
                        </thead>

                        <tbody>
                          {formData.features.map((feature, index) => {
                            const featureWithId = {
                              ...feature,
                              id:
                                (feature as any).id ||
                                `feature-${index}-${Date.now()}`,
                            };
                            return (
                              <SortableFeatureRow
                                key={featureWithId.id}
                                feature={featureWithId}
                                index={index}
                                onRemove={removeFeature}
                                onEdit={editFeature}
                                isBeingEdited={editingFeatureIndex === index}
                              />
                            );
                          })}
                        </tbody>
                      </table>
                    </SortableContext>
                  </DndContext>
                </div>
              </div>
            )}

            {/* Add New Feature */}
            <div
              ref={featureFormRef}
              className="border border-dashed border-voxa-neutral-300 dark:border-voxa-neutral-600 rounded-lg p-4 bg-voxa-neutral-50/50 dark:bg-voxa-neutral-800/50"
            >
              <div className="flex items-center gap-2 mb-3">
                {isFeatureEditMode ? (
                  <>
                    <Edit className="w-4 h-4 text-blue-500" />
                    <h4 className="font-medium text-sm">Edit Feature</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={cancelEdit}
                      className="ml-auto text-gray-500 hover:text-gray-700"
                    >
                      Cancel
                    </Button>
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 text-voxa-teal-500" />
                    <h4 className="font-medium text-sm">Add New Feature</h4>
                  </>
                )}
              </div>
              <div className="space-y-3">
                {/* First row - Key and Value */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <CustomInput
                    props={{
                      label: "Feature Key",
                      value: newFeature.key,
                      onChange: (e) =>
                        handleFeatureChange("key", e.target.value),
                      placeholder: "calls",
                    }}
                  />
                  <CustomInput
                    props={{
                      label: "Value (Optional)",
                      value: newFeature.value || "",
                      onChange: (e) =>
                        handleFeatureChange("value", e.target.value),
                      placeholder: "1000, unlimited, etc.",
                    }}
                  />
                </div>

                {/* Second row - Description */}
                <div className="flex items-center gap-2">
                  <div className="flex-1">
                    <CustomInput
                      props={{
                        label: "Description",
                        value: newFeature.description,
                        onChange: (e) =>
                          handleFeatureChange("description", e.target.value),
                        placeholder:
                          "e.g. Voice selection included, 1000 calls per month",
                      }}
                    />
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="w-4 h-4 text-muted-foreground cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent side="right" className="max-w-xs">
                        <div className="text-xs">
                          <div className="font-semibold mb-1">
                            Template Variables:
                          </div>
                          <div>
                            <code className="bg-muted px-1 rounded">
                              $value
                            </code>{" "}
                            — feature value
                            <br />
                            <code className="bg-muted px-1 rounded">
                              $months
                            </code>{" "}
                            — number of months in billing period
                            <br />
                            <code className="bg-muted px-1 rounded">
                              $days
                            </code>{" "}
                            — number of days in billing period
                          </div>
                          <div className="mt-2">
                            Use syntax:{" "}
                            <code className="bg-muted px-1 rounded">{`{{ $value }} calls per month`}</code>
                          </div>
                          <div className="mt-2">
                            You can use arithmetic operations, e.g.{" "}
                            <code className="bg-muted px-1 rounded">{`{{ $value * $months }}`}</code>{" "}
                            or{" "}
                            <code className="bg-muted px-1 rounded">{`{{ $value / $days }}`}</code>
                          </div>
                          <div className="mt-1 text-muted-foreground">
                            Example:{" "}
                            <code className="bg-muted px-1 rounded">{`{{ $value * $months }} total calls in billing period`}</code>
                          </div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                {/* Third row - Note and controls */}
                <div className="flex flex-col sm:flex-row gap-3 items-end">
                  <div className="flex-1">
                    <CustomInput
                      props={{
                        label: "Note (Optional)",
                        value: newFeature.note || "",
                        onChange: (e) =>
                          handleFeatureChange("note", e.target.value),
                        placeholder: "Tooltip text",
                      }}
                    />
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="included"
                        checked={newFeature.included}
                        onCheckedChange={(checked) =>
                          handleFeatureChange("included", checked)
                        }
                      />
                      <Label
                        htmlFor="included"
                        className="text-sm whitespace-nowrap"
                      >
                        Included
                      </Label>
                    </div>
                    <Button
                      type="button"
                      onClick={addFeature}
                      variant="outline"
                      size="sm"
                      className={`whitespace-nowrap ${
                        isFeatureEditMode
                          ? "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/40"
                          : "bg-voxa-teal-50 dark:bg-voxa-teal-900/20 border-voxa-teal-200 dark:border-voxa-teal-700 text-voxa-teal-700 dark:text-voxa-teal-300 hover:bg-voxa-teal-100 dark:hover:bg-voxa-teal-900/40"
                      }`}
                    >
                      {isFeatureEditMode ? (
                        <>
                          <Save className="w-4 h-4 mr-1" />
                          Save Feature
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4 mr-1" />
                          Add Feature
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Plan Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground/80">
              Plan Settings
            </h3>
            <div className="w-full flex justify-between items-center gap-2 p-3 border border-voxa-neutral-200 dark:border-voxa-neutral-800 rounded-lg">
              <div className="flex items-center gap-2">
                <Label htmlFor="plan-visible" className="font-medium text-base">
                  Visible on billing page
                </Label>
                <p className="text-sm text-voxa-neutral-400 dark:text-voxa-neutral-500">
                  {formData.visible ? "Shown" : "Hidden"}
                </p>
              </div>
              <div className="flex items-center ms-auto">
                <Switch
                  id="plan-visible"
                  checked={formData.visible}
                  onCheckedChange={(checked) =>
                    handleInputChange("visible", checked)
                  }
                  className="data-[state=unchecked]:bg-voxa-neutral-200 dark:data-[state=checked]:bg-voxa-teal-500 dark:data-[state=unchecked]:bg-voxa-neutral-800"
                />
              </div>
            </div>
            <div className="w-full flex justify-between items-center gap-2 p-3 border border-voxa-neutral-200 dark:border-voxa-neutral-800 rounded-lg">
              <div className="flex items-center gap-2">
                <Label
                  htmlFor="plan-most-popular"
                  className="font-medium text-base"
                >
                  Mark as most popular
                </Label>
                <p className="text-sm text-voxa-neutral-400 dark:text-voxa-neutral-500">
                  {formData.mostPopular ? "Highlighted" : "Normal"}
                </p>
              </div>
              <div className="flex items-center ms-auto">
                <Switch
                  id="plan-most-popular"
                  checked={formData.mostPopular}
                  onCheckedChange={(checked) =>
                    handleInputChange("mostPopular", checked)
                  }
                  className="data-[state=unchecked]:bg-voxa-neutral-200 dark:data-[state=checked]:bg-voxa-teal-500 dark:data-[state=unchecked]:bg-voxa-neutral-800"
                />
              </div>
            </div>
          </div>
        </form>

        <DialogFooter className="mt-8">
          <div className="flex flex-col sm:flex-row gap-3 w-full">
            {!isEditMode && (
              <Button
                type="button"
                variant="outline"
                onClick={handleSaveAsDraft}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <Save className="w-4 h-4" />
                {currentDraftId ? "Update Draft" : "Save as Draft"}
              </Button>
            )}
            <CustomButton
              props={{
                value: isLoading
                  ? isEditMode
                    ? "Updating..."
                    : "Creating..."
                  : isEditMode
                  ? "Update Plan"
                  : "Create Plan",
                onClick: handleSubmit,
                loading: isLoading,
                className:
                  "flex-1 text-voxa-neutral-50 bg-voxa-teal-600 hover:bg-voxa-teal-500",
              }}
            />
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
