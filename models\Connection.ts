import mongoose from "mongoose";

const ConnectionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  ip: String,
  userAgent: String,
  device: String, // e.g., "Desktop", "Mobile", "Tablet"
  browser: String, // optional if you parse user agent
  os: String, // e.g., "Windows", "macOS", "Android"
  location: String, // e.g., "Paris, France"
  sessionId: {
    type: String,
    unique: true, // Add unique constraint
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  endedAt: {
    type: Date, // filled when the session ends (logout, timeout, etc.)
  },
  lastSeen: {
    type: Date,
    default: Date.now, // defaults to createdAt; should be updated on activity
  },
  active: {
    type: Boolean,
    default: true,
  },
});

const Connection =
  mongoose.models.Connection || mongoose.model("Connection", ConnectionSchema);
export default Connection;
