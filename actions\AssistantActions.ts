"use server";

import dbConnect from "@/lib/mongodb";
import Assistant from "@/models/Assistant";
import Entreprise from "@/models/Entreprise";
import { getEntrepriseByAdminID } from "./Entreprise";
import { Assistant as AssistantType } from "@/types";

export async function CreateAssistant(
  name: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const EntrepriseID = entrepriseResponse.entreprise._id;

    await dbConnect();

    const newAssistant = await Assistant.create({ name: name });
    if (!newAssistant) {
      return { success: false, error: "Failed to create assistant" };
    }

    const entreprise = await Entreprise.findById(EntrepriseID);
    if (!entreprise) {
      return { success: false, error: "Entreprise not found" };
    }

    if (!entreprise.assistants) {
      entreprise.assistants = [];
    }

    entreprise.assistants.push(newAssistant._id);
    await entreprise.save();

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

//don't touch please, whenever there's an issue with getting the data (Conversations issue) from the assistants page, just remove the comments in the function below
export async function getEntrepriseAssistants(): Promise<{
  success: boolean;
  error?: string;
  assistants?: any[];
}> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }
    /*
        const conversation = await Conversation.create({
            CallDuration: "10",
            CallType: "INCOMING",
            CallUUID: "1234",
            ClientID: "1234",
            ConversationID: "1234",
            Date: "2021-10-10",
            Direction: "INCOMING",
            From: "1234",
            To: "1234",
            phone: "1234",
            script: "1234",
            status: "completed",
            entreprise: entrepriseResponse.entreprise._id
        })
        */
    const entrepriseID = entrepriseResponse.entreprise._id;
    await dbConnect();

    const entreprise = (await Entreprise.findById(entrepriseID)
      .populate({
        path: "assistants",
        match: { status: "ACTIVE" },
        populate: {
          path: "numbers",
          populate: {
            path: "goals",
            match: { goal_visibility: "VISIBLE" },
            populate: [
              { path: "conversations", select: "duration" },
              { path: "tags" },
            ],
          },
        },
      })
      .lean()) as any;

    if (!entreprise) {
      return { success: false, error: "Entreprise not found" };
    }

    if (!entreprise.assistants || entreprise.assistants.length === 0) {
      return { success: false, error: "No assistants found" };
    }

    const formattedAssistants = entreprise.assistants.map((assistant: any) => {
      // Function to format seconds into hh:mm:ss
      const formatDuration = (totalSeconds: number) => {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        return hours > 0
          ? `${hours}h ${minutes}min ${seconds}s`
          : minutes > 0
          ? `${minutes}min ${seconds}s`
          : `${seconds}s`;
      };

      // Calculate assistant-level total duration by summing all conversation.duration
      const totalAssistantDuration = (assistant.numbers || []).reduce(
        (accNum: number, num: any) => {
          return (
            accNum +
            (num.goals || []).reduce((accGoal: number, goal: any) => {
              return (
                accGoal +
                (goal.conversations || []).reduce(
                  (accConv: number, conv: any) => {
                    return accConv + (Number(conv.duration) || 0);
                  },
                  0
                )
              );
            }, 0)
          );
        },
        0
      );

      return {
        _id: assistant._id.toString(),
        name: assistant.name,
        status: assistant.status,
        time: formatDuration(totalAssistantDuration),
        numbers: (assistant.numbers || []).map((num: any) => {
          const totalNumberDuration = (num.goals || []).reduce(
            (accGoal: number, goal: any) => {
              return (
                accGoal +
                (goal.conversations || []).reduce(
                  (accConv: number, conv: any) => {
                    return accConv + (Number(conv.duration) || 0);
                  },
                  0
                )
              );
            },
            0
          );

          return {
            _id: num._id.toString(),
            number: num.number,
            country: num.country,
            status: num.status,
            is_whatsapp: num.is_whatsapp,
            is_sms: num.is_sms,
            is_calls: num.is_calls,
            assistant: num.assistant ? num.assistant.toString() : null,
            time: formatDuration(totalNumberDuration),
            goals: (num.goals || [])
              .map((goal: any) => ({
                _id: goal._id.toString(),
                name: goal.name,
                country: goal.country,
                goalType: goal.goalType,
                status: goal.status,
                template_type: goal.goal_template_type,
                is_debug: goal.is_debug,
              }))
              .sort((a: any) => (a.goalType === "INCOMING" ? -1 : 1)),
          };
        }),
      };
    });

    return { success: true, assistants: formattedAssistants };
  } catch (err: any) {
    console.error("Error fetching assistants:", err);
    return { success: false, error: err.message };
  }
}

export type AssistantWithDurations = {
  _id: string;
  name: string;
  status: string;
  time: number;
  numbers: {
    _id: string;
    number: string;
    country: string;
    status: string;
    is_whatsapp: boolean;
    is_sms: boolean;
    is_calls: boolean;
    assistant: string | null;
    time: number;
  }[];
};

export async function getAssistantsWithDurations(): Promise<{
  success: boolean;
  data?: AssistantWithDurations[];
  error?: string;
}> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const entrepriseID = entrepriseResponse.entreprise._id;
    await dbConnect();

    const entreprise = await Entreprise.findById(entrepriseID);
    if (
      !entreprise ||
      !entreprise.assistants ||
      entreprise.assistants.length === 0
    ) {
      return { success: true, data: [] };
    }

    const data = await Assistant.aggregate([
      { $match: { _id: { $in: entreprise.assistants }, status: "ACTIVE" } },
      {
        $lookup: {
          from: "phones",
          localField: "numbers",
          foreignField: "_id",
          as: "numbers",
        },
      },
      { $unwind: { path: "$numbers", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "goals",
          localField: "numbers.goals",
          foreignField: "_id",
          as: "goals",
        },
      },
      { $unwind: { path: "$goals", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "conversations",
          localField: "goals.conversations",
          foreignField: "_id",
          as: "conversations",
        },
      },
      { $unwind: { path: "$conversations", preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          conversationDuration: {
            $toDouble: "$conversations.duration",
          },
        },
      },
      {
        $group: {
          _id: {
            assistantId: "$_id",
            numberId: "$numbers._id",
          },
          assistantName: { $first: "$name" },
          assistantStatus: { $first: "$status" },
          number: { $first: "$numbers.number" },
          numberCountry: { $first: "$numbers.country" },
          numberStatus: { $first: "$numbers.status" },
          is_whatsapp: { $first: "$numbers.is_whatsapp" },
          is_sms: { $first: "$numbers.is_sms" },
          is_calls: { $first: "$numbers.is_calls" },
          assistant: { $first: "$numbers.assistant" },
          totalNumberDuration: { $sum: "$conversationDuration" },
        },
      },
      {
        $group: {
          _id: "$_id.assistantId",
          name: { $first: "$assistantName" },
          status: { $first: "$assistantStatus" },
          numbers: {
            $push: {
              _id: "$_id.numberId",
              number: "$number",
              country: "$numberCountry",
              status: "$numberStatus",
              is_whatsapp: "$is_whatsapp",
              is_sms: "$is_sms",
              is_calls: "$is_calls",
              assistant: "$assistant",
              time: "$totalNumberDuration",
            },
          },
          time: { $sum: "$totalNumberDuration" },
        },
      },
      // Sort the numbers array by _id using $addFields and $sortArray (MongoDB 5.2+)
      {
        $addFields: {
          numbers: {
            $sortArray: {
              input: "$numbers",
              sortBy: { _id: 1 },
            },
          },
        },
      },
      // Add a $sort stage for stable ordering by name (ascending), then _id
      { $sort: { _id: 1 } },
    ]);

    // Convert all _id fields to string using forEach
    data.forEach((assistant: AssistantType) => {
      assistant._id = assistant._id?.toString();
      if (Array.isArray(assistant.numbers)) {
        assistant.numbers.forEach((num: any) => {
          num._id = num._id?.toString();
          if (num.assistant) {
            num.assistant = num.assistant.toString();
          }
        });
      }
    });


    return { success: true, data: data as AssistantWithDurations[] };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
