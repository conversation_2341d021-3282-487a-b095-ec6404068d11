"use client";

import {
  getPlanByCode,
  createSubscription,
  createSubscriptionWithCard,
} from "@/actions/BillingActions";
import { useSearchParams } from "next/navigation";
import { useEffect, useState, useMemo } from "react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import CustomButton from "@/components/CustomFormItems/Button";
import { Crown, Check } from "lucide-react";
import SubscriptionConfirmationDialog from "./SubscriptionConfirmationDialog";
import { Plan } from "@/types";
import { toast } from "sonner";
import { renderTemplate } from "@/lib/renderTemplate";
import { featurePeriodMap } from "@/lib/billingUtils";
import { useRouter } from "next/navigation";
import { mutate } from "swr";

export default function SpecialPlan() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const code = searchParams?.get("code");

  const [plan, setPlan] = useState<Plan | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedBillingPeriod, setSelectedBillingPeriod] =
    useState<string>("monthly");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!code) return;
    setLoading(true);
    getPlanByCode(code)
      .then((res) => {
        if (res.success && res.data) setPlan(res.data);
        else setPlan(null);
      })
      .finally(() => setLoading(false));
  }, [code]);

  const availablePeriods = useMemo(() => {
    if (!plan) return [];
    const periodOrder = ["daily", "weekly", "monthly", "yearly"];
    return Object.entries(plan.billing_options || {})
      .filter(([, opt]: any) => opt && opt.current_price > 0)
      .map(([period]) => period)
      .sort((a, b) => periodOrder.indexOf(a) - periodOrder.indexOf(b));
  }, [plan]);

  useEffect(() => {
    if (
      availablePeriods.length &&
      !availablePeriods.includes(selectedBillingPeriod)
    ) {
      setSelectedBillingPeriod(availablePeriods[0]);
    }
  }, [availablePeriods, selectedBillingPeriod]);

  if (!code || loading || !plan) return null;

  const billingOption =
    plan.billing_options?.[
      selectedBillingPeriod as keyof typeof plan.billing_options
    ];
  if (!billingOption || billingOption.current_price <= 0) return null;

  const handleSelect = () => setIsDialogOpen(true);
  const handleClose = () => setIsDialogOpen(false);

  const handleConfirmSubscription = async () => {
    if (!plan) return;
    setIsProcessing(true);
    try {
      const cardId = "default-card-id";
      const response = await createSubscriptionWithCard(
        undefined,
        code,
        selectedBillingPeriod as any,
        cardId
      );
      if (response.success) {
        toast.success("Subscription successful");
        if (response.data?.url) {
          window.location.href = response.data.url;
        } else {
          window.location.reload();
        }
      } else {
        toast.error(response.error || "Subscription failed");
      }
    } catch (err: any) {
      toast.error(err.message || "Subscription error");
    } finally {
      setIsProcessing(false);
      setIsDialogOpen(false);
    }
  };

  const handleSubscribeWithCard = async (cardId: string) => {
    if (!plan) return;
    setIsProcessing(true);
    try {
      const response = await createSubscriptionWithCard(
        undefined,
        code,
        selectedBillingPeriod as any,
        cardId
      );
      if (response.success) {
        toast.success("Subscription successful");

        // Remove the 'code' parameter from URL after successful subscription
        const currentParams = new URLSearchParams(searchParams?.toString());
        currentParams.delete("code");

        // Get current pathname
        const pathname = window.location.pathname;

        // Construct new URL - if no params left, just use pathname, otherwise include remaining params
        const newUrl = currentParams.toString()
          ? `${pathname}?${currentParams.toString()}`
          : pathname;

        router.replace(newUrl, { scroll: false });

        setTimeout(() => {
          mutate("subscriptions");
          mutate("current-subscription");
          mutate(["invoices", 1]);
        }, 2000);
      } else {
        toast.error(response.error || "Subscription failed");
      }
    } catch (err: any) {
      toast.error(err.message || "Subscription error");
    } finally {
      setIsProcessing(false);
      setIsDialogOpen(false);
    }
  };

  return (
    <div className="w-full flex justify-center py-8">
      <div className="max-w-md w-full">
        <div className="text-center mb-4">
          <span
            className="inline-block px-5 py-2 rounded-lg font-bold text-yellow-900 border-2 border-yellow-400 shadow-[0_0_12px_2px_#ffd700,0_0_2px_#fffbe6] bg-gradient-to-r from-[#fffbe6] via-[#ffe066] to-[#ffd700] tracking-wider
                  dark:text-yellow-200 dark:border-yellow-700 dark:shadow-[0_0_8px_1px_rgba(180,150,50,0.3)] dark:bg-gradient-to-r dark:from-[#3a3200] dark:via-[#5a4b00] dark:to-[#7a6600]"
          >
            <span className="dark:drop-shadow-[0_0_1px_rgba(0,0,0,0.7)]">
              🏆 Special Golden Offer! 🏆
            </span>
          </span>
        </div>
        <Card
          className="border-2 border-yellow-400 bg-gradient-to-br from-yellow-50 via-yellow-100 to-white shadow-xl
                         dark:border-yellow-700 dark:bg-gradient-to-br dark:from-[#1a1600] dark:via-[#2a2400] dark:to-[#1a1700] dark:shadow-[0_0_15px_2px_rgba(180,150,50,0.2)]"
        >
          <CardHeader className="relative text-center">
            <div className="absolute -top-6 left-1/2 transform -translate-x-1/2">
              <Crown className="w-12 h-12 text-yellow-500 fill-yellow-500 dark:text-yellow-500 dark:fill-yellow-500 dark:drop-shadow-[0_0_4px_rgba(180,150,50,0.4)]" />
            </div>
            <CardTitle className="mt-8 text-2xl font-bold text-yellow-700 dark:text-yellow-300">
              {plan.name}
            </CardTitle>
            <p className="text-sm text-yellow-600 dark:text-yellow-500">
              {plan.description}
            </p>
          </CardHeader>

          <div className="px-6 py-4">
            {availablePeriods.length > 1 && (
              <Tabs
                value={selectedBillingPeriod}
                onValueChange={setSelectedBillingPeriod}
                className="mb-6 w-full"
              >
                <TabsList className="mx-auto p-1 bg-transparent border-0">
                  <div
                    className="flex justify-center gap-1 p-1 rounded-xl bg-gradient-to-r from-yellow-100 to-yellow-200 border border-yellow-300 shadow-inner
                      dark:from-[#1a1600] dark:to-[#3a3200] dark:border-yellow-800 dark:shadow-[0_0_5px_rgba(100,80,0,0.3)]"
                  >
                    {availablePeriods.map((period) => (
                      <TabsTrigger
                        key={period}
                        value={period}
                        className="relative px-5 py-2 rounded-lg capitalize font-bold transition-all duration-200
                      data-[state=active]:bg-gradient-to-r data-[state=active]:from-yellow-500 data-[state=active]:to-yellow-600
                      data-[state=active]:text-white
                      data-[state=active]:shadow-[0_0_5px_#d4af37]
                      hover:bg-yellow-300
                      text-yellow-700
                      bg-transparent
                      border-0
                      dark:data-[state=active]:bg-gradient-to-r dark:data-[state=active]:from-yellow-600 dark:data-[state=active]:to-yellow-700
                      dark:data-[state=active]:text-white
                      dark:data-[state=active]:shadow-[0_0_8px_rgba(180,150,50,0.4)]
                      dark:hover:bg-[#2a2400]
                      dark:text-yellow-400"
                      >
                        {period}
                        <span
                          className="absolute inset-x-1 -bottom-1 h-0.5 bg-yellow-500 opacity-0 transition-opacity
                             data-[state=active]:opacity-100
                             dark:bg-yellow-500"
                        ></span>
                      </TabsTrigger>
                    ))}
                  </div>
                </TabsList>
              </Tabs>
            )}

            <CardContent className="text-center">
              <div className="text-4xl font-extrabold text-yellow-700 dark:text-yellow-300">
                €{billingOption.current_price}
              </div>
              <p className="text-sm text-yellow-600 dark:text-yellow-500 capitalize">
                {selectedBillingPeriod}
              </p>
            </CardContent>

            <div className="mb-4 space-y-2">
              {plan.features?.map((feature, idx) => {
                const variables = {
                  value: feature.value || "",
                  months: featurePeriodMap[selectedBillingPeriod]?.months ?? "",
                  days: featurePeriodMap[selectedBillingPeriod]?.days ?? "",
                };
                let desc = feature.description;
                try {
                  desc = renderTemplate(desc, variables);
                } catch {}
                return (
                  <div key={idx} className="flex items-center gap-2">
                    <div
                      className={`w-5 h-5 flex items-center justify-center ${
                        feature.included ? "bg-yellow-200" : "bg-gray-200"
                      } rounded-full
                      dark:${
                        feature.included ? "bg-yellow-800" : "bg-gray-700"
                      }`}
                    >
                      <Check
                        className={
                          feature.included
                            ? "text-yellow-500 dark:text-yellow-500"
                            : "text-gray-400"
                        }
                        size={16}
                      />
                    </div>
                    <span
                      className={
                        feature.included
                          ? "text-yellow-700 dark:text-yellow-300"
                          : "text-gray-500 dark:text-gray-400"
                      }
                    >
                      {desc}
                    </span>
                  </div>
                );
              })}
            </div>

            <CustomButton
              props={{
                value: "Subscribe Now",
                onClick: handleSelect,
                className:
                  "w-full bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg " +
                  "dark:from-[#d4af37] dark:to-[#b89f14] dark:text-white dark:hover:from-[#e6c260] dark:hover:to-[#c9a845] " +
                  "dark:shadow-[0_0_8px_2px_rgba(180,150,50,0.3)] dark:border dark:border-yellow-600",
              }}
            />
          </div>

          <SubscriptionConfirmationDialog
            isOpen={isDialogOpen}
            onClose={() => setIsDialogOpen(false)}
            onCloseComplete={() => {}}
            onSubscribeWithCard={handleSubscribeWithCard}
            selectedPlan={plan}
            billingPeriod={selectedBillingPeriod}
            isProcessingSubscription={isProcessing}
          />
        </Card>
      </div>
    </div>
  );
}
