"use client";

import { useState, useEffect, useRef, useLayoutEffect } from "react";
import { createRoot, Root } from "react-dom/client";
import { getConversationLiveTranscription } from "@/actions/ConversationActions";
import { Mi<PERSON>, <PERSON><PERSON>, User, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { socket } from "@/lib/socket";

interface TranscriptionMessage {
  from: string;
  text: string;
}

declare global {
  interface Window {
    documentPictureInPicture: {
      requestWindow: (options?: {
        width?: number;
        height?: number;
      }) => Promise<Window>;
    };
  }
}

const pipStyles = `
  /* =================================================================
     RESET & BASE STYLES
     ================================================================= */
  
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, 
                 "Helvetica Neue", Arial, sans-serif;
  }

  body {
    background-color: #f9fafb;
    color: #1f2937;
    font-size: 14px;
    line-height: 1.5;
    display: flex;
    flex-direction: column;
    height: 100vh;
  }

  /* =================================================================
     LAYOUT COMPONENTS
     ================================================================= */

  .pip-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f9fafb;
  }

  .pip-header {
    background-color: #111827;
    color: white;
    padding: 8px 12px;
    flex-shrink: 0;
    border-bottom: 1px solid #374151;
  }

  .pip-header h1 {
    font-size: 16px;
    font-weight: 500;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .pip-main {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: calc(100vh - 60px);
    min-height: 0;
  }

  /* =================================================================
     MESSAGE COMPONENTS
     ================================================================= */

  .message {
    display: flex;
    gap: 8px;
    max-width: 85%;
    word-wrap: break-word;
  }

  .message.ai {
    align-self: flex-start;
  }

  .message.user {
    align-self: flex-end;
  }

  .message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #9ca3af;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: white;
  }

  .message-content {
    padding: 8px 12px;
    border-radius: 12px;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  .message.ai .message-content {
    background-color: #e5e7eb;
    border: 1px solid #d1d5db;
  }

  .message.user .message-content {
    background-color: #dbeafe;
  }

  .message-content p {
    margin: 0;
    word-wrap: break-word;
  }

  /* =================================================================
     SCROLLBAR STYLING
     ================================================================= */

  .pip-main::-webkit-scrollbar {
    width: 8px;
  }

  .pip-main::-webkit-scrollbar-track {
    background: transparent;
  }

  .pip-main::-webkit-scrollbar-thumb {
    background-color: #9ca3af;
    border-radius: 4px;
  }

  .pip-main::-webkit-scrollbar-thumb:hover {
    background-color: #6b7280;
  }

  /* Firefox scrollbar */
  .pip-main {
    scrollbar-width: thin;
    scrollbar-color: #9ca3af transparent;
  }

  /* =================================================================
     UTILITY CLASSES
     ================================================================= */

  .flex {
    display: flex;
  }

  .items-center {
    align-items: center;
  }

  .gap-2 {
    gap: 8px;
  }

  .break-words {
    overflow-wrap: break-word;
    word-break: break-word;
  }
`;

function PipContent({
  messages,
  shouldScrollToBottom,
}: {
  messages: TranscriptionMessage[];
  shouldScrollToBottom: boolean;
}) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const isFirstRender = useRef(true);

  const scrollToBottom = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  };

  useLayoutEffect(() => {
    if (!scrollRef.current) return;

    if (isFirstRender.current) {
      setTimeout(() => {
        scrollToBottom();
        isFirstRender.current = false;
      }, 500);
      return;
    }

    if (shouldScrollToBottom) {
      scrollToBottom();
    }
  }, [messages, shouldScrollToBottom]);

  return (
    <div className="pip-container">
      <header className="pip-header">
        <h1 className="flex items-center gap-2">
          <Mic size={16} /> Live Transcript
        </h1>
      </header>
      <main ref={scrollRef} className="pip-main">
        {messages.map((msg, index) => {
          const isAI = msg.from.includes("IA") || msg.from.includes("🤖");
          return (
            <div key={index} className={`message ${isAI ? "ai" : "user"}`}>
              <div className="message-avatar">
                {isAI ? <Bot size={16} /> : <User size={16} />}
              </div>
              <div className="message-content">
                <p>{msg.text}</p>
              </div>
            </div>
          );
        })}
      </main>
    </div>
  );
}

export default function InteractivePipTranscript({
  conversationId,
}: {
  conversationId?: string;
}) {
  const [pipWindow, setPipWindow] = useState<Window | null>(null);
  const [messages, setMessages] = useState<TranscriptionMessage[]>([]);
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);
  const reactRootRef = useRef<Root | null>(null);

  const setupSocketListeners = () => {
    if (!socket.connected) {
      socket.connect();
    }

    const handleNewTranscriptionMessage = (data: {
      conversationId: string;
      latestMessage: TranscriptionMessage;
      timestamp: Date;
    }) => {
      if (data.conversationId === conversationId && data.latestMessage) {
        const pipMain = pipWindow?.document.querySelector(".pip-main");
        let wasAtBottom = true;
        if (pipMain) {
          const { scrollTop, scrollHeight, clientHeight } = pipMain;
          wasAtBottom = scrollTop + clientHeight >= scrollHeight - 20;
        }

        setShouldScrollToBottom(wasAtBottom);
        setMessages((prevMessages) => [...prevMessages, data.latestMessage]);
      }
    };

    socket.on("new_transcription_message", handleNewTranscriptionMessage);
    return () => {
      socket.off("new_transcription_message", handleNewTranscriptionMessage);
    };
  };

  useEffect(() => {
    if (!pipWindow || !conversationId) return;

    if (reactRootRef.current) {
      reactRootRef.current.render(
        <PipContent
          messages={messages}
          shouldScrollToBottom={shouldScrollToBottom}
        />
      );
    }

    const fetchInitialTranscript = async () => {
      try {
        const response = await getConversationLiveTranscription(conversationId);
        if (response.success && response.transcription) {
          setMessages(response.transcription);
        } else {
          setMessages([
            {
              from: "🤖 IA",
              text: "Could not fetch live data. Displaying example.",
            },
            { from: "Client", text: "This is a fully interactive window." },
            {
              from: "🤖 IA",
              text: "You can scroll, select text, and click buttons.",
            },
          ]);
        }
      } catch (error) {
        console.error("Failed to fetch transcript:", error);
      }
    };

    fetchInitialTranscript();
    const cleanup = setupSocketListeners();
    return cleanup;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pipWindow, conversationId]);

  useEffect(() => {
    if (pipWindow && reactRootRef.current) {
      reactRootRef.current.render(
        <PipContent
          messages={messages}
          shouldScrollToBottom={shouldScrollToBottom}
        />
      );
    }
  }, [messages, pipWindow, shouldScrollToBottom]);

  const cleanupWindow = () => {
    setPipWindow(null);
    setMessages([]);
    reactRootRef.current = null;
  };

  const openFallbackWindow = () => {
    try {
      const newWindow = window.open(
        "",
        "LiveTranscript",
        "width=380,height=500,resizable=yes,scrollbars=yes,status=no,location=no,toolbar=no,menubar=no"
      );

      if (!newWindow) {
        alert(
          "Pop-up blocked. Please allow pop-ups for this site and try again."
        );
        return;
      }

      newWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Live Transcript</title>
            <style>${pipStyles}</style>
          </head>
          <body>
            <div id="app-container"></div>
          </body>
        </html>
      `);
      newWindow.document.close();

      const appContainer = newWindow.document.getElementById("app-container");
      if (appContainer) {
        reactRootRef.current = createRoot(appContainer);
      }

      const checkClosed = setInterval(() => {
        if (newWindow.closed) {
          clearInterval(checkClosed);
          cleanupWindow();
        }
      }, 1000);

      setPipWindow(newWindow);
    } catch (error) {
      console.error("Error opening fallback window:", error);
      alert(
        "Failed to open transcript window. Please check your browser settings."
      );
    }
  };

  const openPipWindow = async () => {
    try {
      const newPipWindow = await window.documentPictureInPicture.requestWindow({
        width: 380,
        height: 500,
      });

      const styleEl = newPipWindow.document.createElement("style");
      styleEl.textContent = pipStyles;
      newPipWindow.document.head.appendChild(styleEl);

      const appContainer = newPipWindow.document.createElement("div");
      newPipWindow.document.body.appendChild(appContainer);
      reactRootRef.current = createRoot(appContainer);

      newPipWindow.addEventListener("pagehide", cleanupWindow, { once: true });
      setPipWindow(newPipWindow);
    } catch (error) {
      console.error("Error opening PiP window:", error);
      openFallbackWindow();
    }
  };

  const togglePip = async () => {
    if (pipWindow) {
      pipWindow.close();
      return;
    }

    if ("documentPictureInPicture" in window) {
      await openPipWindow();
    } else {
      openFallbackWindow();
    }
  };

  useEffect(() => {
    return () => {
      pipWindow?.close();
      socket.off("new_transcription_message");
    };
  }, [pipWindow]);

  return (
    <Button
      onClick={togglePip}
      disabled={!conversationId}
      className="h-6 w-full bg-blue-600 hover:bg-blue-500 dark:bg-blue-800 dark:hover:bg-blue-700 text-white text-[11px] rounded-md disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-1"
    >
      {pipWindow ? "Close" : "Open"} Live Transcript
      <FileText className="w-3 h-3" />
    </Button>
  );
}
