import { useLayoutEffect, useState } from "react";

// Define screen size breakpoints (following Tailwind CSS defaults)
export type ScreenSize = "xs" | "sm" | "md" | "lg" | "xl" | "2xl";

const breakpoints = {
  xs: 0, // Extra small devices
  sm: 640, // Small devices like mobile phones
  md: 768, // Medium devices like tablets
  lg: 1024, // Large devices like laptops
  xl: 1280, // Extra large devices like desktops
  "2xl": 1536, // Extremely large devices
};

export const useScreenSize = (): ScreenSize => {
  // Default to 'lg' during SSR to prevent hydration issues
  const [screenSize, setScreenSize] = useState<ScreenSize>("lg");

  useLayoutEffect(() => {
    // Function to determine the current screen size
    const getScreenSize = (): ScreenSize => {
      if (typeof window === "undefined") return "lg";

      const width = window.innerWidth;

      if (width < breakpoints.sm) return "xs";
      if (width < breakpoints.md) return "sm";
      if (width < breakpoints.lg) return "md";
      if (width < breakpoints.xl) return "lg";
      if (width < breakpoints["2xl"]) return "xl";
      return "2xl";
    };

    // Set the initial screen size
    setScreenSize(getScreenSize());

    // Update screen size when the window is resized
    const handleResize = () => {
      setScreenSize(getScreenSize());
    };

    window.addEventListener("resize", handleResize);

    // Clean up the event listener
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return screenSize;
};

export default useScreenSize;
