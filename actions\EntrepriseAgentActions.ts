"use server"

import dbConnect from "@/lib/mongodb";
import { getEntrepriseByAdminID } from "./Entreprise";
import { EntrepriseAgent } from "@/models/User";

export async function CreateEntrepriseAgent(
  agentName: string,
  agentEmail: string,
  agentNumber: string,
  agentPassword: string,
): Promise<{ success: boolean; error?: string }> {
  try {
    await dbConnect()
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const entrepriseID = entrepriseResponse.entreprise._id;
    const newAgent = await EntrepriseAgent.create({
      name: agentName,
      email: agentEmail,
      phone: agentNumber,
      password: agentPassword,
      entreprise: entrepriseID,
    });

    if (!newAgent) {
      throw new Error("Failed to create Entreprise Agent");
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function getEntrepriseAgents(){
    try {
        await dbConnect()
        const entrepriseResponse = await getEntrepriseByAdminID();
        if (!entrepriseResponse.success) {
        return { success: false, error: entrepriseResponse.error };
        }
    
        const entrepriseID = entrepriseResponse.entreprise._id;
        const agents = await EntrepriseAgent.find({ entreprise: entrepriseID }).select("-password").lean();
    
        if (!agents) {
        throw new Error("Failed to fetch Entreprise Agents");
        }

        const formattedAgents = agents.map((agent) => ({
            id: agent._id?.toString(),
            name: agent.name,
            email: agent.email,
            phone: agent.phone,
            created_at: agent.createdAt,
        }));
    
        return { success: true, agents: formattedAgents };
    } catch (err: any) {
        return { success: false, error: err.message };
    }
}