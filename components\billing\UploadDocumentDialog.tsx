"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FileUpload } from "@/components/ui/file-upload";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PlusIcon } from "lucide-react";
import { toast } from "sonner";
import {
  GetDocumentSignedURL,
  SaveDocumentDetails,
} from "@/actions/DocumentActions";

interface UploadDocumentDialogProps {
  onUploadSuccess: () => void;
}

export default function UploadDocumentDialog({
  onUploadSuccess,
}: UploadDocumentDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentName, setDocumentName] = useState("");
  const [category, setCategory] = useState("billing");
  const [loading, setLoading] = useState(false);

  const handleFileChange = (files: File[]) => {
    if (files.length > 0) {
      const file = files[0];
      setSelectedFile(file);
      if (!documentName) {
        setDocumentName(file.name.split(".")[0]);
      }
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !documentName.trim()) {
      toast.error("Please select a file and enter a document name");
      return;
    }

    setLoading(true);

    try {
      // Get signed URL
      const signedURLResult = await GetDocumentSignedURL(
        selectedFile.name,
        selectedFile.type
      );

      if (!signedURLResult.success) {
        toast.error(signedURLResult.error);
        setLoading(false);
        return;
      }

      const { url } = signedURLResult.data!;

      // Upload file to S3
      const uploadResponse = await fetch(url, {
        method: "PUT",
        body: selectedFile,
        headers: {
          "Content-Type": selectedFile.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error("Failed to upload file");
      }

      const fileUrl = url.split("?")[0];

      // Save document details to database
      const saveResult = await SaveDocumentDetails(
        {
          name: documentName.trim(),
          originalName: selectedFile.name,
          size: selectedFile.size,
          type: selectedFile.type,
          category: category,
        },
        fileUrl
      );

      if (!saveResult.success) {
        toast.error(saveResult.error);
        setLoading(false);
        return;
      }

      toast.success("Document uploaded successfully");
      setIsOpen(false);
      resetForm();
      onUploadSuccess();
    } catch (error: any) {
      console.error("Upload error:", error);
      toast.error(error.message || "Failed to upload document");
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
    setDocumentName("");
    setCategory("billing");
  };

  const handleClose = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      resetForm();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogTrigger asChild>
        <Button
          onClick={() => setIsOpen(true)}
          className="flex items-center gap-2 ms-auto"
        >
          <PlusIcon className="w-4 h-4" />
          Add Document
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Document</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="documentName">Document Name</Label>
            <Input
              id="documentName"
              value={documentName}
              onChange={(e) => setDocumentName(e.target.value)}
              placeholder="Enter document name"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="category">Category</Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="billing">Billing</SelectItem>
                <SelectItem value="legal">Legal</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>File Upload</Label>
            <div className="mt-1">
              <FileUpload onChange={handleFileChange} />
            </div>
            {selectedFile && (
              <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                <p className="w-0 min-w-full text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                  Selected: {selectedFile.name} (
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                </p>
              </div>
            )}
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              className="flex-1"
              disabled={loading || !selectedFile || !documentName.trim()}
            >
              {loading ? "Uploading..." : "Upload"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
