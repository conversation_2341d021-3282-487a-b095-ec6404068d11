"use client";

import { updateBalanceAlertThreshold } from "@/actions/BillingActions";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { fetchEntreprise } from "@/redux/BusinessDashboard/subSlices/EntrepriseSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { Save } from "lucide-react";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";

interface BillingSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function BillingSettingsDialog({
  open,
  onOpenChange,
}: BillingSettingsDialogProps) {
  const { entreprise } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardEntreprise
  );
  const dispatch = useDispatch<AppDispatch>();
  const [threshold, setThreshold] = useState<number>(10);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (entreprise && typeof entreprise.balanceAlertThreshold === "number") {
      setThreshold(entreprise.balanceAlertThreshold);
    }
  }, [entreprise, open]);

  const handleSave = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    setSaving(true);
    try {
      const res = await updateBalanceAlertThreshold(threshold);
      if (res.success) {
        toast.success("Balance alert threshold updated!");
        dispatch(fetchEntreprise(["balanceAlertThreshold"]));
        onOpenChange(false);
      } else {
        toast.error(res.error || "Failed to update threshold");
      }
    } catch {
      toast.error("Failed to update threshold");
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Billing Settings</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSave}>
          <div className="space-y-6 mt-2">
            <div className="space-y-2">
              <Label
                htmlFor="balance-alert-threshold"
                className="text-base font-medium"
              >
                Balance Alert Threshold
              </Label>
              <div className="flex items-center gap-2">
                <input
                  id="balance-alert-threshold"
                  type="number"
                  min={0}
                  value={loading ? "" : threshold}
                  onChange={(e) => setThreshold(Number(e.target.value))}
                  className="w-24 px-3 py-2 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-md bg-voxa-neutral-50 dark:bg-voxa-neutral-900 text-foreground text-base focus:outline-none focus:ring-2 focus:ring-voxa-teal-500"
                  disabled={loading || saving}
                />
                <span className="text-muted-foreground text-sm">€</span>
              </div>
              <div className="text-xs text-muted-foreground">
                You will be notified when your balance drops below this amount.
              </div>
            </div>
          </div>
          <DialogFooter className="mt-4">
            <Button
              type="submit"
              disabled={saving || loading}
              className="flex items-center gap-2 bg-voxa-teal-600 hover:bg-voxa-teal-500 text-white"
            >
              <Save className="w-4 h-4" />
              {saving ? "Saving..." : "Save"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
