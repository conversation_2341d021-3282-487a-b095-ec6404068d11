import { FileUpload } from "@/components/ui/file-upload";
import { toast } from "sonner";
import { useEffect, useState } from "react";
import { checkDuplicateNumbers } from "@/actions/ClientsActions";
import ButtonLoader from "../Loaders/ButtonLoader";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { checkNumbersValidity, GetTableData } from "@/lib/csv/CSVFunctions";
import { ClientTable } from "../tables/ClientsData";
import { ScrollArea } from "../ui/scroll-area";
import { Label } from "../ui/label";
import { CountriesSelect } from "../dropdowns/CountriesSelect";
import { DuplicateNumbersTable } from "../tables/DuplicateClients";
import {
  setError,
  handleFileUpload,
  UploadClients,
  setCountry,
  setImportContactsDialogOpen,
} from "@/redux/BusinessDashboard/subSlices/ClientsSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { useDispatch, useSelector } from "react-redux";
import { CountryCode } from "libphonenumber-js";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import AttachFileRoundedIcon from "@mui/icons-material/AttachFileRounded";
import { FileStructureTable } from "../tables/FileStructureTable";
import { ForceUploadClients } from "../dialogs/ForceUploadClients";
import { Button } from "../ui/button";

export default function ImportContacts() {
  const dispatch = useDispatch<AppDispatch>();
  const { files, error, country, importContactsDialogOpen } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardClients
  );

  const [Loading, setLoading] = useState(false);
  const [invalidNumbers, setInvalidNumbers] = useState<any[]>([]);
  const [totalRows, setTotalRows] = useState<number>(0);
  const [valid_rows, set_valid_rows] = useState<number>(0);
  const [data_rows, set_data_rows] = useState<any[]>([]);
  const [duplicateNumbers, setDuplicateNumbers] = useState<any[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [phoneColumn, setPhoneColumn] = useState<0 | 1 | null>(null);

  const handleChange = (UploadedFiles: any) => {
    console.log(UploadedFiles[0]);
    if (UploadedFiles.length === 0) return;

    const file = UploadedFiles[0];
    const allowedTypes = ["text/csv", "text/plain"];
    if (!allowedTypes.includes(file.type)) {
      dispatch(setError(true));
      toast.error("Please select a CSV or Text file");
      return;
    }

    setSelectedFile(file);

    dispatch(
      handleFileUpload([{ name: file.name, type: file.type, size: file.size }])
    );
  };

  const UploadUsers = async () => {
    if (!country) {
      toast.error("Please select a country before uploading the file.");
      return;
    }
    if (!selectedFile) {
      toast.error("Please fill all fields");
      return;
    }
    if (phoneColumn === null) {
      toast.error("Please select a column for the phone numbers.");
      return;
    }
    const allowedTypes = ["text/csv", "text/plain"];
    if (!allowedTypes.includes(selectedFile.type)) {
      toast.error("Please select a CSV or Text file");
      return;
    }

    setLoading(true);

    let cleanedData = [...data_rows];

    if (invalidNumbers.length > 0) {
      const invalidSet = new Set(
        invalidNumbers.map((entry) => entry.number.replace(/\s+/g, ""))
      );

      cleanedData = cleanedData.filter((row) => {
        const phone = (
          phoneColumn === 1 ? row.firstValue : row.secondValue
        ).replace(/\s+/g, "");
        return !invalidSet.has(phone);
      });
    }

    await dispatch(UploadClients({ data: cleanedData, phoneColumn }));

    setLoading(false);
  };

  useEffect(() => {
    console.log("Selected file:", selectedFile);
    if (selectedFile) {
      if (!country) {
        toast.error("Please select a country before uploading the file.");
        return;
      }

      GetTableData(selectedFile)
        .then((rows: any) => {
          setTotalRows(rows.length);
          set_data_rows(rows);
        })
        .catch((err: any) => console.error("Error getting total rows:", err));
    }
  }, [selectedFile, country]);

  useEffect(() => {
    if (data_rows.length > 0 && country && phoneColumn !== null) {
      Promise.all([
        checkNumbersValidity(data_rows, phoneColumn, country),
        checkDuplicateNumbers(data_rows, phoneColumn, country),
      ])
        .then(([warnings, result]) => {
          setInvalidNumbers(warnings);

          if (result.success) {
            setDuplicateNumbers(result.duplicateNumbers || []);
            const invalidCount = warnings.length;
            const duplicateCount = result?.duplicateNumbers?.length || 0;

            set_valid_rows(data_rows.length - (invalidCount + duplicateCount));
          } else {
            console.error("Error checking duplicate numbers:", result.error);
          }
        })
        .catch((err) => {
          console.error("Error checking numbers:", err);
        });
    }
  }, [data_rows, country, phoneColumn]);

  // Handle editing a phone number in data_rows
  const handleEditNumber = (index: number, newNumber: string) => {
    // Create a copy of data_rows
    const updatedRows = [...data_rows];

    // Update the phone number in the correct column
    if (phoneColumn !== null && updatedRows[index]) {
      if (phoneColumn === 0) {
        updatedRows[index].firstValue = newNumber;
      } else if (phoneColumn === 1) {
        updatedRows[index].secondValue = newNumber;
      }

      // Update the data_rows state
      set_data_rows(updatedRows);

      // Re-validate the numbers
      if (country) {
        Promise.all([
          checkNumbersValidity(updatedRows, phoneColumn, country),
          checkDuplicateNumbers(updatedRows, phoneColumn, country),
        ])
          .then(([warnings, result]) => {
            setInvalidNumbers(warnings);

            if (result.success) {
              setDuplicateNumbers(result.duplicateNumbers || []);
              const invalidCount = warnings.length;
              const duplicateCount = result?.duplicateNumbers?.length || 0;

              set_valid_rows(
                updatedRows.length - (invalidCount + duplicateCount)
              );
            }
          })
          .catch((err) => {
            console.error("Error checking numbers after edit:", err);
          });
      }
    }
  };

  // Handle deleting an invalid number from data_rows
  const handleDeleteNumber = (index: number) => {
    // Create a copy of data_rows
    const updatedRows = [...data_rows];

    // Remove the row at the specified index
    if (index >= 0 && index < updatedRows.length) {
      updatedRows.splice(index, 1);

      // Update the data_rows state
      set_data_rows(updatedRows);

      // Re-validate the numbers
      if (country && phoneColumn !== null) {
        Promise.all([
          checkNumbersValidity(updatedRows, phoneColumn, country),
          checkDuplicateNumbers(updatedRows, phoneColumn, country),
        ])
          .then(([warnings, result]) => {
            setInvalidNumbers(warnings);

            if (result.success) {
              setDuplicateNumbers(result.duplicateNumbers || []);
              const invalidCount = warnings.length;
              const duplicateCount = result?.duplicateNumbers?.length || 0;

              set_valid_rows(
                updatedRows.length - (invalidCount + duplicateCount)
              );
            }
          })
          .catch((err) => {
            console.error("Error checking numbers after deletion:", err);
          });
      }
    }
  };

  return (
    <Dialog
      open={importContactsDialogOpen}
      onOpenChange={(open) => {
        dispatch(setImportContactsDialogOpen(open));
      }}
    >
      <DialogTrigger asChild>
        <button
          onClick={() => dispatch(setImportContactsDialogOpen(true))} // Ensure dialog opens
          className="max-md:w-full text-nowrap text-sm bg-voxa-neutral-500 dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-800active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950 transition-all duration-150 text-white pl-2 pr-4 py-2 rounded-md font-medium flex justify-center items-center gap-1"
        >
          <AttachFileRoundedIcon />
          Import Contacts
        </button>
      </DialogTrigger>
      <DialogContent
        className={` py-5 px-2 sm:px-5 rounded-md w-full max-h-[calc(100vh-50px)] flex flex-col ${
          files?.length > 0 ? "max-w-[900px]" : "max-w-[400px]"
        }`}
      >
        <DialogHeader className="flex justify-between items-center">
          <DialogTitle>Import Contacts</DialogTitle>
        </DialogHeader>
        <div className="grow overflow-auto">
          <p className="text-sm text-black/60 dark:text-voxa-neutral-200">
            Import your contacts from a CSV file.
          </p>
          <div className="grid w-full items-center gap-1.5 mt-3">
            <Label htmlFor="country" className="text-sm">
              Country <span className="text-red-500">*</span>
            </Label>
            <CountriesSelect
              country={country}
              selectCountry={(newCountry: "" | CountryCode) =>
                dispatch(setCountry(newCountry))
              }
              classnames="translate-y-px py-5"
            />
          </div>
          <p className="text-xs text-orange-400 text-center mt-4">
            The CSV file should contain the following columns: phone number,
            name
          </p>

          <div className="flex flex-col gap-3 mt-4">
            <FileUpload onChange={handleChange} type=".csv, .txt" />
            {error && (
              <p className="text-red-500 text-sm">Only 1 CSV file is allowed</p>
            )}
            {selectedFile && !error && country && (
              <Tabs defaultValue="fileDetails" className="w-full mt-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger className="relative" value="fileDetails">
                    File Details
                  </TabsTrigger>
                  <TabsTrigger className="relative" value="warnings">
                    Warnings
                    {invalidNumbers.length > 0 && (
                      <span className="absolute -top-1 z-50 -right-2 flex h-4 w-4 items-center justify-center rounded-full bg-orange-500 text-xs text-forground">
                        {invalidNumbers.length}
                      </span>
                    )}
                  </TabsTrigger>
                  <TabsTrigger className="relative" value="duplicates">
                    Duplicates
                    {duplicateNumbers.length > 0 && (
                      <span className="absolute -top-1 z-50 -right-2 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-forground">
                        {duplicateNumbers.length}
                      </span>
                    )}
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="fileDetails">
                  <div className="px-3 text-sm text-voxa-neutral-600 dark:text-gray-300 grid grid-cols-2 gap-4 items-start">
                    <p className="flex max-sm:flex-col">
                      <strong className="text-forground/60">File Name:</strong>
                      &nbsp;
                      {files[0]?.name}
                    </p>
                    <div className="w-full gap-4 grid grid-cols-2">
                      <p className="flex max-sm:flex-col">
                        <strong className="text-forground/60">Size:</strong>
                        &nbsp;
                        {files[0]?.size} bytes
                      </p>
                      <p className="flex max-sm:flex-col">
                        <strong className="text-forground/60">Rows:</strong>
                        &nbsp;
                        {totalRows}
                      </p>
                    </div>
                  </div>
                  <div className="p-3 gap-2 flex flex-col">
                    <span className="text-xs sm:text-sm text-voxa-teal-500">
                      Select which column contains the phone numbers you want to
                      call
                    </span>
                    <ScrollArea className="h-60 w-full">
                      <FileStructureTable
                        data={data_rows}
                        phoneColumn={phoneColumn}
                        setPhoneColumn={setPhoneColumn}
                        country={country}
                      />
                    </ScrollArea>
                  </div>
                </TabsContent>
                <TabsContent value="warnings">
                  {invalidNumbers.length > 0 ? (
                    <div>
                      <ScrollArea className="h-60 w-full">
                        <p className="text-orange-500 text-sm text-center">
                          Below are the numbers that are not valid.
                        </p>
                        <ClientTable
                          data={invalidNumbers}
                          onEditNumber={handleEditNumber}
                          onDeleteNumber={handleDeleteNumber}
                        />
                      </ScrollArea>
                    </div>
                  ) : (
                    <div className="p-3">
                      <p className="text-green-500 text-sm text-center">
                        All numbers are valid
                      </p>
                    </div>
                  )}
                </TabsContent>
                <TabsContent value="duplicates">
                  <div className="p-3">
                    {duplicateNumbers.length > 0 ? (
                      <>
                        <p className="text-red-500 text-sm text-center">
                          Below are the numbers that already exist in the
                          database.
                        </p>
                        <ScrollArea className="h-60 w-full">
                          <DuplicateNumbersTable
                            duplicateNumbers={duplicateNumbers}
                            onEditNumber={handleEditNumber}
                            onDeleteNumber={handleDeleteNumber}
                          />
                        </ScrollArea>
                      </>
                    ) : (
                      <p className="text-green-500 text-sm text-center">
                        No duplicate numbers
                      </p>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </div>
        </div>
        {files.length > 0 &&
        country &&
        invalidNumbers.length === 0 &&
        duplicateNumbers.length === 0 ? (
          <Button
            disabled={files.length === 0 || error || Loading || !country}
            onClick={UploadUsers}
            className={`${
              Loading && "cursor-not-allowed"
            }w-full text-voxa-neutral-50 hover:bg-voxa-teal-500 font-medium flex justify-center items-center gap-2 ${
              files.length === 0 || error
                ? "cursor-not-allowed"
                : "cursor-pointer"
            }`}
          >
            {Loading ? (
              <>
                Uploading Contacts
                <ButtonLoader />
              </>
            ) : (
              "Upload"
            )}
          </Button>
        ) : (
          <ForceUploadClients
            handleUpload={UploadUsers}
            invalid_numbers={invalidNumbers.length}
            duplicate_numbers={duplicateNumbers.length}
            valid_rows={valid_rows}
            files={files}
            error={error}
            Loading={Loading}
            country={country}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
