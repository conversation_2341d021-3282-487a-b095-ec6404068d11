{"yourAssistants": "Vos assistants", "searchGoalsPlaceholder": "Rechercher des objectifs par ID et nom", "search": "<PERSON><PERSON><PERSON>", "searching": "Recherche en cours..", "clear": "<PERSON><PERSON><PERSON><PERSON>", "searchResultsFor": "Résultats de recherche pour \"{{searchTerm}}\"", "foundGoals": "{{count}} objectif trouvé", "errorLoadingGoals": "E<PERSON>ur lors du chargement des objectifs : {{message}}", "noGoalsFound": "Aucun objectif correspondant à votre recherche.", "failedToLoadAssistants": "Échec du chargement des assistants.", "noAssistantsFound": "Aucun assistant trouvé.", "meet": {"addClientToMeet": "Ajouter un client à Meet", "allFieldsRequired": "Tous les champs sont obligatoires", "country": "Pays", "phoneNumber": "Numéro de téléphone", "googleMeetPin": "Code PIN Google Meet", "addingClient": "<PERSON><PERSON><PERSON> du <PERSON>", "addClient": "Ajouter le client"}, "import": {"selectCsvOrTextFile": "Veuillez sélectionner un fichier CSV ou texte", "selectCountry": "Veuillez sélectionner un pays avant de télécharger le fichier.", "fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs", "selectPhoneColumn": "<PERSON><PERSON><PERSON>z sélectionner une colonne pour les numéros de téléphone.", "importContacts": "Importer des contacts", "country": "Pays", "dontSaveInDb": "Ne pas enregistrer dans la base clients.", "saveInDb": "Enregistrer dans la base clients.", "csvColumnsInfo": "Le fichier csv doit contenir les colonnes suivantes : numéro de téléphone, nom", "selectPhoneColumnInfo": "Sélectionnez la colonne contenant les numéros de téléphone à appeler", "invalidNumbersBelow": "Ci-dessous les numéros non valides.", "allNumbersValid": "Tous les numéros sont valides", "duplicatesBelow": "Ci-dessous les numéros déjà présents dans la base de données.", "noDuplicates": "Aucun numéro en double", "uploadingContacts": "Importation des contacts", "upload": "Importer"}, "group": {"groupRequired": "Le groupe est requis", "goalIdRequired": "L'identifiant de l'objectif est requis", "addGroupToGoal": "Ajouter un groupe à l'objectif", "searchGroup": "Rechercher un groupe", "enterFirstLetters": "Entrez les premières lettres...", "selectGroup": "Sélectionner un groupe", "selectAGroup": "Sélectionner un groupe", "addingToGoal": "Ajout à l'objectif", "addToGoal": "Ajouter à l'objectif"}, "existingClient": {"clientRequired": "Le client est requis", "goalIdRequired": "L'identifiant de l'objectif est requis", "addExistingClient": "Ajouter un client existant", "searchByName": "Rechercher par nom", "enterName": "Entrer le nom", "searchByNumber": "Rechercher par numéro", "enterNumber": "<PERSON><PERSON><PERSON> le numéro", "selectClient": "Sélectionner un client", "clientsFound": "{{count}} clients trouvés", "searchByNameAbove": "Recherchez par nom ci-dessus", "noClientsFound": "Aucun client trouvé", "noAttachedGoals": "Aucun objectif attaché", "addingToGoal": "Ajout à l'objectif", "addToGoal": "Ajouter à l'objectif"}, "addContact": {"requiredFields": "Le numéro de téléphone, l'objectif et le pays sont obligatoires", "addNewContact": "Ajouter un nouveau contact", "name": "Nom", "country": "Pays", "phoneNumber": "Numéro de téléphone", "messageDrop": "Message Drop", "optional": "Optionnel", "dontSaveInDb": "Ne pas enregistrer dans la base clients.", "saveInDb": "Enregistrer dans la base clients.", "addingContact": "<PERSON><PERSON><PERSON>", "addContact": "A<PERSON>ter le contact"}, "organigramme": {"failedToLoadGoals": "Échec du chargement des objectifs.", "noGoalsFound": "Aucun objectif trouvé"}, "manageNumbers": {"manageNumbers": "<PERSON><PERSON><PERSON> les numéros", "addExistingNumber": "Ajouter un numéro existant à {{botName}}.", "add": "Ajouter", "noActiveNumbers": "Aucun numéro actif trouvé.", "requestNewNumber": "Demander un nouveau numéro.", "submitting": "envoi en cours...", "submitRequest": "Envoyer la demande"}, "phone": {"calls": "APPELS", "only": "SEULEMENT", "sms": "SMS", "whatsapp": "WHATSAPP", "status": {"active": "Actif", "inactive": "Inactif"}}, "createGoal": {"outboundCalling": "Appels sortants", "googleMeet": "Google Meet", "googleMeetIntegrationInfo": "Informations sur l'intégration Google Meet", "multiTranslation": "Traduction multi-directionnelle", "smsWhatsappNotifications": "Notifications SMS/WhatsApp", "comingSoon": "Bientôt disponible", "createGoal": "<PERSON><PERSON>er un objectif", "chooseHowToCreate": "Choisissez comment créer un objectif :", "chooseFromTemplate": "Choisir à partir d'un modèle"}, "goalCard": {"googleMeetIconAlt": "Icône Google Meet", "outgoingCall": "Appel sortant", "incomingCall": "A<PERSON> entrant"}, "pagination": {"previous": "Précédent", "next": "Suivant"}, "countriesSelect": {"selectCountry": "Sélectionner un pays"}, "select": {"selectAnOption": "Sélectionner une option", "searchPlaceholder": "Rechercher...", "noResultsFound": "Aucun résultat trouvé."}, "goalDropdown": {"goalOptions": "Options de l'objectif", "started": "<PERSON><PERSON><PERSON><PERSON>", "notStarted": "Non démarré", "pending": "En attente", "paused": "En pause", "stopped": "<PERSON><PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON>", "start": "<PERSON><PERSON><PERSON><PERSON>", "restart": "<PERSON><PERSON><PERSON><PERSON>", "resume": "Reprendre", "pause": "Pause", "stop": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Paramètres", "clients": "Clients", "addClient": "Ajouter un client", "addClients": "Ajouter des clients", "addExistingClient": "Ajouter un client existant", "oneClient": "Un client", "importList": "Importer une liste", "addExistingGroup": "Ajouter un groupe existant", "tags": "Tags", "noTags": "Aucun tag pour cet objectif"}, "createTag": {"fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs", "failedToCreate": "Échec de la création du tag", "createTag": "<PERSON><PERSON>er un tag", "createNewTag": "C<PERSON>er un nouveau tag", "tagName": "Nom du tag", "namePlaceholder": "Nom", "tagColor": "<PERSON><PERSON><PERSON> du <PERSON>", "cancel": "Annuler", "creatingTag": "Création du tag"}, "addTags": {"addTags": "Ajouter des tags", "addTagsToGoal": "Ajouter des tags à l'objectif", "selectTags": "Sélectionner des tags", "noAvailableTags": "Aucun tag disponible", "selectATag": "Sélectionner un tag", "removeTag": "Retirer {{tag}}", "cancel": "Annuler", "addingTags": "A<PERSON>t des <PERSON>"}, "duplicateGoal": {"duplicateGoal": "Dupliquer l'objectif", "confirmation": "Êtes-vous sûr de vouloir dupliquer cet objectif ?", "cancel": "Annuler", "success": "Objectif du<PERSON> avec succès !", "errorDuplicating": "Erreur lors de la duplication de l'objectif : {{error}}"}, "deleteGoal": {"archiveGoal": "Archiver l'objectif", "confirmation": "Êtes-vous sûr de vouloir archiver cet objectif ?", "cancel": "Annuler", "success": "Objectif archivé avec succès", "failed": "Échec de la suppression de l'objectif"}, "addAssistant": {"requestNewAssistant": "De<PERSON>er un nouvel assistant", "nameMinLength": "Le nom de l'assistant doit comporter au moins 3 caractères.", "createdAfterApproval": "L'assistant est créé après approbation de la demande (24h).", "assistantName": "Nom de l'assistant", "namePlaceholder": "Nom", "submitting": "envoi en cours...", "submitRequest": "Envoyer la demande"}, "pipWidget": {"liveStats": "📊 Statistiques en direct", "time": "TEMPS", "value": "VALEUR", "openPip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "closePip": "<PERSON><PERSON><PERSON>"}, "meetGoalClients": {"goalsClients": "Clients de l'objectif", "noClientsFound": "Aucun client trouvé pour cet objectif.", "dialInNumber": "Numéro <PERSON>"}, "createEditGoal": {"header": {"editTitle": "Mettre à jour l'objectif", "createTitle": "Créer un nouvel objectif", "googleMeetInfo": "Intégration Google Meet – Documentation", "transferOn": "Transfert ACTIVÉ", "transferOff": "Transfert DÉSACTIVÉ"}, "stats": {"totalDuration": "Durée totale", "allCalls": "Tous les appels", "calledClients": "Clients appelés", "missedCalls": "<PERSON><PERSON><PERSON> manq<PERSON>", "answeredCalls": "<PERSON><PERSON><PERSON> r<PERSON>us", "totalCost": "Coût total"}, "topSection": {"goalName": "Nom de l'objectif: ", "goalID": "ID de l'objectif: ", "goalClients": "Clients de l'objectif"}, "details": {"section": "Détails de l'objectif:", "goalName": "Nom de l'objectif", "goalNamePlaceholder": "Entrez le nom de l'objectif", "goalContext": "Contexte de l'objectif", "goalContextPlaceholder": "Ex: Vente d’ordinateurs", "targetCountry": "Pays ciblé", "incomingOutgoingSwitch": "Sélection des appels entrants/sortants", "incoming": "Appels entrants", "outgoing": "Appels sortants", "incomingInfo": "Les appels entrants sont ceux effectués par les clients vers votre assistant.", "outgoingOnlyInfo": "Vous ne pouvez créer que des objectifs d'appels sortants. Vous avez déjà un objectif d'appels entrants."}, "forwarding": {"section": "Transférer vers des groupes/membres Ringover :", "atLeastOne": "Au moins un est requis", "groupId": "ID du groupe", "groupIdPlaceholder": "Sélectionner l'ID du groupe", "taNumbers": "Numéros TA", "taNumbersPlaceholder": "Sélectionner un numéro TA"}, "prompt": {"section": "Prompt de conversation :", "viewScript": "Voir le Script", "fromDatabase": "Depuis la base de données", "fromComputer": "Depuis l’ordinateur", "uploadPrompt": "<PERSON><PERSON><PERSON><PERSON>r un prompt", "downloadExample": "Télécharger un exemple", "viewFile": "Voir le fichier", "notMuted": "Non muet", "muted": "<PERSON><PERSON>", "aiVoiceEnabled": "Voix IA activée", "aiVoiceDisabled": "Voix IA désactivée"}, "advanced": {"section": "Paramètres avancés", "durationBetweenCalls": "Du<PERSON>e entre plusieurs appels", "durationBetweenCallsPlaceholder": "Entrez la durée en secondes", "ringingDuration": "<PERSON><PERSON><PERSON> de sonnerie", "ringingDurationPlaceholder": "Entrez la durée de sonnerie en secondes", "messagesDrop": "Messages envoyés :", "noMessage": "Pas de message", "sms": "SMS", "whatsapp": "Whatsapp", "messagesDropInfo": "Configurez les messages envoyés à vos clients en cas d'appels manqués (optionnel)", "smsLimitInfo": "Un SMS standard autorise 160 caractères. L'utilisation d’emojis ou d’Unicode réduit la limite à 70 caractères.", "voicemailDrop": "Message vocal :", "noVoicemail": "Pas de message vocal", "text": "Texte", "audio": "Audio", "voicemailDropTextInfo": "Configurez ce que l'agent IA dira en cas de détection de répondeur", "voicemailDropAudioInfo": "Enregistrez ou téléversez un audio depuis votre appareil local.", "voicemailCharLimitInfo": "Un message vocal de 700 caractères équivaut à environ 1 minute d’audio. La durée peut varier selon la vitesse de parole et les pauses.", "humanIntroduction": "Introduction humaine :", "noIntroduction": "Pas d’introduction", "humanIntroTextInfo": "Configurez le message d’introduction avec un enregistrement humain.", "numberOfRetries": "Nombre de tentatives :", "pronounceClientName": "Prononcer le nom du client :", "pronounceClientNameInfo": "Choisissez si vous souhaitez mentionner le nom du client ou non.", "enabled": "Activé", "disabled": "Désactivé", "pronounceClientHonorific": "Prononcer le titre de civilité (IA) :", "pronounceClientHonorificInfo": "Choisissez si vous souhaitez mentionner le titre (M./Mme) ou non.", "messagePlaceholder": "Entrez votre message ici...", "charactersLabel": "caractères"}, "aiVoice": {"section": "Paramètres de voix IA préférés :", "description": "Choisissez dans les listes ci-dessous la voix IA souhaitée pour les deux genres (Homme/Femme).", "male": "<PERSON><PERSON>", "assistantNameForMale": "Nom de l’assistant (Homme)", "assistantNameForMalePlaceholder": "Ex : <PERSON>", "voiceId": "ID de la voix", "voiceIdPlaceholder": "Sélectionner l’ID de la voix", "female": "<PERSON>mme", "assistantNameForFemale": "Nom de l’assistante (Femme)", "assistantNameForFemalePlaceholder": "Ex : <PERSON>"}, "submit": {"createGoal": "Créer l'objectif", "creatingGoal": "Création de l'objectif", "editGoal": "Mettre à jour l'objectif", "editingGoal": "Mise à jour de l'objectif"}, "rangesSlider": {"selectTimeSlots": "Sélectionner des créneaux horaires", "totalHours": "{{hours}} <PERSON><PERSON>", "selectUpTo": "Sélectionnez jusqu’à 3 créneaux de disponibilité pour votre bot. Chaque créneau est fixe", "addSlot": "Ajouter un créneau", "slot": "Créneau {{index}} : {{start}} - {{end}}", "removeSlot": "Su<PERSON><PERSON><PERSON> le créneau", "doubleClickToAdd": "Double-cliquez sur la piste pour ajouter un nouveau créneau."}, "audioRecorder": {"startRecording": "Démarrer l'enregistrement", "pause": "Pause", "resume": "Reprendre", "uploadAudio": "Téléverser un fichier audio", "recording": "Enregistrement en cours...", "seconds": "{{seconds}}s"}, "scriptCard": {"usedByGoals": "{{count}} Objectifs", "uploadedAt": "Télévers<PERSON> le", "view": "Voir", "selected": "Sélectionné", "noName": "Sans nom"}}}