export interface CallStatusStat {
  titleKey: string;
  value: number;
  icon?: string;
  color?: string;
  subStats: Record<string, number | string>;
}

export interface User {
  _id: string;
  name?: string;
  lastname?: string;
  email: string;
  password?: string;
  phone?: string;
  connections?: (string | Connection)[];
  created_at?: Date;
  role: "ADMIN" | "ENTREPRISE_AGENT" | "ENTREPRISE_ADMIN";
  entreprise?: string | Entreprise;
  provider?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Connection {
  _id: string;
  user: string | User;
  ip?: string;
  userAgent?: string;
  device?: string;
  browser?: string;
  os?: string;
  location?: string;
  sessionId?: string;
  createdAt?: Date;
  endedAt?: Date;
  lastSeen?: Date;
  active?: boolean;
  online?: boolean;
}

export interface Goal {
  _id: string;
  name?: string;
  country?: string;
  GoalContext?: string;
  assistant?: string | User;
  status?: "PENDING" | "PAUSED" | "COMPLETED" | "STOPPED" | "STARTED";
  entreprise?: string | Entreprise;
  phoneNumber?: string;
  assistant_number?: string | PhoneNumber;
  goal_visibility?: "VISIBLE" | "HIDDEN";
  updated_at?: Date;
  created_at?: Date;
  goal_template_type?: "DEMARCHAGE" | "GOOGLE_MEET" | "MULTI_TRANSLATION";
}

export interface DemarchageGoal extends Goal {
  goal_template_type: "DEMARCHAGE";
  clients?: Array<{
    client_id?: string | User;
    status?:
      | "ANSWERED"
      | "NOT_CALLED"
      | "MISSED"
      | "VOICEMAIL_DETECTED"
      | "FAILED";
    calls_count?: number;
    name?: string;
    phone?: string;
    gender?: string;
    message_drop?: string;
    updated_at?: Date;
  }>;
  goalType?: "INCOMING" | "OUTGOING";
  conversations?: (string | Conversation)[];
  prompt?: string;
  tags?: Tag[];
  availability?: Array<{ start_time?: Date; end_time?: Date }>;
  retry_count?: number;
  forwarded_to_number?: string | PhoneNumber;
  forwarded_to_group_id?: string | Group;
  is_debug?: boolean;
  answered_conversations_ids?: (string | Conversation)[];
  voicemail_detected_conversations_ids?: (string | Conversation)[];
  failed_conversations_ids?: (string | Conversation)[];
  missed_conversations_ids?: (string | Conversation)[];
  incall_conversations_ids?: (string | Conversation)[];
  avoid_script_and_direct_transfer?: boolean;
  messages_on_missed_call?: "NONE" | "SMS" | "WHATSAPP";
  message_on_missed_call_content?: string;
  voicemail_drop_type?: "TEXT" | "AUDIO" | "NONE";
  voicemail_drop_content?: string;
  voicemail_drop_audio_url?: string;
  voicemail_drop_audio_duration?: number;
  is_pronounce_client_name_enabled?: boolean;
  is_pronounce_client_honorific_enabled?: boolean;
  ai_name_male?: string;
  ai_name_female?: string;
  duration_between_calls?: number;
  ringing_duration?: number;
  male_voice?: { id?: string | Voice; provider?: string };
  female_voice?: { id?: string | Voice; provider?: string };
  human_introduction_enabled?: boolean;
  human_introduction_url?: string;
  human_introduction_audio_duration: number;
  is_template?: boolean;
  template_id?: string | DemarchageGoal;
}

export interface GoogleMeetGoal extends Goal {
  goal_template_type: "GOOGLE_MEET";
  clients?: Array<{
    google_meet_dial_in_number?: string;
    google_meet_pin?: string;
  }>;
  enable_mute?: boolean;
  enable_AI_voice?: boolean;
  ai_name_male?: string;
  ai_name_female?: string;
  male_voice?: { id?: string | Voice; provider?: string };
  female_voice?: { id?: string | Voice; provider?: string };
  prompt?: string;
  status?: "PENDING" | "PAUSED" | "COMPLETED" | "STOPPED" | "STARTED";
}

export interface MultiTranslationGoal extends Goal {
  goal_template_type: "MULTI_TRANSLATION";
  clients?: Array<{
    client_id?: string | User;
    status?:
      | "ANSWERED"
      | "NOT_CALLED"
      | "MISSED"
      | "VOICEMAIL_DETECTED"
      | "FAILED";
    calls_count?: number;
    name?: string;
    phone?: string;
    gender?: string;
    updated_at?: Date;
  }>;
  conversations?: (string | Conversation)[];
  answered_conversations_ids?: (string | Conversation)[];
  voicemail_detected_conversations_ids?: (string | Conversation)[];
  failed_conversations_ids?: (string | Conversation)[];
  missed_conversations_ids?: (string | Conversation)[];
  incall_conversations_ids?: (string | Conversation)[];
  availability?: Array<{ start_time?: Date; end_time?: Date }>;
  retry_count?: number;
  duration_between_calls?: number;
  ringing_duration?: number;
}

export type AnyGoal = Partial<Goal> &
  Partial<DemarchageGoal> &
  Partial<GoogleMeetGoal> &
  Partial<MultiTranslationGoal>;

export interface PhoneNumber {
  _id: string;
  number?: string;
  country?: string;
  assistant?: string | User;
  status?: "ACTIVE" | "PENDING" | "INACTIVE";
  goals?: (string | AnyGoal)[];
  created_at?: Date;
  entreprise_id?: string | Entreprise;
  is_whatsapp?: boolean;
  is_sms?: boolean;
  is_calls?: boolean;
  goal_template_type: string;
}

export interface Conversation {
  _id: string;
  sid?: string;
  assistant_id?: string | User;
  assistant_name?: string;
  direction?: string;
  duration?: string;
  entreprise_id?: string | Entreprise;
  from_country?: string;
  from_number?: string;
  goal_id?: string | Goal;
  recording_sid?: string;
  status?: string;
  to_city?: string;
  to_client_id?: string | User;
  to_country?: string;
  to_name?: string;
  to_number?: string;
  transcript?: string;
  voicemail_detected?: boolean;
  who_hang_up?: string;
  initiated_time?: string;
  ringing_time?: string;
  inprogress_time?: string;
  completed_time?: string;
  child_sid?: string;
  child_id?: string | Conversation;
  child_name?: string;
  child_type?: string;
  parent_sid?: string;
  feelings?: string;
  merged_transcript?: any[];
  recording_status?: string;
  recording_url?: string;
  recording_duration?: string;
  summary?: string;
  transcript_whisper?: string;
  forward_start_time_in_audio?: string;
  type?: string;
  body?: string;
  is_voicemail_drop?: boolean;
  client_engagement?: number;
  total_speaking_time_seconds?: number;
  most_talkative_speaker_name?: string;
  speaker_durations_list?: any[];
  tags?: Tag[];
  notes?: string;
  created_at?: Date;
  isLiked?: boolean | null;
}

export interface Entreprise {
  _id: string;
  name?: string;
  assistants?: (string | User)[];
  admin?: string | User;
  agents?: (string | User)[];
  numbers?: (string | PhoneNumber)[];
  siret?: string;
  kbis?: string;
  sepa?: string;
  rib?: string;
  phone?: string;
  corpName?: string;
  country?: string;
  region?: string;
  street?: string;
  appartment?: string;
  legalRepresentantName?: string;
  legalRepresentantIdendity?: string;
  cpa?: string;
  field?: string;
  credit?: number;
  balance?: number;
  balanceAlertThreshold?: number;
  representantIsOwner?: boolean;
  stripe_customer_id?: string;
  payments?: (string | Payment)[];
  subscriptions?: (string | Subscription)[];
  current_subscription?: string | Subscription;
  invoices?: (string | Invoice)[];
  documents?: (string | Document)[];
  clients?: (string | User)[];
  tags?: (string | Tag)[];
  scripts?: (string | AssistantScript)[];
  aws_credentials?: {
    AWS_ACCESS_KEY_ID?: string;
    AWS_SECRET_ACCESS_KEY?: string;
    REGION?: string;
    BUCKET_NAME?: string;
  };
  created_at?: Date;
}

export interface Group {
  _id: string;
  name: string;
  members?: (string | User)[];
  date?: Date;
  entreprise?: string | Entreprise;
  created_at?: Date;
}

export interface Voice {
  _id: string;
  instructions?: Array<{
    displayed_instructions_title?: string;
    origin_instructions_title?: string;
    instructions_text?: string;
  }>;
  url?: string;
  displayed_ai_voice?: string;
  origin_ai_voice?: string;
  gender?: string;
  available?: boolean;
  price?: string;
  provider?: string;
}

export interface Tag {
  _id: string;
  name: string;
  color: string;
  created_at?: Date;
}

export interface AssistantScript {
  _id: string;
  name: string;
  content: string;
  createdAt?: Date;
  entreprise?: string | Entreprise;
  created_at?: Date;
}

export interface Document {
  _id: string;
  name?: string;
  originalName?: string;
  url?: string;
  size?: number;
  type?: string;
  category?: "billing" | "legal" | "other";
  entreprise_id?: string | Entreprise;
  created_at?: Date;
}

export interface Payment {
  _id: string;
  stripe_payment_intent_id?: string;
  stripe_session_id?: string;
  amount?: number;
  currency?: string;
  status?: "pending" | "completed" | "failed" | "refunded";
  description?: string;
  entreprise_id?: string | Entreprise;
  payment_method?: {
    type?: string;
    brand?: string;
    last4?: string;
  };
  paid_at?: Date;
  created_at?: Date;
  updated_at?: Date;
}

export interface PlanFeature {
  _id?: string;
  key: string;
  value?: string;
  description: string;
  included: boolean;
  note?: string;
}

export interface Plan {
  _id: string;
  name?: string;
  description?: string;
  billing_options?: {
    monthly?: {
      original_price: number;
      current_price: number;
      stripe_price_id?: string;
    };
    yearly?: {
      original_price: number;
      current_price: number;
      stripe_price_id?: string;
    };
    weekly?: {
      original_price: number;
      current_price: number;
      stripe_price_id?: string;
    };
    daily?: {
      original_price: number;
      current_price: number;
      stripe_price_id?: string;
    };
  };
  currency?: string;
  features?: PlanFeature[];
  stripe_product_id?: string;
  visible?: boolean;
  mostPopular?: boolean;
  sort_order?: number;
  created_at?: Date;
  updated_at?: Date;
  // Runtime fields added by API calls
  subscription_count?: number;
  active_subscription_count?: number;
}

export interface Subscription {
  _id: string;
  plan_id: string | Plan;
  entreprise_id: string | Entreprise;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  code?: string;
  status:
    | "active"
    | "past_due"
    | "unpaid"
    | "canceled"
    | "incomplete"
    | "incomplete_expired"
    | "trialing"
    | "paused";
  price_at_subscription: number;
  currency: string;
  billing_period: "monthly" | "yearly" | "weekly" | "daily";
  current_period_start: Date;
  current_period_end: Date;
  trial_start?: Date;
  trial_end?: Date;
  cancel_at_period_end: boolean;
  canceled_at?: Date;
  cancellation_reason?: string;
  usage?: Map<string, number>;
  payments?: (string | Payment)[];
  metadata?: Map<string, string>;
  created_at?: Date;
  updated_at?: Date;
}

export interface Assistant {
  _id: string;
  name?: string;
  goals?: (string | Goal)[];
  conversations?: (string | Conversation)[];
  status?: "ACTIVE" | "PENDING" | "INACTIVE";
  numbers?: (string | PhoneNumber)[];
  created_at?: Date;
}

export interface Invoice {
  _id: string;
  stripe_invoice_id: string;
  entreprise_id: string | Entreprise;
  subscription_id?: string | Subscription;
  stripe_customer_id: string;
  stripe_subscription_id?: string;
  status: "draft" | "open" | "paid" | "void" | "uncollectible" | "upcoming";
  amount_due: number;
  amount_paid: number;
  amount_remaining: number;
  subtotal: number;
  total: number;
  tax?: number;
  currency: string;
  description?: string;
  number?: string;
  created: Date;
  due_date?: Date;
  period_start?: Date;
  period_end?: Date;
  finalized_at?: Date;
  paid_at?: Date;
  voided_at?: Date;
  next_payment_attempt?: Date;
  attempt_count?: number;
  hosted_invoice_url?: string;
  invoice_pdf?: string;
  lines?: Array<{
    id?: string;
    amount?: number;
    currency?: string;
    description?: string;
    quantity?: number;
    unit_amount?: number;
    price?: {
      id?: string;
      nickname?: string;
      product?: string;
    };
    period?: {
      start?: Date;
      end?: Date;
    };
  }>;
  discount?: {
    coupon?: {
      id?: string;
      name?: string;
      percent_off?: number;
      amount_off?: number;
    };
    amount?: number;
  };
  default_payment_method?: {
    type?: string;
    brand?: string;
    last4?: string;
  };
  auto_advance?: boolean;
  collection_method?: "charge_automatically" | "send_invoice";
  metadata?: Record<string, string>;
  created_at?: Date;
  updated_at?: Date;
  plan_name?: string;
}

export interface PlanLink {
  _id: string;
  code?: string;
  plan_id?: string | Plan;
  uses_left?: number | null;
  createdAt?: Date;
  updatedAt?: Date;
}
