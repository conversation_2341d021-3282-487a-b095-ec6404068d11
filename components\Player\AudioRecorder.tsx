"use client";

import { useState, useRef, useEffect } from "react";
import CustomButton from "../CustomFormItems/Button";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";

export default function AudioRecorder({
  setAudioBlob,
  audioUrl,
  setAudioUrl,
  setDuration,
}: {
  setAudioBlob: any;
  audioUrl: string | null;
  setAudioUrl: any;
  setDuration: any;
}) {
  const { t } = useTranslation("assistants");
  const [recording, setRecording] = useState(false);
  const [paused, setPaused] = useState(false);
  const [seconds, setSeconds] = useState(0);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const handleAudioChange = (url: string, blob: Blob | null) => {
    setAudioUrl(url);
    setAudioBlob(blob);
    if (blob) {
      const audio = document.createElement("audio");
      audio.src = url;
      audio.load();
    }
  };

  useEffect(() => {
    if (recording && !paused) {
      timerRef.current = setInterval(() => {
        setSeconds((prev) => prev + 1);
      }, 1000);
    }

    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [recording, paused]);

  const startRecording = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    const mediaRecorder = new MediaRecorder(stream);
    mediaRecorderRef.current = mediaRecorder;
    audioChunksRef.current = [];

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunksRef.current.push(event.data);
      }
    };

    mediaRecorder.onstop = () => {
      const audioBlob = new Blob(audioChunksRef.current, {
        type: "audio/webm",
      });
      const url = URL.createObjectURL(audioBlob);
      setAudioUrl(url);
      setAudioBlob(audioBlob);
      handleAudioChange(url, audioBlob);
    };

    mediaRecorder.start();
    setRecording(true);
    setPaused(false);
    setSeconds(0);
  };

  const stopRecording = () => {
    mediaRecorderRef.current?.stop();
    mediaRecorderRef.current?.stream
      .getTracks()
      .forEach((track) => track.stop());
    setRecording(false);
    setPaused(false);
    setDuration(seconds);
    setSeconds(0);
    if (timerRef.current) clearInterval(timerRef.current);
  };

  const togglePause = () => {
    if (!mediaRecorderRef.current) return;
    if (paused) {
      mediaRecorderRef.current.resume();
    } else {
      mediaRecorderRef.current.pause();
    }
    setPaused(!paused);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith("audio/")) {
      const url = URL.createObjectURL(file);
      handleAudioChange(url, file);

      const audio = document.createElement("audio");
      audio.src = url;
      audio.load();
      audio.addEventListener("loadedmetadata", () => {
        if (!isNaN(audio.duration)) {
          setDuration(audio.duration);
        }
      });
    }
  };

  const rippleStyle =
    "absolute w-full h-full bg-white rounded-full animate-ripple";

  return (
    <div className="h-full">
      {recording ? (
        <div className="fixed inset-0 bg-accent bg-opacity-60 flex flex-col items-center justify-center z-50">
          <div className="relative flex flex-col items-center justify-center gap-4">
            {/* Ripple + Red Button Container */}
            <div className="relative w-32 h-32 flex items-center justify-center">
              {/* Ripple Effects */}
              <span className={`${rippleStyle} opacity-20 delay-0`} />
              <span className={`${rippleStyle} opacity-15 delay-300`} />
              <span className={`${rippleStyle} opacity-10 delay-600`} />

              {/* Red Recording Button */}
              <button
                onClick={stopRecording}
                className="w-24 h-24 bg-red-600 hover:bg-red-500 transition-all rounded-full text-white text-lg font-bold flex items-center justify-center z-10"
              >
                {t("createEditGoal.audioRecorder.seconds", { seconds })}
              </button>
            </div>

            {/* Pause Button */}
            <button
              onClick={togglePause}
              className="px-4 z-50 py-2 bg-foreground text-accent rounded-md text-sm font-medium hover:bg-gray-600 dark:hover:bg-gray-200"
            >
              {paused
                ? t("createEditGoal.audioRecorder.resume")
                : t("createEditGoal.audioRecorder.pause")}
            </button>
          </div>
        </div>
      ) : (
        <div className="w-full grid grid-cols-1 md:grid-cols-2 justify-center items-center gap-4">
          <CustomButton
            props={{
              onClick: startRecording,
              value: t("createEditGoal.audioRecorder.startRecording"),
              className:
                "w-full border-2 bg-voxa-neutral-100 hover:bg-voxa-neutral-200 border-voxa-teal-600 text-voxa-teal-600 hover:dark:bg-voxa-neutral-800",
            }}
          />
          <Input
            type="file"
            accept="audio/*"
            onChange={handleFileUpload}
            className="cursor-pointer w-full"
            aria-label={t("createEditGoal.audioRecorder.uploadAudio")}
          />
          {audioUrl && (
            <audio
              src={audioUrl}
              controls
              className="h-10 md:col-span-2 w-full bg-voxa-neutral-200 dark:bg-voxa-neutral-800 rounded-full"
              aria-label={t("createEditGoal.audioRecorder.playAudio")}
            />
          )}
        </div>
      )}
    </div>
  );
}
