"use server";

import connectToDatabase from "@/lib/mongodb";
import Business from "@/models/Entreprise";
import crypto from "crypto";
import { getEntrepriseByAdminID } from "./Entreprise";

const ENCRYPTION_KEY_RAW = process.env.AWS_CRED_ENCRYPT_KEY || "default_secret_key_32bytes_1234567890!";
const ENCRYPTION_KEY = Buffer.from(ENCRYPTION_KEY_RAW).slice(0, 32);
const IV_LENGTH = 16;

function encrypt(text: string) {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipheriv("aes-256-cbc", ENCRYPTION_KEY, iv);
  let encrypted = cipher.update(text);
  encrypted = Buffer.concat([encrypted, cipher.final()]);
  return iv.toString("hex") + ":" + encrypted.toString("hex");
}

function decrypt(text: string) {
  if (!text) return "";
  const [ivHex, encryptedHex] = text.split(":");
  const iv = Buffer.from(ivHex, "hex");
  const encryptedText = Buffer.from(encryptedHex, "hex");
  const decipher = crypto.createDecipheriv("aes-256-cbc", ENCRYPTION_KEY, iv);
  let decrypted = decipher.update(encryptedText);
  decrypted = Buffer.concat([decrypted, decipher.final()]);
  return decrypted.toString();
}

export async function saveAwsCredentials({
  AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY,
  REGION,
  BUCKET_NAME,
}: {
  AWS_ACCESS_KEY_ID: string;
  AWS_SECRET_ACCESS_KEY: string;
  REGION: string;
  BUCKET_NAME: string;
}) {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success || !entrepriseResponse.entreprise?._id) {
    return { error: "Could not determine current entreprise" };
  }
  const entrepriseId = entrepriseResponse.entreprise._id;
  await connectToDatabase();
  const encryptedCreds = {
    AWS_ACCESS_KEY_ID: encrypt(AWS_ACCESS_KEY_ID),
    AWS_SECRET_ACCESS_KEY: encrypt(AWS_SECRET_ACCESS_KEY),
    REGION: encrypt(REGION),
    BUCKET_NAME: encrypt(BUCKET_NAME),
  };
  await Business.findByIdAndUpdate(
    entrepriseId,
    { $set: { aws_credentials: encryptedCreds } },
    { upsert: true }
  );
  return { success: true };
}

export async function getAwsCredentialsByEntrepriseId() {
  const entrepriseResponse = await getEntrepriseByAdminID();
  if (!entrepriseResponse.success || !entrepriseResponse.entreprise?._id) return null;
  const entrepriseId = entrepriseResponse.entreprise._id;
  await connectToDatabase();
  const entreprise: any = await Business.findById(entrepriseId).lean();
  if (!entreprise || typeof entreprise !== 'object' || !('aws_credentials' in entreprise) || !entreprise.aws_credentials) return null;
  const creds = entreprise.aws_credentials;
  return {
    AWS_ACCESS_KEY_ID: decrypt(creds.AWS_ACCESS_KEY_ID),
    AWS_SECRET_ACCESS_KEY: decrypt(creds.AWS_SECRET_ACCESS_KEY),
    REGION: decrypt(creds.REGION),
    BUCKET_NAME: decrypt(creds.BUCKET_NAME),
  };
} 