import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import CustomInput from "../CustomFormItems/Input";
import { useState } from "react";
import { toast } from "sonner";
import { GetRagFilesSignedUrl } from "@/actions/UploadFiles";
import { SaveRagCollectionDetails } from "@/actions/CollectionActions";
import CustomSelect from "../CustomFormItems/Select";
import { useTranslation } from "react-i18next";

export function CreateRagCollectionSheet() {
  const { t } = useTranslation("rag");
  const ragTypeOptions = [
    {
      value: "image",
      label: [t("rag_type") + ": " + t("image", "Image"), "png, jpg, jpeg"],
      accept: ".png,.jpg,.jpeg",
    },
    {
      value: "text_document",
      label: [
        t("rag_type") + ": " + t("text_document", "Text Document"),
        "pdf, docx, txt",
      ],
      accept:
        ".docx,.pdf,.gdoc,.gslides,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    },
    {
      value: "video",
      label: [t("rag_type") + ": " + t("video", "Video"), "mkv, mp4, avi"],
      accept: ".mkv,.mp4,.avi,.mov,.webm",
    },
    {
      value: "url",
      label: [t("url_label"), "text"],
      accept: "",
    },
  ];

  const [ragType, setRagType] = useState("image");
  const [data, setData] = useState<{
    name: string;
    file: File | null;
    url?: string;
  }>({
    name: "",
    file: null,
    url: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.name === "file") {
      const file = e.target.files![0];
      const accept =
        ragTypeOptions.find((opt) => opt.value === ragType)?.accept || "";
      if (accept && file) {
        const allowed = accept
          .split(",")
          .map((ext) => ext.trim().toLowerCase());
        const fileName = file.name.toLowerCase();
        const fileType = file.type.toLowerCase();
        const matches = allowed.some((ext) =>
          ext.startsWith(".") ? fileName.endsWith(ext) : fileType === ext
        );
        if (!matches) {
          return toast.error(`Please upload a valid file type: ${accept}`);
        }
      }
      setData({
        ...data,
        file: file,
      });
      return;
    }
    if (e.target.name === "url") {
      setData({
        ...data,
        url: e.target.value,
      });
      return;
    }
    setData({
      ...data,
      [e.target.name]: e.target.value,
    });
  };

  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const handleSaveChanges = async () => {
    if (!data.name || (ragType === "url" ? !data.url : !data.file)) {
      toast.error("Please fill all fields");
      return;
    }
    setLoading(true);
    try {
      let fileUrl = "";
      if (ragType === "url") {
        const saveResponse = await SaveRagCollectionDetails(
          data.name,
          data.url!
        );
        if (saveResponse.success) {
          toast.success("Collection created successfully");
          setOpen(false);
          return;
        } else {
          toast.error(saveResponse.error || "Failed to create collection");
        }
      } else {
        const signedUrlResponse = await GetRagFilesSignedUrl(
          data.file!.name || "RAG_File",
          data.name
        );
        if (signedUrlResponse.failure !== undefined) {
          toast.error(signedUrlResponse.failure);
          return;
        }
        const url = signedUrlResponse.success.url;
        fileUrl = url.split("?")[0];
        await fetch(url, {
          method: "PUT",
          body: data.file!,
          headers: {
            "Content-Type": data.file!.type,
          },
        });
        const saveResponse = await SaveRagCollectionDetails(data.name, fileUrl);
        if (saveResponse.success) {
          toast.success("Collection created successfully");
          setOpen(false);
          return;
        } else {
          toast.error(saveResponse.error || "Failed to create collection");
        }
      }
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <Button
        className="flex max-md:w-full gap-1 text-sm justify-center items-center transition-all duration-150 text-white px-4 py-2 rounded-md bg-voxa-neutral-500 dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-800active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950"
        onClick={() => setOpen(true)}
      >
        {t("create")}
      </Button>
      <SheetContent>
        <SheetHeader>
          <SheetTitle className="dark:text-voxa-neutral-50 text-center text-xl mb-4">
            {t("new_collection")}
          </SheetTitle>
          <SheetDescription>{t("insert_details")}</SheetDescription>
        </SheetHeader>
        <div className="grid gap-4 py-4">
          <CustomInput
            props={{
              type: "text",
              label: t("name"),
              placeholder: t("rag_name_placeholder"),
              className: "w-full",
              required: true,
              name: "name",
              onChange: handleChange,
              value: data.name,
            }}
          />
          <CustomSelect
            label={t("rag_type")}
            value={ragType}
            onValueChange={setRagType}
            items={ragTypeOptions}
          />
          {ragType === "url" ? (
            <CustomInput
              props={{
                type: "text",
                label: t("url_label"),
                placeholder: t("url_placeholder"),
                className: "w-full",
                required: true,
                name: "url",
                onChange: handleChange,
                value: data.url,
              }}
            />
          ) : (
            <CustomInput
              props={{
                type: "file",
                label: t("upload_file"),
                className: "w-full",
                required: true,
                name: "file",
                accept: ragTypeOptions.find((opt) => opt.value === ragType)
                  ?.accept,
                onChange: handleChange,
              }}
            />
          )}
        </div>
        <SheetFooter>
          <Button
            onClick={handleSaveChanges}
            className={`mt-8 flex w-full gap-1 text-sm justify-center items-center transition-all duration-150 text-white px-4 py-2 rounded-md bg-voxa-teal-600 hover:bg-voxa-teal-400 dark:hover:bg-voxa-neutral-800active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950 ${
              loading ? "opacity-50 cursor-not-allowed" : ""
            }`}
            disabled={loading}
          >
            {loading ? t("creating_collection") : t("save_changes")}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
