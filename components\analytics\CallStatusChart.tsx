"use client";

import { useTheme } from "next-themes";
import { useEffect, useMemo, useRef } from "react";
import { Label, Pie, PieChart } from "recharts";
import useSWR from "swr";

import ExportDropdown, {
  ExportDropdownItem,
} from "@/components/analytics/ExportDropdown";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Skeleton } from "@/components/ui/skeleton";

import {
  exportChartAsImage,
  exportChartAsPDF,
} from "@/lib/analytics/chartExport";
import { CountryCode } from "@/lib/countries";
import {
  CallStatusStats,
  convertCountriesToUppercase,
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { store } from "@/redux/store";

const chartConfig = {
  "Clients Not Reached": { label: "Missed", color: "#e76e50" },
  "Answered No Transfer": { label: "Not Transferred", color: "#eab308" },
  "Transferred to Agents": { label: "Transferred", color: "#2a9d90" },
} satisfies ChartConfig;

// SWR fetcher for call status stats (local logic, not via redux thunk)
const fetcher = async (): Promise<CallStatusStats> => {
  // Get filters from store
  const filters = store.getState().analytics.filters;
  const {
    startDate,
    endDate,
    conversationTypes,
    countries,
    callDuration,
    ringingTime,
  } = filters;

  // Dynamically import getConversationsStats
  const { getConversationsStats } = await import("@/actions/AnalyticsActions");
  const res = await getConversationsStats(
    startDate ? new Date(startDate) : undefined,
    endDate ? new Date(endDate) : undefined,
    conversationTypes,
    countries.length
      ? convertCountriesToUppercase(countries as CountryCode[])
      : countries,
    callDuration,
    ringingTime
  );

  if (!res.success) {
    throw new Error(res.error || "Failed to fetch call status stats");
  }
  if (!("data" in res)) {
    throw new Error("No data returned from getConversationsStats");
  }
  return res.data as CallStatusStats;
};

export function CallStatusChart({
  isAnimationActive = true,
  setLoading,
}: {
  isAnimationActive?: boolean;
  setLoading?: (loading: boolean) => void;
}) {
  const { theme, systemTheme } = useTheme();
  const isDarkMode =
    theme === "dark" || (theme === "system" && systemTheme === "dark");

  const chartRef = useRef<HTMLDivElement>(null);

  // Define the expected type for call status stats

  // Use SWR to fetch call status stats
  const {
    data: callStatusStats,
    isLoading: callStatusLoading,
    error,
  } = useSWR<CallStatusStats>(
    ["callStatusStats", store.getState().analytics.filters],
    fetcher,
    {
      revalidateOnFocus: false,
      dedupingInterval: 10000,
    }
  );

  // Call setLoading when loading state changes
  useEffect(() => {
    if (setLoading) setLoading(callStatusLoading);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [callStatusLoading]);

  // build chart data
  const chartData = useMemo(() => {
    if (!callStatusStats) return [];
    return [
      {
        callStatus: "Clients Not Reached",
        count: callStatusStats.missed || 0,
        fill: chartConfig["Clients Not Reached"].color,
      },
      {
        callStatus: "Answered No Transfer",
        count: callStatusStats.notTransferred || 0,
        fill: chartConfig["Answered No Transfer"].color,
      },
      {
        callStatus: "Transferred to Agents",
        count: callStatusStats.transferred || 0,
        fill: chartConfig["Transferred to Agents"].color,
      },
    ];
  }, [callStatusStats]);

  const totalCalls = useMemo(
    () => chartData.reduce((sum, e) => sum + e.count, 0),
    [chartData]
  );

  // Define export items for the dropdown
  const exportItems: ExportDropdownItem[] = [
    {
      title: "Export as PNG",
      action: async () => {
        try {
          await exportChartAsImage(
            chartRef.current,
            "png",
            "call-status-chart"
          );
        } finally {
        }
      },
    },
    {
      title: "Export as SVG",
      action: async () => {
        try {
          await exportChartAsImage(
            chartRef.current,
            "svg",
            "call-status-chart"
          );
        } finally {
        }
      },
    },
    {
      title: "Export as PDF",
      action: async () => {
        try {
          await exportChartAsPDF(chartRef.current, "call-status-chart");
        } finally {
        }
      },
    },
  ];

  return (
    <div ref={chartRef} className="flex w-full">
      <Card className="bg-voxa-neutral-50 dark:bg-voxa-neutral-900 dark:border-voxa-neutral-700 transition-all duration-150 w-full">
        <CardHeader className="pb-0 flex flex-row items-start justify-between">
          <div>
            <CardTitle className="dark:text-voxa-neutral-50">
              Call Status Overview
            </CardTitle>
          </div>

          <ExportDropdown
            items={exportItems}
            buttonText="Export"
            loadingText="Exporting..."
            className=""
          />
        </CardHeader>

        <CardContent className="flex flex-wrap justify-center items-center gap-6">
          {callStatusLoading ? (
            <>
              <Skeleton className="aspect-square w-[200px] h-[200px] mx-auto" />
              <div className="flex flex-col justify-center gap-3 text-sm min-w-[180px]">
                <Skeleton className="h-6 w-32 mb-2" />
                <Skeleton className="h-6 w-32 mb-2" />
                <Skeleton className="h-6 w-32 mb-2" />
                <Skeleton className="h-6 w-32" />
              </div>
            </>
          ) : error ? (
            <div className="text-center text-red-500 p-4">
              Error loading call status data
            </div>
          ) : totalCalls === 0 ? (
            <>
              <div className="aspect-square w-[200px] mx-auto flex items-center justify-center">
                <div className="relative w-[160px] h-[160px] flex items-center justify-center">
                  {/* Outer circle with shadow */}
                  <div className="absolute inset-0 rounded-full bg-gray-300 dark:bg-gray-700 shadow-[0_2px_8px_rgba(0,0,0,0.15)] dark:shadow-[0_2px_8px_rgba(0,0,0,0.35)]"></div>
                  {/* Inner circle (creates hollow effect) - increased size for thinner stroke */}
                  <div className="absolute w-[120px] h-[120px] rounded-full bg-voxa-neutral-50 dark:bg-voxa-neutral-900 z-10"></div>
                  {/* Text in the middle */}
                  <div className="z-20 flex flex-col items-center justify-center">
                    <span className="text-2xl font-bold text-foreground">
                      0
                    </span>
                    <span className="text-sm text-muted-foreground">Total</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col justify-center gap-3 text-sm min-w-[180px]">
                {Object.entries(chartConfig).map(([key, cfg]) => (
                  <div
                    key={key}
                    className="flex items-center justify-between gap-2"
                  >
                    <div className="flex items-center gap-2">
                      <span
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: cfg.color }}
                      />
                      <span className="text-foreground">{cfg.label}</span>
                    </div>
                    <div className="flex gap-1">
                      <span className="rounded-md px-2 py-0.5 w-12 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-foreground">
                        0
                      </span>
                      <span className="rounded-md px-2 py-0.5 w-12 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-muted-foreground">
                        0.0%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <>
              <ChartContainer
                config={chartConfig}
                className="aspect-square w-[200px] mx-auto"
              >
                <PieChart>
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent hideLabel />}
                  />
                  <Pie
                    data={chartData}
                    dataKey="count"
                    nameKey="callStatus"
                    innerRadius={60}
                    strokeWidth={0} // Remove stroke
                    isAnimationActive={isAnimationActive}
                  >
                    <Label
                      content={({ viewBox }) => {
                        if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                          return (
                            <text
                              x={viewBox.cx}
                              y={viewBox.cy}
                              textAnchor="middle"
                              dominantBaseline="middle"
                            >
                              <tspan
                                x={viewBox.cx}
                                y={viewBox.cy}
                                style={{
                                  fill: isDarkMode ? "#ffffff" : "#000000",
                                  fontSize: "24px",
                                  fontWeight: "bold",
                                }}
                              >
                                {totalCalls.toLocaleString()}
                              </tspan>
                              <tspan
                                x={viewBox.cx}
                                y={(viewBox.cy || 0) + 24}
                                style={{
                                  fill: isDarkMode ? "#aaaaaa" : "#888888",
                                  fontSize: "14px",
                                }}
                              >
                                Total
                              </tspan>
                            </text>
                          );
                        }
                      }}
                    />
                  </Pie>
                </PieChart>
              </ChartContainer>

              <div className="flex flex-col justify-center gap-3 text-sm min-w-[180px]">
                {chartData.map((entry) => {
                  const cfg =
                    chartConfig[entry.callStatus as keyof typeof chartConfig];
                  const percent =
                    totalCalls > 0
                      ? ((entry.count / totalCalls) * 100).toFixed(1)
                      : "0.0";
                  return (
                    <div
                      key={entry.callStatus}
                      className="flex items-center justify-between gap-2"
                    >
                      <div className="flex items-center gap-2">
                        <span
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: cfg.color }}
                        />
                        <span className="text-foreground">{cfg.label}</span>
                      </div>
                      <div className="flex gap-1">
                        <span className="rounded-md px-2 py-0.5 w-12 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-foreground">
                          {entry.count.toLocaleString()}
                        </span>
                        <span className="rounded-md px-2 py-0.5 w-12 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-muted-foreground">
                          {percent}%
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
