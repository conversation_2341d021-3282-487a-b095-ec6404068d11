"use server"

import Rag<PERSON>ollection from "@/models/RagCollection"
import { getEntrepriseByAdminID } from "./Entreprise"
import { DeleteObjectCommand } from "@aws-sdk/client-s3";
import {BusinessFilesS3} from "@/lib/S3Client"

import mongoose from "mongoose"
export async function SaveRagCollectionDetails(name: string, fileUrl: string): Promise<{success: boolean, error?: string}>{
    try{
        const response = await getEntrepriseByAdminID()
        if(!response.success) return {success: false, error: response.error}
        const entrepriseID = response.entreprise._id
        const newCollection = await RagCollection.create({name, file: fileUrl, entreprise: entrepriseID})

        if(!newCollection) return {success: false, error: "Failed to save collection details"}

        return {success: true}
    }catch(err: any){
        return {success: false, error: err.message}
    }
}

export async function GetRagCollections(): Promise<{success: boolean, collections?: any[], error?: string}>{
    try{
        const response = await getEntrepriseByAdminID()
        if(!response.success) return {success: false, error: response.error}
        const entrepriseID = response.entreprise._id
        const collections = await RagCollection.find({entreprise: entrepriseID})

        if(!collections) return {success: false, error: "Failed to fetch collections"}
        const parsedCollections = collections.map(collection => {
            return {
                _id: collection._id.toString(),
                name: collection.name,
                file: collection.file,
                createdAt: collection.createdAt
            }
        })
        return {success: true, collections: parsedCollections}
    }catch(err: any){
        return {success: false, error: err.message}
    }
}


export async function DeleteRagCollectionByID(id: string): Promise<{ success: boolean; error?: string }> {
    try {
        const collectionID = new mongoose.Types.ObjectId(id);
        
        const collection = await RagCollection.findById(collectionID);
        if (!collection) return { success: false, error: "Collection not found" };

        const fileUrl = collection.file;
        if (!fileUrl) return { success: false, error: "File URL missing in collection" };

        const s3Key = fileUrl.split("amazonaws.com/")[1];

        const deleteCommand = new DeleteObjectCommand({
            Bucket: process.env.AWS_BUCKET_NAME!,
            Key: s3Key,
        });

        await BusinessFilesS3.send(deleteCommand);

        const response = await RagCollection.findByIdAndDelete(collectionID);
        if (!response) return { success: false, error: "Failed to delete collection" };

        return { success: true };
    } catch (err: any) {
        return { success: false, error: err.message };
    }
}

export async function GetEntrepriseRagCollectionLinks(): Promise<{success: boolean, links?: string[], error?: string}>{
    try{
        const response = await getEntrepriseByAdminID()
        if(!response.success) return {success: false, error: response.error}
        const entrepriseID = response.entreprise._id
        const collections = await RagCollection.find({entreprise: entrepriseID})

        if(!collections) return {success: false, error: "Failed to fetch collections"}
        const links = collections.map(collection => collection.file)
        return {success: true, links}
    }catch(err: any){
        return {success: false, error: err.message}
    }

}