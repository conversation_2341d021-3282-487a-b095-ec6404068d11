import dbConnect from "@/lib/mongodb";
import Group from "@/models/Groups";
import Entreprise from "@/models/Entreprise";
import { User } from "@/models/User";
import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { verifyUserToken } from "@/lib/cognito/Token";
import CRM from "@/models/CRM";

export async function GET(req: Request) {
  try {
    await dbConnect();

    const cookieStore = cookies();
    const token = (await cookieStore).get("plugin_id_token")?.value;

    console.log("Token from cookies:", token);

    if (!token) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    // ✅ Verify Cognito token
    const { valid, payload, error } = await verifyUserToken(token);

    if (!valid || !payload?.email) {
      return NextResponse.json(
        {
          error: "Token invalide ou expiré.",
          detail: error?.message || "Token non valide.",
        },
        { status: 401 }
      );
    }

    // ✅ Find user by email
    const user = await User.findOne({ email: payload.email });
    if (!user) {
      return NextResponse.json({ error: "Utilisateur introuvable." }, { status: 404 });
    }

    // ✅ Find entreprise by user ID
    const entreprise = await Entreprise.findOne({ admin: user._id });
    if (!entreprise) {
      return NextResponse.json({ error: "Entreprise introuvable." }, { status: 404 });
    }

    const crm = await CRM.findOne({ entreprise: entreprise._id, name: "Ringover" });
    if (!crm) {
      return NextResponse.json({ error: "Aucune configuration Ringover trouvée pour cette entreprise." }, { status: 404 });
    }
    const baseUrl = crm.baseUrl
    const apiKey = crm.apiKey;
    
    const ringoverGroupsResponse = await fetch(`${baseUrl}/groups`, {
      method: "GET",
        headers: {
            "Content-Type": "application/json",
            Authorization: apiKey,
        }
    });

    if(ringoverGroupsResponse.status !== 200) {
      const errorData = await ringoverGroupsResponse.json();
        return NextResponse.json({ error: errorData.message || "Erreur lors de la récupération des groupes Ringover." }, { status: ringoverGroupsResponse.status });
    }
    const data = await ringoverGroupsResponse.json();
    const groups = data.list.map((group: any) => ({
      id: group.id,
      name: group.name,
      description: group.description,
      members: group.members || [],
    }));

    return NextResponse.json({ groups }, { status: 200 });

  } catch (err: any) {
    console.error("Erreur serveur:", err);
    return NextResponse.json({ error: "Erreur interne du serveur." }, { status: 500 });
  }
}
