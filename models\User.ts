import mongoose from "mongoose";

enum Role {
  ADMIN = "ADMIN",
  ENTREPRISE_AGENT = "ENTREPRISE_AGENT",
  ENTREPRISE_ADMIN = "ENTREPRISE_ADMIN",
}

enum ProvidersEnum {
  GOOGLE_PROVIDER,
  MICROSOFT_PROVIDER,
  CREDENTIALS_PROVIDER,
}

const User =
  mongoose.models.User ||
  mongoose.model(
    "User",
    new mongoose.Schema(
      {
        name: { type: String },
        lastname: { type: String },
        email: { type: String, unique: true },
        password: { type: String },
        phone: { type: String },
        connections: [
          {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Connection",
          },
        ],
        created_at: {
          type: Date,
          default: Date.now,
        },
      },
      { discriminatorKey: "role", timestamps: true }
    )
  );

const AdminSchema = new mongoose.Schema({});

const EntrepriseAgentSchema = new mongoose.Schema({
  entreprise: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Entreprise",
  },
});

const EntrepriseAdminSchema = new mongoose.Schema({
  entreprise: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Entreprise",
  },
  provider: {
    type: String,
    enum: Object.values(ProvidersEnum),
  },
});

const Admin =
  mongoose.models.Admin ||
  User.discriminators?.[Role.ADMIN] ||
  User.discriminator(Role.ADMIN, AdminSchema);
const EntrepriseAgent =
  mongoose.models.EntrepriseAgent ||
  User.discriminators?.[Role.ENTREPRISE_AGENT] ||
  User.discriminator(Role.ENTREPRISE_AGENT, EntrepriseAgentSchema);
const EntrepriseAdmin =
  mongoose.models.EntrepriseAdmin ||
  User.discriminators?.[Role.ENTREPRISE_ADMIN] ||
  User.discriminator(Role.ENTREPRISE_ADMIN, EntrepriseAdminSchema);

export { User, Admin, EntrepriseAgent, EntrepriseAdmin, Role };
