"use client";

import CustomButton from "@/components/CustomFormItems/Button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Toolt<PERSON>,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { featurePeriodMap, formatBillingPeriod } from "@/lib/billingUtils";
import { renderTemplate } from "@/lib/renderTemplate";
import { Plan } from "@/types";
import { Check, Crown, Info, Star, Zap } from "lucide-react";

interface PlanCardProps {
  plan: Plan;
  billingPeriod: string;
  isSelected: boolean;
  onSelectPlan: (planId: string) => void;
}

export default function PlanCard({
  plan,
  billingPeriod,
  isSelected,
  onSelectPlan,
}: PlanCardProps) {
  const isMostPopular = plan.mostPopular;

  // Get pricing for the selected billing period
  const billingOption =
    plan.billing_options?.[billingPeriod as keyof typeof plan.billing_options];
  const hasDiscount =
    billingOption && billingOption.original_price > billingOption.current_price;

  // If no pricing for this period, don't render the card
  if (!billingOption || billingOption.current_price <= 0) {
    return null;
  }

  const formatPrice = (price: number, currency: string) => {
    const symbol = currency === "eur" ? "€" : "$";
    return `${symbol}${price}`;
  };

  return (
    <Card
      className={`flex flex-col relative transition-all duration-300 hover:shadow-xl group hover:scale-[1.02] ${
        isMostPopular
          ? "border-voxa-teal-500 shadow-lg bg-gradient-to-b from-voxa-teal-50/50 to-white dark:from-voxa-teal-900/20 dark:to-gray-900"
          : "border-sidebar-border hover:border-voxa-teal-500/50 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50"
      }`}
    >
      {/* Popular Badge */}
      {isMostPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 text-white px-3 py-1 shadow-lg">
            <Star className="w-3 h-3 mr-1" />
            Most Popular
          </Badge>
        </div>
      )}

      <CardHeader className="pb-4 relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-voxa-teal-50/20 to-transparent dark:from-voxa-teal-900/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-t-lg" />

        <div className="relative z-10">
          <CardTitle className="text-xl mb-2 flex items-center gap-2">
            {plan.name}
            {isMostPopular && <Crown className="w-5 h-5 text-voxa-teal-600" />}
          </CardTitle>
          <p className="text-sm text-muted-foreground">{plan.description}</p>
        </div>
      </CardHeader>

      <CardContent className="grow relative">
        <div className="flex flex-col h-full relative z-10">
          {/* Pricing */}
          <div className="mb-6">
            <div className="flex items-baseline gap-2">
              {hasDiscount && (
                <span className="text-lg text-muted-foreground line-through">
                  {formatPrice(billingOption.original_price, plan.currency!)}
                </span>
              )}
              <span className="text-3xl font-bold text-voxa-teal-600">
                {formatPrice(billingOption.current_price, plan.currency!)}
              </span>
            </div>
            <p className="text-sm text-muted-foreground">
              {formatBillingPeriod(billingPeriod)}
            </p>
            {hasDiscount && (
              <Badge className="mt-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Save{" "}
                {formatPrice(
                  billingOption.original_price - billingOption.current_price,
                  plan.currency!
                )}
              </Badge>
            )}
          </div>

          {/* Features */}
          <div className="grow space-y-3 mb-6">
            {plan.features?.map((feature: any, index: number) => {
              // Prepare variables for template rendering
              const variables = {
                value: feature.value || "",
                months: featurePeriodMap[billingPeriod]?.months ?? "",
                days: featurePeriodMap[billingPeriod]?.days ?? "",
              };
              return (
                <div key={index} className="flex items-center gap-3">
                  <div
                    className={`w-5 h-5 rounded-full flex items-center justify-center ${
                      feature.included
                        ? "bg-voxa-teal-100 dark:bg-voxa-teal-900/30"
                        : "bg-muted"
                    }`}
                  >
                    <Check
                      className={`w-3 h-3 ${
                        feature.included
                          ? "text-voxa-teal-600 dark:text-voxa-teal-400"
                          : "text-muted-foreground"
                      }`}
                    />
                  </div>
                  <span
                    className={`text-sm flex-1 ${
                      feature.included
                        ? "text-foreground"
                        : "text-muted-foreground"
                    }`}
                  >
                    {renderTemplate(feature.description, variables)}
                  </span>
                  {feature.note && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="w-4 h-4 text-muted-foreground hover:text-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">{feature.note}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
              );
            })}
          </div>

          {/* Select Button */}
          <CustomButton
            props={{
              value: isSelected
                ? "Selected"
                : isMostPopular
                ? "Subscribe Now"
                : "Choose Plan",
              onClick: () => onSelectPlan(plan._id),
              className: `w-full transition-all duration-300 ${
                isMostPopular
                  ? "bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 hover:from-voxa-teal-700 hover:to-voxa-teal-800 text-white shadow-lg hover:shadow-xl"
                  : isSelected
                  ? "bg-voxa-teal-600 text-white"
                  : "bg-gradient-to-r from-voxa-neutral-100 to-voxa-neutral-200 dark:from-voxa-neutral-800 dark:to-voxa-neutral-700 hover:from-voxa-teal-50 hover:to-voxa-teal-100 dark:hover:from-voxa-teal-900/30 dark:hover:to-voxa-teal-800/50 text-foreground hover:text-voxa-teal-700 dark:hover:text-voxa-teal-300 border border-voxa-neutral-300 dark:border-voxa-neutral-600 hover:border-voxa-teal-300 dark:hover:border-voxa-teal-600"
              }`,
              icon: isMostPopular ? (
                <Zap className="w-4 h-4 ml-2" />
              ) : undefined,
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
