import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";

export function SignupTabs() {
  const router = useRouter();

  const [formData, setFormData] = useState({
    name: "",
    siret: "",
    email: "",
    field: "",
    password: "",
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.id]: e.target.value });
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const res = await fetch("/api/auth/signup/entreprise", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!res.ok) {
        throw new Error("Failed to create account");
      }
      const data = await res.json();

      localStorage.setItem("accessToken", data.accessToken);
      localStorage.setItem("refreshToken", data.refreshToken);

      router.push("/dashboard");
    } catch (error) {
      console.error("Signup error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Tabs defaultValue="entreprise" className="w-[400px]">
      <TabsList className="grid w-full grid-cols-2 bg-black">
        <TabsTrigger value="entreprise" className="text-white py-2">
          Entreprise
        </TabsTrigger>
        <TabsTrigger value="agent" className="text-white py-2">
          Agent
        </TabsTrigger>
      </TabsList>
      <TabsContent value="entreprise" className="mt-2">
        <Card className="bg-black border-voxa-neutral-700">
          <CardHeader>
            <CardTitle className="text-white">Entreprise Account</CardTitle>
            <CardDescription>
              Create an account for your entreprise.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2 dark:text-voxa-neutral-200">
            <div className="space-y-1">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={handleChange}
                className="border-voxa-neutral-700 focus:border-white"
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="siret">SIRET</Label>
              <Input
                id="siret"
                value={formData.siret}
                onChange={handleChange}
                className="border-voxa-neutral-700 focus:border-white"
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                className="border-voxa-neutral-700 focus:border-white"
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="field">Field</Label>
              <Input
                id="field"
                value={formData.field}
                onChange={handleChange}
                className="border-voxa-neutral-700 focus:border-white"
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                className="border-voxa-neutral-700 focus:border-white"
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-center items-center">
            <Button onClick={handleSubmit} disabled={loading}>
              {loading ? "Creating..." : "Create Account"}
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="agent">
        <Card className="bg-black border-voxa-neutral-700">
          <CardHeader>
            <CardTitle className="text-white">Agent Account</CardTitle>
            <CardDescription>
              Create an Agent account to join the entreprise.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2 dark:text-voxa-neutral-200">
            <div className="space-y-1">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                className="border-voxa-neutral-700 focus:border-white"
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="username">Entreprise SIRET</Label>
              <Input
                id="username"
                className="border-voxa-neutral-700 focus:border-white"
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                className="border-voxa-neutral-700 focus:border-white"
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="password">password</Label>
              <Input
                id="password"
                className="border-voxa-neutral-700 focus:border-white"
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-center items-center">
            <Button> Create Account </Button>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
