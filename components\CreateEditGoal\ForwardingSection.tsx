import React, { useEffect } from "react";
import CustomSelect from "@/components/CustomFormItems/Select";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  setGroupId,
  setForwardToNumber,
  setAgents,
  setGroups,
  setRingoverApiKey,
  setRingoverBaseUrl,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";
import { GetEntrepriseRingoverParams } from "@/actions/CRMActions";
import { getTaAgents } from "@/actions/TaActions";
import { toast } from "sonner";

export const ForwardingSection: React.FC = () => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const {
    ringoverBaseUrl,
    ringoverApiKey,
    groupId,
    forwardToNumber,
    groups,
    agents,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const params = await GetEntrepriseRingoverParams();
        if (params.apiKey) dispatch(setRingoverApiKey(params.apiKey));
        if (params.baseUrl) dispatch(setRingoverBaseUrl(params.baseUrl));
      } catch (err: any) {
        toast.error(err.message);
      }
    };
    fetchInitialData();
  }, [dispatch]);

  useEffect(() => {
    const fetchGroups = async () => {
      try {
        const response = await fetch(`${ringoverBaseUrl}/groups`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: ringoverApiKey,
          },
        });
        if (response.status !== 200) throw new Error("Failed to fetch groups");
        const data = await response.json();
        dispatch(
          setGroups(
            data.list.map((group: any) => ({
              id: group.group_id,
              name: group.name,
            }))
          )
        );
      } catch (err: any) {
        toast.error(err.message);
      }
    };
    if (ringoverApiKey && ringoverBaseUrl) fetchGroups();
  }, [ringoverApiKey, ringoverBaseUrl, dispatch]);

  useEffect(() => {
    const fetchEntrepriseAgents = async () => {
      try {
        const response = await getTaAgents();
        if (!response.success) throw new Error(response.error);
        dispatch(setAgents(response.agents || []));
      } catch (err: any) {
        toast.error(err.message);
      }
    };
    fetchEntrepriseAgents();
  }, [dispatch]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <span className="-mb-4 md:col-span-2 text-lg font-semibold">
        {t("createEditGoal.forwarding.section")}&nbsp;
        <span className="text-red-500">*</span>&nbsp;
        <span className="md:text-sm text-xs text-voxa-neutral-700">
          {t("createEditGoal.forwarding.atLeastOne")}
        </span>
      </span>
      <CustomSelect
        label={t("createEditGoal.forwarding.groupId")}
        value={groupId}
        onValueChange={(value) => dispatch(setGroupId(value))}
        placeholder={t("createEditGoal.forwarding.groupIdPlaceholder")}
        items={groups.map((group) => ({
          value: group.id.toString(),
          label: [group.id, group.name],
        }))}
      />
      <CustomSelect
        label={t("createEditGoal.forwarding.taNumbers")}
        value={forwardToNumber}
        onValueChange={(value) => dispatch(setForwardToNumber(value))}
        placeholder={t("createEditGoal.forwarding.taNumbersPlaceholder")}
        items={agents.map((agent) => ({
          value: agent.phone,
          label: [agent.phone, agent.name],
        }))}
      />
    </div>
  );
};
