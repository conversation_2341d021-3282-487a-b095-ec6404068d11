import { cn } from "@/lib/utils";

const hexToRGBA = (hex: string, opacity: number) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

type DetailedStatsCardProps = {
  title: string;
  value?: number;
  icon: React.ReactNode;
  color: string;
  subStats?: { [key: string]: number };
  str?: string;
  className?: string;
};

export default function CallLogsStatsCard({
  title,
  value,
  icon,
  color,
  subStats,
  str,
  className,
}: DetailedStatsCardProps) {
  return (
    <div
      className={cn(
        "rounded-xl dark:shadow-md p-6 pt-4.5 space-y-3 w-full hover:scale-x-102 hover:scale-y-104",
        className
      )}
    >
      {/* Header with Icon and Title */}
      <div className="flex h-10 items-center gap-2 text-voxa-secondary">
        <div
          className="w-fit h-fit p-2.5 flex items-center justify-center rounded-full"
          style={{ backgroundColor: hexToRGBA(color, 0.2) }}
        >
          {icon}
        </div>
        <h1 className="text-xl md:text-lg font-semibold text-voxa-neutral-600 dark:text-voxa-neutral-50">
          {title}
        </h1>
      </div>

      {/* Main Value */}
      <h1 className="text-3xl h-8 font-medium ml-2 text-start break-words text-forground/70 dark:text-voxa-neutral-100 text-voxa-neutral-500">
        {str ?? value}
      </h1>

      {/* Sub Stats */}
      <div className="mt-2 h-8 flex flex-col gap-1 text-sm dark:text-voxa-neutral-200 text-voxa-neutral-500 whitespace-nowrap sm:justify-between">
        {subStats && Object.keys(subStats).length > 0 && (
          <>
            {Object.entries(subStats).map(([key, val]) => (
              <div key={key} className="flex justify-between gap-4">
                <span className="capitalize">{key}</span>
                <span className="font-semibold ">{val}</span>
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  );
}
