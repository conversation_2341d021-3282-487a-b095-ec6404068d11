import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>it<PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@radix-ui/react-label";
import AddRoundedIcon from "@mui/icons-material/AddRounded";
import { useState } from "react";
import { toast } from "sonner";
import ButtonLoader from "../Loaders/ButtonLoader";
import CustomInput from "../CustomFormItems/Input";
import { CountriesSelect } from "../dropdowns/CountriesSelect";
import {
  setAddContact,
  setName,
  setCountry,
  setNumber,
  CreateNewClient,
} from "@/redux/BusinessDashboard/subSlices/ClientsSlice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { CountryCode } from "libphonenumber-js";
import { Button } from "../ui/button";

export default function AddContact() {
  const { Name, phoneNumber, country, addContact } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardClients
  );

  const dispatch = useDispatch<AppDispatch>();
  const [Loading, setLoading] = useState(false);

  const handleCreateNewClient = async () => {
    if (phoneNumber === "" || country === "") {
      toast.error("Phone number and Country are required");
      return;
    }
    setLoading(true);
    await dispatch(CreateNewClient());
    setLoading(false);
  };

  return (
    <Dialog
      open={addContact}
      onOpenChange={(open) => dispatch(setAddContact(open))}
    >
      <DialogTrigger asChild>
        <button
          onClick={() => dispatch(setAddContact(true))}
          className="max-md:w-full text-nowrap text-sm bg-voxa-neutral-500 dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-800active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950 transition-all duration-150 text-white pl-2 pr-4 py-2 rounded-md font-medium flex justify-center items-center gap-1"
        >
          <AddRoundedIcon />
          Add Contact
        </button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>Add new Contact</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col gap-3 mt-4">
          <CustomInput
            props={{
              label: "Name",
              name: "Name",
              placeholder: "Name",
              value: Name,
              onChange: (e) => dispatch(setName(e.target.value)),
            }}
          />
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="Name" className="text-sm font-medium">
              Country <span className="text-red-500">*</span>
            </Label>
            <CountriesSelect
              country={country}
              selectCountry={(newCountry: "" | CountryCode) =>
                dispatch(setCountry(newCountry))
              }
            />
          </div>
          <CustomInput
            props={{
              label: "Phone Number",
              name: "number",
              placeholder: "Phone Number",
              value: phoneNumber,
              onChange: (e) => dispatch(setNumber(e.target.value)),
              required: true,
            }}
          />
        </div>

        <DialogFooter className="mt-8">
          <Button
            onClick={handleCreateNewClient}
            disabled={Loading}
            className={
              Loading
                ? "w-full cursor-not-allowed"
                : "w-full text-voxa-neutral-50 bg-voxa-teal-600 hover:bg-voxa-teal-500"
            }
          >
            {Loading ? (
              <>
                Adding Contact
                <ButtonLoader />
              </>
            ) : (
              "Add Contact"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
