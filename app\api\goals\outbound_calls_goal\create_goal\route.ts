import dbConnect from "@/lib/mongodb";
import Entreprise from "@/models/Entreprise";
import { User } from "@/models/User";
import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { verifyUserToken } from "@/lib/cognito/Token";
import { CreateDemarchageGoal } from "@/actions/GoalActions";

export async function POST(req: Request) {
  try {
    await dbConnect();

    const cookieStore = cookies();
    const token = (await cookieStore).get("plugin_id_token")?.value;

    console.log("Token from cookies:", token);

    if (!token) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    // ✅ Verify Cognito token
    const { valid, payload, error } = await verifyUserToken(token);

    if (!valid || !payload?.email) {
      return NextResponse.json(
        {
          error: "Token invalide ou expiré.",
          detail: error?.message || "Token non valide.",
        },
        { status: 401 }
      );
    }

    let body;
    try {
        body = await req.json();
    } catch (parseError) {
        console.error("Erreur de parsing de la requête:", parseError);
        return NextResponse.json(
        { error: "Requête mal formée. Veuillez envoyer un corps JSON valide." },
        { status: 400 }
        );
    }

    const {
        name,
        assistantID,
        prompt,
        country,
        numberID,
        IncomingSelected,
        goalContext,
        groupid,
        NumberRetries,
        ranges,
        ForwardToNumber,
        incoming_call_transfer_on,
        messages_on_missed_call,
        message_on_missed_call_content,
        voicemail_drop_type,
        voicemail_drop_content,
        goal_assistant_name_for_male,
        goal_assistant_name_for_female,
        audio_blob,
        audioDuration,
        pronounce_client_name_enabled,
        pronounce_client_honorific_enabled,
        duration_between_calls,
        ringing_duration,
        male_voice,
        female_voice,
        human_introduction_audio_blob,
        human_introduction_audio_duration,
        human_introduction_enabled
    } = body

    console.log("Received body:", body);

    // ✅ Find user by email
    const user = await User.findOne({ email: payload.email });
    if (!user) {
      return NextResponse.json({ error: "Utilisateur introuvable." }, { status: 404 });
    }

    // ✅ Find entreprise by user ID
    const entreprise = await Entreprise.findOne({ admin: user._id });
    if (!entreprise) {
      return NextResponse.json({ error: "Entreprise introuvable." }, { status: 404 });
    }

    // Example: Replace the following with actual values from your request or context
    const response = await CreateDemarchageGoal({
      name,
      assistantID,
      prompt,
      country,
      numberID,
      IncomingSelected: false,
      goalContext,
      groupid,
      retry_count: NumberRetries,
      ranges,
      ForwardToNumber,
      incoming_call_transfer_on,
      messages_on_missed_call,
      message_on_missed_call_content,
      voicemail_drop_type,
      voicemail_drop_content,
      goal_assistant_name_for_male,
      goal_assistant_name_for_female,
      audio_blob,
      audio_duration: audioDuration,
      pronounce_client_name_enabled,
      pronounce_client_honorific_enabled,
      duration_between_calls,
      ringing_duration,
      male_voice,
      female_voice,
      human_introduction_audio_blob,
      human_introduction_audio_duration,
      human_introduction_enabled,
      entreprise_id: entreprise._id.toString()
    })

    if(!response.success) {
        return NextResponse.json({
            error: response.error
        }, {
            status: 400
        })
    }

    return NextResponse.json({
        message: "Objectif créé avec succès.",
    }, {
        status: 201
    })

  } catch (err: any) {
    console.error("Erreur serveur:", err);
    return NextResponse.json({ error: "Erreur interne du serveur." }, { status: 500 });
  }
}
