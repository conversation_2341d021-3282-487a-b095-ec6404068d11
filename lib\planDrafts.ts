// Client-side draft management utilities
import { CreatePlanData } from "@/actions/BillingActions";

export interface PlanDraft extends CreatePlanData {
  _id: string;
  created_at: Date;
  updated_at: Date;
}

export interface DraftResponse {
  success: boolean;
  error?: string;
  data?: any;
}

const DRAFTS_KEY = "planDrafts";

// Save plan as draft
export function savePlanDraft(
  draftData: CreatePlanData,
  draftId?: string
): DraftResponse {
  try {
    const existingDrafts = getPlanDraftsSync();

    if (draftId) {
      // Update existing draft
      const draftIndex = existingDrafts.findIndex((d) => d._id === draftId);
      if (draftIndex === -1) {
        return { success: false, error: "Draft not found" };
      }

      const updatedDraft: PlanDraft = {
        ...draftData,
        _id: draftId,
        created_at: existingDrafts[draftIndex].created_at,
        updated_at: new Date(),
      };

      existingDrafts[draftIndex] = updatedDraft;
      localStorage.setItem(DRAFTS_KEY, JSON.stringify(existingDrafts));

      return {
        success: true,
        data: updatedDraft,
      };
    } else {
      // Create new draft
      const newDraft: PlanDraft = {
        ...draftData,
        _id: Date.now().toString(),
        created_at: new Date(),
        updated_at: new Date(),
      };

      existingDrafts.push(newDraft);
      localStorage.setItem(DRAFTS_KEY, JSON.stringify(existingDrafts));

      return {
        success: true,
        data: newDraft,
      };
    }
  } catch (error: any) {
    console.error("Error saving plan draft:", error);
    return {
      success: false,
      error: error.message || "Failed to save plan draft",
    };
  }
}

// Get all plan drafts (synchronous)
export function getPlanDraftsSync(): PlanDraft[] {
  try {
    const draftsStr = localStorage.getItem(DRAFTS_KEY);
    if (!draftsStr) return [];

    const drafts = JSON.parse(draftsStr);
    // Convert date strings back to Date objects
    return drafts.map((draft: any) => ({
      ...draft,
      created_at: new Date(draft.created_at),
      updated_at: new Date(draft.updated_at),
    }));
  } catch (error) {
    console.error("Error fetching plan drafts:", error);
    return [];
  }
}

// Get all plan drafts (async for consistency)
export function getPlanDrafts(): Promise<DraftResponse> {
  try {
    const drafts = getPlanDraftsSync();

    return Promise.resolve({
      success: true,
      data: drafts,
    });
  } catch (error: any) {
    console.error("Error fetching plan drafts:", error);
    return Promise.resolve({
      success: false,
      error: error.message || "Failed to fetch plan drafts",
    });
  }
}

// Delete a plan draft
export function deletePlanDraft(draftId: string): DraftResponse {
  try {
    const existingDrafts = getPlanDraftsSync();
    const updatedDrafts = existingDrafts.filter(
      (draft) => draft._id !== draftId
    );
    localStorage.setItem(DRAFTS_KEY, JSON.stringify(updatedDrafts));

    return {
      success: true,
      data: { message: "Draft deleted successfully" },
    };
  } catch (error: any) {
    console.error("Error deleting plan draft:", error);
    return {
      success: false,
      error: error.message || "Failed to delete plan draft",
    };
  }
}

// Get a specific draft by ID
export function getPlanDraftById(draftId: string): PlanDraft | null {
  try {
    const drafts = getPlanDraftsSync();
    return drafts.find((draft) => draft._id === draftId) || null;
  } catch (error) {
    console.error("Error fetching plan draft:", error);
    return null;
  }
}

// Clear all drafts
export function clearAllDrafts(): DraftResponse {
  try {
    localStorage.removeItem(DRAFTS_KEY);
    return {
      success: true,
      data: { message: "All drafts cleared successfully" },
    };
  } catch (error: any) {
    console.error("Error clearing drafts:", error);
    return {
      success: false,
      error: error.message || "Failed to clear drafts",
    };
  }
}
