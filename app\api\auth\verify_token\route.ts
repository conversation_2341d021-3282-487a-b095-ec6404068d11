import { NextResponse } from "next/server";
import jwt from "jsonwebtoken";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { token } = body;

    if (!token) {
      return NextResponse.json({ error: "Token requis." }, { status: 400 });
    }

    // Get the JWT secret
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error("JWT_SECRET non configuré");
      return NextResponse.json(
        { error: "Configuration du serveur manquante." },
        { status: 500 }
      );
    }

    // Verify and decode the JWT token
    const decoded = jwt.verify(token, jwtSecret);

    return NextResponse.json(
      {
        message: "Token valide.",
        payload: decoded,
        valid: true,
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.error("Erreur lors de la vérification du token:", error);

    // Handle specific JWT errors
    if (error.name === "TokenExpiredError") {
      return NextResponse.json(
        { error: "Token expiré.", valid: false },
        { status: 401 }
      );
    }

    if (error.name === "JsonWebTokenError") {
      return NextResponse.json(
        { error: "Token invalide.", valid: false },
        { status: 401 }
      );
    }

    if (error.name === "NotBeforeError") {
      return NextResponse.json(
        { error: "Token pas encore valide.", valid: false },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: "Erreur interne du serveur." },
      { status: 500 }
    );
  }
}
