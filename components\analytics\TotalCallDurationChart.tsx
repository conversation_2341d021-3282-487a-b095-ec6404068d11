"use client";

import { useTheme } from "next-themes";
import { useEffect, useMemo, useRef } from "react";
import { Label, Pie, PieChart } from "recharts";
import useSWR from "swr";

import ExportDropdown, {
  ExportDropdownItem,
} from "@/components/analytics/ExportDropdown";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Skeleton } from "@/components/ui/skeleton";
import {
  exportChartAsImage,
  exportChartAsPDF,
} from "@/lib/analytics/chartExport";
import { CountryCode } from "@/lib/countries";
import {
  TotalCallDurationStats,
  convertCountriesToUppercase,
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { store } from "@/redux/store";

const chartConfig = {
  CALL: {
    label: "Calls",
    color: "#2a9d90",
  },
  CALL_INBOUND: {
    label: "Inbound Calls",
    color: "#3b82f6", // blue
  },
  CALL_OUTBOUND: {
    label: "Outbound Calls",
    color: "#f97316", // orange
  },
  MEET: {
    label: "Meetings",
    color: "#8b5cf6", // purple
  },
} satisfies ChartConfig;

// Direction colors for inbound/outbound display
const directionColors = {
  inbound: "#3b82f6", // blue
  outbound: "#f97316", // orange
};

// Helper function to format seconds to a readable time string
const formatSeconds = (seconds: number): string => {
  if (!seconds && seconds !== 0) return "N/A";

  if (seconds < 60) {
    return `${Math.round(seconds)} sec`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return remainingSeconds > 0
      ? `${minutes} min ${remainingSeconds} sec`
      : `${minutes} min`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
};

// SWR fetcher for total call duration stats
const fetcher = async (): Promise<TotalCallDurationStats> => {
  const filters = store.getState().analytics.filters;
  const {
    startDate,
    endDate,
    conversationTypes,
    countries,
    callDuration,
    ringingTime,
  } = filters;

  const { getTotalCallDurationByType } = await import(
    "@/actions/AnalyticsActions"
  );
  const res = await getTotalCallDurationByType(
    startDate ? new Date(startDate) : undefined,
    endDate ? new Date(endDate) : undefined,
    conversationTypes,
    countries.length
      ? convertCountriesToUppercase(countries as CountryCode[])
      : countries,
    callDuration,
    ringingTime
  );

  if (!res.success) {
    throw new Error(res.error || "Failed to fetch total call duration stats");
  }
  if (!("data" in res)) {
    throw new Error("No data returned from getTotalCallDurationByType");
  }
  return res.data as TotalCallDurationStats;
};

export default function TotalCallDurationChart({
  isAnimationActive = true,
  setLoading,
}: {
  isAnimationActive?: boolean;
  setLoading?: (loading: boolean) => void;
}) {
  const { theme, systemTheme } = useTheme();
  const isDarkMode =
    theme === "dark" || (theme === "system" && systemTheme === "dark");

  const chartRef = useRef<HTMLDivElement>(null);

  // Use SWR to fetch total call duration stats
  const {
    data: totalCallDuration,
    isLoading: totalCallDurationLoading,
    error,
  } = useSWR<TotalCallDurationStats>(
    ["totalCallDurationStats", store.getState().analytics.filters],
    fetcher,
    {
      revalidateOnFocus: false,
      dedupingInterval: 10000,
    }
  );

  // Call setLoading when loading state changes
  useEffect(() => {
    if (setLoading) setLoading(totalCallDurationLoading);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [totalCallDurationLoading]);

  // Convert data to chart format
  const chartData = useMemo(() => {
    if (!totalCallDuration) return [];

    return [
      {
        type: "CALL_INBOUND",
        duration: totalCallDuration.CALL.inbound,
        fill: chartConfig.CALL_INBOUND.color,
        name: "Inbound Calls",
      },
      {
        type: "CALL_OUTBOUND",
        duration: totalCallDuration.CALL.outbound,
        fill: chartConfig.CALL_OUTBOUND.color,
        name: "Outbound Calls",
      },
      {
        type: "MEET",
        duration: totalCallDuration.MEET.total,
        fill: chartConfig.MEET.color,
        name: "Meetings",
      },
    ].filter((item) => item.duration > 0); // Only include items with duration > 0
  }, [totalCallDuration]);

  const totalDuration = useMemo(
    () => totalCallDuration?.grandTotal || 0,
    [totalCallDuration]
  );

  // Create expanded data for the legend
  const expandedData = useMemo(() => {
    if (!totalCallDuration) return [];

    return [
      {
        type: "CALL",
        label: chartConfig.CALL.label,
        color: chartConfig.CALL.color,
        inbound: totalCallDuration.CALL.inbound,
        outbound: totalCallDuration.CALL.outbound,
        total: totalCallDuration.CALL.total,
        isMeeting: false, // Flag to identify if it's a meeting (for conditional rendering)
      },
      {
        type: "MEET",
        label: chartConfig.MEET.label,
        color: chartConfig.MEET.color,
        inbound: 0,
        outbound: 0,
        total: totalCallDuration.MEET.total,
        isMeeting: true, // Flag to identify if it's a meeting
      },
    ];
  }, [totalCallDuration]);

  // Define export items for the dropdown
  const exportItems: ExportDropdownItem[] = [
    {
      title: "Export as PNG",
      action: async () => {
        try {
          await exportChartAsImage(
            chartRef.current,
            "png",
            "total-call-duration-chart"
          );
        } finally {
        }
      },
    },
    {
      title: "Export as SVG",
      action: async () => {
        try {
          await exportChartAsImage(
            chartRef.current,
            "svg",
            "total-call-duration-chart"
          );
        } finally {
        }
      },
    },
    {
      title: "Export as PDF",
      action: async () => {
        try {
          await exportChartAsPDF(chartRef.current, "total-call-duration-chart");
        } finally {
        }
      },
    },
  ];

  return (
    <div ref={chartRef} className="flex w-full">
      <Card className="bg-voxa-neutral-50 dark:bg-voxa-neutral-900 dark:border-voxa-neutral-700 transition-all duration-150 w-full">
        <CardHeader className="pb-0 flex flex-row items-start justify-between">
          <div>
            <CardTitle className="dark:text-voxa-neutral-50">
              Total Call Duration
            </CardTitle>
          </div>

          <ExportDropdown
            items={exportItems}
            buttonText="Export"
            loadingText="Exporting..."
            className=""
          />
        </CardHeader>

        <CardContent className="flex flex-wrap justify-center items-center gap-6 mt-2">
          {totalCallDurationLoading ? (
            <>
              <Skeleton className="aspect-square w-[200px] h-[200px] mx-auto" />
              <div className="flex flex-col justify-center gap-3 text-sm min-w-[180px]">
                <Skeleton className="h-6 w-32 mb-2" />
                <Skeleton className="h-6 w-32 mb-2" />
                <Skeleton className="h-6 w-32" />
              </div>
            </>
          ) : error ? (
            <div className="text-center text-red-500 p-4">
              Error loading total call duration data
            </div>
          ) : (
            <>
              {/* Chart area */}
              {totalDuration === 0 ? (
                <div className="aspect-square w-[200px] mx-auto flex items-center justify-center">
                  <div className="relative w-[160px] h-[160px] flex items-center justify-center">
                    {/* Outer circle with shadow */}
                    <div className="absolute inset-0 rounded-full bg-gray-300 dark:bg-gray-700 shadow-[0_2px_8px_rgba(0,0,0,0.15)] dark:shadow-[0_2px_8px_rgba(0,0,0,0.35)]"></div>
                    {/* Inner circle (creates hollow effect) */}
                    <div className="absolute w-[120px] h-[120px] rounded-full bg-voxa-neutral-50 dark:bg-voxa-neutral-900 z-10"></div>
                    {/* Text in the middle */}
                    <div className="z-20 flex flex-col items-center justify-center">
                      <span className="text-2xl font-bold text-foreground">
                        0
                      </span>
                      <span className="text-sm text-muted-foreground">
                        Total
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <ChartContainer
                  config={chartConfig}
                  className="aspect-square w-[200px] mx-auto"
                >
                  <PieChart>
                    <ChartTooltip
                      cursor={false}
                      content={
                        <ChartTooltipContent
                          formatter={(value, name) => (
                            <div>
                              <span
                                className="font-medium"
                                style={{
                                  color:
                                    chartConfig[
                                      name as keyof typeof chartConfig
                                    ]?.color || "#000",
                                }}
                              >
                                {name}
                              </span>{" "}
                              {formatSeconds(Number(value))}
                            </div>
                          )}
                        />
                      }
                    />
                    <Pie
                      data={chartData}
                      dataKey="duration"
                      nameKey="name"
                      innerRadius={60}
                      strokeWidth={0} // Remove stroke
                      isAnimationActive={isAnimationActive}
                    >
                      <Label
                        content={({ viewBox }) => {
                          if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                            return (
                              <text
                                x={viewBox.cx}
                                y={viewBox.cy}
                                textAnchor="middle"
                                dominantBaseline="middle"
                              >
                                <tspan
                                  x={viewBox.cx}
                                  y={viewBox.cy}
                                  style={{
                                    fill: isDarkMode ? "#ffffff" : "#000000",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  {formatSeconds(totalDuration)}
                                </tspan>
                                <tspan
                                  x={viewBox.cx}
                                  y={(viewBox.cy || 0) + 24}
                                  style={{
                                    fill: isDarkMode ? "#aaaaaa" : "#888888",
                                    fontSize: "14px",
                                  }}
                                >
                                  Total
                                </tspan>
                              </text>
                            );
                          }
                        }}
                      />
                    </Pie>
                  </PieChart>
                </ChartContainer>
              )}

              {/* Legend with duration breakdown - always shown */}
              <div className="flex flex-col justify-center gap-3 text-sm min-w-[220px]">
                {expandedData.map((entry) => {
                  const percent =
                    totalDuration > 0
                      ? ((entry.total / totalDuration) * 100).toFixed(1)
                      : "0.0";

                  return (
                    <div key={entry.type} className="flex flex-col gap-1">
                      {/* Type heading with total */}
                      <div className="flex items-center justify-between gap-2">
                        <div className="flex items-center gap-2">
                          <span
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: entry.color }}
                          />
                          <span className="text-foreground font-medium">
                            {entry.label}
                          </span>
                        </div>

                        {/* Right: duration + percentage in visible pills */}
                        <div className="flex gap-1">
                          <span className="rounded-md px-2 py-0.5 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-foreground">
                            {formatSeconds(entry.total)}
                          </span>
                          <span className="rounded-md px-2 py-0.5 w-12 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-muted-foreground">
                            {percent}%
                          </span>
                        </div>
                      </div>

                      {/* Inbound/Outbound details - Only for non-meeting types */}
                      {!entry.isMeeting && (
                        <div className="ml-5 flex flex-col gap-1 text-xs mt-1">
                          <div className="flex items-center justify-between gap-2">
                            <div className="flex items-center gap-1.5">
                              <span
                                className="w-2 h-2 rounded-full"
                                style={{
                                  backgroundColor: directionColors.inbound,
                                }}
                              />
                              <span className="text-muted-foreground">
                                Inbound
                              </span>
                            </div>
                            <span className="rounded-md px-2 py-0.5 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-foreground">
                              {formatSeconds(entry.inbound)}
                            </span>
                          </div>
                          <div className="flex items-center justify-between gap-2">
                            <div className="flex items-center gap-1.5">
                              <span
                                className="w-2 h-2 rounded-full"
                                style={{
                                  backgroundColor: directionColors.outbound,
                                }}
                              />
                              <span className="text-muted-foreground">
                                Outbound
                              </span>
                            </div>
                            <span className="rounded-md px-2 py-0.5 text-xs text-center bg-voxa-neutral-100 dark:bg-voxa-neutral-800 text-foreground">
                              {formatSeconds(entry.outbound)}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
