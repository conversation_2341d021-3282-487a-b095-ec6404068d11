import { CountryCode } from "@/lib/countries";
import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";

// Types for analytics data
export interface ConversationMetrics {
  averageCallDuration: number;
  maxCallDuration: number;
  averageRingingTime: number;
  maxRingingTime: number;
  averageClientEngagement: number;
  most_talkative_speaker_name: string;
  mostTalkativeSpeakerCount: number;
}

export interface CallStatusStats {
  missed: number;
  transferred: number;
  notTransferred: number;
}

export interface ConversationTypesStats {
  CALL: {
    inbound: number;
    outbound: number;
    total: number;
  };
  SMS: {
    inbound: number;
    outbound: number;
    total: number;
  };
  WHATSAPP: {
    inbound: number;
    outbound: number;
    total: number;
  };
  MEET: {
    inbound: number;
    outbound: number;
    total: number;
  };
}

// New interface for total call duration stats
export interface TotalCallDurationStats {
  CALL: {
    inbound: number;
    outbound: number;
    total: number;
  };
  MEET: {
    total: number;
  };
  grandTotal: number;
}

export interface TimelineStatsEntry {
  date: string;
  missed: number;
  answered: number;
}

export interface HeatmapSeries {
  name: string;
  data: number[];
}

export interface HeatmapStats {
  total: HeatmapSeries[];
  missed: HeatmapSeries[];
  answered: HeatmapSeries[];
}

export interface Filters {
  startDate: string | null;
  endDate: string | null;
  conversationTypes: {
    inbound: boolean;
    outbound: boolean;
    missed: boolean;
    answered: boolean;
    notTransferred: boolean;
    transferred: boolean;
    calls: boolean;
    whatsapp: boolean;
    sms: boolean;
    meet: boolean;
  };
  countries: CountryCode[];
  callDuration: {
    min: number | null;
    max: number | null;
  };
  ringingTime: {
    min: number | null;
    max: number | null;
  };
}

// New type for country data
interface CountryData {
  country: string;
  conversations: number;
}

export interface AnalyticsState {
  timelinePeriod: string;
  filterSidebarOpen: boolean;
  filters: Filters;
  filtersInput: Filters; // Added filtersInput state

  // Add exportedAnalytics state
  exportedAnalytics: {
    callStatusChart: boolean;
    totalCallDurationChart: boolean;
    conversationsTypesChart: boolean;
    averageRingingTime: boolean;
    averageCallDuration: boolean;
    averageClientEngagement: boolean;
    mostTalkativeSpeaker: boolean;
    callStatusByTimeLine: {
      day: boolean;
      week: boolean;
      month: boolean;
      weekday: boolean;
      hour: boolean;
    };
    heatmap: {
      total: boolean;
      answered: boolean;
      missed: boolean;
    };
    conversationsMap: boolean;
  };
  // Add loading state mirroring exportedAnalytics, but with boolean values
  loading: {
    callStatusChart: boolean;
    totalCallDurationChart: boolean;
    conversationsTypesChart: boolean;
    averageRingingTime: boolean;
    averageCallDuration: boolean;
    averageClientEngagement: boolean;
    mostTalkativeSpeaker: boolean;
    callStatusByTimeLine: {
      day: boolean;
      week: boolean;
      month: boolean;
      weekday: boolean;
      hour: boolean;
    };
    heatmap: {
      total: boolean;
      answered: boolean;
      missed: boolean;
    };
    conversationsMap: boolean;
  };
}

const initialFilters: Filters = {
  startDate: null,
  endDate: null,
  conversationTypes: {
    inbound: true,
    outbound: true,
    missed: true,
    answered: true,
    notTransferred: true,
    transferred: true,
    calls: true,
    whatsapp: true,
    sms: true,
    meet: true,
  },
  countries: [],
  callDuration: {
    min: null,
    max: null,
  },
  ringingTime: {
    min: null,
    max: null,
  },
};

const initialState: AnalyticsState = {
  timelinePeriod: "day",
  filterSidebarOpen: false,
  filters: initialFilters,
  filtersInput: initialFilters, // Initialize filtersInput with the same value as filters
  exportedAnalytics: {
    callStatusChart: true,
    totalCallDurationChart: true,
    conversationsTypesChart: true,
    averageRingingTime: true,
    averageCallDuration: true,
    averageClientEngagement: true,
    mostTalkativeSpeaker: true,
    callStatusByTimeLine: {
      day: true,
      week: true,
      month: true,
      weekday: true,
      hour: true,
    },
    heatmap: {
      total: true,
      answered: true,
      missed: true,
    },
    conversationsMap: true,
  },
  // Add initial loading state
  loading: {
    callStatusChart: true,
    totalCallDurationChart: true,
    conversationsTypesChart: true,
    averageRingingTime: true,
    averageCallDuration: true,
    averageClientEngagement: true,
    mostTalkativeSpeaker: true,
    callStatusByTimeLine: {
      day: true,
      week: true,
      month: true,
      weekday: true,
      hour: true,
    },
    heatmap: {
      total: true,
      answered: true,
      missed: true,
    },
    conversationsMap: true,
  },
};

// Helper function to convert country codes to uppercase
export const convertCountriesToUppercase = (
  countries: CountryCode[]
): CountryCode[] => {
  return countries.map((country) => country.toUpperCase() as CountryCode);
};

const analyticsSlice = createSlice({
  name: "analytics",
  initialState,
  reducers: {
    setTimelinePeriod(state, action: PayloadAction<string>) {
      state.timelinePeriod = action.payload;
    },
    setFilterSidebarOpen(state, action: PayloadAction<boolean>) {
      state.filterSidebarOpen = action.payload;
    },
    setFilters(state, action: PayloadAction<Partial<Filters>>) {
      state.filters = { ...state.filters, ...action.payload };
    },
    setFiltersInput(state, action: PayloadAction<Partial<Filters>>) {
      state.filtersInput = { ...state.filtersInput, ...action.payload };
    },
    setFiltersFromInput(state) {
      state.filters = { ...state.filtersInput };
    },
    resetFilters(state) {
      state.filters = initialFilters;
      state.filtersInput = initialFilters;
    },
    setExportedAnalytics(
      state,
      action: PayloadAction<Partial<AnalyticsState["exportedAnalytics"]>>
    ) {
      state.exportedAnalytics = {
        ...state.exportedAnalytics,
        ...action.payload,
      };
    },
    resetExportedAnalytics(state) {
      state.exportedAnalytics = initialState.exportedAnalytics;
    },
    // Setters for loading state
    setLoading(
      state,
      action: PayloadAction<DeepPartial<AnalyticsState["loading"]>>
    ) {
      // Hardcoded deep merge for nested objects
      state.loading = {
        ...state.loading,
        ...action.payload,
        callStatusByTimeLine: action.payload.callStatusByTimeLine
          ? {
              ...state.loading.callStatusByTimeLine,
              ...action.payload.callStatusByTimeLine,
            }
          : state.loading.callStatusByTimeLine,
        heatmap: action.payload.heatmap
          ? {
              ...state.loading.heatmap,
              ...action.payload.heatmap,
            }
          : state.loading.heatmap,
      };
    },
    resetLoading(state) {
      state.loading = initialState.loading;
    },
  },
});

export const {
  setTimelinePeriod,
  setFilterSidebarOpen,
  setFilters,
  setFiltersInput,
  setFiltersFromInput,
  resetFilters,
  setExportedAnalytics,
  resetExportedAnalytics,
  setLoading,
  resetLoading,
} = analyticsSlice.actions;
export default analyticsSlice.reducer;

// Utility type for deep partials
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
