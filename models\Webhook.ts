import mongoose from "mongoose";
import { InferSchemaType } from "mongoose";

const EventSchema = new mongoose.Schema(
  {
    method: { type: String, default: "POST" },
    path: { type: String, default: "" },
  },
  { _id: false }
);

const EventsSchema = new mongoose.Schema(
  {
    "callback-status": EventSchema,
    "recording-status": EventSchema,
    "make-call": EventSchema,
  },
  { _id: false }
);

const LogEventSchema = new mongoose.Schema(
  {
    url: { type: String },
    method: { type: String },
    response_code: { type: String },
    timestamp: { type: String },
    header: { type: String },
    body: { type: String },
  },
  { _id: false }
);

const WebhookSchema = new mongoose.Schema({
  entreprise_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Entreprise",
  },
  events: EventsSchema,
  logs_events: {
    type: [LogEventSchema],
    default: [],
  },
  webhook_secret_key: {
    type: String,
  },
  enabled: {
    type: Boolean,
    default: true,
  },
});

const Webhook =
  mongoose.models.Webhook || mongoose.model("Webhook", WebhookSchema);

export default Webhook;
