// --- balanceAlertPlugin.ts ---

import { Schema } from "mongoose";
import { sendBalanceAlertEmail } from "@/actions/EmailActions";
import { User } from "@/models/User";

/**
 * Plugin to send an email alert when balance drops below threshold.
 * Supports both doc.save() and findOneAndUpdate(), including $inc updates.
 */
export function attachBalanceAlertMiddlewares(schema: Schema) {
  // -------- HANDLE .save() --------
  schema.pre("save", async function (next) {
    // Only check existing docs with changed balance
    if (
      typeof this.isModified !== "function" ||
      !this.isModified("balance") ||
      this.isNew
    ) {
      return next();
    }

    // Load previous balance, threshold, and admin ref
    const old: any = await (this as any).constructor
      .findById(this._id)
      .select("balance balanceAlertThreshold admin")
      .lean();
    if (!old || !old.admin) return next();

    // Fetch admin user record
    const admin = await User.findById(old.admin, "email name");
    if (!admin?.email) return next();

    const oldBalance: number = old.balance;
    const threshold: number = old.balanceAlertThreshold;
    const newBalance: number = Number(this.balance);
    const email: string = admin.email;
    const username: string = admin.name || "User";

    // Trigger email if crossing threshold
    if (oldBalance > threshold && newBalance <= threshold) {
      sendBalanceAlertEmail(email, username, newBalance, threshold).catch(
        console.error
      );
    }

    next();
  });

  // --- HANDLE findOneAndUpdate() ---
  schema.pre("findOneAndUpdate", async function (next) {
    const filter = this.getQuery();
    const update: any = this.getUpdate();

    // Load old balance, threshold, and admin ref
    const old: any = await this.model
      .findOne(filter)
      .select("balance balanceAlertThreshold admin")
      .lean();
    if (!old || !old.admin) return next();

    // Fetch admin user record
    const admin = await User.findById(old.admin, "email name");
    if (!admin?.email) return next();

    const oldBalance: number = old.balance;
    const threshold: number = old.balanceAlertThreshold;
    const email: string = admin.email;
    // const email = "<EMAIL>";
    const username: string = admin.name || "User";

    // Compute new balance for $set, direct, or $inc
    let newBalance: number | undefined;
    if (typeof update.balance === "number") {
      newBalance = update.balance;
    } else if (typeof update.$set?.balance === "number") {
      newBalance = update.$set.balance;
    } else if (typeof update.$inc?.balance === "number") {
      newBalance = oldBalance + update.$inc.balance;
    }
    if (typeof newBalance !== "number") return next();

    // Trigger email if crossing threshold
    if (oldBalance > threshold && newBalance <= threshold) {
      sendBalanceAlertEmail(email, username, newBalance, threshold).catch(
        console.error
      );
    }

    next();
  });
}
