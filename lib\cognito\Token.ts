// app/lib/cognito/Token.ts
import { CognitoJwtVerifier } from "aws-jwt-verify";

const verifier = CognitoJwtVerifier.create({
  userPoolId: process.env.COGNITO_USER_POOL_ID!,
  tokenUse: "id", // or "access" depending on your use case
  clientId: process.env.COGNITO_CLIENT_ID!,
});

type VerifiedPayload = {
  email?: string;
  sub: string;
  [key: string]: any; // other claims
};

type VerifyResult =
  | { valid: true; payload: VerifiedPayload; error?: undefined }
  | { valid: false; payload?: undefined; error: Error };

export async function verifyUserToken(token: string): Promise<VerifyResult> {
  try {
    const payload = (await verifier.verify(token)) as VerifiedPayload;
    return { valid: true, payload };
  } catch (err: any) {
    return { valid: false, error: err };
  }
}
