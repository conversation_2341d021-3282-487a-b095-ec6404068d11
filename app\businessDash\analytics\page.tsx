"use client";

import { AnalyticsCard } from "@/components/analytics/AnalyticsCard";
import CallStatusByTimeLineChart from "@/components/analytics/CallStatusByTimeLineChart";
import { CallStatusChart } from "@/components/analytics/CallStatusChart";
import ConversationsMapChartWrapper from "@/components/analytics/ConversationsMapChartWrapper";
import ConversationsTypesChart from "@/components/analytics/ConversationsTypeChart";
import ExportableAnalytics from "@/components/analytics/ExportableAnalytics";
import ExportAnalyticsDialog from "@/components/analytics/ExportAnalyticsDialog";
import ExportDropdown, {
  ExportDropdownItem,
} from "@/components/analytics/ExportDropdown";
import FilterSidebar from "@/components/analytics/FiltersSidebar";
import FloatingFilterButton from "@/components/analytics/FloatingFilterButton";
import Heatmap<PERSON>hart from "@/components/analytics/HeatmapChart";
import SelectedDateFilter from "@/components/analytics/SelectedDateFilter";
import TotalCallDurationChart from "@/components/analytics/TotalCallDurationChart";
import CustomButton from "@/components/CustomFormItems/Button";
import {
  exportChartAsImage,
  exportChartAsPDF,
} from "@/lib/analytics/chartExport";
import { CountryCode } from "@/lib/countries";
import {
  convertCountriesToUppercase,
  resetExportedAnalytics,
  resetLoading,
  setFilterSidebarOpen,
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { AppDispatch, RootState, store } from "@/redux/store";
import FilterAltRoundedIcon from "@mui/icons-material/FilterAltRounded";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import useSWR from "swr";

// Initialize ApexCharts with a safe dynamic import for client-side only
let ApexChartsModule: any = null;
if (typeof window !== "undefined") {
  import("apexcharts").then((module) => {
    ApexChartsModule = module.default;
  });
}

// Helper function to format seconds to a readable time string
const formatSeconds = (seconds: number): string => {
  if (!seconds && seconds !== 0) return "N/A";

  if (seconds < 60) {
    return `${Math.round(seconds)} sec`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return remainingSeconds > 0
      ? `${minutes} min ${remainingSeconds} sec`
      : `${minutes} min`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
};

type BoolTree = boolean | { [key: string]: BoolTree };

function isExportReady(
  exportedAnalytics: BoolTree,
  loading: BoolTree
): boolean {
  // base case: leaf boolean
  if (typeof exportedAnalytics === "boolean") {
    // if the export is true, loading *must* be false
    return !exportedAnalytics || (typeof loading === "boolean" && !loading);
  }

  // recursive case: both must be objects of the same shape
  for (const key of Object.keys(exportedAnalytics)) {
    const expVal = exportedAnalytics[key];
    const loadVal = (loading as any)[key];

    // if there's any mismatch, bail out
    if (!isExportReady(expVal, loadVal)) {
      return false;
    }
  }

  return true;
}

// SWR fetcher for conversation metrics (local logic, not via redux thunk)
const fetchConversationMetricsSWR = async (): Promise<any> => {
  const filters = store.getState().analytics.filters;
  const {
    startDate,
    endDate,
    conversationTypes,
    countries,
    callDuration,
    ringingTime,
  } = filters;

  const { getConversationMetrics } = await import("@/actions/AnalyticsActions");
  const res = await getConversationMetrics(
    startDate ? new Date(startDate) : undefined,
    endDate ? new Date(endDate) : undefined,
    conversationTypes,
    countries.length
      ? convertCountriesToUppercase(countries as CountryCode[])
      : countries,
    callDuration,
    ringingTime
  );

  if (!res.success) {
    throw new Error(res.error || "Failed to fetch conversation metrics");
  }
  if (!("data" in res)) {
    throw new Error("No data returned from getConversationMetrics");
  }
  return res.data;
};

export default function AnalyticsPage() {
  const { t } = useTranslation("analytics");
  const exportAnalyticsRef = useRef<HTMLDivElement>(null);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [exportState, setExportState] = useState<{
    format: string;
    export: boolean;
  }>({
    format: "",
    export: false,
  });
  const [isExporting, setIsExporting] = useState(false);

  const dispatch = useDispatch<AppDispatch>();
  // Get filters from Redux for dependency tracking
  const filters = useSelector((state: RootState) => state.analytics.filters);

  // Add selectors for exportedAnalytics and loading state
  const exportedAnalytics = useSelector(
    (state: RootState) => state.analytics.exportedAnalytics
  );
  const loading = useSelector((state: RootState) => state.analytics.loading);

  // Use SWR to fetch conversation metrics (local fetcher)
  const { data: conversationMetrics, isLoading: conversationMetricsLoading } =
    useSWR(["conversationMetrics", filters], fetchConversationMetricsSWR, {
      revalidateOnFocus: false,
      dedupingInterval: 10000,
    });

  // Define base IDs for all heatmaps
  const heatmapBaseIds = ["heatmap", "heatmap-0", "heatmap-1", "heatmap-2"];

  // Store prepared heatmaps info for restoration
  const preparedHeatmaps: {
    imgContainer: HTMLElement | null;
    chartContainer: HTMLElement | null;
    parent: HTMLElement | null;
  }[] = [];

  // Single export function that handles all export logic
  const exportAnalyticsDashboard = async (format: "png" | "svg" | "pdf") => {
    preparedHeatmaps.length = 0; // Reset the array

    try {
      // Get ApexCharts instance
      let ApexChartsInstance = ApexChartsModule;
      if (!ApexChartsInstance && typeof window !== "undefined") {
        // @ts-expect-error: Access global ApexCharts
        ApexChartsInstance = window.ApexCharts;
      }

      if (ApexChartsInstance) {
        // Prepare all heatmaps for export
        for (const baseId of heatmapBaseIds) {
          const chartId = `${baseId}-chart`;
          const imageContainerId = `${baseId}-as-image`;
          const chartContainerId = `${baseId}-container`;

          const imgContainer = document.getElementById(imageContainerId);
          const chartContainer = document.getElementById(chartContainerId);
          const parent = chartContainer?.parentElement;

          if (imgContainer && chartContainer && parent) {
            try {
              const { imgURI } = await ApexChartsInstance.exec(
                chartId,
                "dataURI"
              );
              const img = document.createElement("img");
              img.src = imgURI;
              img.style.width = "100%";
              img.style.height = "350px";
              img.style.objectFit = "contain";

              imgContainer.innerHTML = "";
              imgContainer.appendChild(img);
              imgContainer.classList.remove("hidden");
              parent.removeChild(chartContainer);

              // Store references for restoration
              preparedHeatmaps.push({ imgContainer, chartContainer, parent });
            } catch (err) {
              console.error(`Failed to prepare ${baseId} for export:`, err);
            }
          }
        }
      }

      // Export the entire container
      if (format === "pdf") {
        await exportChartAsPDF(
          exportAnalyticsRef.current,
          "analytics-dashboard"
        );
      } else {
        await exportChartAsImage(
          exportAnalyticsRef.current,
          format,
          "analytics-dashboard"
        );
      }
    } catch (err) {
      console.error(`Export ${format.toUpperCase()} failed:`, err);
    } finally {
      // Restore all heatmaps
      for (const { imgContainer, chartContainer, parent } of preparedHeatmaps) {
        if (imgContainer && chartContainer && parent) {
          parent.appendChild(chartContainer);
          imgContainer.classList.add("hidden");
          imgContainer.innerHTML = "";
        }
      }
      setIsExporting(false);
    }
  };

  // Memoized export ready state
  const exportReady = isExportReady(exportedAnalytics, loading);

  // Trigger export when the export count changes
  useEffect(() => {
    if (!exportReady || !exportState.export) return;

    const doExport = async () => {
      await exportAnalyticsDashboard(
        exportState.format as "png" | "svg" | "pdf"
      );
      dispatch(resetExportedAnalytics());
      setExportState({ export: false, format: "" });
      dispatch(resetLoading());
    };
    doExport();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exportState.export, exportState.format, exportReady]); // Add exportReady to dependencies

  // Request a new export by updating export state
  const requestExport = (format: "png" | "svg" | "pdf") => {
    setIsExporting(true);
    setExportState({ export: true, format });
  };

  // Define export items for the dropdown
  const exportItems: ExportDropdownItem[] = [
    {
      title: "All",
      subItems: [
        {
          title: "Export as PNG",
          action: () => requestExport("png"),
        },
        {
          title: "Export as SVG",
          action: () => requestExport("svg"),
        },
        {
          title: "Export as PDF",
          action: () => requestExport("pdf"),
        },
      ],
    },
    {
      title: "Custom",
      action: () => setIsExportDialogOpen(true),
    },
  ];

  return (
    <div className="rounded-2xl flex flex-col gap-2">
      <h1 className="w-full max-sm:text-center text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        {t("pageTitle")}
      </h1>
      <div className="flex flex-wrap gap-2 justify-between">
        <div className="flex flex-wrap gap-2 grow">
          <SelectedDateFilter />
          <CustomButton
            props={{
              value: t("buttons.filter"),
              icon: <FilterAltRoundedIcon />,
              className: "dark:bg-voxa-neutral-800 w-full sm:w-fit py-5",
              onClick: () => dispatch(setFilterSidebarOpen(true)),
            }}
          />
        </div>
        <ExportDropdown
          items={exportItems}
          buttonText={t("buttons.exportAll") || "Export"}
          loadingText={t("buttons.exporting") || "Exporting..."}
          buttonClassName="dark:bg-voxa-neutral-800 w-full py-5"
          className="w-full sm:w-fit"
          isExporting={isExporting}
        />
      </div>
      <FilterSidebar />
      <ExportAnalyticsDialog
        open={isExportDialogOpen}
        onOpenChange={(open) => {
          setIsExportDialogOpen(open);
        }}
        onExport={requestExport}
        onDialogClose={() => {
          dispatch(resetExportedAnalytics());
          dispatch(resetLoading());
        }}
      />

      {/* Hidden component used only for exporting */}
      {isExporting && (
        <div className="fixed top-0 left-0 max-w-screen pointer-events-none opacity-0 overflow-auto">
          <div className="w-[1300px]">
            <ExportableAnalytics ref={exportAnalyticsRef} />
          </div>
        </div>
      )}

      {/* Visible analytics container */}
      <div className="analytics-container flex flex-col gap-2 bg-background">
        <div className="flex flex-wrap [&>*]:grow [&>*]:w-fit gap-2">
          <CallStatusChart />
          <TotalCallDurationChart />
        </div>
        <div className="flex flex-wrap [&>*]:grow [&>*]:w-fit gap-2">
          <ConversationsTypesChart />
        </div>
        <div className="flex flex-wrap gap-2">
          <AnalyticsCard
            title="Average Ringing Time"
            tooltip="Average time a caller waits before an agent picks up the call"
            value={formatSeconds(conversationMetrics?.averageRingingTime || 0)}
            rightLabel={`max: ${formatSeconds(
              conversationMetrics?.maxRingingTime || 0
            )}`}
            loading={conversationMetricsLoading}
          />
          <AnalyticsCard
            title="Average Call Duration"
            tooltip="Average length of all completed calls"
            value={formatSeconds(conversationMetrics?.averageCallDuration || 0)}
            rightLabel={`max: ${formatSeconds(
              conversationMetrics?.maxCallDuration || 0
            )}`}
            loading={conversationMetricsLoading}
          />
          <AnalyticsCard
            title="Average Client Engagement"
            tooltip="Average engagement score for all conversations"
            value={`${conversationMetrics?.averageClientEngagement || 0} sec`}
            loading={conversationMetricsLoading}
          />
          <AnalyticsCard
            title="Most Talkative Speaker"
            tooltip="The participant who talks the most in meetings"
            value={conversationMetrics?.most_talkative_speaker_name || "N/A"}
            rightLabel={`${
              conversationMetrics?.mostTalkativeSpeakerCount || 0
            } meetings`}
            loading={conversationMetricsLoading}
          />
        </div>
        <CallStatusByTimeLineChart />
        <HeatmapChart />
        <ConversationsMapChartWrapper />
      </div>
      <FloatingFilterButton />
    </div>
  );
}
