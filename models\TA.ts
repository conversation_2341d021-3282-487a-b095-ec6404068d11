import mongoose from 'mongoose';

const TASchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    email: {
        type: String,
        required: true,
    },
    phone: {
        type: String,
        required: true,
    },
    entreprise: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Entreprise',
    }
});

const TA = mongoose.models.TA || mongoose.model('TA', TASchema);
export default TA;