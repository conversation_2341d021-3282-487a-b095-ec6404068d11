"use client";

import useS<PERSON> from "swr";
import {
  cancelSubscription,
  getCurrentSubscription,
  getSubscriptions,
  reactivateSubscription,
  switchActiveSubscription,
} from "@/actions/BillingActions";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { formatDistanceToNow } from "date-fns";
import {
  AlertCircle,
  ArrowRightLeft,
  Calendar,
  Check,
  Clock,
  Crown,
  History,
  RefreshCw,
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface SubscriptionHistoryItem {
  _id: string;
  plan_id: {
    name: string;
    description: string;
    current_price: number;
    currency: string;
    billing_period: string;
  };
  status: string;
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  canceled_at?: string;
  trial_start?: string;
  trial_end?: string;
  price_at_subscription: number;
  created_at: string;
  stripe_subscription_id?: string;
}

export default function SubscriptionHistory({}) {
  // Remove local loading/subscriptions state
  // Add SWR for fetching subscriptions and current subscription id
  const fetcher = async () => {
    const [historyResponse, currentSubscriptionResponse] = await Promise.all([
      getSubscriptions(),
      getCurrentSubscription(),
    ]);
    if (!historyResponse.success)
      throw new Error(
        historyResponse.error || "Failed to load subscription history"
      );
    if (!currentSubscriptionResponse.success)
      throw new Error(
        currentSubscriptionResponse.error ||
          "Failed to load current subscription"
      );
    return {
      subscriptions: historyResponse.data || [],
      currentSubscriptionId: currentSubscriptionResponse.data?._id || "",
    };
  };

  const { data, error, isLoading, mutate, isValidating } = useSWR(
    "subscriptions",
    fetcher
  );

  const subscriptions: SubscriptionHistoryItem[] = data?.subscriptions || [];
  const currentSubscriptionId = data?.currentSubscriptionId || "";

  const [switchingSubscription, setSwitchingSubscription] = useState<
    string | null
  >(null);
  const [togglingRenewal, setTogglingRenewal] = useState<string | null>(null);

  const handleRefresh = async () => {
    await mutate();
  };

  const formatPrice = (price: number, currency: string) => {
    const symbol = currency === "eur" ? "€" : "$";
    return `${symbol}${price}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getStatusBadge = (
    status: string,
    cancelAtPeriodEnd?: boolean,
    periodEnd?: string
  ) => {
    // For active subscriptions that will not renew
    if (status === "active" && cancelAtPeriodEnd && periodEnd) {
      const now = new Date();
      const endDate = new Date(periodEnd);
      const diffInMs = endDate.getTime() - now.getTime();
      const diffInDays = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));

      // If expiring in 3 days or less, show remaining time as separate element
      if (diffInDays <= 3 && diffInDays > 0) {
        const timeRemaining = diffInDays === 1 ? "1d" : `${diffInDays}d`;
        return (
          <div className="flex items-center gap-2">
            <Badge variant="default" className="flex items-center gap-1">
              <Crown className="w-3 h-3" />
              Active
            </Badge>
            <span className="text-xs bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300 px-1.5 py-0.5 rounded font-medium">
              {timeRemaining}
            </span>
          </div>
        );
      }

      // Otherwise, just show "Active" like a normal active subscription
      return (
        <Badge variant="default" className="flex items-center gap-1">
          <Crown className="w-3 h-3" />
          Active
        </Badge>
      );
    }

    const statusConfig = {
      active: { variant: "default" as const, label: "Active", icon: Crown },
      trialing: {
        variant: "secondary" as const,
        label: "Trial",
        icon: Calendar,
      },
      past_due: {
        variant: "destructive" as const,
        label: "Past Due",
        icon: AlertCircle,
      },
      canceled: {
        variant: "outline" as const,
        label: "Expired",
        icon: AlertCircle,
      },
      incomplete: {
        variant: "secondary" as const,
        label: "Incomplete",
        icon: AlertCircle,
      },
      incomplete_expired: {
        variant: "destructive" as const,
        label: "Expired",
        icon: AlertCircle,
      },
      unpaid: {
        variant: "destructive" as const,
        label: "Unpaid",
        icon: AlertCircle,
      },
      paused: { variant: "secondary" as const, label: "Paused", icon: Clock },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
      icon: AlertCircle,
    };

    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const getPeriodInfo = (subscription: SubscriptionHistoryItem) => {
    const start = formatDate(subscription.current_period_start);
    const end = formatDate(subscription.current_period_end);

    if (subscription.trial_start && subscription.trial_end) {
      const trialStart = formatDate(subscription.trial_start);
      const trialEnd = formatDate(subscription.trial_end);
      return `Trial: ${trialStart} - ${trialEnd}`;
    }

    return `${start} - ${end}`;
  };

  const handleSwitchSubscription = async (subscriptionId: string) => {
    if (subscriptionId === currentSubscriptionId) return;

    setSwitchingSubscription(subscriptionId);
    try {
      const response = await switchActiveSubscription(subscriptionId);
      if (response.success) {
        toast.success("Successfully switched to this subscription!");
        await mutate();
      } else {
        toast.error(response.error || "Failed to switch subscription");
      }
    } catch (error: any) {
      console.error("Error switching subscription:", error);
      toast.error("Failed to switch subscription");
    } finally {
      setSwitchingSubscription(null);
    }
  };

  const isValidSubscription = (status: string) => {
    return ["active", "trialing"].includes(status);
  };

  const handleToggleRenewal = async (
    subscriptionId: string,
    currentStatus: boolean
  ) => {
    setTogglingRenewal(subscriptionId);
    try {
      const response = currentStatus
        ? await cancelSubscription(subscriptionId)
        : await reactivateSubscription(subscriptionId);

      if (response.success) {
        const action = currentStatus ? "disabled" : "enabled";
        toast.success(`Renewal ${action} successfully!`);
        await mutate();
      } else {
        toast.error(response.error || "Failed to toggle renewal");
      }
    } catch (error: any) {
      console.error("Error toggling renewal:", error);
      toast.error("Failed to toggle renewal");
    } finally {
      setTogglingRenewal(null);
    }
  };

  return (
    <div className="w-full">
      <Accordion
        type="single"
        collapsible
        defaultValue="subscription-history"
        className="w-full"
      >
        <AccordionItem
          value="subscription-history"
          className="!border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg overflow-hidden"
        >
          <AccordionTrigger className="py-5 px-4 font-medium text-base bg-voxa-neutral-50 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700 transition-colors flex items-center">
            <div className="flex items-center gap-2">
              <History className="w-5 h-5 text-voxa-teal-500 dark:text-voxa-teal-400" />
              <span>Subscription History</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4">
            <div className="mt-4 mb-4 w-full">
              {isLoading ? (
                <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                  <div className="flex items-center justify-center gap-2 text-muted-foreground">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    Loading subscription history...
                  </div>
                </div>
              ) : error ? (
                <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                  <History className="w-10 h-10 mx-auto mb-4 text-voxa-neutral-300 dark:text-voxa-neutral-600" />
                  <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 mb-2">
                    Failed to load subscription history
                  </p>
                  <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 text-sm">
                    {error.message}
                  </p>
                </div>
              ) : subscriptions.length > 0 ? (
                <div className="w-0 min-w-full border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg shadow-sm overflow-auto">
                  <div className="min-w-[800px]">
                    <div className="w-full flex justify-between items-center p-4 bg-voxa-neutral-50 dark:bg-voxa-neutral-900 border-b border-voxa-neutral-200 dark:border-voxa-neutral-700">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-voxa-neutral-500 dark:text-voxa-neutral-400" />
                        <h3 className="font-medium">Subscription History</h3>
                        <span className="bg-voxa-teal-100 dark:bg-voxa-teal-900 text-voxa-teal-700 dark:text-voxa-teal-300 text-xs px-2 py-1 rounded-full">
                          {subscriptions.length === 1
                            ? "1 subscription"
                            : `${subscriptions.length} subscriptions`}
                        </span>
                      </div>
                      <Button
                        variant="outline"
                        onClick={handleRefresh}
                        disabled={isValidating}
                        className="text-sm bg-voxa-neutral-100 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700 transition-colors"
                        type="button"
                      >
                        <RefreshCw
                          className={`w-4 h-4 mr-2 ${
                            isValidating ? "animate-spin" : ""
                          }`}
                        />
                        Refresh
                      </Button>
                    </div>
                    <div className="overflow-y-auto">
                      <table className="w-full">
                        <thead className="bg-voxa-neutral-100 dark:bg-voxa-neutral-800 sticky top-0">
                          <tr>
                            <th className="p-2 text-left text-xs font-medium">
                              Plan
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Status
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Period
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Amount
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Created
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Renewal
                            </th>
                            <th className="p-2 text-left text-xs font-medium">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {subscriptions.map((subscription) => (
                            <tr
                              key={subscription._id}
                              className="border-t border-voxa-neutral-200 dark:border-voxa-neutral-800 hover:bg-voxa-neutral-50 dark:hover:bg-voxa-neutral-800 transition-colors"
                            >
                              <td className="p-2">
                                <div>
                                  <div className="font-medium text-sm">
                                    {subscription.plan_id.name}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {subscription.plan_id.billing_period}
                                  </div>
                                </div>
                              </td>
                              <td className="p-2">
                                <div className="flex items-center gap-2">
                                  {getStatusBadge(
                                    subscription.status,
                                    subscription.cancel_at_period_end,
                                    subscription.current_period_end
                                  )}
                                </div>
                              </td>
                              <td className="p-2 text-sm">
                                {getPeriodInfo(subscription)}
                              </td>
                              <td className="p-2 text-sm font-medium">
                                {formatPrice(
                                  subscription.price_at_subscription,
                                  subscription.plan_id.currency
                                )}
                              </td>
                              <td className="p-2 text-xs text-muted-foreground">
                                <div>{formatDate(subscription.created_at)}</div>
                                <div className="text-xs">
                                  {formatDistanceToNow(
                                    new Date(subscription.created_at),
                                    { addSuffix: true }
                                  )}
                                </div>
                              </td>
                              <td className="p-2">
                                <div className="flex items-center gap-2">
                                  {isValidSubscription(subscription.status) ? (
                                    <div className="flex items-center gap-2">
                                      <Switch
                                        checked={
                                          !subscription.cancel_at_period_end
                                        }
                                        onCheckedChange={(checked) =>
                                          handleToggleRenewal(
                                            subscription._id,
                                            !checked
                                          )
                                        }
                                        disabled={
                                          togglingRenewal === subscription._id
                                        }
                                        className="scale-75 data-[state=unchecked]:bg-red-200 data-[state=unchecked]:[&_span]:!bg-red-800 dark:data-[state=checked]:bg-voxa-teal-500 dark:data-[state=unchecked]:bg-voxa-neutral-800"
                                      />
                                      <span className="text-xs text-muted-foreground">
                                        {subscription.cancel_at_period_end
                                          ? "Disabled"
                                          : "Renew"}
                                      </span>
                                      {togglingRenewal === subscription._id && (
                                        <RefreshCw className="w-3 h-3 animate-spin text-muted-foreground" />
                                      )}
                                    </div>
                                  ) : (
                                    <span className="text-xs text-muted-foreground">
                                      N/A
                                    </span>
                                  )}
                                </div>
                              </td>
                              <td className="p-2 text-center">
                                <div className="flex items-center gap-1">
                                  {isValidSubscription(subscription.status) &&
                                    (subscription._id ===
                                    currentSubscriptionId ? (
                                      <Badge className="bg-voxa-teal-100 text-voxa-teal-700 dark:bg-voxa-teal-900 dark:text-voxa-teal-300 text-xs h-7 px-2 flex items-center gap-1">
                                        <Check className="w-3 h-3" />
                                        Current
                                      </Badge>
                                    ) : (
                                      <Button
                                        variant="default"
                                        size="sm"
                                        onClick={() =>
                                          handleSwitchSubscription(
                                            subscription._id
                                          )
                                        }
                                        disabled={
                                          switchingSubscription ===
                                          subscription._id
                                        }
                                        className="text-xs h-7 px-2 bg-voxa-teal-600 hover:bg-voxa-teal-700"
                                        type="button"
                                      >
                                        {switchingSubscription ===
                                        subscription._id ? (
                                          <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                                        ) : (
                                          <ArrowRightLeft className="w-3 h-3 mr-1" />
                                        )}
                                        Switch
                                      </Button>
                                    ))}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                  <History className="w-10 h-10 mx-auto mb-4 text-voxa-neutral-300 dark:text-voxa-neutral-600" />
                  <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 mb-2">
                    No subscription history available
                  </p>
                  <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 text-sm">
                    Your subscription history will appear here once you
                    subscribe to a plan
                  </p>
                </div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
