import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import {
  setEnableAIVoice,
  setEnableMute,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useTranslation } from "react-i18next";

export const AIVoiceMuteSwitch = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation("assistants");
  const { enableAIVoice, enableMute } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );

  return (
    <>
      <div className="flex items-center space-x-3 h-full">
        <Switch
          id="ai-voice-switch"
          checked={enableAIVoice}
          onCheckedChange={(checked) => dispatch(setEnableAIVoice(checked))}
        />
        <Label
          htmlFor="ai-voice-switch"
          className="dark:text-voxa-neutral-200 text-nowrap"
        >
          {enableAIVoice
            ? t("createEditGoal.prompt.aiVoiceEnabled")
            : t("createEditGoal.prompt.aiVoiceDisabled")}
        </Label>
      </div>
      <div className="flex items-center space-x-3 h-full">
        <Switch
          id="mute-switch"
          checked={enableMute}
          onCheckedChange={(checked) => dispatch(setEnableMute(checked))}
        />
        <Label
          htmlFor="mute-switch"
          className="dark:text-voxa-neutral-200 text-nowrap"
        >
          {enableMute
            ? t("createEditGoal.prompt.muted")
            : t("createEditGoal.prompt.notMuted")}
        </Label>
      </div>
    </>
  );
};
