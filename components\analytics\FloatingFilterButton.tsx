"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { setFilterSidebarOpen } from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import FilterAltRoundedIcon from "@mui/icons-material/FilterAltRounded";
import { useTranslation } from "react-i18next";

export default function FloatingFilterButton() {
  const { t } = useTranslation("analytics");
  const dispatch = useDispatch<AppDispatch>();

  return (
    <Button
      onClick={() => dispatch(setFilterSidebarOpen(true))}
      className="fixed bottom-6 right-6 z-50 rounded-full h-12 w-12 p-0 shadow-lg bg-primary hover:bg-primary/90"
      aria-label={t("buttons.filter")}
    >
      <FilterAltRoundedIcon fontSize="medium" />
    </Button>
  );
}
