import ExportDropdown, {
  ExportDropdownItem,
} from "@/components/analytics/ExportDropdown";
import MainLoader from "@/components/Loaders/MainLoader";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  exportChartAsImage,
  exportChartAsPDF,
} from "@/lib/analytics/chartExport";
import { CountryCode } from "@/lib/countries";
import {
  convertCountriesToUppercase,
  HeatmapSeries,
  HeatmapStats,
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { store } from "@/redux/store";
import { ApexOptions } from "apexcharts";
import { useEffect, useRef, useState } from "react";
import Chart from "react-apexcharts";
import { useSelector } from "react-redux";
import { useTheme } from "next-themes";
import useScreenSize from "@/hooks/useScreenSize";
import useSWR from "swr";

type CallMetricType = "total" | "answered" | "missed";

// Initialize ApexCharts with a safe dynamic import for client-side only
let ApexChartsModule: any = null;
if (typeof window !== "undefined") {
  import("apexcharts").then((module) => {
    ApexChartsModule = module.default;
  });
}

// SWR fetcher for heatmap stats
const fetchHeatmapStatsSWR = async (): Promise<HeatmapStats> => {
  const filters = store.getState().analytics.filters;
  const {
    startDate,
    endDate,
    conversationTypes,
    countries,
    callDuration,
    ringingTime,
  } = filters;

  const { getCallVolumeByHourAndDay } = await import(
    "@/actions/AnalyticsActions"
  );
  const res = await getCallVolumeByHourAndDay(
    startDate ? new Date(startDate) : undefined,
    endDate ? new Date(endDate) : undefined,
    conversationTypes,
    countries.length
      ? convertCountriesToUppercase(countries as CountryCode[])
      : countries,
    callDuration,
    ringingTime
  );

  if (!res.success) {
    throw new Error(res.error || "Failed to fetch heatmap stats");
  }
  if (!("data" in res)) {
    throw new Error("No data returned from getCallVolumeByHourAndDay");
  }
  return res.data as HeatmapStats;
};

// Props interface for the component
interface HeatmapChartProps {
  baseId?: string;
  defaultMetric?: CallMetricType;
  isAnimationActive?: boolean;
  setLoading?: (loading: boolean) => void;
}

const HeatmapChart = ({
  baseId = "heatmap",
  defaultMetric = "total",
  isAnimationActive = true,
  setLoading,
}: HeatmapChartProps) => {
  // Generate IDs based on the baseId
  const chartId = `${baseId}-chart`;
  const imageContainerId = `${baseId}-as-image`;
  const chartContainerId = `${baseId}-container`;

  const { theme, systemTheme } = useTheme();
  const isDarkMode =
    theme === "dark" || (theme === "system" && systemTheme === "dark");
  const [activeMetric, setActiveMetric] =
    useState<CallMetricType>(defaultMetric);

  // Use the custom screen size hook
  const screenSize = useScreenSize();

  // Define chart parameters based on screen size
  const chartParameters = {
    xs: {
      showLabels: false,
      dataLabelSize: "8px",
      showBorders: false,
      cellRadius: 0,
    },
    sm: {
      showLabels: false,
      dataLabelSize: "9px",
      showBorders: false,
      cellRadius: 2,
    },
    md: {
      showLabels: true,
      dataLabelSize: "10px",
      showBorders: true,
      cellRadius: 5,
    },
    lg: {
      showLabels: true,
      dataLabelSize: "12px",
      showBorders: true,
      cellRadius: 10,
    },
    xl: {
      showLabels: true,
      dataLabelSize: "12px",
      showBorders: true,
      cellRadius: 10,
    },
    "2xl": {
      showLabels: true,
      dataLabelSize: "12px",
      showBorders: true,
      cellRadius: 10,
    },
  };

  // Get current parameters based on screen size
  const currentParams = chartParameters[screenSize];

  const chartRef = useRef<HTMLDivElement>(null);

  // Get filters from Redux for dependency tracking
  const filters = useSelector((state: any) => state.analytics.filters);

  // Use SWR to fetch heatmap stats
  const {
    data: heatmapStats,
    isLoading: heatmapLoading,
    error: heatmapError,
  } = useSWR<HeatmapStats>(["heatmapStats", filters], fetchHeatmapStatsSWR, {
    revalidateOnFocus: false,
    dedupingInterval: 10000,
  });

  // Call setLoading when loading state changes
  useEffect(() => {
    if (setLoading) setLoading(heatmapLoading);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [heatmapLoading]);

  // Get the current data series based on active metric
  const getCurrentData = (): HeatmapSeries[] => {
    if (!heatmapStats) return [];
    return heatmapStats[activeMetric] || [];
  };

  // Get color scheme based on active metric and theme
  const getColorScheme = () => {
    if (isDarkMode) {
      switch (activeMetric) {
        case "missed":
          return [
            "#5a0909", // Higher intensity dark red for zero
            "#8c0013", // Darkest red for lowest values
            "#a3001a",
            "#b51029",
            "#d41c35", // Transition to darker red for higher values
            "#f83a3a",
            "#ff6b6b", // Brightest red for highest values
          ];
        case "answered":
          return [
            "#0a3d1d", // Higher intensity dark green for zero
            "#05512a", // Darkest green for lowest values
            "#086537",
            "#0a7a3d",
            "#0e9d4f", // Transition to darker green for higher values
            "#22c085",
            "#3ad598", // Brightest green for highest values
          ];
        default:
          return [
            "#0a2c52", // Higher intensity dark blue for zero
            "#0e4b8a", // Darkest blue for lowest values
            "#1256a5",
            "#1662b0",
            "#1e7cd2", // Transition to darker blue for higher values
            "#25c1df",
            "#47cfe8", // Brightest blue/aqua for highest values
          ];
      }
    } else {
      switch (activeMetric) {
        case "missed":
          return [
            "#ffcaca", // Higher intensity light red for zero
            "#ff9999", // More intense light coral/pink for lower values
            "#ff7070",
            "#ff4d4d",
            "#e62e3c", // More intense transition to darker red
            "#c71c29",
            "#a3101c",
          ];
        case "answered":
          return [
            "#b3ffb3", // Higher intensity light green for zero
            "#75e0b0", // More intense light mint/teal for lower values
            "#4cd896",
            "#20cc7d",
            "#089c56", // More intense transition to darker green
            "#06753f",
            "#044f2b",
          ];
        default:
          return [
            "#b3d9ff", // Higher intensity light blue for zero
            "#76dbf5", // More intense light aqua/cyan for lower values
            "#47cfe8",
            "#1cc2e0",
            "#2170cc", // More intense transition to darker blue
            "#1a5aa8",
            "#144580",
          ];
      }
    }
  };

  // Chart configuration with a color scale based on the active metric
  const options: ApexOptions = {
    theme: {
      mode: isDarkMode ? "dark" : "light",
    },
    chart: {
      animations: {
        enabled: isAnimationActive,
      },
      type: "heatmap",
      background: isDarkMode ? "#1f1f1f" : "#f4f4f5",
      toolbar: {
        show: false, // Disable the native export button
      },
    },
    // Add states configuration to disable hover effects
    states: {
      hover: {
        filter: {
          type: "contrast", // Disables the hover effect
        },
      },
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: currentParams.dataLabelSize,
      },
    },
    colors: getColorScheme(),
    xaxis: {
      categories: Array.from({ length: 24 }, (_, i) => `${i}h`),
      labels: {
        show: currentParams.showLabels,
      },
    },
    yaxis: {
      labels: {
        show: currentParams.showLabels,
      },
    },
    stroke: {
      width: currentParams.showBorders ? 1 : 0,
      colors: [isDarkMode ? "#404040" : "#eceff1"],
    },
    grid: {
      show: false,
    },
    plotOptions: {
      heatmap: {
        radius: currentParams.cellRadius,
        enableShades: false,
        colorScale: {
          ranges: [
            { from: 0, to: 0, color: getColorScheme()[0] }, // Special color for zero
            { from: 1, to: 3, color: getColorScheme()[1] },
            { from: 4, to: 6, color: getColorScheme()[2] },
            { from: 7, to: 12, color: getColorScheme()[3] },
            { from: 13, to: 18, color: getColorScheme()[4] },
            { from: 19, to: 29, color: getColorScheme()[5] },
            { from: 30, to: 1000, color: getColorScheme()[6] },
          ],
        },
      },
    },
  };

  // Get title based on active metric
  const getTitle = () => {
    switch (activeMetric) {
      case "missed":
        return "Missed Calls Heatmap";
      case "answered":
        return "Answered Calls Heatmap";
      default:
        return "Call Volume Heatmap";
    }
  };

  // Export using ApexCharts native method
  const exportApexChart = async (format: "png" | "svg" | "pdf") => {
    const imgContainer = document.getElementById(imageContainerId);
    const chartContainer = document.getElementById(chartContainerId);
    const parent = chartContainer?.parentElement;

    try {
      let ApexChartsInstance = ApexChartsModule;
      if (!ApexChartsInstance && typeof window !== "undefined") {
        // @ts-expect-error: Access global ApexCharts
        ApexChartsInstance = window.ApexCharts;
      }

      if (ApexChartsInstance && imgContainer && chartContainer && parent) {
        const { imgURI } = await ApexChartsInstance.exec(chartId, "dataURI");
        const img = document.createElement("img");
        img.src = imgURI;
        img.style.width = "100%";
        img.style.height = "350px";
        img.style.objectFit = "contain";

        imgContainer.innerHTML = "";
        imgContainer.appendChild(img);
        imgContainer.classList.remove("hidden");
        parent.removeChild(chartContainer);
      }

      if (format === "pdf") {
        await exportChartAsPDF(
          chartRef.current,
          `call-heatmap-${activeMetric}`
        );
      } else {
        await exportChartAsImage(
          chartRef.current,
          format,
          `call-heatmap-${activeMetric}`
        );
      }
    } catch (err) {
      console.error(`Export ${format.toUpperCase()} failed:`, err);
    } finally {
      if (imgContainer && chartContainer && parent) {
        parent.appendChild(chartContainer);
        imgContainer.classList.add("hidden");
        imgContainer.innerHTML = "";
      }
    }
  };

  // Define export items for the dropdown
  const exportItems: ExportDropdownItem[] = [
    {
      title: "Export as PNG",
      action: () => exportApexChart("png"),
    },
    {
      title: "Export as SVG",
      action: () => exportApexChart("svg"),
    },
    {
      title: "Export as PDF",
      action: () => exportApexChart("pdf"),
    },
  ];

  return (
    <div ref={chartRef} className="flex max-w-full">
      <Card className="bg-voxa-neutral-50 dark:bg-voxa-neutral-900 dark:border-voxa-neutral-700 transition-all duration-150 w-full">
        <CardHeader className="pb-0">
          {/* Add flex container for title and export button */}
          <div className="flex flex-row items-start justify-between mb-2">
            <div>
              <CardTitle className="dark:text-voxa-neutral-50">
                {getTitle()}
              </CardTitle>
            </div>

            <ExportDropdown
              items={exportItems}
              buttonText="Export"
              loadingText="Exporting..."
              className=""
            />
          </div>

          {/* Keep tabs section intact */}
          <Tabs
            value={activeMetric}
            onValueChange={(value) => setActiveMetric(value as CallMetricType)}
            className="mt-2"
          >
            <TabsList className="flex flex-wrap h-auto dark:bg-transparent w-fit mx-auto">
              <TabsTrigger
                value="total"
                className="text-sm w-fit dark:data-[state=active]:bg-voxa-neutral-800"
              >
                Total
              </TabsTrigger>
              <TabsTrigger
                value="answered"
                className="text-sm w-fit dark:data-[state=active]:bg-voxa-neutral-800"
              >
                Answered
              </TabsTrigger>
              <TabsTrigger
                value="missed"
                className="text-sm w-fit dark:data-[state=active]:bg-voxa-neutral-800"
              >
                Missed
              </TabsTrigger>
              {/* For some reason, if we remove this div, the "Missed" TabsTrigger won't be displayed in the exported chart */}
              <div></div>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent className="flex justify-center items-center px-1 pt-1">
          <div className="w-full">
            {heatmapLoading ? (
              <div className="h-[350px] flex items-center justify-center">
                <MainLoader />
              </div>
            ) : heatmapError ? (
              <div className="text-center text-red-500 h-[350px] flex items-center justify-center">
                <p>Error loading data</p>
              </div>
            ) : !heatmapStats ? (
              <div className="text-center text-amber-500 h-[350px] flex items-center justify-center">
                <p>No data available</p>
              </div>
            ) : (
              <div>
                <div id={imageContainerId} className="hidden"></div>
                <div id={chartContainerId}>
                  <Chart
                    options={{
                      ...options,
                      chart: {
                        ...options.chart,
                        id: chartId,
                      },
                    }}
                    series={getCurrentData().map((s) => ({ ...s }))}
                    type="heatmap"
                    height={350}
                  />
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HeatmapChart;
