"use server"

import CRM from "@/models/CRM"
import { getEntrepriseByAdminID } from "./Entreprise"

export async function UpdateRingoverAPIKeyBaseUrls(
    apikey: string,
    baseUrl: string,
): Promise<{ success: boolean; error?: string }> {
    try {
        const entrepriseResponse = await getEntrepriseByAdminID();
        if (!entrepriseResponse.success) {
            return { success: false, error: entrepriseResponse.error };
        }
        
        const entrepriseID = entrepriseResponse.entreprise._id;

        const existingCRM = await CRM.findOne({ entreprise: entrepriseID });

        if (existingCRM) {
            existingCRM.apiKey = apikey;
            existingCRM.baseUrl = baseUrl;

            await existingCRM.save()

            return { success: true };
        } else {
            const newCRM = await CRM.create({
                name: "Ringover",
                entreprise: entrepriseID,
                apiKey: apikey,
                baseUrl: baseUrl,
            });

            if (!newCRM) {
                throw new Error("Failed to update Ringover API Key and Base URLs");
            }

            return { success: true };
        }
    } catch (err: any) {
        return { success: false, error: err.message };
    }
}


export async function GetEntrepriseRingoverParams(): Promise<{success: boolean, error?: string, apiKey?: string, baseUrl?: string}> {
    try {
        const entrepriseResponse = await getEntrepriseByAdminID()
        if (!entrepriseResponse.success) return {success: false, error: entrepriseResponse.error}
        const entrepriseID = entrepriseResponse.entreprise._id
        const crm = await CRM.findOne({entreprise: entrepriseID, name: "Ringover"})
        if (!crm) throw new Error("Failed to fetch Ringover API Key and Base Urls")
        return {success: true, apiKey: crm.apiKey, baseUrl: crm.baseUrl}
    } catch (err: any) {
        return {success: false, error: err.message}
    }

} 