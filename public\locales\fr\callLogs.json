{"pageTitle": "Journaux d'appels", "waitingList": {"calls": "appels", "title": "Liste d'attente", "noCallsMessage": "Aucun appel en liste d'attente", "helpCenter": "Centre d'aide", "description": "Les appelants en attente apparaîtront ici. Prenez les appels en attente à tout moment pour réduire le temps d'attente et augmenter la satisfaction client.", "noCallsForDay": "Aucun appel pour ce jour", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er", "beforeDate": "Avant le {{date}}"}, "filters": {"title": "Filtres", "sid": "SID", "conversationId": "ID de conversation", "recipientDetails": "<PERSON>é<PERSON> du destinataire", "name": "Nom", "phoneNumber": "Numéro de téléphone", "conversationType": "Type de conversation", "interactionType": "Type d'interaction", "dateRange": "Période", "search": "<PERSON><PERSON><PERSON>", "searching": "Recherche en cours...", "clearAllFilters": "Effacer tous les filtres", "noResults": "Pas d'appels avec ces filtres", "types": {"whatsapp": "WhatsApp", "sms": "SMS", "call": "<PERSON><PERSON>", "meet": "Réunion", "incoming": "Entrant", "outgoing": "Sortant", "missed": "<PERSON><PERSON><PERSON>", "answered": "Répondu"}}, "badges": {"sid": "SID", "conversationId": "ID de conversation", "name": "Nom", "phone": "Téléphone", "date": "Date", "type": "Type", "call": "<PERSON><PERSON>"}, "buttons": {"filteredCalls": "appels filtrés", "search": "<PERSON><PERSON><PERSON>", "searching": "Recherche", "exportMissedCalls": "Exporter les appels manqués", "filter": "<PERSON><PERSON><PERSON>", "loadMore": "Charger plus", "submitting": "Soumission..."}, "export": {"title": "Exporter les appels manqués", "selectedDateRange": "Sélectionner une période", "description": "Sélectionnez une période jusqu'à 7 jours pour exporter les appels manqués.", "submitExport": "Soumettre l'exportation"}, "callDetails": {"title": "Dé<PERSON> de l'appel", "smsDetails": "<PERSON><PERSON><PERSON> du SMS", "whatsappDetails": "Détails WhatsApp", "audioRecording": "Enregistrement audio", "processingAudio": "Traitement audio en cours, veuil<PERSON>z rafraîchir dans quelques secondes !", "conversationFeelings": "Sentiments de la conversation", "summary": "Résumé", "outgoingMessage": "Message sortant", "incomingMessage": "Message entrant", "transfer": "Transfert", "meetingStatistics": "Statistique des Intervenants", "mostTalkativeSpeakerName": "Intervenant ayant le plus parlé", "leastTalkativeSpeaker": "Intervenant ayant le moins parlé", "total_speaking_time_seconds": "Temps total de parole", "percent": "pourcent", "evaluateAI": "Évaluer l'IA"}, "search": {"byGoalId": "Rechercher par ID d'objectif", "goalId": "ID d'objectif"}, "notesTabs": {"account": "<PERSON><PERSON><PERSON>", "password": "Historique", "callFlow": "Flux d'appel", "messageFlow": "Flux de message", "whatsappFlow": "Flux de conversation WhatsApp", "trackFlow": "<PERSON><PERSON><PERSON> le flux de cet", "call": "appel", "message": "message", "whatsapp": "messages WhatsApp", "callSid": "SID d'appel:", "parentSid": "SID parent:", "childSid": "SID enfant:", "goalId": "ID d'objectif:", "conversationHistory": "Historique des conversations", "timeline": {"clientEngagement": "Taux de participation Client", "completed": "<PERSON><PERSON><PERSON><PERSON>", "incoming": "A<PERSON> entrant", "outgoing": "Appel sortant", "transferred": "<PERSON><PERSON><PERSON><PERSON>", "transferedFrom": "<PERSON><PERSON><PERSON><PERSON>", "ringing": "Son<PERSON><PERSON>", "callEnded": "<PERSON><PERSON> terminé", "voicemail": "Messagerie vocale détectée", "pickedUp": "Décroché par le client", "pickedUpAgent": "Décroché par l'agent", "transferredTo": "Transféré à l'agent", "transferedTo": "Transféré à l'agent", "missedCall": "<PERSON><PERSON> manq<PERSON>", "sentMessage": "Message envoyé", "sentWhatsapp": "Message WhatsApp envoyé", "receivedMessage": "Message reçu", "receivedWhatsapp": "Message WhatsApp reçu", "callEndedBy": {"ai": "Appel terminé par l'IA", "client": "Appel terminé par le client", "support": "Appel terminé par le support", "thirdParty": "Appel terminé par un tiers", "machineDetected": "Machine détectée", "noAnswer": "Pas de réponse", "callCompleted": "<PERSON><PERSON> terminé"}}}, "numberHistory": {"exportConversations": "Exporter les conversations", "conversationsFound": " conversations", "noConversationHistory": "Aucun historique de conversation trouvé", "sid": "SID", "status": "Statut", "from": "De", "date": "Date"}, "callLogGroups": {"transfer": "Transfert"}, "stats": {"clientsNotReached": "Clients non atteints", "missedCalls": "<PERSON><PERSON><PERSON> manq<PERSON>", "voicemailDetected": "Messagerie vocale détectée", "answeredNoTransfer": "Répondu sans transfert", "notInterested": "<PERSON><PERSON>", "agentUnavailable": "Agent indisponible", "transferredToAgents": "Transférés aux agents", "sentMessages": "Messages envoyés", "whatsApp": "WhatsApp", "sms": "SMS", "dropped": "Abandonnés", "detected": "Détectés", "voicemailSent": "Messagerie vocale envoyée"}, "entities": "Entités", "speaker": "Intervenant", "type": "Type", "text": "Texte"}