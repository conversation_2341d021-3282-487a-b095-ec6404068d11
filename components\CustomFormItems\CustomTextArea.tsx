import { Textarea } from "../ui/textarea";
import { Label } from "../ui/label";
import { JSX } from "react";

interface TextareaProps {
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  readonly?: boolean;
  disabled?: boolean;
  rows?: number;
  className?: string;
  parentClassName?: string;
  labelIcon?: JSX.Element;
}

export default function CustomTextarea({ props }: { props?: TextareaProps }) {
  return (
    <div
      className={`relative grid w-full items-start gap-1 ${props?.parentClassName}`}
    >
      {props?.label && (
        <Label
          className="text-sm flex gap-2 items-center"
          htmlFor={props?.name}
        >
          {props?.labelIcon}
          {props?.label}
          {props?.required && (
            <span className="text-red-500 -translate-x-1"> * </span>
          )}
        </Label>
      )}
      <Textarea
        id={props?.name}
        name={props?.name}
        className={`mt-1 ${props?.className}`}
        placeholder={props?.placeholder}
        value={props?.value}
        onChange={props?.onChange}
        onBlur={props?.onBlur}
        onFocus={props?.onFocus}
        readOnly={props?.readonly}
        disabled={props?.disabled}
        rows={props?.rows ?? 6}
      />
    </div>
  );
}
