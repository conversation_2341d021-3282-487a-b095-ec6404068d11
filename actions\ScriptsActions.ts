"use server"

import dbConnect from "@/lib/mongodb"
import AssistantScript from "@/models/AssistantScript"
import { getEntrepriseByAdminID } from "./Entreprise"
import Entreprise from "@/models/Entreprise"
import mongoose from "mongoose"

export async function CreateScript(content: string, name: string, tags: { name?: string, color?: string }[]): Promise<{ success: boolean, error?: string, script?: any }> {
    try {
        const entrepriseResponse = await getEntrepriseByAdminID();
        if (!entrepriseResponse.success) {
            return { success: false, error: entrepriseResponse.error };
        }

        const entrepriseID = entrepriseResponse.entreprise._id;
        await dbConnect();

        const utcDate = new Date()

        const Script = await AssistantScript.create({
            name,
            content,
            tags,
            entreprise: entrepriseID,
            createdAt: utcDate
        });

        if (!Script) {
            return { success: false, error: "Failed to create script" };
        }

        const updatedEntreprise = await Entreprise.findByIdAndUpdate(entrepriseID, {
            $push: { scripts: Script._id }
        });

        if (!updatedEntreprise) {
            return { success: false, error: "Failed to update entreprise" };
        }

        const FilteredScript = {
            id: Script._id.toString(),
            name: Script.name,
            content: Script.content,
            tags: Script.tags,
            createdAt: Script.createdAt?.toString(),
        };

        return { success: true, script: FilteredScript };
    } catch (err: any) {
        return { success: false, error: err.message };
    }
}


export async function getEntrepriseScripts(): Promise<{
    success: boolean;
    scripts?: any;
    error?: string;
  }> {
    try {
      const entrepriseResponse = await getEntrepriseByAdminID();
  
      if (!entrepriseResponse.success) {
        return { success: false, error: entrepriseResponse.error };
      }
  
      const Scripts = await AssistantScript.find({
        entreprise: entrepriseResponse.entreprise._id,
      });
  
      if (!Scripts) {
        return { success: false, error: "Failed to get scripts" };
      }
  
      const filteredScripts = Scripts.map((script) => ({
        id: script._id.toString(),
        name: script.name,
        content: script.content,
        tags: script.tags,
        createdAt: script.createdAt?.toString(),
      }));
  
      return { success: true, scripts: filteredScripts };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  }
  

export async function DeleteScript(scriptID: string): Promise<{success: boolean, error?: string}> {
    try{
        const EntrepriseResponse = await getEntrepriseByAdminID()
        if(!EntrepriseResponse.success){
            return {success: false, error: EntrepriseResponse.error}
        }
        await dbConnect()
        const ScriptObjectID = new mongoose.Types.ObjectId(scriptID)
        const deletedScript = await AssistantScript.findByIdAndDelete(ScriptObjectID)
        await Entreprise.findByIdAndUpdate(EntrepriseResponse.entreprise._id, {
            $pull: {
                scripts: ScriptObjectID
            }
        })

        if(!deletedScript){
            return {success: false, error: "Failed to delete script"}
        }
        return {success: true}
    }catch(err: any){
        return {success: false, error: err.message}
    }

}

export async function getScriptById(scriptID: string): Promise<{ success: boolean; script?: any; error?: string }> {
    try {
        await dbConnect();
        const script = await AssistantScript.findById(scriptID);
        if (!script) {
            return { success: false, error: "Script not found" };
        }
        return {
            success: true,
            script: {
                id: script._id.toString(),
                name: script.name,
                content: script.content,
                tags: script.tags,
                createdAt: script.createdAt?.toString(),
            },
        };
    } catch (err: any) {
        return { success: false, error: err.message };
    }
}

export async function UpdateScript(
    scriptID: string,
    name: string,
    content: string,
    tags: { name?: string; color?: string }[]
): Promise<{ success: boolean; script?: any; error?: string }> {
    try {
        await dbConnect();
        const updatedScript = await AssistantScript.findByIdAndUpdate(
            scriptID,
            {
                $set: {
                    name,
                    content,
                    tags,
                },
            },
            { new: true }
        );
        if (!updatedScript) {
            return { success: false, error: "Failed to update script" };
        }
        return {
            success: true,
            script: {
                id: updatedScript._id.toString(),
                name: updatedScript.name,
                content: updatedScript.content,
                tags: updatedScript.tags,
                createdAt: updatedScript.createdAt?.toString(),
            },
        };
    } catch (err: any) {
        return { success: false, error: err.message };
    }
}