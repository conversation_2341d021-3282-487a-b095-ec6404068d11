import { cn } from "@/lib/utils";
import { setFiltersInput } from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { useState } from "react";
import { useDispatch } from "react-redux";

// Define a type for the date range result
interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
}

type DateRangeType =
  | "today"
  | "yesterday"
  | "lastWeek"
  | "last15Days"
  | "thisMonth"
  | null;

export default function DateRangePresets() {
  const dispatch = useDispatch();
  const [dateRangeType, setDateRangeType] = useState<DateRangeType>(null);

  const handleTypeChange = (type: DateRangeType) => {
    if (type === null) return;

    const { startDate, endDate } = getDateRangeForType(type);
    setDateRangeType(type);
    dispatch(
      setFiltersInput({
        startDate: startDate?.toISOString() || "",
        endDate: endDate?.toISOString() || "",
      })
    );
  };

  const getDateRangeForType = (type: DateRangeType): DateRange => {
    const now = new Date();
    const startOfDay = (date: Date): Date =>
      new Date(date.setHours(0, 0, 0, 0));
    // const endOfDay = (date: Date): Date =>
    //   new Date(date.setHours(23, 59, 59, 999));

    switch (type) {
      case "today":
        return { startDate: startOfDay(new Date()), endDate: new Date() };
      case "yesterday":
        const yesterday = new Date(now);
        yesterday.setDate(now.getDate() - 1);
        return {
          startDate: startOfDay(yesterday),
          endDate: null,
        };
      case "lastWeek":
        const sevenDaysAgo = new Date(now);
        sevenDaysAgo.setDate(now.getDate() - 7);
        return { startDate: startOfDay(sevenDaysAgo), endDate: null };
      case "last15Days":
        const fifteenDaysAgo = new Date(now);
        fifteenDaysAgo.setDate(now.getDate() - 15);
        return { startDate: startOfDay(fifteenDaysAgo), endDate: null };
      case "thisMonth":
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        return { startDate: startOfDay(firstDay), endDate: null };
      default:
        return { startDate: null, endDate: null };
    }
  };

  return (
    <div className="flex flex-col gap-2 w-full">
      {(
        [
          { value: "today", label: "Today" },
          { value: "yesterday", label: "Yesterday" },
          { value: "lastWeek", label: "Last Week" },
          { value: "last15Days", label: "Last 15 Days" },
          { value: "thisMonth", label: "This Month" },
        ] as const
      ).map((option) => (
        <div
          key={option.value}
          onClick={() => handleTypeChange(option.value)}
          className={cn(
            "cursor-pointer rounded-md py-1 px-3 text-sm text-center transition-all",
            "hover:bg-primary/10 border border-border",
            dateRangeType === option.value
              ? "bg-primary/20 border-primary font-medium shadow-sm"
              : "bg-background"
          )}
        >
          {option.label}
        </div>
      ))}
    </div>
  );
}
