import { Button } from "@/components/ui/button";
import { parsePhoneNumberFromString } from "libphonenumber-js";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import CustomInput from "../CustomFormItems/Input";
import { CountriesSelect } from "../dropdowns/CountriesSelect";
import { CountryCode } from "libphonenumber-js";
import { useState } from "react";
import { Label } from "../ui/label";
import { toast } from "sonner";
import { createTaAgent } from "@/actions/TaActions";

export function CreateTAUserDialog() {
  const [country, setCountry] = useState<CountryCode | "">("FR");
  const [agentName, setAgentName] = useState<string>("");
  const [agentNumber, setAgentNumber] = useState<string>("");
  const [agentEmail, setAgentEmail] = useState<string>("");
  const [open, setOpen] = useState(false);

  const [loading, setLoading] = useState(false);
  const handleCreateAgent = async () => {
    try {
      setLoading(true);
      const phoneNumber = parsePhoneNumberFromString(
        agentNumber,
        country as CountryCode
      );

      if (!phoneNumber || !phoneNumber.isValid()) {
        toast.error("Invalid phone number for selected country.");
        return;
      }
      if (!agentName || !agentNumber || !agentEmail) {
        toast.error("Please fill all the fields.");
        return;
      }

      const formattedNumber = phoneNumber.number;

      const response = await createTaAgent(
        agentName,
        agentEmail,
        formattedNumber
      );

      if (response.success) {
        toast.success("Agent created successfully");
        setAgentName("");
        setAgentNumber("");
        setAgentEmail("");
        setCountry("");
        setOpen(false);
        window.location.reload();
      } else {
        toast.error(response.error || "Failed to create agent");
      }
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
        if (!open) {
          setAgentName("");
          setAgentNumber("");
          setAgentEmail("");
        }
      }}
    >
      <DialogTrigger asChild>
        <Button className="w-fit self-end">
          <span className="font-medium text-xs py-1 px-2">Create Agent</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] shadow-none">
        <DialogHeader>
          <DialogTitle>Create new Entreprise Agent</DialogTitle>
          <DialogDescription className="text-center text-foreground"></DialogDescription>
          <CustomInput
            props={{
              label: "Agent Name",
              placeholder: "Enter agent name",
              type: "text",
              required: true,
              className: "mb-2",
              onChange: (e) => setAgentName(e.target.value),
              value: agentName,
            }}
          />
          <CustomInput
            props={{
              label: "Agent Email",
              placeholder: "Enter agent email",
              type: "text",
              required: true,
              className: "mb-2",
              onChange: (e) => setAgentEmail(e.target.value),
              value: agentEmail,
            }}
          />
          <div className="w-full max-sm:flex-col">
            <div className="w-full flex flex-col gap-1 justify-start items-start mb-5">
              <Label className="text-sm text-foreground font-medium">
                Country <span className="text-red-500">*</span>
              </Label>
              <CountriesSelect
                country={country}
                selectCountry={(newCountry: "" | CountryCode) =>
                  setCountry(newCountry)
                }
                classnames="translate-y-px py-5 w-full"
              />
            </div>
          </div>
          <CustomInput
            props={{
              label: "Phone Number",
              placeholder: "Enter agent number",
              type: "text",
              required: true,
              className: "mb-2",
              parentClassName: "mt-4",
              onChange: (e) => setAgentNumber(e.target.value),
              value: agentNumber,
            }}
          />
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild disabled={loading}>
            <Button className="hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950">
              Cancel
            </Button>
          </DialogClose>
          <Button
            onClick={handleCreateAgent}
            disabled={loading}
            className={`
              ${
                loading
                  ? "active:bg-blue-500/70"
                  : "bg-blue-500 hover:bg-blue-600 active:bg-blue-500/70"
              } text-white
            `}
          >
            {loading ? "Creating..." : "Create Agent"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
