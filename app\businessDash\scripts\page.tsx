"use client";

import ImportScript from "@/components/popups/ImportScript";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import {
  CreateScript,
  DeleteScript,
  getEntrepriseScripts,
  getScriptById,
  UpdateScript,
} from "@/actions/ScriptsActions";
import ScriptContentPopup from "@/components/popups/EditScriptContent";
import { getTimeFromTimestamp } from "@/lib/Strings/DateFormat";
import { CreateCustomScript } from "@/components/Sidebars/CreateCustomSciptSheet";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import MainLoader from "@/components/Loaders/MainLoader";
import CustomPagination from "@/components/pagination/CustomPagination";
import { usePagination } from "@/hooks/usePagination";
import ScriptCard from "@/components/Dashboard/Cards/ScriptCard";
import { Tag } from "@/components/Dashboard/Cards/ScriptCard2";
import { EditCustomScriptSheet } from "@/components/Sidebars/EditCustomScriptSheet";

export default function Scripts() {
  const [importFile, setImportFile] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [scriptName, setScriptName] = useState("");
  const [Scripts, setScripts] = useState<
    {
      id: string;
      name: string;
      content: string;
      createdAt: number;
      usedBy: number;
      tags: Tag[];
    }[]
  >([]);
  const [scriptsLoading, setScriptsLoading] = useState(true);
  const [ScriptContent, setScriptContent] = useState<string>("");
  const [ScriptContentOpen, setScriptContentOpen] = useState<boolean>(false);
  const { t } = useTranslation("scripts");
  const [editSheetOpen, setEditSheetOpen] = useState(false);
  const [editScript, setEditScript] = useState<any>(null);
  const [editLoading, setEditLoading] = useState(false);

  const {
    currentItems: currentScripts,
    currentPage: currentScriptPage,
    setCurrentPage: setCurrentScriptPage,
  } = usePagination(Scripts, 18);

  const uploadScript = async () => {
    if (!scriptName) {
      toast.error("Please enter a script name");
      return;
    }
    if (files.length === 0 || files.length > 1) {
      toast.error("Please select one file");
      return;
    }

    setLoading(true);
    try {
      const file = files[0];
      if (file.type !== "text/plain") {
        toast.error("Only .txt files are allowed");
        setError(true);
        return;
      }
      if (file.size > 20 * 1024) {
        toast.error("File size must be 20KB or lower.");
        return;
      }

      const reader = new FileReader();

      const fileContent = await new Promise<string>((resolve, reject) => {
        reader.onload = () => {
          resolve(reader.result as string);
        };
        reader.onerror = (err) => {
          console.error(err);
          reject(new Error("Failed to read the file"));
        };
        reader.readAsText(file);
      });
      const response = await CreateScript(fileContent, scriptName, []);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      if (response.script) {
        setScripts([...Scripts, response.script]);
        toast.success("Script uploaded successfully");
        setImportFile(false);
        setScriptName("");
      }
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };
  const handleFileUpload = (files: File[]) => {
    if (files.length === 0) return;

    const file = files[0];
    if (file.type !== "text/plain") {
      toast.error("Only .txt files are allowed");
      setError(true);
      return;
    }

    if (file.size > 20 * 1024) {
      toast.error("File size must be 20KB or lower.");
      return;
    }

    setFiles([file]);
    setError(false);
    console.log(file);
  };

  const getScripts = async () => {
    try {
      const response = await getEntrepriseScripts();
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      setScripts(response.scripts);
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setScriptsLoading(false);
    }
  };

  const handleClickEyeIcon = (content: string) => {
    setScriptContent(content);
    setScriptContentOpen(true);
  };

  const handleUpdateButton = async (id: string) => {
    setEditLoading(true);
    try {
      const response = await getScriptById(id);
      if (!response.success || !response.script) {
        toast.error(response.error || "Failed to fetch script");
        return;
      }
      setEditScript(response.script);
      setEditSheetOpen(true);
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setEditLoading(false);
    }
  };

  const handleEditSave = async (updated: {
    name: string;
    content: string;
    tags: any[];
  }) => {
    if (!editScript) return;
    setEditLoading(true);
    try {
      const response = await UpdateScript(
        editScript.id,
        updated.name,
        updated.content,
        updated.tags
      );
      if (!response.success || !response.script) {
        toast.error(response.error || "Failed to update script");
        return;
      }
      setScripts((prev) =>
        prev.map((s) =>
          s.id === editScript.id ? { ...s, ...response.script } : s
        )
      );
      toast.success("Script updated successfully");
      setEditSheetOpen(false);
      setEditScript(null);
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setEditLoading(false);
    }
  };

  const handleDeleteButton = async (id: string) => {
    try {
      const reponse = await DeleteScript(id);
      if (!reponse.success) {
        toast.error(reponse.error);
        return;
      }
      const newScripts = Scripts.filter((script) => script.id !== id);
      setScripts(newScripts);
      toast.success("Script deleted successfully");
    } catch (err: any) {
      toast.error(err.message);
    }
  };

  useEffect(() => {
    getScripts();
  }, []);

  return (
    <div className="relative w-full flex flex-col gap-4">
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        {t("page.title")}
      </h1>
      <div className="w-full flex justify-between max-lg:flex-wrap text-nowrap gap-2 items-center font-semibold">
        <div className="w-full flex max-sm:flex-wrap gap-4 sm:gap-6 md:gap-2 items-center justify-between">
          <p className="w-full max-sm:text-center">
            {t("page.totalScripts")}{" "}
            <span className="max-lg:w-full text-voxa-teal-500">
              {Scripts.length}
            </span>
          </p>
        </div>
        <div className="max-lg:w-full max-sm:flex-wrap w-fit flex justify-end gap-2 items-center">
          <ImportScript
            setOpen={setImportFile}
            open={importFile}
            scriptName={scriptName}
            setScriptName={setScriptName}
            Loading={loading}
            setLoading={setLoading}
            uploadScript={uploadScript}
            setImportFile={setImportFile}
            setError={setError}
            setFiles={setFiles}
            handleFileUpload={handleFileUpload}
            files={files}
            error={error}
          />
          <CreateCustomScript setScripts={setScripts} Scripts={Scripts} />
        </div>
      </div>
      {scriptsLoading ? (
        <div className="absolute w-full flex justify-center items-center h-[calc(100vh-2rem)] sm:h-[calc(100vh-3rem)]">
          <MainLoader />
        </div>
      ) : (
        <>
          <div className="mt-4 w-full grid lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {currentScripts.map((script, index) => {
              const date = getTimeFromTimestamp(script.createdAt);
              return (
                <ScriptCard
                  key={index}
                  script={script}
                  date={date}
                  t={t}
                  handleUpdateButton={handleUpdateButton}
                  handleDeleteButton={handleDeleteButton}
                  handleClickEyeIcon={handleClickEyeIcon}
                />
              );
            })}
          </div>
          <CustomPagination
            itemsPerPage={18}
            totalItems={Scripts.length}
            currentPage={currentScriptPage}
            onPageChange={setCurrentScriptPage}
          />
        </>
      )}
      {ScriptContentOpen && (
        <ScriptContentPopup
          setScriptContentOpen={setScriptContentOpen}
          content={ScriptContent}
        />
      )}
      <EditCustomScriptSheet
        open={editSheetOpen}
        setOpen={setEditSheetOpen}
        script={editScript}
        onSave={handleEditSave}
        loading={editLoading}
        setLoading={setEditLoading}
      />
    </div>
  );
}
