function InfiniteScroll() {
  const logos = [
    { name: "ONRTECH" },
    { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>" },
    { name: "<PERSON><PERSON><PERSON>" },
  ];

  return (
    <ul className="z-20 mt-8 bg-indigo-600 py-6 rounded-md bg-clip-padding group backdrop-filter bg-gradient-to-br from-teal-500/20 via-transparent to-voxa-teal-900 backdrop-blur-sm bg-opacity-10 border-gray-100 flex items-center justify-center md:justify-start gap-4 space-x-16 [&_li]:mx-8 overflow-hidden max-w-[1600px] mx-auto mask-gradient border-2 drop-shadow-md shadow-sm">
      <div className="flex space-x-16 gap-4 animate-loop-scroll group-hover:paused items-center">
        {logos.map((logo, index) => (
          <div
            key={index}
            className={`text-center text-white font-bold text-2xl max-w-full drop-shadow-lg shadow-black`}
          >
            <p className="leading-tight">{logo.name}</p>
          </div>
        ))}
      </div>
      <div
        className="flex space-x-16 gap-4 animate-loop-scroll group-hover:paused items-center"
        aria-hidden="true"
      >
        {logos.map((logo, index) => (
          <div
            key={index}
            className={`text-center text-white font-bold text-2xl max-w-full drop-shadow-lg shadow-black`}
          >
            <p className="leading-tight">{logo.name}</p>
          </div>
        ))}
      </div>
      <div
        className="flex space-x-16 gap-4 animate-loop-scroll group-hover:paused items-center"
        aria-hidden="true"
      >
        {logos.map((logo, index) => (
          <div
            key={index}
            className={`text-center text-white font-bold text-2xl max-w-full drop-shadow-lg shadow-black`}
          >
            <p className="leading-tight">{logo.name}</p>
          </div>
        ))}
      </div>
    </ul>
  );
}

export default InfiniteScroll;
