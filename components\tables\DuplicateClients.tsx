import {
  Table,
  TableBody,
  Table<PERSON>aption,
  TableCell,
  TableHeader,
  TableRow,
  TableHead,
} from "@/components/ui/table";
import { parsePhoneNumberFromString } from "libphonenumber-js";
import * as Flags from "country-flag-icons/react/3x2";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { Edit, Trash2 } from "lucide-react";

interface WarningEntry {
  phone: string;
  name: string;
  type: "duplicate";
  index?: number; // Index in data_rows
}

interface ClientTableProps {
  duplicateNumbers: WarningEntry[];
  onEditNumber?: (index: number, newNumber: string) => void;
  onDeleteNumber?: (index: number) => void;
}

function getFlagComponent(countryCode?: string) {
  if (!countryCode) return null;
  const Flag = (Flags as any)[countryCode.toUpperCase()];
  return Flag ? (
    <Flag style={{ width: 24, display: "inline-block", marginRight: 4 }} />
  ) : null;
}

function getCountryCode(phone: string) {
  try {
    const phoneNumber = parsePhoneNumberFromString(phone);
    return phoneNumber?.country;
  } catch {
    return undefined;
  }
}

export function DuplicateNumbersTable({
  duplicateNumbers,
  onEditNumber,
  onDeleteNumber,
}: ClientTableProps) {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editedNumber, setEditedNumber] = useState<string>("");
  const [deletingIndex, setDeletingIndex] = useState<number | null>(null);

  const handleEdit = (index: number, number: string) => {
    setEditingIndex(index);
    setEditedNumber(number);
  };

  const handleSave = (index: number) => {
    if (onEditNumber && duplicateNumbers[index].index !== undefined) {
      onEditNumber(duplicateNumbers[index].index!, editedNumber);
    }
    setEditingIndex(null);
  };

  const handleCancel = () => {
    setEditingIndex(null);
    setDeletingIndex(null);
  };

  const handleDeleteClick = (index: number) => {
    setDeletingIndex(index);
  };

  const handleConfirmDelete = (index: number) => {
    if (onDeleteNumber && duplicateNumbers[index].index !== undefined) {
      onDeleteNumber(duplicateNumbers[index].index!);
    }
    setDeletingIndex(null);
  };
  return (
    <Table className="max-h-48">
      <TableCaption>
        A list of duplicate phone numbers and their client names.
      </TableCaption>
      <TableHeader>
        <TableRow className="hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-950">
          <TableHead>Phone Number</TableHead>
          <TableHead>Client Name</TableHead>
          {(onEditNumber || onDeleteNumber) && (
            <TableHead className="w-0">Actions</TableHead>
          )}
        </TableRow>
      </TableHeader>
      <TableBody>
        {duplicateNumbers.map((entry, index) => {
          const phoneValue = entry.phone;
          const countryCode = getCountryCode(phoneValue);
          return (
            <TableRow
              key={index}
              className="hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900"
            >
              <TableCell className="font-medium">
                {editingIndex === index ? (
                  <Input
                    value={editedNumber}
                    onChange={(e) => setEditedNumber(e.target.value)}
                    className="h-8 w-full"
                  />
                ) : (
                  <>
                    {getFlagComponent(countryCode)}
                    {phoneValue}
                  </>
                )}
              </TableCell>
              <TableCell>{entry.name || "Unknown"}</TableCell>
              {(onEditNumber || onDeleteNumber) && (
                <TableCell className="w-0 pr-4">
                  {editingIndex === index ? (
                    <div className="flex space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSave(index)}
                        className="h-7 px-2"
                      >
                        Save
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancel}
                        className="h-7 px-2"
                      >
                        Cancel
                      </Button>
                    </div>
                  ) : deletingIndex === index ? (
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleConfirmDelete(index)}
                        className="h-7 px-2 bg-red-500 text-white hover:bg-red-600 hover:text-white"
                      >
                        Delete
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancel}
                        className="h-7 px-2"
                      >
                        Cancel
                      </Button>
                    </div>
                  ) : (
                    <div className="flex space-x-1">
                      {onEditNumber && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(index, phoneValue)}
                          className="h-7 w-7 p-0 hover:bg-gray-300"
                          title="Edit"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                      {onDeleteNumber && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteClick(index)}
                          className="h-7 w-7 p-0 text-red-500 hover:text-red-700 hover:bg-gray-300"
                          title="Delete"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  )}
                </TableCell>
              )}
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
}
