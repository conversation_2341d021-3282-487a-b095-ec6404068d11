import { configureStore } from "@reduxjs/toolkit";
import businessDashboardReducer from "./BusinessDashboard/BusinessDashboardSlice";
import analyticsReducer from "./BusinessDashboard/subSlices/AnalyticsSlice";
import webhookReducer from "./BusinessDashboard/subSlices/WebhookSlice";

export const store = configureStore({
  reducer: {
    businessDashboard: businessDashboardReducer,
    analytics: analyticsReducer,
    webhooks: webhookReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
