import { Calendar } from "@/components/ui/calendar";
import { setFiltersInput } from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { RootState } from "@/redux/store";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";

export default function DateRangePicker() {
  const dispatch = useDispatch();
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.analytics.filtersInput // <-- use filtersInput
  );

  // Add state to control which month is displayed in each calendar
  const [startMonth, setStartMonth] = useState<Date>(
    startDate ? new Date(startDate) : new Date()
  );
  const [endMonth, setEndMonth] = useState<Date>(
    endDate ? new Date(endDate) : new Date()
  );

  // Update the displayed months when dates change
  useEffect(() => {
    if (startDate) {
      setStartMonth(new Date(startDate));
    }
  }, [startDate]);

  useEffect(() => {
    if (endDate) {
      setEndMonth(new Date(endDate));
    }
  }, [endDate]);

  const onStartDateSelect = (date: Date | undefined) => {
    dispatch(
      setFiltersInput({
        startDate: date ? date.toISOString() : null,
      })
    );
  };

  const onEndDateSelect = (date: Date | undefined) => {
    // If a date is selected, set the time to 23:59:59.999 (end of day)
    let endDateTime = null;
    if (date) {
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);
      endDateTime = endOfDay.toISOString();
    }

    dispatch(
      setFiltersInput({
        endDate: endDateTime,
      })
    );
  };

  return (
    <div className="flex flex-wrap gap-4 mt-4">
      <div className="grow min-w-[230px]">
        <label className="mb-1 text-sm flex items-center gap-1">
          <CalendarTodayIcon fontSize="small" />
          Start Date
        </label>
        <Calendar
          mode="single"
          classNames={{
            month: "gap-4 w-full flex flex-col",
            day: "rounded-md p-0 text-[#343434] dark:text-voxa-neutral-200 hover:bg-voxa-teal-600/10 dark:hover:bg-voxa-teal-600/10",
            selected:
              "bg-voxa-teal-600 dark:bg-voxa-teal-600 hover:!bg-voxa-teal-600 dark:hover:!bg-voxa-teal-600 text-white",
            day_button: "w-full h-8 font-medium text-sm",
            today:
              "bg-voxa-teal-100 dark:bg-voxa-teal-800 text-voxa-teal-600 dark:text-voxa-teal-500",
          }}
          selected={startDate ? new Date(startDate) : undefined}
          onSelect={onStartDateSelect}
          month={startMonth}
          onMonthChange={setStartMonth}
          disabled={{
            after: endDate ? new Date(endDate) : new Date(),
          }}
        />
      </div>
      <div className="grow min-w-[230px]">
        <label className="mb-1 text-sm flex items-center gap-1">
          <CalendarTodayIcon fontSize="small" />
          End Date
        </label>
        <Calendar
          mode="single"
          classNames={{
            month: "gap-4 w-full flex flex-col",
            day: "rounded-md p-0 text-[#343434] dark:text-voxa-neutral-200 hover:bg-voxa-teal-600/10 dark:hover:bg-voxa-teal-600/10",
            selected:
              "bg-voxa-teal-600 dark:bg-voxa-teal-600 hover:!bg-voxa-teal-600 dark:hover:!bg-voxa-teal-600 text-white",
            day_button: "w-full h-8 font-medium text-sm",
            today:
              "bg-voxa-teal-100 dark:bg-voxa-teal-800 text-voxa-teal-600 dark:text-voxa-teal-500",
          }}
          selected={endDate ? new Date(endDate) : undefined}
          onSelect={onEndDateSelect}
          month={endMonth}
          onMonthChange={setEndMonth}
          className="rounded-md border"
          disabled={{
            after: new Date(),
            before: startDate ? new Date(startDate) : undefined,
          }}
        />
      </div>
    </div>
  );
}
