import { openai } from "@/lib/openai"

export async function GetClientsGender(clientList: any[]){
    try{
        const genderPrompt = `
        Given the following list of clients (name + phone, and optionally messageDrop), return ONLY a JSON array (not an object) where each item contains: "name", "phone", "gender" ("Male", "Female", or "Unknown"). Include "messageDrop" ONLY if it exists in the input item.

        Do not include any explanation or code block. Respond with raw JSON only, no \`\`\` or formatting.

        ${JSON.stringify(clientList, null, 2)}
        `;
        const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            temperature: 0,
            messages: [
                {
                role: "system",
                content: "You are a helpful assistant that detects gender based on names.",
                },
                {
                role: "user",
                content: genderPrompt,
                },
            ],
        });
        console.log("OpenAI Raw Response:", completion.choices[0].message.content);

        const genderedClients = JSON.parse(completion.choices[0].message.content || "[]");
        console.log("Gendered Clients:", genderedClients);
        return genderedClients;
    }catch(err: any) {
        console.error("Error in GetClientsGender:", err);
    }
} 


export async function GetClientGender(name: string, phone: string, country: string) {
    try {
        const genderPrompt = `
            Given the following client (name + phone + country), return ONLY a JSON object with the fields: "name", "phone", "country", and "gender" ("Male", "Female", or "Unknown").
            Do not include any explanation or code block. Respond with raw JSON only, no \`\`\` or formatting.

            {
                "name": "${name}",
                "phone": "${phone}",
                "country": "${country}"
            }
        `;

        const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            temperature: 0,
            messages: [
                {
                    role: "system",
                    content: "You are a helpful assistant that detects gender based on names.",
                },
                {
                    role: "user",
                    content: genderPrompt,
                },
            ],
        });

        const clientWithGender = JSON.parse(completion.choices[0].message.content || "{}");
        console.log("Client With Gender:", clientWithGender);
        return clientWithGender;
    } catch (err: any) {
        console.error("Error in GetClientGender:", err);
    }
}
