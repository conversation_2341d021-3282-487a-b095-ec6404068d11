import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tag as TagType } from "@/types";
import { Tag } from "lucide-react";

export function TagsDropdown({ tags }: { tags: TagType[] }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <p
          className={`text-xs ${
            tags.length > 0 ? tags[0].color : ""
          } rounded-full px-2 py-1 cursor-pointer hover:bg-opacity-75 active:bg-opacity-80 transition-all duration-150 flex items-center gap-1`}
        >
          <Tag className="w-4 h-4" />
          {tags[0].name}
        </p>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="gap-2 flex">
        {tags.map((tag, index) => (
          <DropdownMenuItem
            className={`${tag.color} text-white flex justify-center`}
            key={index}
          >
            <span className="text-xs"> {tag.name} </span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
