import Navbar from "@/components/Nav/Navbar";
import { Suspense } from "react";
import { NextAuthProvider } from "@/providers/NextAuthProvider";
import "./globals.css";
import MainLoadingPage from "../components/Loaders/loading";
import { Toaster } from "@/components/ui/sonner";
import { CheckCircleIcon, XCircleIcon } from "lucide-react";
import LanguageProvider from "@/providers/LanguageProvider";
import { TooltipProvider } from "@/components/ui/tooltip";
import ReduxProvider from "@/providers/Store";
import { Poppins } from "next/font/google";
import { ThemeProvider } from "@/providers/ThemeProvider";
import { Spotlight } from "@/components/ui/spotlight";
import { ModalProvider } from "@/components/ui/animated-modal";
import { CookieModal } from "@/components/dialogs/CookieModal";
import { SWRProvider } from "@/providers/SwrProvider";
import SocketProvider from "@/providers/SocketProvider";

const PoppinsFont = Poppins({
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  preload: false,
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      className="antialiased bg-background dark:bg-[rgb(20,20,20)] text-[rgb(20,20,20)] dark:white"
      suppressHydrationWarning
    >
      <body className={PoppinsFont.className}>
        <NextAuthProvider>
          <ReduxProvider>
            <SocketProvider>
              <ThemeProvider>
                <LanguageProvider>
                  <SWRProvider>
                    <TooltipProvider>
                      <ModalProvider>
                        <Spotlight />
                        <Navbar />
                        <Suspense fallback={<MainLoadingPage />}>
                          {children}
                        </Suspense>
                        <CookieModal />
                      </ModalProvider>
                    </TooltipProvider>
                    <Toaster
                      icons={{
                        success: (
                          <CheckCircleIcon className="w-5 h-5 text-green-500" />
                        ),
                        error: <XCircleIcon className="w-5 h-5 text-red-500" />,
                      }}
                    />
                  </SWRProvider>
                </LanguageProvider>
              </ThemeProvider>
            </SocketProvider>
          </ReduxProvider>
        </NextAuthProvider>
      </body>
    </html>
  );
}
