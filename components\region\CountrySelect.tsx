import React from "react";
import { CountryDropdown, RegionDropdown } from "react-country-region-selector";
import "@/app/globals.css";

const CountrySelect = ({
  data,
  setData,
}: {
  data: BusinessDetailsFormType;
  setData: any;
}) => {
  const onChangeCountry = (val: any) => {
    setData({ ...data, country: val });
    if (!val) {
      setData({ ...data, city: "" });
    }
  };

  const selectStyle =
    "bg-sidebar border-sidebar-border border px-4 py-1.5 rounded-md border-input";

  return (
    <div className="flex max-sm:flex-col gap-4">
      <CountryDropdown
        value={data.country}
        name="country"
        onChange={onChangeCountry}
        className={selectStyle}
      />
      <RegionDropdown
        country={data.country}
        value={data.city}
        name="city"
        onChange={(val) => setData({ ...data, city: val })}
        className={selectStyle}
      />
    </div>
  );
};

export default CountrySelect;
