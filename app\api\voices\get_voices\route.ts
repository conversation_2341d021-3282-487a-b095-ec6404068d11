import dbConnect from "@/lib/mongodb";
import Entreprise from "@/models/Entreprise";
import { User } from "@/models/User";
import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { verifyUserToken } from "@/lib/cognito/Token";
import Voice from "@/models/Voice";

export async function GET(req: Request) {
  try {
    await dbConnect();

    const cookieStore = cookies();
    const token = (await cookieStore).get("plugin_id_token")?.value;

    console.log("Token from cookies:", token);

    if (!token) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    // ✅ Verify Cognito token
    const { valid, payload, error } = await verifyUserToken(token);

    if (!valid || !payload?.email) {
      return NextResponse.json(
        {
          error: "Token invalide ou expiré.",
          detail: error?.message || "Token non valide.",
        },
        { status: 401 }
      );
    }

    // ✅ Find user by email
    const user = await User.findOne({ email: payload.email });
    if (!user) {
      return NextResponse.json({ error: "Utilisateur introuvable." }, { status: 404 });
    }

    // ✅ Find entreprise by user ID
    const entreprise = await Entreprise.findOne({ admin: user._id });
    if (!entreprise) {
      return NextResponse.json({ error: "Entreprise introuvable." }, { status: 404 });
    }

    const voices = await Voice.find({});
    if (!voices) {
        return NextResponse.json({ error: "No voices found" }, { status: 404 });
    }

    return NextResponse.json({ voices }, { status: 200 });

  } catch (err: any) {
    console.error("Erreur serveur:", err);
    return NextResponse.json({ error: "Erreur interne du serveur." }, { status: 500 });
  }
}
