import React from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { setRetryCount } from "@/redux/BusinessDashboard/subSlices/GoalSlice";

const retriesNumber = [0, 1, 2];

export const NumberRetries: React.FC = () => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();

  const { retryCount } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );

  return (
    <div className="space-y-2">
      <h1 className="font-semibold text-lg">
        {t("createEditGoal.advanced.numberOfRetries")}
      </h1>
      <div className="flex gap-3">
        {retriesNumber.map((retry: number, index: number) => (
          <button
            key={index}
            onClick={() =>
              retry === 0
                ? dispatch(setRetryCount(retry))
                : toast.error("will be available in next version.")
            }
            className={`px-4 py-1.5 rounded-lg font-semibold transition-all ${
              retry === retryCount
                ? "bg-voxa-teal-600 text-background"
                : "border-2 border-voxa-teal-600 text-voxa-teal-600 hover:bg-voxa-teal-500 hover:text-neutral-200 dark:hover:text-black"
            }`}
          >
            {retry}
          </button>
        ))}
      </div>
    </div>
  );
};
