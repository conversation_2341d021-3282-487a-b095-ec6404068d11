"use client";

import caracters from "@/testData/caracters.json";
import { useEffect, useState, useCallback, useRef } from "react";
import { SearchIcon, UserRoundX, Bookmark } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  setPattern,
  setGroupPattern,
  getGroups,
  GetClients,
  GetClientsByPattern,
  handleCloseClientDetails,
} from "@/redux/BusinessDashboard/subSlices/ClientsSlice";
import AddContact from "@/components/popups/AddContact";
import ImportContacts from "@/components/popups/ImportContacts";
import ClientDetails from "@/components/Dashboard/SideComponents/ClientDetails";
import { CreateGroupDialog } from "@/components/dialogs/CreateGroup";
import clsx from "clsx";
import ClientCard from "@/components/Dashboard/Cards/ClientCard2";
import GroupCard from "@/components/Dashboard/Cards/GroupCard";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import CircularLoaderSmall from "@/components/Loaders/CircularLoaderSmall";
import CustomPagination from "@/components/pagination/CustomPagination";
import { usePagination } from "@/hooks/usePagination";
import useScreenSize from "@/hooks/useScreenSize";
import GroupSortSelect from "@/components/dropdowns/GroupSortSelect";
import { usePathname } from "next/navigation";

export default function Clients() {
  const dispatch = useDispatch<AppDispatch>();
  const {
    clientDetailsOpen,
    selectedLetter,
    clients,
    pattern,
    groupPattern,
    groups,
    clientsLoading,
    groupsLoading,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardClients
  );
  const [AddGroupOpen, setAddGroupOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const groupInputRef = useRef<HTMLInputElement>(null);
  const screenSize = useScreenSize();
  const pathname = usePathname();

  useEffect(() => {
    if (pathname === "/businessDash/clients") {
      dispatch(handleCloseClientDetails());
    }
  }, [pathname, dispatch]);

  const getClientItemsPerPage = () => {
    if (
      screenSize === "xs" ||
      screenSize === "sm" ||
      screenSize === "md" ||
      screenSize === "lg"
    ) {
      return 10;
    }
    return 20;
  };
  const clientItemsPerPage = getClientItemsPerPage();

  const {
    currentItems: currentClients,
    currentPage: currentClientPage,
    setCurrentPage: setCurrentClientPage,
  } = usePagination(clients, clientItemsPerPage);

  const {
    currentItems: currentGroups,
    currentPage: currentGroupPage,
    setCurrentPage: setCurrentGroupPage,
  } = usePagination(groups, 12);

  const handleGetClients = useCallback(
    async (letter: string) => {
      await dispatch(GetClients({ letter: letter }));
    },
    [dispatch]
  );

  const HandleGetClientsByPattern = useCallback(async () => {
    await dispatch(GetClientsByPattern());
  }, [dispatch]);

  const HandleGetGroups = useCallback(async () => {
    await dispatch(getGroups());
  }, [dispatch]);

  useEffect(() => {
    handleGetClients("favorites");
  }, [handleGetClients]);

  useEffect(() => {
    const handler = setTimeout(() => {
      HandleGetClientsByPattern();
    }, 500);
    return () => {
      clearTimeout(handler);
    };
  }, [HandleGetClientsByPattern, pattern]);

  useEffect(() => {
    const handler = setTimeout(() => {
      HandleGetGroups();
    }, 500);
    return () => {
      clearTimeout(handler);
    };
  }, [HandleGetGroups, groupPattern]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (document.activeElement === inputRef.current && e.key === "Enter") {
        e.preventDefault();
        HandleGetClientsByPattern();
      } else if (
        document.activeElement === groupInputRef.current &&
        e.key === "Enter"
      ) {
        e.preventDefault();
        HandleGetGroups();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [HandleGetClientsByPattern, HandleGetGroups]);

  useEffect(() => {
    HandleGetGroups();
  }, [HandleGetGroups]);
  return (
    <div className="rounded-2xl w-full flex flex-col gap-4">
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        Your Clients
      </h1>
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 justify-between items-center gap-2">
        <div className="w-full lg:max-w-md flex items-center">
          <Input
            ref={inputRef}
            value={pattern}
            onChange={(e) => {
              const value = e.target.value;
              dispatch(setPattern(value));
              if (value === "") {
                setCurrentClientPage(1);
                handleGetClients(selectedLetter || "favorites");
              }
            }}
            placeholder="Search (name or phone)..."
            className="w-full rounded-r-none"
          />
          <Button
            onClick={HandleGetClientsByPattern}
            className="h-10 w-10 bg-voxa-teal-600 hover:bg-voxa-teal-500 font-semibold transition-all duration-200 rounded-l-none"
          >
            <SearchIcon className="scale-125 text-white w-5 h-5" />
          </Button>
        </div>
        <div className="flex max-sm:flex-col justify-center lg:justify-end max-lg:w-full gap-2">
          <AddContact />
          <ImportContacts />
        </div>
      </div>
      <div className="flex gap-1 sm:gap-1.5 flex-wrap w-full items-center justify-center">
        {caracters.map((caracter: string, index: number) => (
          <div
            onClick={() => {
              setCurrentClientPage(1);
              handleGetClients(caracter);
            }}
            key={index}
            className={clsx(
              "bg-sidebar border-sidebar-border border rounded-md cursor-pointer transition-all duration-200 justify-center items-center flex h-[30px] sm:h-[33px]",
              index == 2 ? "px-[11px]" : "w-[30px] sm:w-[33px]"
            )}
          >
            <p
              className={`${
                selectedLetter === caracter
                  ? "text-orange-400"
                  : "dark:text-white"
              }`}
            >
              {index === 0 ? (
                <Bookmark
                  className={clsx(
                    "size-5",
                    selectedLetter === caracter && "fill-orange-400"
                  )}
                />
              ) : index === 1 ? (
                <UserRoundX
                  className={clsx(
                    "size-5",
                    selectedLetter === caracter && "fill-orange-400"
                  )}
                />
              ) : index === 2 ? (
                "No Name"
              ) : (
                caracter
              )}
            </p>
          </div>
        ))}
      </div>
      {/* Loading State */}
      {clientsLoading ? (
        <div className="h-10 w-full flex items-center justify-center">
          <CircularLoaderSmall />
        </div>
      ) : (
        <>
          {clients.length === 0 ? (
            <div className="h-10 w-full flex items-center justify-center text-center font-medium dark:text-voxa-neutral-50 text-lg">
              {selectedLetter === "favorites" ? (
                <>No Favorites yet</>
              ) : selectedLetter === "blacklist" ? (
                <>No Blacklist yet</>
              ) : (
                <>No clients found with this criteria</>
              )}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-3 w-full justify-center">
                {currentClients.map((client, index) => (
                  <ClientCard key={index} client={client} />
                ))}
              </div>
              <CustomPagination
                itemsPerPage={clientItemsPerPage}
                totalItems={clients.length}
                currentPage={currentClientPage}
                onPageChange={setCurrentClientPage}
              />
            </>
          )}
        </>
      )}
      <div className="p-1 pl-2.5 bg-voxa-neutral-400 dark:bg-voxa-neutral-900 rounded-lg font-medium flex w-full items-center justify-between text-white">
        <p>Groups</p>
        <div className="flex items-center gap-2">
          <CreateGroupDialog
            addGroupOpen={AddGroupOpen}
            setAddGroupOpen={setAddGroupOpen}
          />
        </div>
      </div>
      <div className="mb-1 w-full flex max-sm:flex-col max-sm:items-end sm:justify-between gap-2">
        <div className="flex w-full sm:w-fit lg:max-w-md">
          <Input
            ref={groupInputRef}
            value={groupPattern}
            onChange={(e) => {
              const value = e.target.value;
              dispatch(setGroupPattern(value));
              setCurrentGroupPage(1);
            }}
            placeholder="Search (name)..."
            className="w-full rounded-r-none"
          />
          <Button
            onClick={HandleGetGroups}
            className="h-10 w-10 bg-voxa-teal-600 hover:bg-voxa-teal-500 font-semibold transition-all duration-200 rounded-l-none"
          >
            <SearchIcon className="scale-125 text-white w-5 h-5" />
          </Button>
        </div>
        <GroupSortSelect />
      </div>
      {groupsLoading ? (
        <div className="w-full flex justify-center items-center h-[50px]">
          <CircularLoaderSmall />
        </div>
      ) : (
        <>
          {groups.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 w-full items-center justify-center">
              {currentGroups.map((group, index) => (
                <GroupCard
                  key={group._id || index}
                  group={group}
                  index={index}
                />
              ))}
            </div>
          ) : (
            <div className="h-[200px] w-full flex items-center justify-center text-center font-medium dark:text-voxa-neutral-50 text-lg">
              No Groups yet
            </div>
          )}
          <CustomPagination
            itemsPerPage={12}
            totalItems={groups.length}
            currentPage={currentGroupPage}
            onPageChange={setCurrentGroupPage}
          />
        </>
      )}
      {clientDetailsOpen && <ClientDetails />}
    </div>
  );
}
