import { NextRequest, NextResponse } from 'next/server';
import { openai } from "@/lib/openai"

export async function POST(request: NextRequest) {
  try {
    const { text, targetLanguage } = await request.json();

    if (!text || !targetLanguage) {
      return NextResponse.json(
        { error: 'Text and target language are required' },
        { status: 400 }
      );
    }

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are a professional translator. Translate the given text to ${targetLanguage}. 
          Return only the translated text without any additional formatting, explanations, or quotes. 
          Maintain the original meaning and tone. If the text contains HTML tags or special formatting, preserve them.`
        },
        {
          role: "user",
          content: text
        }
      ],
      temperature: 0.2,
      max_tokens: 1000,
    });

    const translatedText = completion.choices[0]?.message?.content?.trim() || text;

    return NextResponse.json({ translatedText });
  } catch (error) {
    console.error('Translation API error:', error);
    return NextResponse.json(
      { error: 'Translation failed' },
      { status: 500 }
    );
  }
} 