// components/BusinessInfo.tsx
import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { motion } from "framer-motion";

const BusinessInfo = () => {
  return (
    <div className="p-4 max-w-7xl mx-auto relative z-10 w-full mt-32">
      <motion.h2
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="text-3xl font-semibold text-center mb-8"
      >
        How It Works?
      </motion.h2>
      <p className="text-lg text-voxa-neutral-300 mb-6">
        Our setup process is simple, fast, and efficient, with integration
        ranging from <strong>3 to 7 days</strong>. Here are the key steps:
      </p>

      <Accordion type="single" collapsible>
        <AccordionItem value="step1">
          <AccordionTrigger>
            <strong>Analyze Your Needs</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              We gather as much information about your business, your scripts,
              and your call structure to understand your goals and expectations.
            </p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="step2">
          <AccordionTrigger>
            <strong>Feature Personalization</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              We define together the features and details of your future virtual
              teleoperators so they adapt perfectly to your needs.
            </p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="step3">
          <AccordionTrigger>
            <strong>Your Teleoperator Demo</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              We present a personalized demo of your virtual teleoperator for
              validation.
            </p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="step4">
          <AccordionTrigger>
            <strong>System Integration</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              Once the demo is validated, we integrate our technology into your
              existing interfaces, if possible.
            </p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="step5">
          <AccordionTrigger>
            <strong>Delivery and Training</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              We deliver the final product and train you on how to use your
              virtual teleoperators for immediate adoption.
            </p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="step6">
          <AccordionTrigger>
            <strong>Follow-Up and Adjustments</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              A thorough follow-up is carried out during the first month to make
              necessary adjustments and ensure optimal performance.
            </p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="step7">
          <AccordionTrigger>
            <strong>Maintenance and Ongoing Support</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              We remain by your side for daily maintenance and ongoing
              development throughout our collaboration.
            </p>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <motion.h2
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="text-3xl font-semibold text-center mt-24 mb-8"
      >
        Benefits of Our Virtual Teleoperators
      </motion.h2>

      <Accordion type="single" collapsible>
        <AccordionItem value="benefit1">
          <AccordionTrigger>
            <strong>Time Savings</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              With our virtual agents, you no longer need to handle the
              recruitment and training of human teleoperators, saving you time
              and resources.
            </p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="benefit2">
          <AccordionTrigger>
            <strong>Access to New Markets</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              Our technology allows you to prospect internationally without
              worrying about language barriers or the need for qualified
              personnel.
            </p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="benefit3">
          <AccordionTrigger>
            <strong>No More Human Management</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              Forget about managing call center teams. Our virtual agents are
              always operational, available 24/7, with no interruptions.
            </p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="benefit4">
          <AccordionTrigger>
            <strong>Significant Economic Gain</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              Reduce operational costs, including salaries, office expenses, and
              training, while paying only for the call minutes.
            </p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="benefit5">
          <AccordionTrigger>
            <strong>Exploiting Strategic Time Slots</strong>
          </AccordionTrigger>
          <AccordionContent>
            <p>
              Our technology helps you manage thousands of calls during
              high-opportunity hours, maximizing your commercial prospects.
            </p>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default BusinessInfo;
