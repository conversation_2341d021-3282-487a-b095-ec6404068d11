import React from "react";
import { StatsCardProps } from "@/components/Dashboard/Cards/StatsCard";
import {
  Clock7Icon,
  HandCoinsIcon,
  HeadsetIcon,
  PhoneCallIcon,
  PhoneOffIcon,
  UsersRoundIcon,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import StatsCard from "@/components/Dashboard/Cards/StatsCard";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

export const StatsCards: React.FC = () => {
  const { t } = useTranslation("assistants");
  const {
    gettingGoal,
    goalDuration,
    goalConversations,
    calledClients,
    missedCalls,
    answeredCalls,
    totalCost,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );

  const statsCards: StatsCardProps[] = [
    {
      title: t("createEditGoal.stats.totalDuration"),
      value: goalDuration,
      icon: <Clock7Icon className="w-6 h-6 text-[#d97706]" />,
      color: "#d97706",
    },
    {
      title: t("createEditGoal.stats.allCalls"),
      value: goalConversations,
      icon: <HeadsetIcon className="w-6 h-6 text-[#2563eb]" />,
      color: "#2563eb",
    },
    {
      title: t("createEditGoal.stats.calledClients"),
      value: `${calledClients}`,
      icon: <UsersRoundIcon className="w-6 h-6 text-[#7c3aed]" />,
      color: "#7c3aed",
    },
    {
      title: t("createEditGoal.stats.missedCalls"),
      value: missedCalls,
      icon: <PhoneOffIcon className="w-6 h-6 text-[#dc2626]" />,
      color: "#dc2626",
    },
    {
      title: t("createEditGoal.stats.answeredCalls"),
      value: answeredCalls,
      icon: <PhoneCallIcon className="w-6 h-6 text-green-600" />,
      color: "#16a34a",
    },
    {
      title: t("createEditGoal.stats.totalCost"),
      value: `${totalCost} €`,
      icon: <HandCoinsIcon className="w-6 h-6 text-[#06b6d4]" />,
      color: "#06b6d4",
    },
  ];

  return (
    <div className="rounded-xl flex flex-col items-center text-center transition-transform duration-300 relative overflow-hidden">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 w-full">
        {statsCards.map((card, index) => (
          <StatsCard
            key={index}
            title={card.title}
            value={card.value}
            gettingGoal={gettingGoal}
            icon={card.icon}
            color={card.color}
          />
        ))}
      </div>
    </div>
  );
};
