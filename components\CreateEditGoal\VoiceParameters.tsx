import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import CustomInput from "@/components/CustomFormItems/Input";
import CustomSelect from "@/components/CustomFormItems/Select";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  setGoalAssistantNameForMale,
  setMaleVoice,
  setGoalAssistantNameForFemale,
  setFemaleVoice,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";
import { fetchVoices } from "@/redux/BusinessDashboard/subSlices/VoicesSlice";

export const VoiceParameters = () => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();

  const {
    maleVoice,
    femaleVoice,
    goalAssistantNameForMale,
    goalAssistantNameForFemale,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );
  const { voices } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardVoices
  );

  useEffect(() => {
    dispatch(fetchVoices());
  }, [dispatch]);

  useEffect(() => {
    if (voices.length > 0) {
      if (!maleVoice.id) {
        dispatch(
          setMaleVoice({
            id: voices[0]._id,
            provider: voices[0].provider,
          })
        );
      }
      if (!femaleVoice.id) {
        dispatch(
          setFemaleVoice({
            id: voices[0]._id,
            provider: voices[0].provider,
          })
        );
      }
    }
  }, [voices, maleVoice, femaleVoice, dispatch]);

  return (
    <div className="w-full lg:col-span-2">
      <p className="pb-2 lg:col-span-2 text-lg font-semibold flex gap-2 items-center">
        {t("createEditGoal.aiVoice.section")}
      </p>
      <p className="lg:col-span-2 text-sm text-voxa-neutral-700">
        {t("createEditGoal.aiVoice.description")}
      </p>

      <h1 className="mt-1.5 lg:col-span-2 text-lg font-medium">
        {t("createEditGoal.aiVoice.male")}
      </h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CustomInput
          props={{
            name: "AssistantNameForMale",
            label: t("createEditGoal.aiVoice.assistantNameForMale"),
            type: "text",
            placeholder: t(
              "createEditGoal.aiVoice.assistantNameForMalePlaceholder"
            ),
            value: goalAssistantNameForMale,
            onChange: (e) =>
              dispatch(setGoalAssistantNameForMale(e.target.value)),
          }}
        />
        <CustomSelect
          label={t("createEditGoal.aiVoice.voiceId")}
          searchable
          value={maleVoice.id}
          onValueChange={(value) =>
            dispatch(
              setMaleVoice({
                id: value,
                provider:
                  voices.find((voice) => voice._id === value)?.provider || "",
              })
            )
          }
          placeholder={t("createEditGoal.aiVoice.voiceIdPlaceholder")}
          items={voices.map((voice) => ({
            value: voice._id,
            label: [voice.displayed_ai_voice, voice._id],
          }))}
        />
      </div>

      <h1 className="mt-1.5 lg:col-span-2 text-lg font-medium">
        {t("createEditGoal.aiVoice.female")}
      </h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CustomInput
          props={{
            name: "AssistantNameForFemale",
            label: t("createEditGoal.aiVoice.assistantNameForFemale"),
            type: "text",
            placeholder: t(
              "createEditGoal.aiVoice.assistantNameForFemalePlaceholder"
            ),
            value: goalAssistantNameForFemale,
            onChange: (e) =>
              dispatch(setGoalAssistantNameForFemale(e.target.value)),
          }}
        />
        <CustomSelect
          label={t("createEditGoal.aiVoice.voiceId")}
          searchable
          value={femaleVoice.id}
          onValueChange={(value) =>
            dispatch(
              setFemaleVoice({
                id: value,
                provider:
                  voices.find((voice) => voice._id === value)?.provider || "",
              })
            )
          }
          placeholder={t("createEditGoal.aiVoice.voiceIdPlaceholder")}
          items={voices.map((voice) => ({
            value: voice._id,
            label: [voice.displayed_ai_voice, voice._id],
          }))}
        />
      </div>
    </div>
  );
};
