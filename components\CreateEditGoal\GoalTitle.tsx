import { Info, PhoneIncomingIcon, PhoneOutgoingIcon } from "lucide-react";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

interface GoalTitleProps {
  goalID: string;
  templateType: string;
}

export const GoalTitle: React.FC<GoalTitleProps> = ({
  goalID,
  templateType,
}) => {
  const { t } = useTranslation("assistants");
  const { goalType } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );
  return (
    <h1 className="flex max-sm:justify-center items-center text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50 gap-2">
      {goalID !== "create"
        ? t("createEditGoal.header.editTitle")
        : t("createEditGoal.header.createTitle")}
      {": "}
      {templateType === "DEMARCHAGE" ? (
        <>
          {t("createGoal.outboundCalling")}
          {goalType === "INCOMING" ? (
            <PhoneIncomingIcon className="text-orange-500 size-6" />
          ) : (
            goalType === "OUTGOING" && (
              <PhoneOutgoingIcon className="text-green-600 size-6" />
            )
          )}
        </>
      ) : templateType === "GOOGLE_MEET" ? (
        <>
          {t("createGoal.googleMeet")}
          <Link
            href="https://business-files-bucket.s3.eu-west-3.amazonaws.com/BusinessFiles/Google+Meet+Integration.pdf"
            target="_blank"
            rel="noopener noreferrer"
            className="ml-2 text-voxa-teal-600 hover:text-voxa-teal-500"
            title={t("createEditGoal.header.googleMeetInfo")}
          >
            <Info className="size-6" />
          </Link>
        </>
      ) : templateType === "MULTI_TRANSLATION" ? (
        <>{t("createGoal.multiTranslation")}</>
      ) : templateType === "SMS_WHATSAPP" ? (
        <>{t("createGoal.smsWhatsappNotifications")}</>
      ) : (
        <></>
      )}
    </h1>
  );
};
