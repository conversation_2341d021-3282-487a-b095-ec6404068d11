"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { CopyIcon, UserIcon } from "lucide-react";
import { handleCopyText } from "@/lib/Strings/Copy";
import { toast } from "sonner";
import { GetEntrepriseRingoverParams } from "@/actions/CRMActions";
import MainLoader from "@/components/Loaders/MainLoader";
import CustomPagination from "@/components/pagination/CustomPagination";
import { usePagination } from "@/hooks/usePagination";

export default function BusinessDashHome() {
  const [groupUsers, setGroupUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [apiKey, setApiKey] = useState<string>("");
  const [fromGetFunction, setFromGetFunction] = useState<boolean>(false);
  const itemsPerPage = 8;

  const params = useSearchParams();
  const usersbaseurl = params?.get("usersbaseurl") as string;
  const groupname = params?.get("groupName") as string;

  const {
    currentItems: currentUsers,
    currentPage: pagCurrentPage,
    setCurrentPage: setPagCurrentPage,
  } = usePagination(groupUsers ?? [], itemsPerPage);

  useEffect(() => {
    getRingoverParams();
  }, []);

  useEffect(() => {
    const getUsers = async () => {
      try {
        const response = await fetch(usersbaseurl, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: apiKey,
          },
        });
        if (response.status !== 200) throw new Error("Failed to fetch users");
        const data = await response.json();
        setGroupUsers(data.users);
      } catch (err: any) {
        console.error(err.message);
      } finally {
        setLoading(false);
      }
    };
    if (apiKey) getUsers();
  }, [fromGetFunction, apiKey, usersbaseurl]);

  const getRingoverParams = async () => {
    try {
      const response = await GetEntrepriseRingoverParams();
      if (!response.success) return;
      if (response.apiKey) setApiKey(response.apiKey);
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setFromGetFunction(true);
    }
  };

  const getRandomColor = () => {
    const colors = [
      "bg-red-500",
      "bg-blue-500",
      "bg-green-500",
      "bg-yellow-500",
      "bg-purple-500",
      "bg-pink-500",
    ];
    return colors[Math.floor(Math.random() * colors?.length)];
  };

  return (
    <div className="relative w-full flex flex-col gap-4">
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        {groupname} Users{" "}
        <span className="text-xl font-medium">({groupUsers?.length || 0})</span>
      </h1>

      {loading ? (
        <div className="absolute w-full flex justify-center items-center h-[calc(100vh-2rem)] sm:h-[calc(100vh-3rem)]">
          <MainLoader />
        </div>
      ) : !groupUsers || groupUsers?.length === 0 ? (
        <span className="mx-auto mt-6 text-lg font-semibold text-gray-500">
          No users found in this group.
        </span>
      ) : (
        <>
          <div className="flex flex-col gap-4">
            {(currentUsers || []).map((user) => (
              <div
                key={user.user_id}
                className="bg-sidebar border-sidebar-border border rounded-xl p-5 hover:scale-y-104 hover:scale-x-102 transtion-transform duration-200 items-center gap-3 sm:gap-4 w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-7"
              >
                {/* Name */}
                <div className="flex gap-3">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white ${getRandomColor()}`}
                  >
                    <UserIcon className="w-6 h-6" />
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="dark:text-voxa-neutral-50 text-sm font-semibold">
                      Name:
                    </span>
                    <span className="text-foreground/70 dark:text-voxa-neutral-200 text-sm font-medium">
                      {user.concat_name}
                    </span>
                  </div>
                </div>
                {/* Company */}
                <div className="grid grid-cols-[2fr_3fr] sm:grid-cols-1">
                  <span className="dark:text-voxa-neutral-50 text-sm font-semibold">
                    Company:
                  </span>
                  <span className="text-foreground/70 dark:text-voxa-neutral-200 text-sm font-medium">
                    {user.company}
                  </span>
                </div>
                {/* Email */}
                <div className="sm:col-span-2 grid grid-cols-[2fr_3fr] sm:grid-cols-1">
                  <span className="dark:text-voxa-neutral-50 text-sm font-semibold">
                    Email:
                  </span>
                  <span
                    className="text-foreground/70 dark:text-voxa-neutral-200 text-sm font-medium max-sm:truncate max-w-60"
                    title={user.email}
                  >
                    {user.email}
                  </span>
                </div>
                {/* Team ID */}
                <div className="grid grid-cols-[2fr_3fr] sm:grid-cols-1">
                  <span className="dark:text-voxa-neutral-50 text-sm font-semibold">
                    Team ID:
                  </span>
                  <span className="text-foreground/70 dark:text-voxa-neutral-200 text-sm font-medium">
                    {user.team_id}
                  </span>
                </div>
                {/* Plan Type */}
                <div className="grid grid-cols-[2fr_3fr] sm:grid-cols-1">
                  <span className="dark:text-voxa-neutral-50 text-sm font-semibold">
                    Plan Type:
                  </span>
                  <span className="text-foreground/70 dark:text-voxa-neutral-200 text-sm font-medium">
                    {user.plantype || "N/A"}
                  </span>
                </div>
                {/* Ring Duration */}
                <div className="grid grid-cols-[2fr_3fr] sm:grid-cols-1">
                  <span className="dark:text-voxa-neutral-50 text-sm font-semibold">
                    User ID:
                  </span>
                  <div className="flex items-center gap-1">
                    <span className="text-foreground/70 dark:text-voxa-neutral-200 text-sm font-medium">
                      {user.user_id}
                    </span>
                    <button onClick={() => handleCopyText(user.user_id)}>
                      <CopyIcon className="w-5 h-5 text-foreground/70 dark:text-voxa-neutral-200 dark:hover:text-foreground/60 hover:text-foreground/20 transition-colors" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <CustomPagination
            itemsPerPage={itemsPerPage}
            totalItems={groupUsers?.length || 0}
            currentPage={pagCurrentPage}
            onPageChange={setPagCurrentPage}
          />
        </>
      )}
    </div>
  );
}
