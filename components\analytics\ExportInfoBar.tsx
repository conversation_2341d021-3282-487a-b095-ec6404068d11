"use client";

import { CountryCode } from "@/lib/countries";
import { Filters } from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { RootState } from "@/redux/store";
import AccessTimeRoundedIcon from "@mui/icons-material/AccessTimeRounded";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import FilterListOutlinedIcon from "@mui/icons-material/FilterListOutlined";
import { format, parseISO } from "date-fns";
import {
  CheckSquare,
  Hourglass,
  MessageCircle,
  MessageSquare,
  Phone,
  PhoneForwarded,
  PhoneIncoming,
  PhoneMissed,
  PhoneOutgoing,
  Timer,
  Video,
} from "lucide-react";
import Image from "next/image";
import React from "react";
import { useSelector } from "react-redux";

const filterDisplayOptions = {
  conversationTypes: {
    inbound: {
      label: "Inbound",
      icon: <PhoneIncoming className="text-green-500 h-4 w-4" />,
    },
    outbound: {
      label: "Outbound",
      icon: <PhoneOutgoing className="text-blue-500 h-4 w-4" />,
    },
    missed: {
      label: "Missed",
      icon: <PhoneMissed className="text-red-500 h-4 w-4" />,
    },
    answered: {
      label: "Answered",
      icon: <CheckSquare className="text-green-600 h-4 w-4" />,
    },
    notTransferred: {
      label: "Not Transferred",
      icon: <Phone className="text-gray-500 h-4 w-4" />,
    },
    transferred: {
      label: "Transferred",
      icon: <PhoneForwarded className="text-purple-500 h-4 w-4" />,
    },
    calls: {
      label: "Calls",
      icon: <Phone className="text-blue-600 h-4 w-4" />,
    },
    whatsapp: {
      label: "WhatsApp",
      icon: <MessageCircle className="text-green-600 h-4 w-4" />,
    },
    sms: {
      label: "SMS",
      icon: <MessageSquare className="text-yellow-600 h-4 w-4" />,
    },
    meet: {
      label: "Meetings",
      icon: <Video className="text-purple-600 h-4 w-4" />,
    },
  },
};

// Add this function to get flag URLs
const getFlagUrl = (alpha2: string) =>
  `https://flagcdn.com/w20/${alpha2.toLowerCase()}.png`;

// Update the getActiveFilters function to return separate included and excluded lists
const getActiveFilters = (
  filters: Filters
): {
  included: { key: string; label: string; icon: React.ReactNode }[];
  excluded: { key: string; label: string; icon: React.ReactNode }[];
  countries: CountryCode[];
  other: { key: string; label: string; icon: React.ReactNode }[];
} => {
  const included: { key: string; label: string; icon: React.ReactNode }[] = [];
  const excluded: { key: string; label: string; icon: React.ReactNode }[] = [];
  const other: { key: string; label: string; icon: React.ReactNode }[] = [];

  const { conversationTypes, countries, callDuration, ringingTime } = filters;

  // Process conversation types
  Object.entries(conversationTypes).forEach(([key, value]) => {
    const typedKey = key as keyof typeof filterDisplayOptions.conversationTypes;
    const option = filterDisplayOptions.conversationTypes[typedKey];

    if (option) {
      if (value) {
        included.push({ key, ...option });
      } else {
        excluded.push({ key, ...option });
      }
    }
  });

  // Do NOT push countries to 'other' anymore

  if (callDuration.min !== null || callDuration.max !== null) {
    let label = "Duration: ";
    if (callDuration.min !== null && callDuration.max !== null) {
      label += `${callDuration.min}s - ${callDuration.max}s`;
    } else if (callDuration.min !== null) {
      label += `> ${callDuration.min}s`;
    } else if (callDuration.max !== null) {
      label += `< ${callDuration.max}s`;
    }
    other.push({
      key: "callDuration",
      label: label,
      icon: <Timer className="h-4 w-4 text-orange-500" />,
    });
  }

  if (ringingTime.min !== null || ringingTime.max !== null) {
    let label = "Ringing: ";
    if (ringingTime.min !== null && ringingTime.max !== null) {
      label += `${ringingTime.min}s - ${ringingTime.max}s`;
    } else if (ringingTime.min !== null) {
      label += `> ${ringingTime.min}s`;
    } else if (ringingTime.max !== null) {
      label += `< ${ringingTime.max}s`;
    }
    other.push({
      key: "ringingTime",
      label: label,
      icon: <Hourglass className="h-4 w-4 text-indigo-500" />,
    });
  }

  return { included, excluded, countries, other };
};

export default function ExportInfoBar() {
  // For now, use current time as export time
  const exportDate = new Date();

  // Get filters for date range
  const { filters } = useSelector((state: RootState) => state.analytics);
  const { startDate, endDate } = filters;

  const formattedStart = startDate
    ? format(parseISO(startDate), "dd MMM yyyy")
    : "—";
  const formattedEnd = endDate ? format(parseISO(endDate), "dd MMM yyyy") : "—";
  const displayRange =
    !startDate && !endDate ? "All Time" : `${formattedStart} / ${formattedEnd}`;

  const formattedExportDate = format(exportDate, "dd MMM yyyy, HH:mm");

  const { included, excluded, countries, other } = getActiveFilters(filters);

  // Determine if all conversation types are included
  const allConversationTypeKeys = Object.keys(
    filterDisplayOptions.conversationTypes
  );
  const includedConversationTypeKeys = included
    .map((f) => f.key)
    .filter((key) => allConversationTypeKeys.includes(key));
  const showIncluded =
    includedConversationTypeKeys.length > 0 &&
    includedConversationTypeKeys.length < allConversationTypeKeys.length;

  const hasFilters =
    (showIncluded ? includedConversationTypeKeys.length > 0 : false) ||
    excluded.length > 0 ||
    countries.length > 0 ||
    other.length > 0;

  return (
    <div className="bg-gray-100 dark:bg-voxa-neutral-900 border border-gray-300 dark:border-voxa-neutral-700 p-4 rounded-md w-full flex flex-col gap-4 items-center">
      <h2 className="text-lg font-semibold text-voxa-neutral-900 dark:text-white">
        Export Information
      </h2>
      <div className="w-full flex flex-wrap gap-2 items-center justify-center">
        <div className="flex items-center gap-2 bg-white dark:bg-voxa-neutral-800 hover:bg-gray-100 dark:hover:bg-voxa-neutral-700 rounded-sm py-2 px-3 border dark:border-voxa-neutral-700 text-sm">
          <FileDownloadOutlinedIcon
            fontSize="small"
            className="text-voxa-neutral-900 dark:text-voxa-neutral-400"
          />
          <span className="text-sm text-voxa-neutral-900 dark:text-voxa-neutral-300">
            Exported: {formattedExportDate}
          </span>
        </div>
        <div className="flex items-center gap-2 bg-white dark:bg-voxa-neutral-800 hover:bg-gray-100 dark:hover:bg-voxa-neutral-700 rounded-sm py-2 px-3 border dark:border-voxa-neutral-700 text-sm">
          <AccessTimeRoundedIcon
            fontSize="small"
            className="text-voxa-neutral-900 dark:text-voxa-neutral-400"
          />
          <span className="text-sm text-voxa-neutral-900 dark:text-voxa-neutral-300">
            {displayRange}
          </span>
        </div>
      </div>
      <div className="w-full flex flex-col gap-2 items-center justify-center">
        <div className="flex items-center gap-2">
          <FilterListOutlinedIcon
            fontSize="small"
            className="text-voxa-neutral-900 dark:text-voxa-neutral-400"
          />
          <h3 className="text-md font-semibold text-voxa-neutral-900 dark:text-white">
            Active Filters
          </h3>
        </div>

        {hasFilters ? (
          <div className="w-full max-w-4xl">
            {showIncluded && (
              <div className="mb-2">
                <h4 className="text-sm font-medium text-voxa-neutral-700 dark:text-voxa-neutral-400 mb-1">
                  Included:
                </h4>
                <div className="flex flex-wrap gap-2">
                  {included
                    .filter((filter) =>
                      allConversationTypeKeys.includes(filter.key)
                    )
                    .map((filter) => (
                      <span
                        key={filter.key}
                        className="flex items-center gap-2 text-xs text-voxa-neutral-900 dark:text-voxa-neutral-300 bg-white dark:bg-voxa-neutral-800 rounded-md py-1 px-2 border border-green-300 dark:border-green-800"
                      >
                        {filter.icon}
                        <span>{filter.label}</span>
                      </span>
                    ))}
                </div>
              </div>
            )}

            {excluded.length > 0 && (
              <div className="mb-2">
                <h4 className="text-sm font-medium text-voxa-neutral-700 dark:text-voxa-neutral-400 mb-1">
                  Excluded:
                </h4>
                <div className="flex flex-wrap gap-2">
                  {excluded.map((filter) => (
                    <span
                      key={filter.key}
                      className="flex items-center gap-2 text-xs text-voxa-neutral-900 dark:text-voxa-neutral-300 bg-white dark:bg-voxa-neutral-800 rounded-md py-1 px-2 border border-red-300 dark:border-red-800"
                    >
                      {filter.icon}
                      <span>{filter.label}</span>
                    </span>
                  ))}
                </div>
              </div>
            )}

            {countries.length > 0 && (
              <div className="mb-2">
                <h4 className="text-sm font-medium text-voxa-neutral-700 dark:text-voxa-neutral-400 mb-1">
                  Countries:
                </h4>
                <div className="flex flex-wrap gap-2">
                  {countries.map((country) => (
                    <span
                      key={country}
                      className="flex items-center gap-1 text-xs text-voxa-neutral-900 dark:text-voxa-neutral-300 bg-white dark:bg-voxa-neutral-800 rounded-md py-1 px-2 border dark:border-voxa-neutral-700"
                    >
                      <Image
                        key={country}
                        src={getFlagUrl(country)}
                        alt={country}
                        width={16}
                        height={12}
                        className="mr-1"
                        unoptimized
                      />
                      <span>{country}</span>
                    </span>
                  ))}
                </div>
              </div>
            )}

            {other.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-voxa-neutral-700 dark:text-voxa-neutral-400 mb-1">
                  Other Filters:
                </h4>
                <div className="flex flex-wrap gap-2">
                  {other.map((filter) => (
                    <span
                      key={filter.key}
                      className="flex items-center gap-2 text-xs text-voxa-neutral-900 dark:text-voxa-neutral-300 bg-white dark:bg-voxa-neutral-800 rounded-md py-1 px-2 border dark:border-voxa-neutral-700"
                    >
                      {filter.icon}
                      <span>{filter.label}</span>
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center py-2">
            <span className="text-sm text-voxa-neutral-500 dark:text-voxa-neutral-400">
              No active filters
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
