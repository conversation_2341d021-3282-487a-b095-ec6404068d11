"use client";

import { setupCardIntent } from "@/actions/BillingActions";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  getPaymentMethodDisplayName,
  getPaymentMethodIconPath,
} from "@/lib/billingUtils";
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  Elements,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { CreditCard, Loader2 } from "lucide-react";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
);

interface AddCardDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

// Card form component that uses Stripe Elements
const CardForm: React.FC<{
  clientSecret: string;
  onSuccess: () => void;
  onError: (error: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}> = ({ clientSecret, onSuccess, onError, isProcessing, setIsProcessing }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [cardBrand, setCardBrand] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    const cardNumberElement = elements.getElement(CardNumberElement);

    if (!cardNumberElement) {
      setIsProcessing(false);
      return;
    }

    // Confirm the setup intent
    const { error } = await stripe.confirmCardSetup(clientSecret, {
      payment_method: {
        card: cardNumberElement,
      },
    });

    if (error) {
      onError(error.message || "An error occurred while setting up your card.");
      setIsProcessing(false);
    } else {
      toast.success("Card added successfully!");
      onSuccess();
      setIsProcessing(false);
    }
  };

  const handleCardChange = (event: any) => {
    if (event.brand) {
      setCardBrand(event.brand);
    } else {
      setCardBrand(null);
    }
  };

  const getCardIcon = () => {
    const iconPath = getPaymentMethodIconPath("card", cardBrand || undefined);
    const altText = getPaymentMethodDisplayName("card", cardBrand || undefined);

    return (
      <Image
        src={iconPath}
        alt={altText}
        width={30}
        height={20}
        className="border border-gray-200 dark:border-gray-700 rounded"
      />
    );
  };

  const elementOptions = {
    style: {
      base: {
        fontSize: "16px",
        color: "#374151",
        "::placeholder": {
          color: "#9CA3AF",
        },
        fontFamily: "system-ui, -apple-system, sans-serif",
      },
      invalid: {
        color: "#EF4444",
      },
    },
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-4">
        {/* Card Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Card Number
          </label>
          <div className="relative bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors focus-within:border-voxa-teal-500 focus-within:ring-2 focus-within:ring-voxa-teal-500/20">
            <CardNumberElement
              options={elementOptions}
              onChange={handleCardChange}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getCardIcon()}
            </div>
          </div>
        </div>

        {/* Expiry and CVC */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Expiry Date
            </label>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors focus-within:border-voxa-teal-500 focus-within:ring-2 focus-within:ring-voxa-teal-500/20">
              <CardExpiryElement options={elementOptions} />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              CVC
            </label>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors focus-within:border-voxa-teal-500 focus-within:ring-2 focus-within:ring-voxa-teal-500/20">
              <CardCvcElement options={elementOptions} />
            </div>
          </div>
        </div>
      </div>

      <Button
        type="submit"
        disabled={!stripe || isProcessing}
        className="w-full h-11 bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 hover:from-voxa-teal-700 hover:to-voxa-teal-800 text-white font-semibold transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50"
      >
        {isProcessing ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Adding Card...
          </>
        ) : (
          "Add Card"
        )}
      </Button>
    </form>
  );
};

export default function AddCardDialog({
  open,
  onClose,
  onSuccess,
}: AddCardDialogProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [setupError, setSetupError] = useState<string | null>(null);

  useEffect(() => {
    if (open && !clientSecret) {
      initializeSetupIntent();
    }
  }, [open, clientSecret]);

  const initializeSetupIntent = async () => {
    try {
      setSetupError(null);
      const result = await setupCardIntent();
      if (!result.success) {
        setSetupError(result.error || "Failed to initialize card setup");
        return;
      }
      setClientSecret(result.data!.client_secret);
    } catch (error: any) {
      setSetupError(error.message || "Failed to initialize card setup");
    }
  };

  const handleSuccess = () => {
    onSuccess();
    // Reset state for next time
    setClientSecret(null);
    setSetupError(null);
  };

  const handleError = (error: string) => {
    toast.error(error);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open && !isProcessing) {
      onClose();
      // Reset state when closing
      setClientSecret(null);
      setSetupError(null);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md border-0 shadow-2xl">
        <div className="relative">
          {/* Enhanced Header */}
          <DialogHeader className="pb-6 border-b border-gray-100 dark:border-gray-800">
            <DialogTitle className="text-xl font-semibold flex items-center gap-3">
              <div className="w-8 h-8 bg-voxa-teal-100 dark:bg-voxa-teal-900/30 rounded-full flex items-center justify-center">
                <CreditCard className="w-4 h-4 text-voxa-teal-600" />
              </div>
              Add Payment Method
            </DialogTitle>
          </DialogHeader>

          {/* Content */}
          <div className="pt-6 space-y-2">
            {/* Card Form */}
            <div>
              {setupError ? (
                <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center border border-red-200 dark:border-red-800/30">
                  <p className="text-red-600 dark:text-red-400 text-sm mb-4">
                    {setupError}
                  </p>
                  <Button
                    onClick={initializeSetupIntent}
                    variant="outline"
                    size="sm"
                    className="border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/40"
                  >
                    Try Again
                  </Button>
                </div>
              ) : !clientSecret ? (
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-lg p-8 text-center border border-gray-200 dark:border-gray-700">
                  <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin text-voxa-teal-600" />
                  <p className="text-sm text-muted-foreground">
                    Initializing secure payment...
                  </p>
                </div>
              ) : (
                <Elements stripe={stripePromise} options={{ clientSecret }}>
                  <CardForm
                    clientSecret={clientSecret}
                    onSuccess={handleSuccess}
                    onError={handleError}
                    isProcessing={isProcessing}
                    setIsProcessing={setIsProcessing}
                  />
                </Elements>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isProcessing}
                className="flex-1 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                Cancel
              </Button>
            </div>

            {/* Security note */}
            <div className="text-center border-t border-gray-100 dark:border-gray-800 pt-4">
              <p className="text-xs text-muted-foreground flex items-center justify-center gap-1">
                🔒 <span>Secured by Stripe</span>
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
