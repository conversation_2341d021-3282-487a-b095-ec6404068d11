import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import dbConnect from "@/lib/mongodb";
import Group from "@/models/Groups";
import Client from "@/models/Client";
import Entreprise from "@/models/Entreprise";
import { User } from "@/models/User";
import { verifyUserToken } from "@/lib/cognito/Token";

function isValidPhone(phone: string) {
  return /^\+?[0-9]{8,15}$/.test(phone);
}

export async function POST(req: Request) {
  try {
    await dbConnect();

    const cookieStore = cookies();
    const token = (await cookieStore).get("plugin_id_token")?.value;

    if (!token) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    const { valid, payload, error } = await verifyUserToken(token);

    if (!valid || !payload?.email) {
      return NextResponse.json(
        { error: "Token invalide ou expiré.", detail: error?.message },
        { status: 401 }
      );
    }

    let body;
    try {
      body = await req.json();
    } catch (parseError) {
      console.error("Erreur de parsing de la requête:", parseError);
      return NextResponse.json(
        { error: "Requête mal formée. Veuillez envoyer un corps JSON valide." },
        { status: 400 }
      );
    }

    const { numbers, group_name } = body;

    if (!Array.isArray(numbers) || numbers.length === 0) {
      return NextResponse.json(
        { error: "Au moins un numéro est requis." },
        { status: 400 }
      );
    }

    const invalidEntry = numbers.find(
      (n: any) =>
        typeof n.phone !== "string" ||
        !isValidPhone(n.phone) ||
        (n.name && typeof n.name !== "string")
    );

    if (invalidEntry) {
      return NextResponse.json(
        { error: "Format de numéro ou nom invalide dans la liste." },
        { status: 400 }
      );
    }

    const userDetails = await User.findOne({ email: payload.email });

    if (!userDetails) {
      return NextResponse.json({ error: "Utilisateur introuvable." }, { status: 404 });
    }

    const entreprise = await Entreprise.findOne({ admin: userDetails._id });

    if (!entreprise) {
      return NextResponse.json(
        { error: "Entreprise introuvable." },
        { status: 404 }
      );
    }

    // Create clients
    const clientDocs = await Promise.all(
      numbers.map((n: any) =>
        Client.create({
          name: n.name || "",
          phone: n.phone,
          entreprise: entreprise._id,
        })
      )
    );

    const group = await Group.create({
      name: group_name || "Groupe sans nom",
      members: clientDocs.map((c) => c._id),
      entreprise: entreprise._id,
    });

    return NextResponse.json(
      { message: "Groupe créé avec succès", group },
      { status: 200 }
    );
  } catch (err) {
    console.error("Erreur serveur:", err);
    return NextResponse.json(
      { error: "Erreur interne du serveur." },
      { status: 500 }
    );
  }
}
