import { hexOpacity } from "@/lib/Strings/colorTransform";
export interface StatsCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  gettingGoal?: boolean;
}

export default function StatsCard({
  title,
  value,
  icon,
  color,
  gettingGoal,
}: StatsCardProps) {
  return (
    <div
      className={`hover:scale-x-102 hover:scale-y-105 transition-transform duration-300 flex flex-col p-6 gap-4 rounded-xl dark:shadow-lg bg-[${color}]`}
      style={{ backgroundColor: hexOpacity(color, 0.1) }}
    >
      <div className="h-12 flex items-center gap-2 text-voxa-secondary">
        <div
          className="w-10 h-10 p-2 rounded-full"
          style={{ backgroundColor: hexOpacity(color, 0.2) }}
        >
          {icon}
        </div>
        <h1 className="text-xl sm:text-lg font-medium">{title}</h1>
      </div>
      <h1 className="h-8 text-[27px] font-medium ml-2 text-start">
        {gettingGoal ? (
          <div
            className="h-8 w-28 rounded animate-pulse"
            style={{ backgroundColor: hexOpacity(color, 0.3) }}
          />
        ) : (
          value
        )}
      </h1>
    </div>
  );
}
