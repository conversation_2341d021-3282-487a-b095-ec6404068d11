"use client";
import LoginIcon from "@mui/icons-material/Login";
import { signIn, signOut, useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import CircularLoaderSmall from "../Loaders/CircularLoaderSmall";
import Image from "next/image";
import parrot from "@/public/images/Icons/parrot.svg";
import LogoutIcon from "@mui/icons-material/Logout";
import { useTranslation } from "react-i18next";
import Link from "next/link";
import SettingsDropdown from "./SettingsDropdown";
import { EndCurrentConnection } from "@/actions/ConnectionActions";

export default function Navbar() {
  const { data: session, status } = useSession();
  const { t } = useTranslation(["home", "common"]);
  const pathname = usePathname();

  if (pathname?.startsWith("/businessDash") || pathname?.startsWith("/api_docs")) return null;
  const Signout = async () => {
    await EndCurrentConnection();
    signOut({ redirect: false });
    window.location.href = "/api/auth/cognito-logout";
  };
  return (
    <div className="sm:px-16 fixed w-full flex items-center z-50">
      <nav className="sm:mt-4 sm:rounded-xl p-4 sm:p-5 flex flex-wrap justify-between w-full mx-auto gap-2 sm:gap-4 bg-voxa-neutral-50 dark:bg-zinc-950">
        <Link
          href="/"
          className="flex gap-1 sm:gap-2 justify-center items-center"
        >
          <Image
            src={parrot}
            alt="Echo Parrot Icon"
            className="w-6 sm:w-8 invert dark:invert-0"
          />
          <h1 className="font-semibold text-nowrap text-lg sm:text-xl">
            {t("common:logo")}
          </h1>
        </Link>
        <div className="flex items-center gap-2 sm:gap-4">
          {status === "loading" ? (
            <CircularLoaderSmall />
          ) : status === "authenticated" ? (
            <div className="flex gap-2 sm:gap-4 justify-center items-center">
              <Link
                className="font-semibold"
                href={`${
                  session.user.role === "ENTREPRISE_ADMIN"
                    ? "/businessDash"
                    : session.user.role === "ENTREPRISE_AGENT"
                    ? "/userDash"
                    : session.user.role === "ADMIN"
                    ? "/adminDash"
                    : "/"
                }`}
              >
                Dashboard
              </Link>
              <button
                onClick={Signout}
                className="text-red-500 -translate-y-px hover:text-red-500/70 active:text-red-600 transition-all duration-150 justify-center items-center gap-1 flex font-semibold"
              >
                <LogoutIcon fontSize="medium" />
              </button>
            </div>
          ) : (
            !pathname?.startsWith("/auth") && (
              <button
                onClick={() => signIn("cognito")}
                className="px-4 py-2 bg-gray-500/70 hover:bg-gray-400/80 active:bg-gray-500/60 transition-all duration-150 rounded-full flex justify-center items-center gap-1"
              >
                {t("home:login")}
                <LoginIcon className="w-5 h-5" />
              </button>
            )
          )}
          <SettingsDropdown
            chevronClass="rotate-180 rtl:rotate-0"
            contentClass="mt-3.5 sm:mt-5"
          />
        </div>
      </nav>
    </div>
  );
}
