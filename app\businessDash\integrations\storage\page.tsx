"use client";

import aws from "@/public/images/Icons/integrations/aws.svg";
import googledrive from "@/public/images/Icons/integrations/googledrive.svg";
import dropbox from "@/public/images/Icons/integrations/dropbox.svg";
import azure from "@/public/images/Icons/integrations/azure.svg";
import { useTranslation } from "react-i18next";
import {
  IntegrationCards,
  IntegrationCardsProps,
} from "@/components/Dashboard/Cards/IntegrationCard";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import AwsCredentialsDialog from "@/components/dialogs/AwsCredentialsDialog";
import { setAwsDialogOpen } from "@/redux/BusinessDashboard/subSlices/IntegrationSlice";
import React from "react";
import { loadAwsCredentialsThunk } from "@/redux/BusinessDashboard/subSlices/IntegrationSlice";

export default function Integrations() {
  const { t } = useTranslation("integrations");
  const router = useRouter();
  const dispatch = useDispatch();
  const awsCredentials = useSelector(
    (state: RootState) =>
      state.businessDashboard.businessDashboardIntegration.awsCredentials
  );

  React.useEffect(() => {
    dispatch(loadAwsCredentialsThunk() as any);
  }, [dispatch]);

  const awsConnected =
    awsCredentials.AWS_ACCESS_KEY_ID &&
    awsCredentials.AWS_SECRET_ACCESS_KEY &&
    awsCredentials.REGION &&
    awsCredentials.BUCKET_NAME;

  const integrations: IntegrationCardsProps[] = [
    {
      items: [
        {
          icon: aws,
          name: t("storage.items.0"),
          bgColor: "bg-orange-200",
          buttonText: awsConnected ? t("connected") : t("connect"),
          onClick: () => dispatch(setAwsDialogOpen(true)),
        },
        {
          icon: googledrive,
          name: t("storage.items.1"),
          bgColor: "bg-yellow-200",
          comingSoon: false,
          onClick: () =>
            router.push("/businessDash/integrations/storage/providers/drive"),
        },
        {
          icon: azure,
          name: t("storage.items.3"),
          bgColor: "bg-blue-200",
          comingSoon: true,
          onClick: () => console.log("Connecting to Azure"),
        },
        {
          icon: dropbox,
          name: t("storage.items.2"),
          bgColor: "bg-blue-200",
          comingSoon: true,
          onClick: () => console.log("Connecting to Dropbox"),
        },
      ],
    },
  ];
  return (
    <div className="rounded-2xl w-full flex flex-col gap-4">
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        {t("storage.title")}
      </h1>
      {Object.values(integrations).map((section, index) => (
        <IntegrationCards key={index} items={section.items} />
      ))}
      <AwsCredentialsDialog />
    </div>
  );
}
