"use client";

import { createSubscriptionWithCard } from "@/actions/BillingActions";
import { createSubscription, getAllPlans } from "@/actions/BillingActions";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plan } from "@/types";
import { Crown, RefreshCw } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import useSWR, { mutate } from "swr";
import PlanCard from "./PlanCard";
import SubscriptionConfirmationDialog from "./SubscriptionConfirmationDialog";

interface AvailablePlansProps {
  className?: string;
}

const fetchPlans = async () => {
  const response = await getAllPlans();
  if (!response.success) {
    throw new Error(response.error || "Failed to fetch plans");
  }
  return response.data || [];
};

export default function AvailablePlans({ className }: AvailablePlansProps) {
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [selectedBillingPeriod, setSelectedBillingPeriod] =
    useState<string>("monthly");
  const [isSubscriptionDialogOpen, setIsSubscriptionDialogOpen] =
    useState(false);
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [isProcessingSubscription, setIsProcessingSubscription] =
    useState(false);

  const {
    data: plans,
    isLoading,
    error,
  } = useSWR("available-plans", fetchPlans);

  const plansData = plans || [];

  // Get all available billing periods from all plans
  const getAvailableBillingPeriods = () => {
    const periodsSet = new Set<string>();

    plansData.forEach((plan: Plan) => {
      if (plan.billing_options) {
        Object.entries(plan.billing_options).forEach(([period, options]) => {
          if (options && options.current_price > 0) {
            periodsSet.add(period);
          }
        });
      }
    });

    // Sort periods in logical order
    const periodOrder = ["daily", "weekly", "monthly", "yearly"];
    const availablePeriods = Array.from(periodsSet);

    return periodOrder
      .filter((period) => availablePeriods.includes(period))
      .concat(
        availablePeriods.filter((period) => !periodOrder.includes(period))
      );
  };

  const availablePeriods = getAvailableBillingPeriods();

  // Filter plans that have pricing for the selected billing period
  const plansWithSelectedPeriod = plansData.filter((plan: Plan) => {
    const billingOption =
      plan.billing_options?.[
        selectedBillingPeriod as keyof typeof plan.billing_options
      ];
    return billingOption && billingOption.current_price > 0;
  });

  // Sort plans by sort_order
  const sortedPlans = plansWithSelectedPeriod.sort((a: Plan, b: Plan) => {
    const sortOrderA = a.sort_order ?? 0;
    const sortOrderB = b.sort_order ?? 0;
    return sortOrderA - sortOrderB;
  });

  const handleSelectPlan = (planId: string) => {
    const plan = plansData.find((p: Plan) => p._id === planId);
    if (plan) {
      setSelectedPlan(plan);
      setSelectedPlanId(planId);
      setIsSubscriptionDialogOpen(true);
    }
  };

  const handleConfirmSubscription = async () => {
    if (!selectedPlan) return;

    console.log(
      "Starting subscription process for plan:",
      selectedPlan.name,
      "with billing period:",
      selectedBillingPeriod
    );

    try {
      setIsProcessingSubscription(true);
      console.log("Calling createSubscription with plan_id:", selectedPlan._id);

      const response = await createSubscription({
        plan_id: selectedPlan._id,
        billing_period: selectedBillingPeriod as
          | "monthly"
          | "yearly"
          | "weekly"
          | "daily",
        // You can add trial_days here if needed
      });

      console.log("createSubscription response:", response);

      if (response.success) {
        // Close the dialog first
        setIsSubscriptionDialogOpen(false);

        // Redirect to Stripe checkout
        if (response.data?.checkout_url) {
          console.log(
            "Redirecting to checkout URL:",
            response.data.checkout_url
          );
          toast.success("Redirecting to secure checkout...", {
            duration: 2000,
          });

          // Add a small delay to ensure the toast shows before redirect
          setTimeout(() => {
            window.location.href = response.data.checkout_url;
          }, 500);
        } else {
          console.error("No checkout URL in response:", response.data);
          toast.error("No checkout URL received");
        }
      } else {
        console.error("createSubscription failed:", response.error);
        toast.error(response.error || "Failed to create subscription");
      }
    } catch (error: any) {
      console.error("Error in handleConfirmSubscription:", error);
      toast.error(error.message || "Failed to create subscription");
    } finally {
      setIsProcessingSubscription(false);
    }
  };

  const handleSubscribeWithCard = async (cardId: string) => {
    if (!selectedPlan) return;

    console.log("Starting subscription with card:", cardId);

    try {
      setIsProcessingSubscription(true);

      const response = await createSubscriptionWithCard(
        selectedPlan._id,
        undefined,
        selectedBillingPeriod as "monthly" | "yearly" | "weekly" | "daily",
        cardId
      );

      if (response.success) {
        setIsSubscriptionDialogOpen(false);
        setTimeout(() => {
          mutate("subscriptions");
          mutate("current-subscription");
          mutate(["invoices", 1]);
        }, 2000);
        toast.success("Subscription created successfully!");
      } else {
        toast.error(response.error || "Failed to create subscription");
      }
    } catch (error: any) {
      console.error("Error creating subscription with card:", error);
      toast.error(error.message || "Failed to create subscription");
    } finally {
      setIsProcessingSubscription(false);
    }
  };

  const handleCloseSubscriptionDialog = () => {
    setIsSubscriptionDialogOpen(false);
  };

  const handleCloseComplete = () => {
    setSelectedPlan(null);
    setSelectedPlanId(null);
  };

  // Component to render plans grid
  const renderPlansGrid = (plans: Plan[]) => (
    <div className="grid gap-6 lg:grid-cols-2 min-[1100px]:grid-cols-3">
      {plans.map((plan: Plan) => (
        <PlanCard
          key={plan._id}
          plan={plan}
          billingPeriod={selectedBillingPeriod}
          isSelected={selectedPlanId === plan._id}
          onSelectPlan={handleSelectPlan}
        />
      ))}
    </div>
  );

  if (isLoading) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
          <div className="flex items-center justify-center gap-2 text-muted-foreground">
            <RefreshCw className="w-4 h-4 animate-spin" />
            Loading available plans...
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
          <div className="text-red-500">
            Error loading plans: {error.message}
          </div>
        </div>
      </div>
    );
  }

  if (plansData.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <Crown className="w-12 h-12 mx-auto mb-3 opacity-50 text-muted-foreground" />
          <p className="text-lg text-muted-foreground">
            No subscription plans available
          </p>
          <p className="text-sm text-muted-foreground">
            Check back later for our upcoming plans.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Set default billing period to the first available one
  if (
    availablePeriods.length > 0 &&
    !availablePeriods.includes(selectedBillingPeriod)
  ) {
    setSelectedBillingPeriod(availablePeriods[0]);
  }

  return (
    <div className={className}>
      {/* Billing Period Selector - only show if there are multiple periods */}
      {availablePeriods.length > 1 && (
        <div className="mb-6">
          <Tabs
            value={selectedBillingPeriod}
            onValueChange={setSelectedBillingPeriod}
            className="w-full"
          >
            <TabsList
              className="grid gap-6 w-fit mx-auto"
              style={{
                gridTemplateColumns: `repeat(${availablePeriods.length}, minmax(0, 1fr))`,
              }}
            >
              {availablePeriods.map((period) => (
                <TabsTrigger
                  key={period}
                  value={period}
                  className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-gray-200 dark:data-[state=active]:bg-gray-700 data-[state=active]:text-foreground data-[state=active]:shadow-sm transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 capitalize"
                >
                  {period}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>
      )}

      {/* Plans Grid */}
      {renderPlansGrid(sortedPlans)}

      {/* Show message if no plans available for selected period */}
      {sortedPlans.length === 0 && availablePeriods.length > 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Crown className="w-12 h-12 mx-auto mb-3 opacity-50 text-muted-foreground" />
            <p className="text-lg text-muted-foreground">
              No plans available for {selectedBillingPeriod} billing
            </p>
            <p className="text-sm text-muted-foreground">
              Try selecting a different billing period above.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Subscription Confirmation Dialog */}
      <SubscriptionConfirmationDialog
        isOpen={isSubscriptionDialogOpen}
        onClose={handleCloseSubscriptionDialog}
        onCloseComplete={handleCloseComplete}
        onSubscribeWithCard={handleSubscribeWithCard}
        selectedPlan={selectedPlan}
        billingPeriod={selectedBillingPeriod}
        isProcessingSubscription={isProcessingSubscription}
      />
    </div>
  );
}
