"use client";

import {
  GetClientsByGoalID,
  RemoveClientFromGoalWithNumber,
} from "@/actions/ClientsActions";
import { usePara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState, useRef, useCallback } from "react";
import { toast } from "sonner";
import { CopyIcon, GoalIcon, RadioIcon } from "lucide-react";
import {
  getClientsByStatus,
  GetGoalAnsweredConversations,
  GetDemarchageGoal,
  GetGoalOtherConversationNotAnswered,
  getIncallConversations,
  getVoiceMailDetectedGoalConversations,
} from "@/actions/GoalActions";
import CustomButton from "@/components/CustomFormItems/Button";
import { handleCopyText } from "@/lib/Strings/Copy";
import { AssistantGoalDropdown } from "@/components/dropdowns/AssistantGoalDropdown";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import {
  AddOneContact,
  handleAddExistingClientToGoal,
  ImportClientsToGoal as handleImportClientsToGoal,
} from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import CircularLoaderSmall from "@/components/Loaders/CircularLoaderSmall";
import CustomPagination from "@/components/pagination/CustomPagination";
import { usePagination } from "@/hooks/usePagination";
import ClientCard from "@/components/clients/ClientCard";
import ConversationCard from "@/components/clients/ConversationCard";

type FilterOption = {
  onClick: () => void;
  value: string;
};

export default function ContactHome() {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const params = useParams();
  const goalID = params?.id as string;

  const searchParams = useSearchParams();
  const goalName = searchParams?.get("goalName") as string;
  const templateType = searchParams?.get("templateType") as string;

  const [clients, setClients] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState<string>("All Clients");
  const [Otherconversations, setOtherConversations] = useState<any[]>([]);
  const [answeredConversations, setAnsweredConversations] = useState<any[]>([]);
  const [incallConversations, setIncallConversations] = useState<any[]>([]);
  const [incallConversationsSelected, setIncallConversationsSelected] =
    useState<boolean>(false);
  const [openGoalID, setOpenGoalID] = useState<string | null>(null);
  const [goalStatus, setGoalStatus] = useState<string>("");
  const [goalTemplateType, setGoalTemplateType] =
    useState<string>("DEMARCHAGE");
  const [goalType, setGoalType] = useState<string>("");
  const [_incrementSecondsBy, setIncrementSecondsBy] = useState(0);

  const clientsPerPage = 24;
  const itemsPerPage = 12;
  const {
    currentItems: paginatedClients,
    currentPage: clientsPage,
    setCurrentPage: setClientsPage,
  } = usePagination(clients, clientsPerPage);
  const {
    currentItems: paginatedAnsweredConversations,
    currentPage: answeredPage,
    setCurrentPage: setAnsweredPage,
  } = usePagination(answeredConversations, itemsPerPage);
  const {
    currentItems: paginatedOtherConversations,
    currentPage: otherPage,
    setCurrentPage: setOtherPage,
  } = usePagination(Otherconversations, itemsPerPage);
  const {
    currentItems: paginatedIncallConversations,
    currentPage: incallPage,
    setCurrentPage: setIncallPage,
  } = usePagination(incallConversations, itemsPerPage);

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const secondsIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const getGoalClients = useCallback(async () => {
    try {
      setLoading(true);
      setIncallConversationsSelected(false);
      setIncallConversations([]);
      setOtherConversations([]);
      setAnsweredConversations([]);
      const response = await GetClientsByGoalID(goalID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      setClients(response.clients || []);
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to get goal clients");
    } finally {
      setLoading(false);
    }
  }, [goalID]);

  useEffect(() => {
    const fetchIncallConversations = async () => {
      try {
        setIncrementSecondsBy(0);
        setAnsweredConversations([]);
        setOtherConversations([]);
        setClients([]);
        const response = await getIncallConversations(goalID);
        if (!response.success) {
          toast.error(response.error);
          return;
        }
        setIncallConversations(response.incallConversations || []);
      } catch (err: any) {
        console.error(err);
        toast.error("Failed to get goal clients");
      }
    };

    if (incallConversationsSelected) {
      fetchIncallConversations();
      pollingIntervalRef.current = setInterval(() => {
        setIncrementSecondsBy(0);
        fetchIncallConversations();
      }, 10000);
      secondsIntervalRef.current = setInterval(() => {
        setIncrementSecondsBy((prev) => prev + 1);
      }, 1000);
    } else {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
      if (secondsIntervalRef.current) {
        clearInterval(secondsIntervalRef.current);
        secondsIntervalRef.current = null;
      }
      setIncrementSecondsBy(0);
    }
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
      if (secondsIntervalRef.current) {
        clearInterval(secondsIntervalRef.current);
      }
      setIncrementSecondsBy(0);
    };
  }, [incallConversationsSelected, setIncrementSecondsBy, goalID]);

  useEffect(() => {
    getGoalClients();
  }, [goalID, getGoalClients]);

  useEffect(() => {
    const getGoal = async () => {
      try {
        const response = await GetDemarchageGoal(goalID);
        if (!response.success && response.error) {
          toast.error(response.error);
          return;
        } else if (response.goal) {
          setGoalStatus(response.goal.status ?? "");
          setGoalType(response.goal.goalType ?? "");
          setGoalTemplateType(response.goal.template_type ?? "");
        }
      } catch (err: any) {
        toast.error(err.message);
      }
    };
    getGoal();
  }, [goalID]);

  const handleFilterClick = async (
    filter: string,
    action: () => Promise<void>
  ) => {
    setSelectedFilter(filter);
    await action();
  };

  const fetchClientsByStatus = async (
    status:
      | "ANSWERED"
      | "CALLED"
      | "NOT_CALLED"
      | "MISSED"
      | "VOICEMAIL_DETECTED"
      | "FAILED"
  ) => {
    try {
      setLoading(true);
      setIncallConversationsSelected(false);
      setIncallConversations([]);
      setAnsweredConversations([]);
      setOtherConversations([]);
      const response = await getClientsByStatus(goalID, status);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      setClients(response.clients || []);
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to get goal clients");
    } finally {
      setLoading(false);
    }
  };

  const fetchGoalAnsweredConversations = async () => {
    try {
      setOtherConversations([]);
      setClients([]);
      setIncallConversationsSelected(false);
      setIncallConversations([]);
      setLoading(true);
      const response = await GetGoalAnsweredConversations(goalID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      setAnsweredConversations(response.answeredConversations || []);
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to get goal clients");
    } finally {
      setLoading(false);
    }
  };

  const fetchVoiceMailDetecteedConversations = async () => {
    try {
      setOtherConversations([]);
      setClients([]);
      setIncallConversationsSelected(false);
      setIncallConversations([]);
      setLoading(true);
      const response = await getVoiceMailDetectedGoalConversations(goalID);
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      setAnsweredConversations(response.voicemailDetectedConversations || []);
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to get goal clients");
    } finally {
      setLoading(false);
    }
  };

  const fetchOtherConversations = async (filter: "MISSED" | "FAILED") => {
    try {
      setAnsweredConversations([]);
      setClients([]);
      setIncallConversationsSelected(false);
      setIncallConversations([]);
      setLoading(true);
      const response = await GetGoalOtherConversationNotAnswered(
        goalID,
        filter
      );
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      setOtherConversations(response.otherConversations || []);
    } catch (err: any) {
      console.error(err);
      toast.error("Failed to get goal clients");
    } finally {
      setLoading(false);
    }
  };

  const removeClient = async (phone: string) => {
    try {
      const response = await RemoveClientFromGoalWithNumber(phone, goalID);
      if (!response.success && response.error) {
        toast.error(response.error);
        return;
      }
      toast.success("Client removed successfully");
      setClients(clients.filter((client) => client.phone !== phone));
    } catch (err: any) {
      toast.error(err.message);
    }
  };

  const filterOptions: FilterOption[] = [
    { onClick: getGoalClients, value: "All Clients" },
    {
      onClick: () => fetchClientsByStatus("NOT_CALLED"),
      value: "Not Called Clients",
    },
    { onClick: fetchGoalAnsweredConversations, value: "Answered Calls" },
    { onClick: () => fetchOtherConversations("MISSED"), value: "Missed Calls" },
    {
      onClick: () => fetchVoiceMailDetecteedConversations(),
      value: "Voicemail Detected",
    },
    { onClick: () => fetchOtherConversations("FAILED"), value: "Failed" },
    { onClick: () => setIncallConversationsSelected(true), value: "In call" },
  ];

  return (
    <div className="rounded-2xl w-full flex flex-col gap-6">
      {/* Header */}
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        Goal&apos;s Clients History
      </h1>
      <div className="flex-wrap w-full flex max-xl:flex-col xl:items-center gap-3 sm:gap-2 justify-between font-normal">
        <div className="max-sm:mx-auto text-xs sm:text-sm sm:gap-2 text-nowrap max-sm:max-w-[240px]">
          <div>
            <span className="font-semibold">Goal Name: </span>
            <span>{goalName}</span>
          </div>
          <div className="dark:text-voxa-neutral-200 flex gap-1 items-center">
            <span className="font-semibold">Goal ID:</span>
            <span title={goalID} className="truncate">
              {goalID}
            </span>
            <CopyIcon
              onClick={() => handleCopyText(goalID)}
              className="w-3 h-3 cursor-pointer hover:text-voxa-neutral-200 transition-colors duration-200"
            />
          </div>
        </div>
        <div className="max-sm:w-full flex max-xl:flex-wrap items-center place-content-end max-xl:justify-center gap-2">
          <CustomButton
            props={{
              onClick: () => dispatch(AddOneContact({ goalID })),
              value: "Add New Client",
              className:
                "w-full sm:w-fit px-2 sm:px-4 py-2 rounded-md bg-voxa-teal-600 hover:bg-voxa-teal-500 text-voxa-neutral-50",
            }}
          />
          <CustomButton
            props={{
              onClick: () =>
                dispatch(handleAddExistingClientToGoal({ goalID })),

              value: "Add Existing Client",
              className:
                "w-full sm:w-fit px-2 sm:px-4 py-2 rounded-md bg-voxa-teal-600 hover:bg-voxa-teal-500 text-voxa-neutral-50",
            }}
          />
          <CustomButton
            props={{
              onClick: () => dispatch(handleImportClientsToGoal({ goalID })),
              value: "Import Clients",
              className:
                "w-full sm:w-fit px-2 sm:px-4 py-2 rounded-md bg-voxa-teal-600 hover:bg-voxa-teal-500 text-voxa-neutral-50",
            }}
          />
          <div className="max-sm:w-full flex gap-2 items-center">
            <CustomButton
              props={{
                onClick: () =>
                  router.push(
                    `/businessDash/assistants/Goals/CreateEdit/${goalID}?type=edit&templateType=${templateType}`
                  ),
                value: "Goal Settings",
                className:
                  "bg-background hover:bg-background w-full sm:w-fit px-4 py-2 rounded-md border-2 border-voxa-teal-600 text-voxa-teal-600 hover:border-voxa-teal-400 hover:text-voxa-teal-400 transition-colors",
                icon: <GoalIcon className="w-8 h-8" />,
              }}
            />
            <AssistantGoalDropdown
              goalName={goalName}
              goalID={goalID}
              goalStatus={goalStatus}
              goalType={goalType}
              isOpen={goalID === openGoalID}
              setOpenGoalID={setOpenGoalID}
              goal_template_type={goalTemplateType}
            />
          </div>
        </div>
      </div>

      {/* Buttons Container */}
      <div className=" mb-4 flex flex-wrap gap-2 sm:gap-3 xl:gap-4 w-full justify-center">
        {filterOptions.map((option, index) => {
          const isInCall = option.value === "In call";
          const isSelected = selectedFilter === option.value;

          return (
            <div key={index} className="relative">
              <CustomButton
                props={{
                  onClick: async () =>
                    handleFilterClick(
                      option.value,
                      () =>
                        new Promise<void>((resolve) => {
                          option.onClick();
                          resolve();
                        })
                    ),
                  value: option.value,
                  loading: loading,
                  className: `text-voxa-neutral-50 w-fit px-2.5 sm:px-4 py-2 rounded-md transition ${
                    isSelected ? "bg-red-600 dark:bg-red-700" : ""
                  } ${
                    isInCall && !isSelected
                      ? "animate-pulse duration-1500 bg-red-500/70"
                      : ""
                  }`,
                  icon: isInCall && (
                    <div className="flex items-center ml-2">
                      <span className="animate-ping absolute inline-flex w-4 h-4 rounded-full hover:bg-red-400/40 bg-red-400 opacity-80"></span>
                      <RadioIcon className="text-voxa-neutral-50 w-8 h-8" />
                    </div>
                  ),
                }}
              />
            </div>
          );
        })}
      </div>

      {/* Clients List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 justify-center gap-3 md:gap-4">
        {loading ? (
          <div className="w-full lg:col-span-2 xl:col-span-3 flex justify-center items-center">
            <CircularLoaderSmall />
          </div>
        ) : clients.length > 0 ? (
          <>
            {paginatedClients.map((client, index) => (
              <ClientCard
                key={index}
                client={client}
                onRemove={() => removeClient(client.phone)}
              />
            ))}
            <CustomPagination
              className="lg:col-span-2 xl:col-span-3"
              itemsPerPage={clientsPerPage}
              totalItems={clients.length}
              currentPage={clientsPage}
              onPageChange={setClientsPage}
            />
          </>
        ) : answeredConversations.length > 0 ? (
          <>
            {paginatedAnsweredConversations.map((conversation, index) => (
              <ConversationCard key={index} conversation={conversation} />
            ))}
            <CustomPagination
              className="lg:col-span-2 xl:col-span-3"
              itemsPerPage={itemsPerPage}
              totalItems={answeredConversations.length}
              currentPage={answeredPage}
              onPageChange={setAnsweredPage}
            />
          </>
        ) : Otherconversations.length > 0 ? (
          <>
            {paginatedOtherConversations.map((conversation, index) => (
              <ConversationCard key={index} conversation={conversation} />
            ))}
            <CustomPagination
              className="lg:col-span-2 xl:col-span-3"
              itemsPerPage={itemsPerPage}
              totalItems={Otherconversations.length}
              currentPage={otherPage}
              onPageChange={setOtherPage}
            />
          </>
        ) : incallConversations.length > 0 ? (
          <>
            {paginatedIncallConversations.map((conversation, index) => (
              <ConversationCard
                key={index}
                conversation={conversation}
                goalID={goalID}
              />
            ))}
            <CustomPagination
              className="lg:col-span-2 xl:col-span-3"
              itemsPerPage={itemsPerPage}
              totalItems={incallConversations.length}
              currentPage={incallPage}
              onPageChange={setIncallPage}
            />
          </>
        ) : (
          <h1 className="w-full flex items-center justify-center md:col-span-2 xl:col-span-3 dark:text-voxa-neutral-50 text-lg">
            No data found.
          </h1>
        )}
      </div>
    </div>
  );
}
