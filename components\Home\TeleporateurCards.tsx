import React from "react";
import { BentoGrid, BentoGridItem } from "../ui/bento-grid";
import {
  IconPhoneCall,
  IconListCheck,
  IconDatabase,
} from "@tabler/icons-react";

export function TeleoperateursGrid() {
  return (
    <BentoGrid className="mx-auto w-full mt-16">
      {items.map((item, i) => (
        <BentoGridItem
          key={i}
          title={item.title}
          description={item.description}
          header={
            <div className="text-xl font-bold text-center p-4">
              {item.header}
            </div>
          } // Updated header to be text
          icon={item.icon}
          className={i === 3 ? "md:col-span-3" : ""}
        />
      ))}
    </BentoGrid>
  );
}

const items = [
  {
    title: "Real-time voice interaction",
    description:
      "Our teleoperators interact via phone with a customizable voice (gender, accent, tone). They manage both inbound and outbound calls in all languages.",
    header: "Advanced Voice Personalization",
    icon: (
      <IconPhoneCall className="h-6 w-6 text-voxa-neutral-500 group-hover/bento:text-zinc-300" />
    ),
  },
  {
    title: "Adherence to sales scripts and customer service management",
    description:
      "Our agents strictly follow your script and rely on a knowledge base to answer questions while avoiding sensitive topics.",
    header: "Scripting and Interaction Management",
    icon: (
      <IconListCheck className="h-6 w-6 text-voxa-neutral-500 group-hover/bento:text-zinc-300" />
    ),
  },
  {
    title: "Real-time data collection and usage",
    description:
      "Our teleoperators record relevant data, use the caller's name, generate conversation summaries, and perform follow-up actions.",
    header: "Dynamic Data Management",
    icon: (
      <IconDatabase className="h-6 w-6 text-voxa-neutral-500 group-hover/bento:text-zinc-300" />
    ),
  },
  {
    title: "Database management",
    description:
      "We offer complete database management solutions, ensuring enhanced performance and security.",
    header: "Securing Critical Data",
    icon: (
      <IconDatabase className="h-6 w-6 text-voxa-neutral-500 group-hover/bento:text-zinc-300" />
    ),
  },
];
