import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>etDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import CustomInput from "../CustomFormItems/Input";
import { useState } from "react";
import CustomButton from "../CustomFormItems/Button";
import { CheckIcon, PlusCircleIcon, PlusIcon, XIcon } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import CustomTextarea from "../CustomFormItems/CustomTextArea";
import { toast } from "sonner";
import { CreateScript } from "@/actions/ScriptsActions";
import { useTranslation } from "react-i18next";
import { scopedT } from "@/lib/scopedT";
import { Input } from "../ui/input";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { tagColors } from "@/components/Dashboard/SideComponents/TagsNotesDrawer";

export function CreateCustomScript({
  setScripts,
  Scripts,
}: {
  setScripts: any;
  Scripts: any[];
}) {
  const { t } = useTranslation("scripts");
  const c = scopedT(t, "createCustomScript.scriptHTML");
  const g = scopedT(t, "createCustomScript.generateScript");

  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [script_data, set_script_data] = useState({
    script_name: "",
    language: "FR",
    accent: "",
    ai_role: "",
    entreprise_name: "",
    address: "",
    number_of_questions: "",
    goal: "",
    introduction: "",
    questions: [
      {
        question: "",
        placeholder: c("fields.questions.placeholder_1"),
        responses: ["", ""],
      },
      {
        question: "",
        placeholder: c("fields.questions.placeholder_2"),
        responses: ["", ""],
      },
      {
        question: "",
        placeholder: c("fields.questions.placeholder_3"),
        responses: ["", ""],
      },
    ],
    should_include_client_name: "",
    message_when_end_call: "",
    message_when_transfer_call: "",
    tags: [{ name: "", color: tagColors[0] }],
  });

  const handleAddTag = () => {
    if (script_data.tags.length >= 5) return;
    const usedColors = script_data.tags.map((tag) => tag.color);
    const availableColor = tagColors.find(
      (color) => !usedColors.includes(color)
    );
    if (!availableColor) return;
    set_script_data({
      ...script_data,
      tags: [...script_data.tags, { name: "", color: availableColor }],
    });
  };

  const handleTagNameChange = (idx: number, value: string) => {
    set_script_data({
      ...script_data,
      tags: script_data.tags.map(
        (tag: { name: string; color: string }, i: number) =>
          i === idx ? { ...tag, name: value } : tag
      ),
    });
  };

  const handleTagColorChange = (idx: number, color: string) => {
    set_script_data({
      ...script_data,
      tags: script_data.tags.map(
        (tag: { name: string; color: string }, i: number) =>
          i === idx ? { ...tag, color } : tag
      ),
    });
  };

  const handleRemoveTag = (idx: number) => {
    set_script_data({
      ...script_data,
      tags: script_data.tags.filter(
        (_: { name: string; color: string }, i: number) => i !== idx
      ),
    });
  };

  const generateScript = (script_data: any) => {
    const {
      language,
      ai_name,
      address,
      accent,
      ai_role,
      entreprise_name,
      number_of_questions,
      goal,
      questions,
      message_when_end_call,
      message_when_transfer_call,
      voicemail_message,
      derniere_phrase_dite,
    } = script_data;

    const parsedNumber = parseInt(number_of_questions || "0");
    const renderDecisionTree = questions
      .slice(0, parsedNumber)
      .map((q: any, i: number) => {
        const responseYes = q.responses?.[0] || "Réponse possible 1";
        const responseNo = q.responses?.[1] || "Réponse possible 2";
        const isLastQuestion = i === parsedNumber - 1;
        let actionAfter = "";

        if (i === 0 && parsedNumber === 1) {
          actionAfter = `<span class="font-[600]">${g(
            "client"
          )}</span> "${responseYes}"
<span class="font-[600]">${g("ai")}</span> ${g("transfer_call", {
            message_when_transfer_call,
          })}
<span class="font-[600]">${g("client")}</span> "${responseNo}"
<span class="font-[600]">${g("ai")}</span> "${g("end_call", {
            message_when_end_call,
          })}"`;
        } else if (isLastQuestion) {
          actionAfter = `<span class="font-[600]">${g(
            "client"
          )}</span> "${responseYes}"
<span class="font-[600]">${g("ai")}</span> ${g("transfer_call", {
            message_when_transfer_call,
          })}
<span class="font-[600]">${g("client")}</span> "${responseNo}"
<span class="font-[600]">${g("ai")}</span> ${g("fallback_response")} 
`;
        } else if (i === 0) {
          actionAfter = `<span class="font-[600]">${g(
            "client"
          )}</span> "${responseYes}"
<span class="font-[600]">${g("ai")}</span> ${g("go_to_next_question", {
            next_question: i + 2,
          })}
<span class="font-[600]">${g("client")}</span> "${responseNo}"
<span class="font-[600]">${g("ai")}</span> ${g("end_call", {
            message_when_end_call,
          })}`;
        } else {
          actionAfter = `<span class="font-[600]">${g(
            "client"
          )}</span> "${responseYes}"
<span class="font-[600]">${g("ai")}</span> ${g("go_to_next_question", {
            next_question: i + 2,
          })}
<span class="font-[600]">${g("client")}</span> "${responseNo}"
<span class="font-[600]">${g("ai")}</span> "${g("end_call", {
            message_when_end_call,
          })}"`;
        }
        return `${i === 0 ? "\n" : "\n\n"}<strong>${g("question_label", {
          index: i + 1,
        })}</strong> ${q.question}
<strong>${g("possible_answers")}</strong>
${actionAfter}`;
      });
    return `<strong class="text-base text-voxa-teal-600 font-[600]">${g(
      "call_context"
    )}</strong>\n
<strong>${g("your_role_label")}</strong>\n  ${g("your_role_content", {
      ai_name,
      language,
      ai_role,
      entreprise_name,
      address,
    })}
<strong>${g("style_label")}</strong>\n  ${g("style_content", { accent })}
<strong>${g("objective_label")}</strong>\n  ${g("objective_content", {
      number_of_questions,
    })}
${renderDecisionTree}
<strong>${g("special_cases")}</strong>
Situation | ${g("ai")}
${g("case_voicemail")} | ${g("case_voicemail_response", { voicemail_message })}
${g("case_silence")} | ${g("case_silence_response")}
${g("case_callback_request")} | ${g("case_callback_response")}
${g("case_multiple_voices")} | ${g("case_multiple_voices_response")}
${g("case_repeat_request")} | ${g("case_repeat_response", {
      derniere_phrase_dite,
    })}
${g("case_out_of_scope")} | ${g("case_out_of_scope_response", { goal })}
${g("case_client_hangs_up")} | ${g("case_client_hangs_up_response")}
`;
  };

  const pushScriptToDB = async () => {
    try {
      setLoading(true);
      const content = generateScript(script_data);
      const response = await CreateScript(
        content,
        script_data.script_name,
        script_data.tags
      );
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      if (response.script) {
        setScripts([...Scripts, response.script]);
        console.log("Scripts:" + JSON.stringify(Scripts, null, 2));
        toast.success(c("scriptCreated"));
      }
    } catch (err: any) {
      console.error(err);
      toast.success(c("error"));
    } finally {
      setLoading(false);
    }
  };
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <CustomButton
        props={{
          value: c("button"),
          className:
            "w-full md:w-fit bg-voxa-teal-600 hover:bg-voxa-teal-500  text-white px-3",
          onClick: () => setOpen(true),
          icon: <PlusCircleIcon className="w-14 h-14" />,
        }}
      />
      <SheetContent className="!h-screen w-full sm:max-w-[820px] flex flex-col justify-between p-4">
        <SheetHeader>
          <SheetTitle className="text-voxa-neutral-500 dark:text-voxa-neutral-50 text-center">
            {c("title")}
          </SheetTitle>
          <SheetDescription>{c("description")}</SheetDescription>
        </SheetHeader>
        <div className="overflow-y-auto">
          <div className="mt-3 px-2">
            <CustomInput
              props={{
                type: "text",
                label: c("fields.script_name.label"),
                placeholder: c("fields.script_name.placeholder"),
                className: "w-full py-5 mb-3",
                required: true,
                onChange: (e) =>
                  set_script_data({
                    ...script_data,
                    script_name: e.target.value,
                  }),
                name: "script_name",
                value: script_data.script_name,
              }}
            />
            <div className="grid gap-4 py-3 md:grid-cols-2">
              <div className="flex items-center justify-between">
                <Label>{c("fields.script_tags.label")}</Label>
                <Tooltip>
                  <TooltipTrigger asChild className="h-full">
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="h-10 w-10"
                      onClick={handleAddTag}
                      aria-label="Add tag"
                      disabled={
                        script_data.tags.length >= 5 ||
                        script_data.tags.length >= tagColors.length
                      }
                    >
                      <PlusIcon className="w-5 h-5" />
                    </Button>
                  </TooltipTrigger>
                  {script_data.tags.length >= 5 ||
                  script_data.tags.length >= tagColors.length ? (
                    <TooltipContent>
                      {c("fields.script_tags.max_tags")}
                    </TooltipContent>
                  ) : null}
                </Tooltip>
              </div>
              {script_data.tags.map((tag, idx) => (
                <div className="flex w-full items-center" key={idx}>
                  <Input
                    placeholder={c("fields.script_tags.placeholder")}
                    className="rounded-r-none"
                    value={tag.name}
                    onChange={(e) => handleTagNameChange(idx, e.target.value)}
                  />
                  <Tooltip>
                    <TooltipTrigger asChild className="border-r-none">
                      <button
                        type="button"
                        className="h-full inline-flex items-center px-2 bg-white dark:bg-sidebar border border-l-0 border-gray-300 dark:border-sidebar-border rounded-none border-r-none focus:outline-none"
                        tabIndex={0}
                        aria-label="Choose tag color"
                      >
                        <span
                          className="w-5 h-5 rounded-full border-2 border-gray-300 flex items-center justify-center"
                          style={{ background: tag.color }}
                        >
                          <CheckIcon className="w-3 h-3 text-white" />
                        </span>
                      </button>
                    </TooltipTrigger>
                    <TooltipContent
                      sideOffset={4}
                      className="flex gap-1 p-2 bg-white dark:bg-sidebar"
                    >
                      {tagColors.map((color) => {
                        const isUsed = script_data.tags.some(
                          (t, i) => t.color === color && i !== idx
                        );
                        return (
                          <button
                            key={color}
                            type="button"
                            className={`z-50 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors duration-150 ${
                              tag.color === color
                                ? "border-voxa-teal-600"
                                : "border-gray-300"
                            } ${isUsed ? "opacity-40 cursor-not-allowed" : ""}`}
                            style={{ background: color }}
                            onClick={() =>
                              !isUsed && handleTagColorChange(idx, color)
                            }
                            aria-label={`Choose color ${color}`}
                            disabled={isUsed}
                          >
                            {tag.color === color && (
                              <CheckIcon className="w-3 h-3 text-white" />
                            )}
                          </button>
                        );
                      })}
                    </TooltipContent>
                  </Tooltip>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="h-full w-12 rounded-l-none"
                    onClick={() => handleRemoveTag(idx)}
                    aria-label="Remove tag"
                  >
                    <XIcon className="w-5 h-5 text-red-500" />
                  </Button>
                </div>
              ))}
            </div>
            <div className="grid gap-4 py-3 md:grid-cols-2">
              <CustomInput
                props={{
                  type: "text",
                  label: c("fields.language.label"),
                  placeholder: c("fields.language.placeholder"),
                  className: "w-full py-5",
                  required: true,
                  readonly: true,
                  name: "language",
                  value: script_data.language,
                }}
              />
              <div className="flex w-full flex-col gap-2">
                <label htmlFor="accent" className="text-sm font-medium">
                  {c("fields.accent.label")}
                  <span className="text-red-500 -translate-x-1"> * </span>
                </label>
                <Select
                  onValueChange={(value) =>
                    set_script_data({ ...script_data, accent: value })
                  }
                  value={script_data.accent}
                  required
                >
                  <SelectTrigger id="accent" className="w-full !py-5">
                    <SelectValue placeholder={c("fields.accent.placeholder")} />
                  </SelectTrigger>
                  <SelectContent>
                    {[
                      c("fields.accent.options.south"),
                      c("fields.accent.options.north"),
                      c("fields.accent.options.paris"),
                      c("fields.accent.options.quebec"),
                    ].map((label, index) => (
                      <SelectItem key={index} value={label}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid gap-4 py-3 md:grid-cols-2">
              <CustomInput
                props={{
                  type: "text",
                  label: c("fields.ai_role.label"),
                  placeholder: c("fields.ai_role.placeholder"),
                  className: "w-full py-5",
                  required: true,
                  name: "AI_Role",
                  onChange: (e) =>
                    set_script_data({
                      ...script_data,
                      ai_role: e.target.value,
                    }),
                  value: script_data.ai_role,
                }}
              />
              <CustomInput
                props={{
                  type: "text",
                  label: c("fields.entreprise_name.label"),
                  placeholder: c("fields.entreprise_name.placeholder"),
                  className: "w-full py-5",
                  required: true,
                  name: "EntrepriseName",
                  onChange: (e) =>
                    set_script_data({
                      ...script_data,
                      entreprise_name: e.target.value,
                    }),
                  value: script_data.entreprise_name,
                }}
              />
            </div>

            <div className="grid gap-4 py-3 md:grid-cols-2">
              <CustomInput
                props={{
                  type: "text",
                  label: c("fields.address.label"),
                  placeholder: c("fields.address.placeholder"),
                  className: "w-full py-5",
                  required: true,
                  name: "AI_Role",
                  onChange: (e) =>
                    set_script_data({
                      ...script_data,
                      address: e.target.value,
                    }),
                  value: script_data.address,
                }}
              />
              <div className="flex w-full flex-col gap-2">
                <label htmlFor="accent" className="text-sm font-medium">
                  {c("fields.number_of_questions.label")}
                  <span className="text-red-500 -translate-x-1"> * </span>
                </label>
                <Select
                  onValueChange={(value) =>
                    set_script_data({
                      ...script_data,
                      number_of_questions: value,
                    })
                  }
                  value={script_data.number_of_questions}
                  required
                >
                  <SelectTrigger
                    id="nb_questions"
                    className="w-full !py-5 font-medium"
                  >
                    <SelectValue
                      placeholder={c("fields.number_of_questions.placeholder")}
                      className="dark:text-voxa-neutral-200"
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {["1", "2", "3"].map((nb) => (
                      <SelectItem
                        key={nb}
                        value={nb}
                        className="dark:text-voxa-neutral-200 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-800"
                      >
                        {nb}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <CustomInput
              props={{
                type: "text",
                parentClassName: "py-3",
                label: c("fields.goal.label"),
                placeholder: c("fields.goal.placeholder"),
                className: "w-full py-5",
                required: true,
                name: "goal",
                onChange: (e) =>
                  set_script_data({ ...script_data, goal: e.target.value }),
                value: script_data.goal,
              }}
            />

            {script_data.questions
              .slice(0, parseInt(script_data.number_of_questions))
              .map((q, index) => (
                <div
                  key={index}
                  className="grid gap-4 py-3 md:grid-cols-2 items-start"
                >
                  <CustomTextarea
                    props={{
                      name: `questions[${index}].question`,
                      label: c("fields.question", { index: index + 1 }),
                      placeholder: script_data.questions[index].placeholder,
                      onChange: (e) => {
                        const updatedQuestions = [...script_data.questions];
                        updatedQuestions[index].question = e.target.value;
                        set_script_data({
                          ...script_data,
                          questions: updatedQuestions,
                        });
                      },
                      value: q.question,
                      rows: 5,
                      required: true,
                      className: "h-full",
                    }}
                  />
                  <div className="flex gap-3 flex-col">
                    <CustomInput
                      props={{
                        type: "text",
                        label: c("fields.response_1.label"),
                        placeholder: c("fields.response_1.placeholder"),
                        className: "w-full py-5",
                        required: true,
                        name: `questions[${index}].responses[0]`,
                        onChange: (e) => {
                          const updatedQuestions = [...script_data.questions];
                          updatedQuestions[index].responses[0] = e.target.value;
                          set_script_data({
                            ...script_data,
                            questions: updatedQuestions,
                          });
                        },
                        value: q.responses[0],
                      }}
                    />
                    <CustomInput
                      props={{
                        type: "text",
                        label: c("fields.response_2.label"),
                        placeholder: c("fields.response_2.placeholder"),
                        className: "w-full py-5",
                        required: true,
                        name: `questions[${index}].responses[1]`,
                        onChange: (e) => {
                          const updatedQuestions = [...script_data.questions];
                          updatedQuestions[index].responses[1] = e.target.value;
                          set_script_data({
                            ...script_data,
                            questions: updatedQuestions,
                          });
                        },
                        value: q.responses[1],
                      }}
                    />
                  </div>
                </div>
              ))}

            <div className="py-3 flex items-center gap-3">
              <Label
                htmlFor="script"
                className="text-sm font-medium dark:text-voxa-neutral-50"
              >
                {c("fields.include_name")}
              </Label>
              <Switch />
            </div>

            <CustomInput
              props={{
                type: "text",
                parentClassName: "py-3",
                label: c("fields.message_when_end_call.label"),
                placeholder: c("fields.message_when_end_call.placeholder"),
                className: "w-full py-5",
                required: true,
                name: "message_when_end_call",
                onChange: (e) =>
                  set_script_data({
                    ...script_data,
                    message_when_end_call: e.target.value,
                  }),
                value: script_data.message_when_end_call,
              }}
            />
            <CustomInput
              props={{
                type: "text",
                parentClassName: "py-3 pb-5",
                label: c("fields.message_when_transfer_call.label"),
                placeholder: c("fields.message_when_transfer_call.placeholder"),
                className: "w-full py-5",
                required: true,
                name: "message_when_transfer_call",
                onChange: (e) =>
                  set_script_data({
                    ...script_data,
                    message_when_transfer_call: e.target.value,
                  }),
                value: script_data.message_when_transfer_call,
              }}
            />

            <span className="text-forground/80 font-semibold">
              {c("preview_label")}
            </span>
            <div className="relative overflow-y-auto flex-1 h-[30rem] rounded-lg bg-sidebar dark:bg-voxa-neutral-950 border">
              {generateScript(script_data)
                .split("\n")
                .map((line, index) => (
                  <div
                    key={index}
                    className="text-accent-forground/40 dark:text-voxa-neutral-200 font-medium text-sm whitespace-pre-wrap m-2"
                    dangerouslySetInnerHTML={{ __html: line }}
                  />
                ))}
            </div>
          </div>
        </div>
        <SheetFooter className="px-2">
          <Button
            className={`flex w-full gap-1 text-sm justify-center items-center transition-all duration-150 text-white px-4 py-2 rounded-md bg-voxa-teal-600 hover:bg-voxa-teal-500  ${
              loading ? "opacity-50 cursor-not-allowed" : ""
            }`}
            disabled={loading}
            onClick={pushScriptToDB}
          >
            {loading ? c("button_create") : c("button_save")}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
