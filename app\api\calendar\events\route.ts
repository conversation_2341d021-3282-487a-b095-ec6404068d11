import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export const runtime = "nodejs";

export async function GET() {
  const tokenCookie = (await cookies()).get("google_tokens_calendar")?.value;
  if (!tokenCookie) {
    return NextResponse.json({ error: "Not authorized" }, { status: 401 });
  }

  const { access_token } = JSON.parse(tokenCookie);

  const now = new Date().toISOString();

  const res = await fetch(
    `https://www.googleapis.com/calendar/v3/calendars/primary/events?maxResults=5&timeMin=${now}&orderBy=startTime&singleEvents=true`,
    {
      headers: {
        Authorization: `Bearer ${access_token}`,
      },
    }
  );

  if (!res.ok) {
    return NextResponse.json({ error: "Failed to fetch events" }, { status: res.status });
  }

  const data = await res.json();
  return NextResponse.json(data.items ?? []);
}
