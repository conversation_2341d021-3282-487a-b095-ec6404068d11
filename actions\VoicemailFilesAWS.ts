'use server';

import { getServerSession } from 'next-auth';
import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { BusinessFilesS3 } from '@/lib/S3Client';
import authOptions from '@/lib/AuthOptions';

export async function GetAudioVoicemailSignedURL(filename: string) {
  const session = await getServerSession(authOptions);
  if (!session) return { failure: 'Vous devez être connecté pour téléverser des fichiers.' };

  const sanitizedUsername = session.user.name?.replaceAll(' ', '_') || 'anonymous';
  const sanitizedFilename = filename.replaceAll(' ', '_').replace(/[^a-zA-Z0-9_\-\.]/g, '');
  const fullFilename = `${sanitizedUsername}_${Date.now()}_${sanitizedFilename}`;

  const command = new PutObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME!,
    Key: `Voicemail_Audios/${fullFilename}`,
    ContentType: 'audio/webm',
  });

  const uploadUrl = await getSignedUrl(BusinessFilesS3, command, { expiresIn: 60 });

  return {
    success: {
      uploadUrl,
      fileUrl: `https://${process.env.AWS_BUCKET_NAME}.s3.${process.env.AWS_BUCKET_REGION}.amazonaws.com/Voicemail_Audios/${fullFilename}`,
    },
  };
}

export async function GetAudioHumanIntroductionSignedURL(filename: string) {
  const session = await getServerSession(authOptions);
  if (!session) return { failure: 'Vous devez être connecté pour téléverser des fichiers.' };

  const sanitizedUsername = session.user.name?.replaceAll(' ', '_') || 'anonymous';
  const sanitizedFilename = filename.replaceAll(' ', '_').replace(/[^a-zA-Z0-9_\-\.]/g, '');
  const fullFilename = `${sanitizedUsername}_${Date.now()}_${sanitizedFilename}`;

  const command = new PutObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME!,
    Key: `Human_Introduction/${fullFilename}`,
    ContentType: 'audio/webm',
  });

  const uploadUrl = await getSignedUrl(BusinessFilesS3, command, { expiresIn: 60 });

  return {
    success: {
      uploadUrl,
      fileUrl: `https://${process.env.AWS_BUCKET_NAME}.s3.${process.env.AWS_BUCKET_REGION}.amazonaws.com/Human_Introduction/${fullFilename}`,
    },
  };
}

export const uploadAudioToS3 = async (
  audioBlob: Blob | null
): Promise<{ success: boolean; error?: string; url?: string }> => {
  if (!audioBlob) {
    return { success: false, error: 'Aucun audio à téléverser.' };
  }

  const filename = `voicemail_${new Date().toISOString().replace(/[:.]/g, '-')}.mp3`;

  const result = await GetAudioVoicemailSignedURL(filename);

  if ('failure' in result) {
    return { success: false, error: 'Échec du téléversement : ' + result.failure };
  }

  try {
    await fetch(result.success.uploadUrl, {
      method: 'PUT',
      body: audioBlob,
      headers: {
        'Content-Type': 'audio/mpeg',
      },
    });

    return { success: true, url: result.success.fileUrl };
  } catch (error: any) {
    return { success: false, error: 'Erreur lors du téléversement : ' + error.message };
  }
};

export const uploadHumanIntroductionAudio = async (
  audioBlob: Blob | null
): Promise<{ success: boolean; error?: string; url?: string }> => {
  if (!audioBlob) {
    return { success: false, error: 'Aucun audio à téléverser.' };
  }

  const filename = `human_introduction_${new Date().toISOString().replace(/[:.]/g, '-')}.mp3`;

  const result = await GetAudioHumanIntroductionSignedURL(filename);

  if ('failure' in result) {
    return { success: false, error: 'Échec du téléversement : ' + result.failure };
  }

  try {
    await fetch(result.success.uploadUrl, {
      method: 'PUT',
      body: audioBlob,
      headers: {
        'Content-Type': 'audio/mpeg',
      },
    });

    return { success: true, url: result.success.fileUrl };
  } catch (error: any) {
    return { success: false, error: 'Erreur lors du téléversement : ' + error.message };
  }
};

  
export async function getBusinessFilesPresignedUrl(key: string) {
  const command = new GetObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME!,
    Key: key,
    ResponseContentDisposition: "attachment",
  });

  return await getSignedUrl(BusinessFilesS3, command, { expiresIn: 3600 })
}
