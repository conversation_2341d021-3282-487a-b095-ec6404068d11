"use server";

import nodemailer from "nodemailer";
import { generateBalance<PERSON>lertEmailBody } from "@/lib/emailTemplates";

/*
 * Sends a balance alert email to the user when their account balance is at or below the set threshold.
 */
export async function sendBalanceAlertEmail(
  toEmail: string,
  userName: string,
  currentBalance: number,
  balanceThreshold: number,
  currencySymbol: string = "€",
  appUrl: string = "https://app.echoparrot.com/dashboard"
): Promise<void> {
  // Generate the HTML body using the email template function
  const htmlBody = generateBalanceAlertEmailBody(
    userName,
    currentBalance,
    balanceThreshold,
    currencySymbol,
    appUrl
  );

  const from = `"EchoParrot" <${
    process.env.SMTP_FROM || process.env.SMTP_USER
  }>`;
  const subject = "Balance Alert: Your EchoParrot Account";

  try {
    await sendEmail(from, toEmail, subject, htmlBody);
    console.log(`Balance alert email sent successfully to ${toEmail}`);
  } catch (error) {
    console.error(`Failed to send balance alert email to ${toEmail}:`, error);
    throw error;
  }
}

/**
 * Generic function to send an email using Nodemailer.
 */
export async function sendEmail(
  from: string,
  to: string,
  subject: string,
  html: string
): Promise<void> {
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: Number(process.env.SMTP_PORT) || 587,
    secure: true,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });

  try {
    await transporter.sendMail({
      from,
      to,
      subject,
      html,
    });
    console.log(`Email sent successfully to ${to}`);
  } catch (error) {
    console.error(`Failed to send email to ${to}:`, error);
    throw error;
  }
}
