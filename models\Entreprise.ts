import { attachBalanceAlertMiddlewares } from "@/lib/mongooseMiddlewares";
import mongoose from "mongoose";

const EntrepriseSchema = new mongoose.Schema({
  name: {
    type: String,
  },
  assistants: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Assistant",
    },
  ],
  admin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "ENTREPRISE_ADMIN",
  },
  agents: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ENTREPRISE_AGENT",
    },
  ],
  numbers: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Phone",
    },
  ],
  siret: {
    type: String,
    unique: true,
    sparse: true,
    default: undefined,
  },
  kbis: {
    type: String,
  },
  sepa: {
    type: String,
  },
  rib: {
    type: String,
  },
  phone: {
    type: String,
  },
  corpName: {
    type: String,
  },
  country: {
    type: String,
  },
  region: {
    type: String,
  },
  street: {
    type: String,
  },
  appartment: {
    type: String,
  },
  legalRepresentantName: {
    type: String,
  },
  legalRepresentantIdendity: {
    type: String,
  },
  cpa: {
    type: String,
  },
  field: {
    type: String,
  },
  credit: {
    type: Number,
  },
  balance: {
    type: Number,
    default: 0,
  },
  balanceAlertThreshold: {
    type: Number,
    default: 10,
  },
  representantIsOwner: {
    type: Boolean,
  },
  stripe_customer_id: {
    type: String,
    sparse: true,
  },
  payments: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Payment",
    },
  ],
  subscriptions: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subscription",
    },
  ],
  current_subscription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Subscription",
  },
  invoices: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Invoice",
    },
  ],
  documents: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Document",
    },
  ],
  clients: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Client",
    },
  ],
  tags: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Tag",
    },
  ],
  scripts: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "AssistantScript",
    },
  ],
  aws_credentials: {
    AWS_ACCESS_KEY_ID: { type: String },
    AWS_SECRET_ACCESS_KEY: { type: String },
    REGION: { type: String },
    BUCKET_NAME: { type: String },
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
});

attachBalanceAlertMiddlewares(EntrepriseSchema);

const Entreprise =
  mongoose.models.Entreprise || mongoose.model("Entreprise", EntrepriseSchema);

export default Entreprise;
