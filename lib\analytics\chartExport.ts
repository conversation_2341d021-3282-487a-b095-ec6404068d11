import { toPng, toSvg } from "html-to-image";
import html2pdf from "html2pdf.js";

/**
 * Exports a DOM element as an image (PNG or SVG)
 */
export const exportChartAsImage = async (
  element: HTMLElement | null,
  type: "png" | "svg",
  filename: string
) => {
  if (!element) return;
  try {
    const dataUrl =
      type === "png"
        ? await toPng(element, { cacheBust: true })
        : await toSvg(element, { cacheBust: true });
    const link = document.createElement("a");
    link.download = `${filename}.${type}`;
    link.href = dataUrl;
    link.click();
  } catch (err) {
    console.error(`Export ${type.toUpperCase()} failed:`, err);
  }
};

/**
 * Exports a DOM element as a PDF using html2pdf
 */
export const exportChartAsPDF = async (
  element: HTMLElement | null,
  filename: string
) => {
  if (!element) return;
  try {
    const dataUrl = await toPng(element, {
      cacheBust: true,
      pixelRatio: 2, // for high resolution
    });

    const img = document.createElement("img");
    img.src = dataUrl;

    const pdfWidth = element.offsetWidth;
    const pdfHeight = element.offsetHeight;

    // Set image style to match element dimensions. This helps html2pdf to correctly scale it.
    img.style.width = `${pdfWidth}px`;
    img.style.height = `${pdfHeight}px`;

    // jsPDF works best with 'pt' units. 1px = 0.75pt
    const pdfWidthPt = pdfWidth * 0.75;
    const pdfHeightPt = pdfHeight * 0.75;

    html2pdf()
      .set({
        margin: 0,
        filename: `${filename}.pdf`,
        image: { type: "jpeg", quality: 0.98 }, // Use high-quality JPEG to balance size and quality
        html2canvas: { scale: 2 }, // Render the styled image at 2x resolution to preserve detail
        jsPDF: {
          unit: "pt",
          format: [pdfWidthPt, pdfHeightPt],
          orientation: pdfWidthPt > pdfHeightPt ? "landscape" : "portrait",
        },
      })
      .from(img)
      .save()
      .catch((err: any) => console.error("Export PDF failed:", err));
  } catch (err) {
    console.error("Export PDF failed:", err);
  }
};
