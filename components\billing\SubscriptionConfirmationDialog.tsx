"use client";

import {
  calculateTax<PERSON>or<PERSON>mount,
  getSavedCards,
  type SavedCard,
} from "@/actions/BillingActions";
import CustomButton from "@/components/CustomFormItems/Button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  featurePeriodMap,
  formatBillingPeriod,
  getPaymentMethodDisplayName,
  getPaymentMethodIconPath,
} from "@/lib/billingUtils";
import { renderTemplate } from "@/lib/renderTemplate";
import { Plan } from "@/types";
import { ArrowLeft, Calendar, Check, Crown } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";

interface SubscriptionConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCloseComplete?: () => void; // Called after dialog close animation completes
  onSubscribeWithCard: (cardId: string) => void;
  selectedPlan: Plan | null;
  billingPeriod: string;
  isProcessingSubscription: boolean;
}

export default function SubscriptionConfirmationDialog({
  isOpen,
  onClose,
  onCloseComplete,
  onSubscribeWithCard,
  selectedPlan,
  billingPeriod,
  isProcessingSubscription,
}: SubscriptionConfirmationDialogProps) {
  const [savedCards, setSavedCards] = useState<SavedCard[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<string>(""); // card ID only
  const [loading, setLoading] = useState(false);
  const [showPaymentMethods, setShowPaymentMethods] = useState(false);
  const [taxInfo, setTaxInfo] = useState<{
    tax: number;
    total: number;
    currency: string;
  } | null>(null);
  const [taxLoading, setTaxLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadSavedCards();
      setShowPaymentMethods(false); // Reset to initial view when dialog opens
    }
  }, [isOpen]);

  const loadSavedCards = async () => {
    try {
      setLoading(true);
      const response = await getSavedCards();
      if (response.success && response.data) {
        setSavedCards(response.data.cards);
        // Set default payment method if available
        const defaultCard = response.data.cards.find((card) => card.is_default);
        if (defaultCard) {
          setSelectedPaymentMethod(defaultCard.id);
        } else if (response.data.cards.length > 0) {
          setSelectedPaymentMethod(response.data.cards[0].id);
        } else {
          setSelectedPaymentMethod("");
        }
      }
    } catch (error) {
      console.error("Error loading saved cards:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscription = () => {
    if (selectedPaymentMethod) {
      onSubscribeWithCard(selectedPaymentMethod);
    }
  };

  const handleInitialSubscribeClick = () => {
    setShowPaymentMethods(true);
  };

  const handleBackToSummary = () => {
    setShowPaymentMethods(false);
  };

  const getCardIcon = (brand: string) => {
    const iconPath = getPaymentMethodIconPath("card", brand);
    const altText = getPaymentMethodDisplayName("card", brand);

    return (
      <Image
        src={iconPath}
        alt={altText}
        width={24}
        height={16}
        className="border border-gray-200 dark:border-gray-700 rounded"
      />
    );
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
      // Call onCloseComplete after dialog animation completes
      if (onCloseComplete) {
        setTimeout(() => {
          onCloseComplete();
        }, 300); // Match the dialog animation duration
      }
    }
  };

  const formatPrice = (price: number, currency: string) => {
    const symbol = currency === "eur" ? "€" : "$";
    return `${symbol}${price}`;
  };

  // Get the billing option for the selected period
  const getBillingOption = () => {
    if (!selectedPlan?.billing_options) return null;
    return selectedPlan.billing_options[
      billingPeriod as keyof typeof selectedPlan.billing_options
    ];
  };

  // Fetch tax info when dialog opens or billingOption changes
  useEffect(() => {
    const billingOption = getBillingOption();
    if (isOpen && billingOption?.current_price) {
      fetchTax(billingOption.current_price);
    } else {
      setTaxInfo(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, selectedPlan, billingPeriod]);

  const fetchTax = async (amount: number) => {
    setTaxLoading(true);
    try {
      const response = await calculateTaxForAmount(amount);
      if (response.success && response.data) {
        setTaxInfo({
          tax: response.data.tax,
          total: response.data.total,
          currency: response.data.currency,
        });
      } else {
        setTaxInfo(null);
      }
    } catch {
      setTaxInfo(null);
    } finally {
      setTaxLoading(false);
    }
  };

  if (!selectedPlan) return null;

  const billingOption = getBillingOption();
  if (!billingOption) return null;

  const hasDiscount =
    billingOption.original_price > billingOption.current_price;

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-2xl border-0 shadow-2xl max-h-screen sm:max-h-[calc(100vh-2rem)] h-full">
        <div className="relative flex flex-col">
          {/* Header with gradient background */}
          <div className="bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 -m-6 mb-0 rounded-t-lg p-6 text-white">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <Crown className="w-5 h-5" />
                </div>
                Subscribe to {selectedPlan.name}
              </DialogTitle>
              <DialogDescription className="text-white/90 mt-2">
                Review your subscription details below
              </DialogDescription>
            </DialogHeader>
          </div>

          {/* Content */}
          <div className="grow h-0 sm:px-6 py-6 space-y-6 overflow-auto">
            {!showPaymentMethods ? (
              <>
                {/* Plan showcase */}
                <div className="text-center py-4">
                  <div className="flex items-center justify-center gap-3 mb-3">
                    <div className="text-4xl font-bold text-voxa-teal-600">
                      {formatPrice(
                        billingOption.current_price,
                        selectedPlan.currency!
                      )}
                    </div>
                    <span className="text-lg text-muted-foreground">
                      {formatBillingPeriod(billingPeriod)}
                    </span>
                  </div>
                  {hasDiscount && (
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <span className="text-lg text-muted-foreground line-through">
                        {formatPrice(
                          billingOption.original_price,
                          selectedPlan.currency!
                        )}
                      </span>
                      <Badge
                        variant="outline"
                        className="bg-green-50 text-green-700 dark:bg-green-950 dark:text-green-300"
                      >
                        Save{" "}
                        {formatPrice(
                          billingOption.original_price -
                            billingOption.current_price,
                          selectedPlan.currency!
                        )}
                      </Badge>
                    </div>
                  )}
                  <p className="text-muted-foreground">
                    {selectedPlan.description}
                  </p>
                </div>

                {/* Charge breakdown box */}
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-xl p-4 space-y-4 mb-4">
                  <div className="flex items-center gap-3 pb-3 border-b">
                    <Calendar className="w-5 h-5 text-voxa-teal-600" />
                    <div>
                      <h4 className="font-medium">Billing Information</h4>
                      <p className="text-sm text-muted-foreground">
                        You{`'`}ll be charged{" "}
                        {formatPrice(
                          taxInfo?.total ?? billingOption.current_price,
                          selectedPlan.currency!
                        )}{" "}
                        every {billingPeriod.replace("ly", "")}
                      </p>
                    </div>
                  </div>
                  {/* Tax breakdown */}
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-muted-foreground">
                        Subscription
                      </span>
                      <span className="font-semibold text-voxa-teal-600">
                        {formatPrice(
                          billingOption.current_price,
                          selectedPlan.currency!
                        )}
                      </span>
                    </div>
                    {taxLoading ? (
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-muted-foreground">
                          Tax
                        </span>
                        <span className="text-xs text-muted-foreground">
                          Calculating...
                        </span>
                      </div>
                    ) : taxInfo && taxInfo.tax > 0 ? (
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-muted-foreground">
                          Tax
                        </span>
                        <span className="font-semibold">
                          +{taxInfo.tax.toFixed(2)}€
                        </span>
                      </div>
                    ) : null}
                    {taxInfo && (
                      <div className="flex justify-between items-center mt-2 border-t pt-3">
                        <span className="text-sm font-medium text-muted-foreground">
                          Total to pay
                        </span>
                        <span className="font-semibold text-voxa-teal-700">
                          {taxInfo.total.toFixed(2)}€
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Features box */}
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-xl p-4 space-y-4">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Check className="w-4 h-4 text-voxa-teal-600" />
                    Included Features (
                    {selectedPlan.features?.filter((f) => f.included).length})
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {selectedPlan.features
                      ?.filter((feature) => feature.included)
                      .map((feature, index) => {
                        // Prepare variables for template rendering
                        const variables = {
                          value: feature.value || "",
                          months: featurePeriodMap[billingPeriod]?.months ?? "",
                          days: featurePeriodMap[billingPeriod]?.days ?? "",
                        };
                        return (
                          <div
                            key={index}
                            className="flex items-center gap-2 text-sm"
                          >
                            <Check className="w-3 h-3 text-voxa-teal-600 flex-shrink-0" />
                            <span>
                              {renderTemplate(feature.description, variables)}
                            </span>
                          </div>
                        );
                      })}
                  </div>
                </div>
              </>
            ) : (
              <div className="flex flex-col h-full">
                {/* Fixed header section */}
                <div className="flex-shrink-0 sm:px-6">
                  <div className="flex items-center gap-3 mb-4">
                    <button
                      onClick={handleBackToSummary}
                      className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      disabled={isProcessingSubscription}
                    >
                      <ArrowLeft className="w-4 h-4" />
                    </button>
                    <h4 className="font-medium text-lg">
                      Select Payment Method
                    </h4>
                  </div>
                </div>

                {/* Scrollable payment methods section */}
                <div className="flex-1 overflow-auto sm:px-6">
                  {loading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-voxa-teal-600"></div>
                    </div>
                  ) : (
                    <div className="space-y-3 pb-4">
                      {/* Saved Cards */}
                      {savedCards.length === 0 ? (
                        <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                          <Calendar className="w-10 h-10 mx-auto mb-4 text-voxa-neutral-300 dark:text-voxa-neutral-600" />
                          <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 mb-2">
                            No saved cards yet
                          </p>
                          <p className="text-voxa-neutral-400 dark:text-voxa-neutral-500 text-sm">
                            Add a card to subscribe
                          </p>
                        </div>
                      ) : (
                        savedCards.map((card) => (
                          <div
                            key={card.id}
                            className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                              selectedPaymentMethod === card.id
                                ? "border-voxa-teal-600 bg-voxa-teal-50 dark:bg-voxa-teal-950"
                                : "border-gray-200 dark:border-gray-700 hover:border-voxa-teal-300"
                            }`}
                            onClick={() => setSelectedPaymentMethod(card.id)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center">
                                  {getCardIcon(card.brand)}
                                </div>
                                <div>
                                  <div className="font-medium">
                                    {card.brand.toUpperCase()} •••• {card.last4}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    Expires {card.exp_month}/{card.exp_year}
                                  </div>
                                </div>
                                {card.is_default && (
                                  <Badge variant="outline" className="text-xs">
                                    Default
                                  </Badge>
                                )}
                              </div>
                              {selectedPaymentMethod === card.id && (
                                <Check className="w-5 h-5 text-voxa-teal-600" />
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-2">
            <CustomButton
              props={{
                value: "Cancel",
                className: `flex-1 h-11 border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:!bg-gray-100 dark:hover:!bg-gray-700 transition-all duration-200 ${
                  isProcessingSubscription
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`,
                onClick: isProcessingSubscription
                  ? undefined
                  : () => handleOpenChange(false),
              }}
            />
            <CustomButton
              props={{
                value: isProcessingSubscription
                  ? "Creating Subscription..."
                  : !showPaymentMethods
                  ? `Subscribe for ${
                      taxInfo?.total?.toFixed(2) ?? billingOption.current_price
                    }€`
                  : `Subscribe for ${
                      taxInfo?.total?.toFixed(2) ?? billingOption.current_price
                    }€ with Card`,
                className: `flex-1 h-11 bg-gradient-to-r from-voxa-teal-600 to-voxa-teal-700 hover:from-voxa-teal-700 hover:to-voxa-teal-800 text-white font-semibold transition-all duration-200 shadow-lg hover:shadow-xl ${
                  isProcessingSubscription ||
                  (showPaymentMethods && !selectedPaymentMethod)
                    ? "opacity-75 cursor-not-allowed"
                    : ""
                }`,
                onClick: isProcessingSubscription
                  ? undefined
                  : !showPaymentMethods
                  ? handleInitialSubscribeClick
                  : handleSubscription,
              }}
            />
          </div>

          {/* Security note */}
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              🔒 Secure payment powered by Stripe • Cancel anytime
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
