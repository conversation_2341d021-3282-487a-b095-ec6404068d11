"use client";

import { getSubscriptionsByPlan } from "@/actions/BillingActions";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  RefreshCw,
  Calendar,
  CheckCircle,
  AlertCircle,
  Copy,
} from "lucide-react";
import useSWR from "swr";
import { useState } from "react";
import { formatDistanceToNow } from "date-fns";
import { Subscription, Plan, Entreprise, User } from "@/types";
import { toast } from "sonner";
import {
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";

interface PlanSubscribersDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  plan: Plan;
}

const PAGE_LIMIT = 20;

const fetchSubscribers = async (planId: string, page: number) => {
  const res = await getSubscriptionsByPlan(planId, PAGE_LIMIT, page);
  if (!res.success) throw new Error(res.error || "Failed to fetch invoices");
  return res.data;
};

export default function PlanSubscribersDialog({
  open,
  onOpenChange,
  plan,
}: PlanSubscribersDialogProps) {
  const [page, setPage] = useState(1);

  const { data, error, isLoading, isValidating, mutate } = useSWR(
    open ? ["plan-subscribers", plan._id, page] : null,
    () => fetchSubscribers(plan._id, page),
    { keepPreviousData: true }
  );

  const subscriptions: Subscription[] = data?.subscriptions || [];
  const total = data?.total || 0;
  const totalPages = data?.totalPages || 1;

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: {
        variant: "default" as const,
        label: "Active",
        icon: CheckCircle,
      },
      trialing: {
        variant: "outline" as const,
        label: "Trialing",
        icon: Calendar,
      },
      past_due: {
        variant: "destructive" as const,
        label: "Past Due",
        icon: AlertCircle,
      },
      canceled: {
        variant: "secondary" as const,
        label: "Canceled",
        icon: AlertCircle,
      },
      incomplete: {
        variant: "secondary" as const,
        label: "Incomplete",
        icon: AlertCircle,
      },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
      icon: AlertCircle,
    };
    const Icon = config.icon;
    // Add custom dark:bg-muted/60 for dark mode background
    return (
      <Badge
        variant={config.variant}
        className="flex items-center gap-1 dark:bg-muted/60 dark:text-white"
      >
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  // Copy entreprise id to clipboard and show toast
  const handleCopyEntrepriseId = (id: string) => {
    navigator.clipboard.writeText(id);
    toast.success("Entreprise ID copied to clipboard");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[600px] max-h-[95vh] flex flex-col"
        // Make DialogContent a flex column container
      >
        <DialogHeader>
          <DialogTitle>
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Subscribers for <span className="font-semibold">{plan.name}</span>
              <Badge variant="outline" className="ml-2">
                {total} total
              </Badge>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="mt-2 flex-1 flex flex-col min-h-0">
          {isLoading ? (
            <div className="flex items-center justify-center gap-2 py-8 text-muted-foreground">
              <RefreshCw className="w-4 h-4 animate-spin" />
              Loading subscribers...
            </div>
          ) : error ? (
            <div className="text-center py-8 text-muted-foreground">
              <AlertCircle className="w-8 h-8 mx-auto mb-2" />
              <div>Failed to load subscribers</div>
              <div className="text-xs">{error.message}</div>
            </div>
          ) : subscriptions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="w-8 h-8 mx-auto mb-2" />
              <div>No subscribers found for this plan.</div>
            </div>
          ) : (
            <div className="overflow-x-auto flex-1 min-h-0">
              <div className="max-h-[calc(95vh-200px)] overflow-y-auto min-h-0">
                <table className="w-full min-w-[400px]">
                  <thead>
                    <tr className="bg-muted">
                      <th className="p-2 text-left text-xs font-medium">
                        Status
                      </th>
                      <th className="p-2 text-left text-xs font-medium">
                        Entreprise
                      </th>
                      <th className="p-2 text-left text-xs font-medium">
                        Billing Period
                      </th>
                      <th className="p-2 text-left text-xs font-medium">
                        Start Date
                      </th>
                      <th className="p-2 text-left text-xs font-medium">
                        End Date
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {subscriptions.map((sub) => (
                      <tr
                        key={sub._id}
                        className="border-t border-muted-foreground/10 hover:bg-muted/40"
                      >
                        <td className="p-2">{getStatusBadge(sub.status)}</td>
                        <td className="p-2 text-xs">
                          {sub.entreprise_id &&
                            typeof sub.entreprise_id === "object" && (
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <button
                                      className="flex items-center gap-1 hover:underline"
                                      onClick={() =>
                                        handleCopyEntrepriseId(
                                          (sub.entreprise_id as Entreprise)._id
                                        )
                                      }
                                    >
                                      <div className="flex flex-col items-start">
                                        <span>
                                          {(sub.entreprise_id as Entreprise)
                                            .name || "—"}
                                        </span>
                                        <span className="text-[10px] text-muted-foreground">
                                          {
                                            (
                                              (sub.entreprise_id as Entreprise)
                                                .admin as User
                                            )?.email
                                          }
                                        </span>
                                      </div>
                                      <Copy className="w-3 h-3 ml-1" />
                                    </button>
                                  </TooltipTrigger>
                                  <TooltipContent side="top">
                                    Entreprise ID:{" "}
                                    {(sub.entreprise_id as Entreprise)._id}
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            )}
                          {(!sub.entreprise_id ||
                            typeof sub.entreprise_id !== "object") &&
                            "—"}
                        </td>
                        <td className="p-2 text-xs capitalize">
                          {sub.billing_period || "—"}
                        </td>
                        <td className="p-2 text-xs">
                          {sub.current_period_start
                            ? new Date(
                                sub.current_period_start
                              ).toLocaleDateString("fr-FR")
                            : "—"}
                          <div className="text-muted-foreground text-[11px]">
                            {sub.current_period_start
                              ? new Date(
                                  sub.current_period_start
                                ).toLocaleTimeString("fr-FR")
                              : ""}
                          </div>
                        </td>
                        <td className="p-2 text-xs">
                          {sub.current_period_end
                            ? new Date(
                                sub.current_period_end
                              ).toLocaleDateString("fr-FR")
                            : "—"}
                          <div className="text-muted-foreground text-[11px]">
                            {sub.current_period_end
                              ? new Date(
                                  sub.current_period_end
                                ).toLocaleTimeString("fr-FR")
                              : ""}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={page === 1}
                    onClick={() => setPage((p) => Math.max(1, p - 1))}
                  >
                    Previous
                  </Button>
                  <span className="text-xs">
                    Page {page} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={page === totalPages}
                    onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
