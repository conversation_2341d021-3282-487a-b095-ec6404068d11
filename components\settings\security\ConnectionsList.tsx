"use client";

import {
  EndConnection,
  GetCurrentUserConnections,
} from "@/actions/ConnectionActions";
import MainLoader from "@/components/Loaders/MainLoader";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { socket } from "@/lib/socket";
import { useSocketContext } from "@/providers/SocketProvider";
import { Connection } from "@/types";
import { History } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import ConnectionCard from "./ConnectionCard";

export default function ConnectionsList() {
  const [connections, setConnections] = useState<Connection[]>([]);
  const [loading, setLoading] = useState(true);
  const { token, isConnected } = useSocketContext();

  // Fetch online status and lastSeen for each connection
  const fetchOnlineUsers = async () => {
    if (!token) return;

    try {
      const expressServerUrl =
        process.env.NEXT_PUBLIC_EXPRESS_SERVER_URL || "http://localhost:5000";
      const response = await fetch(
        `${expressServerUrl}/api/connections/online`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.connections)) {
          setConnections((prevConnections) =>
            prevConnections.map((conn) => {
              const found = data.connections.find(
                (c: any) => c._id === conn._id
              );
              if (found) {
                return {
                  ...conn,
                  ...(found.online !== undefined
                    ? { online: found.online }
                    : {}),
                  ...(found.lastSeen !== undefined
                    ? { lastSeen: found.lastSeen }
                    : {}),
                };
              }
              return conn;
            })
          );
        }
      } else {
      }
    } catch {}
  };

  const fetchConnections = async () => {
    try {
      setLoading(true);
      const response = await GetCurrentUserConnections();
      if (response.success && response.data) {
        setConnections((prevConnections) => {
          if (!response.data) return prevConnections;
          return response.data.map((conn: Connection) => {
            const prev = prevConnections.find((c) => c._id === conn._id);
            return {
              ...conn,
              ...(prev?.online !== undefined ? { online: prev?.online } : {}),
              ...(prev?.lastSeen !== undefined
                ? { lastSeen: prev?.lastSeen }
                : {}),
            };
          });
        });
      } else {
        toast.error(response.error || "Failed to fetch connections.");
      }
    } catch (error: any) {
      toast.error(error.message || "An unexpected error occurred.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConnections();
    if (token) {
      fetchOnlineUsers();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token]);

  // Listen for connectionStatusChanged event when socket is connected
  useEffect(() => {
    if (!isConnected || !socket) return;
    const handler = (payload: {
      sessionId: string;
      online: boolean;
      lastSeen: string;
    }) => {
      setConnections((prevConnections) =>
        prevConnections.map((conn) => {
          return conn.sessionId === payload.sessionId
            ? {
                ...conn,
                online: payload.online,
                lastSeen: new Date(payload.lastSeen),
              }
            : conn;
        })
      );
    };
    socket.on("connectionStatusChanged", handler);
    return () => {
      socket.off("connectionStatusChanged", handler);
    };
  }, [isConnected]);

  const handleRevoke = async (connectionId: string) => {
    const originalConnections = [...connections];
    // Optimistically update UI
    setConnections(
      connections.map((c) =>
        c._id === connectionId ? { ...c, active: false, online: false } : c
      )
    );

    const response = await EndConnection(connectionId);

    if (response.success) {
      toast.success("Session has been revoked.");
      await fetchConnections(); // Re-fetch to get the latest state
      if (token) {
        await fetchOnlineUsers(); // Re-fetch online users
      }
    } else {
      toast.error(response.error || "Failed to revoke session.");
      // Revert optimistic update on failure
      setConnections(originalConnections);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <MainLoader />
      </div>
    );
  }

  const activeConnections = connections.filter((c) => c.active);
  const activeConnectionsWithOnlineStatus = activeConnections;
  const connectionsWithOnlineStatus = connections;

  if (connections.length === 0) {
    return (
      <div className="text-center p-4 border border-dashed rounded-lg">
        <p>No connections found.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {activeConnectionsWithOnlineStatus.length > 0 ? (
        activeConnectionsWithOnlineStatus.map((conn) => (
          <ConnectionCard key={conn._id} conn={conn} onRevoke={handleRevoke} />
        ))
      ) : (
        <div className="text-center p-4 border border-dashed rounded-lg">
          <p>No active sessions found.</p>
        </div>
      )}

      {connections.length > 0 && (
        <div className="w-full mt-6">
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem
              value="login-history"
              className="!border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg overflow-hidden"
            >
              <AccordionTrigger className="py-3 px-4 font-medium text-base bg-voxa-neutral-50 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-100 dark:hover:bg-voxa-neutral-700 hover:no-underline transition-colors flex items-center">
                <div className="flex items-center gap-2 text-zinc-600 dark:text-voxa-neutral-100">
                  <History className="w-5 h-5" />
                  <span>Login History</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4 bg-white dark:bg-voxa-neutral-900">
                <div className="space-y-4">
                  {connectionsWithOnlineStatus.map((conn) => (
                    <ConnectionCard
                      key={conn._id}
                      conn={conn}
                      onRevoke={handleRevoke}
                    />
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      )}
    </div>
  );
}
