"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { signIn } from "next-auth/react";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";

const Login: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [pending, setPending] = useState(false);
  const router = useRouter();
  const { t } = useTranslation(["login", "common"]);

  const formSubmitted = async (event: React.FormEvent) => {
    event.preventDefault();
    setPending(true);
    setErrorMessage(null);

    try {
      const res = await signIn("credentials", {
        redirect: false,
        email,
        password,
      });
      if (res?.error) {
        console.log("Login error:", res.error);
        setErrorMessage(res.error);
        setPending(false);
      } else {
        const updatedSession = await fetch("/api/auth/session");
        const sessionData = await updatedSession.json();
        setPending(false);
        if (sessionData?.user?.role === "ADMIN") {
          router.push("/admin-dash");
        } else if (sessionData?.user?.role === "ENTREPRISE_AGENT") {
          router.push("/agent-dash");
        } else if (sessionData?.user?.role === "ENTREPRISE_ADMIN") {
          router.push("/businessDash");
        } else {
          router.push("/");
        }
      }
    } catch (error) {
      console.error("Login error:", error);
      setErrorMessage("An error occurred during login");
      setPending(false);
    }
  };

  return (
    <div className="w-full grid min-h-[100vh] xl:min-h-[100vh] px-6">
      <div className="flex items-center justify-center py-12 bg-secondary-600">
        <div className="mx-auto grid max-w-sm mt-20 px-2 w-full">
          <div className="grid gap-2 text-center">
            <h1 className="text-3xl font-bold">Echo Parrot</h1>
            <p className="text-muted-foreground">{t("login:subtitle")}</p>
          </div>
          <form onSubmit={formSubmitted} className="grid gap-3 mt-8">
            <div className="grid gap-2">
              <Label htmlFor="email">{t("login:email")}</Label>
              <Input
                id="email"
                type="email"
                required
                className="border-secondary-900"
                name="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">{t("login:password")}</Label>
              <Input
                id="password"
                type="password"
                name="password"
                required
                className="border-secondary-900"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <LoginButton pending={pending} />
            {errorMessage && (
              <p className="text-sm text-red-500">{errorMessage}</p>
            )}
          </form>
          <div className="mt-8 text-center text-sm">
            {t("login:haveAccount")}
            <Link href="/auth/signup" className="underline">
              {t("login:createAccount")}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

function LoginButton({ pending }: { pending: boolean }) {
  const { t } = useTranslation("login");
  return (
    <Button className="mt-4 w-full" aria-disabled={pending}>
      <p>{pending ? t("logging") : t("login")}</p>
      <ChevronRightIcon className="h-6 w-6 rtl:rotate-180" fontSize="large" />
    </Button>
  );
}

function LoginCognitoButton() {
  const { t } = useTranslation("login");
  return (
    <Button
      variant="outline"
      className="mt-4 w-full"
      onClick={() => signIn("cognito")}
    >
      <p>{t("login")}</p>
      <ChevronRightIcon className="h-6 w-6 rtl:rotate-180" fontSize="large" />
    </Button>
  );
}

export default Login;
