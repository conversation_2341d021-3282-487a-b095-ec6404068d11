import mongoose from 'mongoose';

enum AssistantStatus {
  ACTIVE  = "ACTIVE",
  PENDING = "PENDING",
  INACTIVE = "INACTIVE"
}

const AssistantSchema = new mongoose.Schema({
  name: {
    type: String,
  },
  goals: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Goal',
    },
  ],
  conversations: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Conversation',
    },
  ],
  status: {
    type: String,
    enum: AssistantStatus,
    default: AssistantStatus.PENDING,
  },
  numbers: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Phone',
    }
  ],
  created_at: {
    type: Date,
    default: Date.now,
  }
})

export default mongoose.models.Assistant || mongoose.model('Assistant', AssistantSchema);