"use client";

import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import Backend from "i18next-http-backend";
import LanguageDetector from "i18next-browser-languagedetector";

i18n
  .use(LanguageDetector)
  .use(Backend)
  .use(initReactI18next)
  .init({
    fallbackLng: "en",
    debug: false,
    defaultNS: "common",
    ns: [
      "common",
      "home",
      "toast",
      "errors",
      "login",
      "signup",
      "integrations",
      "voices",
      "callLogs",
      "clients",
      "assistants",
      "scripts",
      "analytics",
      "settings",
      "rag",
    ],
    interpolation: {
      escapeValue: false,
    },
    load: "languageOnly",
    react: {
      useSuspense: false,
    },
    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },
    detection: {
      order: ["localStorage", "navigator"],
      caches: ["localStorage"],
    },
    lookupLocalStorage: "language",
    lookupNavigator: true,
  });

export default i18n;
