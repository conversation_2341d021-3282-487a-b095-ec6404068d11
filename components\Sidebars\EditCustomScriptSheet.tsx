import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import CustomInput from "../CustomFormItems/Input";
import { useState, useEffect, useMemo } from "react";
import { CheckIcon, PlusIcon, XIcon } from "lucide-react";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { toast } from "sonner";
import CustomTextarea from "../CustomFormItems/CustomTextArea";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Tag } from "../Dashboard/Cards/ScriptCard2";

export function EditCustomScriptSheet({
  open,
  setOpen,
  script,
  onSave,
  loading,
  setLoading,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  script: any;
  onSave: (updated: { name: string; content: string; tags: any[] }) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
}) {
  const tagColors = useMemo(
    () => [
      "#10B981",
      "#3B82F6",
      "#F59E0B",
      "#EF4444",
      "#8B5CF6",
      "#06B6D4",
      "#84CC16",
      "#F97316",
      "#EC4899",
      "#6B7280",
    ],
    []
  );

  const [scriptData, setScriptData] = useState({
    name: script?.name || "",
    content: script?.content || "",
    tags: script?.tags?.length ? script.tags : [],
  });

  useEffect(() => {
    setScriptData({
      name: script?.name || "",
      content: script?.content || "",
      tags: script?.tags?.length ? script.tags : [],
    });
  }, [script, tagColors]);

  const handleAddTag = () => {
    if (scriptData.tags.length >= 5) return;
    const usedColors = scriptData.tags.map((tag: Tag) => tag.color);
    const availableColor = tagColors.find(
      (color) => !usedColors.includes(color)
    );
    if (!availableColor) return;
    setScriptData({
      ...scriptData,
      tags: [...scriptData.tags, { name: "", color: availableColor }],
    });
  };

  const handleTagNameChange = (idx: number, value: string) => {
    setScriptData({
      ...scriptData,
      tags: scriptData.tags.map((tag: Tag, i: number) =>
        i === idx ? { ...tag, name: value } : tag
      ),
    });
  };

  const handleTagColorChange = (idx: number, color: string) => {
    setScriptData({
      ...scriptData,
      tags: scriptData.tags.map((tag: Tag, i: number) =>
        i === idx ? { ...tag, color } : tag
      ),
    });
  };

  const handleRemoveTag = (idx: number) => {
    setScriptData({
      ...scriptData,
      tags: scriptData.tags.filter((_: any, i: number) => i !== idx),
    });
  };

  const handleSave = () => {
    if (!scriptData.name.trim()) {
      toast.error("Script name is required");
      return;
    }
    if (!scriptData.content.trim()) {
      toast.error("Script content is required");
      return;
    }
    setLoading(true);
    onSave({
      name: scriptData.name,
      content: scriptData.content,
      tags: scriptData.tags,
    });
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetContent className="!h-screen w-full sm:max-w-[820px] flex flex-col p-4">
        <SheetHeader>
          <SheetTitle>Edit Script</SheetTitle>
          <SheetDescription>Edit the script details below.</SheetDescription>
        </SheetHeader>
        <div className="px-2">
          <CustomInput
            props={{
              type: "text",
              label: "Script Name",
              placeholder: "Enter script name",
              className: "w-full py-5 mb-3",
              required: true,
              onChange: (e) =>
                setScriptData({ ...scriptData, name: e.target.value }),
              name: "script_name",
              value: scriptData.name,
            }}
          />
          <div className="grid gap-4 py-3 md:grid-cols-2">
            <div className="flex items-center justify-between">
              <Label>Tags</Label>
              <Tooltip>
                <TooltipTrigger asChild className="h-full">
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="h-10 w-10"
                    onClick={handleAddTag}
                    aria-label="Add tag"
                    disabled={
                      scriptData.tags.length >= 5 ||
                      scriptData.tags.length >= tagColors.length
                    }
                  >
                    <PlusIcon className="w-5 h-5" />
                  </Button>
                </TooltipTrigger>
                {scriptData.tags.length >= 5 ||
                scriptData.tags.length >= tagColors.length ? (
                  <TooltipContent>Max 5 tags</TooltipContent>
                ) : null}
              </Tooltip>
            </div>
            {scriptData.tags.map((tag: Tag, idx: number) => (
              <div className="flex w-full items-center" key={idx}>
                <Input
                  placeholder="Tag name"
                  className="rounded-r-none"
                  value={tag.name}
                  onChange={(e) => handleTagNameChange(idx, e.target.value)}
                />
                <Tooltip>
                  <TooltipTrigger asChild className="border-r-none">
                    <button
                      type="button"
                      className="h-full inline-flex items-center px-2 bg-white dark:bg-sidebar border border-l-0 border-gray-300 dark:border-sidebar-border rounded-none border-r-none focus:outline-none"
                      tabIndex={0}
                      aria-label="Choose tag color"
                    >
                      <span
                        className="w-5 h-5 rounded-full border-2 border-gray-300 flex items-center justify-center"
                        style={{ background: tag.color }}
                      >
                        <CheckIcon className="w-3 h-3 text-white" />
                      </span>
                    </button>
                  </TooltipTrigger>
                  <TooltipContent
                    sideOffset={4}
                    className="flex gap-1 p-2 bg-white dark:bg-sidebar"
                  >
                    {tagColors.map((color) => {
                      const isUsed = scriptData.tags.some(
                        (t: Tag, i: number) => t.color === color && i !== idx
                      );
                      return (
                        <button
                          key={color}
                          type="button"
                          className={`z-50 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors duration-150 ${
                            tag.color === color
                              ? "border-voxa-teal-600"
                              : "border-gray-300"
                          } ${isUsed ? "opacity-40 cursor-not-allowed" : ""}`}
                          style={{ background: color }}
                          onClick={() =>
                            !isUsed && handleTagColorChange(idx, color)
                          }
                          aria-label={`Choose color ${color}`}
                          disabled={isUsed}
                        >
                          {tag.color === color && (
                            <CheckIcon className="w-3 h-3 text-white" />
                          )}
                        </button>
                      );
                    })}
                  </TooltipContent>
                </Tooltip>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="h-full w-12 rounded-l-none"
                  onClick={() => handleRemoveTag(idx)}
                  aria-label="Remove tag"
                >
                  <XIcon className="w-5 h-5 text-red-500" />
                </Button>
              </div>
            ))}
          </div>
          <CustomTextarea
            props={{
              name: "script_content",
              label: "Script Content",
              placeholder: "Enter script content...",
              onChange: (e) =>
                setScriptData({ ...scriptData, content: e.target.value }),
              value: scriptData.content,
              rows: 12,
              required: true,
              className: "min-h-[420px] mt-1.5",
            }}
          />
        </div>
        <SheetFooter className="px-2">
          <Button
            className={`flex w-full gap-1 text-sm justify-center items-center transition-all duration-150 text-white px-4 py-2 rounded-md bg-voxa-teal-600 hover:bg-voxa-teal-500  ${
              loading ? "opacity-50 cursor-not-allowed" : ""
            }`}
            disabled={loading}
            onClick={handleSave}
          >
            {loading ? "Saving..." : "Save Changes"}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
