"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
// import CloseRoundedIcon from "@mui/icons-material/CloseRounded";
import { Label } from "@radix-ui/react-label";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import ButtonLoader from "../Loaders/ButtonLoader";
import {
  setAddGroupToGoal,
  setSelectedGroupID,
  setSearchTerm,
  fetchGroups,
  addGroupToGoal,
} from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { useDispatch, useSelector } from "react-redux";
import CustomSelect from "../CustomFormItems/Select";
import { useTranslation } from "react-i18next";

export default function AddGroupToGoal() {
  const { t } = useTranslation("assistants");
  const { GoalID, groups, selectedGroupID, searchTerm } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardAssistants
  );
  const dispatch = useDispatch<AppDispatch>();

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const delayDebounceFn = setTimeout(async () => {
      await dispatch(fetchGroups({ searchTerm }));
    }, 500);
    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm, dispatch]);

  const handleAddGroup = async () => {
    if (!selectedGroupID) {
      toast.error(t("group.groupRequired"));
      return;
    }
    if (!GoalID) {
      toast.error(t("group.goalIdRequired"));
      return;
    }
    setLoading(true);
    await dispatch(addGroupToGoal());
    setLoading(false);
  };

  return (
    <Dialog
      open={true}
      onOpenChange={(open) => dispatch(setAddGroupToGoal(open))}
    >
      <DialogTrigger asChild>
        <Button className="hidden">Open Dialog</Button>
      </DialogTrigger>
      <DialogContent className="w-full">
        <DialogHeader>
          <DialogTitle>{t("group.addGroupToGoal")}</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col gap-6 mt-6">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="search" className="text-sm">
              {t("group.searchGroup")}
            </Label>
            <Input
              id="search"
              type="text"
              value={searchTerm}
              onChange={(e) => dispatch(setSearchTerm(e.target.value))}
              placeholder={t("group.enterFirstLetters")}
            />
          </div>

          <div className="grid w-full items-center gap-1.5">
            <CustomSelect
              label={t("group.selectGroup")}
              value={selectedGroupID}
              onValueChange={(value) => dispatch(setSelectedGroupID(value))}
              placeholder={t("group.selectAGroup")}
              items={groups.map((group) => ({
                value: group._id,
                label: group.name,
              }))}
            />
          </div>

          <DialogFooter className="mt-8">
            <Button
              disabled={loading}
              onClick={handleAddGroup}
              className={`w-full ${
                loading
                  ? "cursor-not-allowed"
                  : "bg-voxa-teal-600 hover:bg-voxa-teal-500"
              } text-voxa-neutral-50 flex justify-center items-center gap-2`}
            >
              {loading ? (
                <>
                  {t("group.addingToGoal")} <ButtonLoader />
                </>
              ) : (
                t("group.addToGoal")
              )}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
