"use client";

import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@radix-ui/react-label";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import ButtonLoader from "../Loaders/ButtonLoader";
import {
  Select,
  SelectItem,
  SelectTrigger,
  SelectContent,
  SelectValue,
} from "@/components/ui/select";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  addExistingClientToGoal,
  fetchClients,
  setAddClientToGoal,
  setSelectedClientID,
  fetchClientsByNumber,
} from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import { Button } from "../ui/button";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";
import { useTranslation } from "react-i18next";

export default function AddExistingClientToGoal() {
  const { t } = useTranslation("assistants");
  const { GoalID, clients, selectedClientID, addClientToGoal } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardAssistants
  );
  const dispatch = useDispatch<AppDispatch>();
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchNumber, setSearchNumber] = useState("");

  useEffect(() => {
    const delayDebounceFn = setTimeout(async () => {
      await dispatch(fetchClients({ searchTerm, GoalID }));
    }, 800);
    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm, GoalID, dispatch]);

  useEffect(() => {
    const trimedSearchNumber = searchNumber
      .replace(/ {2,}/g, " ")
      .replace(/ (?! )/g, "");

    const delayDebounceFn = setTimeout(async () => {
      await dispatch(
        fetchClientsByNumber({
          searchTerm: trimedSearchNumber,
          GoalID,
        })
      );
    }, 800);
    return () => clearTimeout(delayDebounceFn);
  }, [searchNumber, GoalID, dispatch]);

  const handleAddClient = async () => {
    if (!selectedClientID) {
      toast.error(t("existingClient.clientRequired"));
      return;
    }

    if (!GoalID) {
      toast.error(t("existingClient.goalIdRequired"));
      return;
    }

    setLoading(true);
    await dispatch(addExistingClientToGoal());
    setLoading(false);
  };

  return (
    <Dialog
      open={addClientToGoal}
      onOpenChange={() => dispatch(setAddClientToGoal(false))}
    >
      <DialogContent className="max-w-md w-full">
        <DialogHeader>
          <DialogTitle>{t("existingClient.addExistingClient")}</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col gap-6 mt-2">
          {/* Search Input */}
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="search" className="text-sm">
              {t("existingClient.searchByName")}
            </Label>
            <Input
              id="search"
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder={t("existingClient.enterName")}
            />
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="search" className="text-sm">
              {t("existingClient.searchByNumber")}
            </Label>
            <Input
              id="search"
              type="text"
              value={searchNumber}
              onChange={(e) => setSearchNumber(e.target.value)}
              placeholder={t("existingClient.enterNumber")}
            />
          </div>

          {/* Select Existing Client */}
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="client" className="text-sm">
              {t("existingClient.selectClient")}
            </Label>
            <Select
              value={selectedClientID}
              onValueChange={(value) => dispatch(setSelectedClientID(value))}
            >
              <SelectTrigger className="h-10">
                <SelectValue
                  placeholder={
                    clients.length > 0
                      ? t("existingClient.clientsFound", {
                          count: clients.length,
                        })
                      : t("existingClient.searchByNameAbove")
                  }
                  className="text-sm text-gray-500"
                >
                  {clients.find(
                    (client: any) => client._id === selectedClientID
                  )?.name ||
                    (clients.length > 0
                      ? t("existingClient.clientsFound", {
                          count: clients.length,
                        })
                      : t("existingClient.noClientsFound"))}
                </SelectValue>
              </SelectTrigger>
              <SelectContent className="z-50 w-full">
                {clients.length > 0 ? (
                  clients.map((client: any, index: number) => (
                    <SelectItem
                      key={index}
                      value={client._id}
                      className="flex justify-between items-center w-full px-4 py-2 border-b last:border-none hover:bg-gray-100 transition"
                    >
                      <div className="flex flex-col">
                        <p className="font-medium text-sm text-gray-600 dark:text-gray-200">
                          {client.name}
                        </p>
                        <p className="text-xs text-gray-500 flex flex-wrap gap-1">
                          {client.goalNames.length > 0 ? (
                            client.goalNames.map((name: string, i: number) => (
                              <span
                                key={i}
                                className="px-2 py-0.5 bg-blue-100 text-blue-600 rounded-md text-[10px]"
                              >
                                {name}
                              </span>
                            ))
                          ) : (
                            <span className="text-gray-400">
                              {t("existingClient.noAttachedGoals")}
                            </span>
                          )}
                        </p>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300  ">
                        {phoneNumberFormat(client.phone, client.country)}
                      </p>
                    </SelectItem>
                  ))
                ) : (
                  <p className="text-center text-gray-400 p-2">
                    {t("existingClient.noClientsFound")}
                  </p>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Add Button */}
          <Button
            disabled={loading}
            onClick={handleAddClient}
            className={`mt-8 ${
              loading
                ? "cursor-not-allowed"
                : "bg-voxa-teal-600 hover:bg-voxa-teal-500"
            } text-voxa-neutral-50`}
          >
            {loading ? (
              <>
                {t("existingClient.addingToGoal")} <ButtonLoader />
              </>
            ) : (
              t("existingClient.addToGoal")
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
