"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { RootState } from "@/redux/store";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";

interface Subscription {
  _id: string;
  plan_id: {
    name: string;
    current_price: number;
    currency: string;
    billing_period: string;
  };
  status: string;
  current_period_end: string;
  trial_end?: string;
  cancel_at_period_end: boolean;
}

interface BillingNotificationsProps {
  className?: string;
}

export default function BillingNotifications({
  className,
}: BillingNotificationsProps) {
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [notifications, setNotifications] = useState<
    Array<{
      type: "info" | "warning" | "error" | "success";
      title: string;
      message: string;
      icon: React.ComponentType<any>;
      daysUntil?: number;
      priority: number;
    }>
  >([]);
  const [isLoading, setIsLoading] = useState(true);

  // Get entreprise balance and threshold from BillingSlice
  const { entreprise } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardEntreprise
  );

  useEffect(() => {
    setNotifications([]);
    if (
      typeof entreprise?.balance === "number" &&
      typeof entreprise?.balanceAlertThreshold === "number" &&
      entreprise.balance < entreprise.balanceAlertThreshold
    ) {
      setNotifications([
        {
          type: "warning",
          title: "Account Balance Below Threshold",
          message: `Your entreprise account balance (${entreprise.balance}€) is below the threshold (${entreprise.balanceAlertThreshold}€). Please add funds to avoid service interruption.`,
          icon: AlertTriangle,
          priority: 100,
        },
      ]);
    }
    setIsLoading(false);
  }, [entreprise]);

  const getAlertClasses = (type: string) => {
    switch (type) {
      case "error":
        return "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20 text-red-800 dark:text-red-200";
      case "warning":
        return "border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-900/20 text-amber-800 dark:text-amber-200";
      case "success":
        return "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20 text-green-800 dark:text-green-200";
      case "info":
        return "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200";
      default:
        return "border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-900/20";
    }
  };

  if (isLoading) {
    return null;
  }

  // Don't render the component if there are no notifications
  if (notifications.length === 0) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5" />
          Billing Notifications
          {notifications.length > 0 && (
            <Badge variant="secondary" className="ml-auto">
              {notifications.length}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {notifications.map((notification, index) => {
          const Icon = notification.icon;
          return (
            <div
              key={index}
              className={`flex items-start gap-3 p-3 rounded-lg border ${getAlertClasses(
                notification.type
              )}`}
            >
              <Icon className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm">{notification.title}</div>
                <div className="text-sm text-muted-foreground mt-0.5">
                  {notification.message}
                </div>
              </div>
              {notification.daysUntil !== undefined && (
                <Badge
                  variant="outline"
                  className="ml-auto text-xs flex-shrink-0"
                >
                  {notification.daysUntil} days
                </Badge>
              )}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}
