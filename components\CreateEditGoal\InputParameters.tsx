import React from "react";
import CustomInput from "@/components/CustomFormItems/Input";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  setDurationBetweenCalls,
  setRingingDuration,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";

export const InputParameters: React.FC = () => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const { durationBetweenCalls, ringingDuration } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );
  return (
    <>
      <CustomInput
        props={{
          name: "maxNumberRetries",
          label: t("createEditGoal.advanced.durationBetweenCalls"),
          type: "number",
          placeholder: t(
            "createEditGoal.advanced.durationBetweenCallsPlaceholder"
          ),
          value: durationBetweenCalls,
          onChange: (e) => dispatch(setDurationBetweenCalls(e.target.value)),
        }}
      />
      <CustomInput
        props={{
          name: "ringingDuration",
          label: t("createEditGoal.advanced.ringingDuration"),
          type: "number",
          placeholder: t("createEditGoal.advanced.ringingDurationPlaceholder"),
          value: ringingDuration,
          onChange: (e) => dispatch(setRingingDuration(e.target.value)),
        }}
      />
    </>
  );
};
