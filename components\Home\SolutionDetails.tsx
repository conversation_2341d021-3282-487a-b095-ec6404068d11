"use client"
import Image from "next/image"

import ia from "@/public/images/ia.webp"
import integration from "@/public/images/integration.webp"
import prise from "@/public/images/prise-en-main.webp"
import stats from "@/public/images/stats.webp"

const Cards = [
    {
        title: "AI-Powered Teleoperators",
        description: "Our teleoperators are powered by AI and can handle all your calls, no matter the volume.",
        image: ia
    },
    {
        title: "Seamless Integration",
        description: "Our solution integrates seamlessly with your existing systems and processes.",
        image: integration
    },
    {
        title: "Easy to Use",
        description: "Our solution is easy to use and requires minimal training.",
        image: prise
    },
    {
        title: "Real-Time Stats",
        description: "Get real-time stats on your calls and monitor your teleoperators' performance.",
        image: stats
    }
]

export function SolutionDetails(){
    return(
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4 mt-16 max-w-7xl">
            {
                Cards.map((card, index) => (
                    <div key={index} className="flex flex-col justify-center p-4 rounded-md shadow-md">
                        <Image src={card.image} alt={card.title} className="rounded-2xl" />
                        <h3 className="text-xl font-semibold mt-4">{card.title}</h3>
                        <p className="mt-2">{card.description}</p>
                    </div>
                ))
            }
        </div>
    )
}