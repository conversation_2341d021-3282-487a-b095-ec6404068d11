import { Filters } from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";

/**
 * Generates MongoDB match conditions based on analytics filters
 * @param conversationTypes Filter settings for conversation types
 * @param countries Filter settings for countries
 * @returns An object with MongoDB match conditions for analytics
 */
export function generateAnalyticsMatchCondition(
  conversationTypes?: Filters["conversationTypes"],
  countries?: string[],
  callDuration?: Filters["callDuration"],
  ringingTime?: Filters["ringingTime"]
): any {
  // If no conversation types provided, return empty object (no filters)
  if (!conversationTypes) {
    return {};
  }

  const matchCondition: any = {};

  // Direction filters (inbound/outbound)
  if (
    conversationTypes.inbound === false &&
    conversationTypes.outbound === false
  ) {
    // If both are false, create an impossible condition to return no results
    matchCondition.direction = "impossible_value_to_match";
  } else if (conversationTypes.inbound === false) {
    // Filter out inbound
    matchCondition.direction = { $ne: "inbound" };
  } else if (conversationTypes.outbound === false) {
    // Only include inbound
    matchCondition.direction = "inbound";
  }

  // Call status filters (missed/answered) - for call type analytics
  if (
    conversationTypes.missed === false &&
    conversationTypes.answered === false
  ) {
    // If both statuses are disabled, return an impossible condition
    matchCondition.status = "impossible_value_to_match";
  } else if (conversationTypes.missed === false) {
    // Filter out missed calls
    matchCondition.status = { $ne: "MISSED" };
  } else if (conversationTypes.answered === false) {
    // Only include missed calls
    matchCondition.status = "MISSED";
  }

  // Transfer filters - only apply if answered calls are included
  if (conversationTypes.answered !== false) {
    if (
      conversationTypes.transferred === false &&
      conversationTypes.notTransferred === false
    ) {
      // If both transfer options are disabled but answered calls are enabled,
      // only allow missed calls
      matchCondition.status = "MISSED";
    } else if (conversationTypes.transferred === false) {
      // Only include missed calls and non-transferred answered calls
      matchCondition.$or = [
        { status: "MISSED" },
        {
          status: "ANSWERED",
          $or: [
            { child_type: { $exists: false } },
            { child_type: null },
            { child_type: "" },
          ],
        },
      ];
    } else if (conversationTypes.notTransferred === false) {
      // Only include missed calls and transferred answered calls
      matchCondition.$or = [
        { status: "MISSED" },
        { status: "ANSWERED", child_type: "CALL" },
      ];
    }
  }

  // Communication type filters using $nin (not in) approach
  const excludedTypes = [];
  if (conversationTypes.calls === false) excludedTypes.push("CALL");
  if (conversationTypes.whatsapp === false) excludedTypes.push("WHATSAPP");
  if (conversationTypes.sms === false) excludedTypes.push("SMS");
  if (conversationTypes.meet === false) excludedTypes.push("MEET");

  if (excludedTypes.length > 0) {
    matchCondition.type = { $nin: excludedTypes };
  }

  // If all types are excluded, this would be equivalent to an impossible condition
  if (excludedTypes.length === 4) {
    matchCondition.type = "impossible_value_to_match";
  }

  // Country filter
  if (countries && countries.length > 0) {
    // Add a $expr to evaluate the clientCountry field based on direction
    matchCondition.$expr = {
      $in: [
        {
          $cond: [
            { $eq: ["$direction", "inbound"] },
            "$from_country",
            "$to_country",
          ],
        },
        countries,
      ],
    };
  }

  // Call duration filter - modified to handle string values
  if (callDuration) {
    if (callDuration.min != null || callDuration.max != null) {
      // Create expressions for min and max conditions
      const conditions = [];

      if (callDuration.min != null) {
        conditions.push({
          $gte: [
            {
              $convert: {
                input: "$duration",
                to: "int",
                onError: 0, // Default to 0 if conversion fails
                onNull: 0,
              },
            },
            callDuration.min,
          ],
        });
      }

      if (callDuration.max != null) {
        conditions.push({
          $lte: [
            {
              $convert: {
                input: "$duration",
                to: "int",
                onError: 0, // Default to 0 if conversion fails
                onNull: 0,
              },
            },
            callDuration.max,
          ],
        });
      }

      // If there are already $expr conditions (like for countries)
      if (matchCondition.$expr) {
        // Combine with existing $expr using $and
        matchCondition.$expr = {
          $and: [matchCondition.$expr, ...conditions],
        };
      } else {
        // If there's only one condition
        if (conditions.length === 1) {
          matchCondition.$expr = conditions[0];
        } else {
          // If there are multiple conditions
          matchCondition.$expr = { $and: conditions };
        }
      }
    }
  }

  // Ringing time filter - modified to calculate based on date difference
  if (ringingTime) {
    if (ringingTime.min != null || ringingTime.max != null) {
      // Create expressions for min and max conditions based on the date difference calculation
      const conditions = [];

      // Calculate ringing duration in seconds
      const ringingDurationExpr = {
        $divide: [
          {
            $subtract: [
              {
                $dateFromString: {
                  dateString: "$inprogress_time",
                  onError: new Date(0),
                  onNull: new Date(0),
                },
              },
              {
                $dateFromString: {
                  dateString: "$ringing_time",
                  onError: new Date(0),
                  onNull: new Date(0),
                },
              },
            ],
          },
          1000,
        ],
      };

      if (ringingTime.min != null) {
        conditions.push({
          $gte: [ringingDurationExpr, ringingTime.min],
        });
      }

      if (ringingTime.max != null) {
        conditions.push({
          $lte: [ringingDurationExpr, ringingTime.max],
        });
      }

      // If there are already $expr conditions
      if (matchCondition.$expr) {
        // Combine with existing $expr using $and
        matchCondition.$expr = {
          $and: [matchCondition.$expr, ...conditions],
        };
      } else {
        // If there's only one condition
        if (conditions.length === 1) {
          matchCondition.$expr = conditions[0];
        } else {
          // If there are multiple conditions
          matchCondition.$expr = { $and: conditions };
        }
      }
    }
  }

  return matchCondition;
}
