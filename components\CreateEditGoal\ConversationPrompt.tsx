import React, { Dispatch, SetStateAction, useEffect } from "react";
import { useTranslation } from "react-i18next";
import Link from "next/link";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import ScriptsCard from "@/components/Dashboard/Cards/ScriptsCard";
import CustomPagination from "@/components/pagination/CustomPagination";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  setSelectedScript,
  setTextPreview,
  setSelectedScriptType,
  setScriptContent,
  getScripts,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";
import { usePagination } from "@/hooks/usePagination";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";

type ConversationPromptProps = {
  setFile: Dispatch<SetStateAction<File | null>>;
  goalID: string;
};

export const ConversationPrompt: React.FC<ConversationPromptProps> = ({
  setFile,
  goalID,
}) => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const {
    scripts,
    selectedScriptType,
    selectedScript,
    fileFromGoal,
    textPreview,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );

  useEffect(() => {
    return () => {
      if (fileFromGoal) {
        URL.revokeObjectURL(fileFromGoal);
      }
    };
  }, [fileFromGoal]);

  useEffect(() => {
    dispatch(getScripts());
  }, [dispatch]);

  const viewFile = (preview: string) => {
    window.open(preview, "_blank");
  };

  const handleFileChange = (e: any) => {
    if (e.target.files.length > 1) {
      toast.error("Only one file can be selected.");
      return;
    }
    const file = e.target.files[0];
    if (!file) return;
    if (file.type !== "text/plain") {
      toast.error("Only .txt files are allowed.");
      return;
    }
    if (file.size > 20 * 1024) {
      toast.error("File size must be 20KB or lower.");
      return;
    }
    const previewURL = URL.createObjectURL(file);
    dispatch(setTextPreview(previewURL));
    setFile(file);
  };

  useEffect(() => {
    if (!selectedScriptType) {
      dispatch(setSelectedScript(null));
      dispatch(setScriptContent(""));
    } else {
      setFile(null);
      dispatch(setTextPreview(null));
    }
  }, [selectedScriptType, dispatch, setFile]);

  const itemsPerPage = 6;
  const {
    currentItems: paginatedScripts,
    currentPage,
    setCurrentPage,
  } = usePagination(scripts, itemsPerPage);

  return (
    <div>
      <div className="text-lg font-semibold flex gap-4 items-center">
        <div>
          {t("createEditGoal.prompt.section")}
          <span className="text-red-500"> *</span>
        </div>
        {goalID === "create" ? (
          <Link
            href="/files/vente_devices.txt"
            download
            className="text-xs font-medium hover:underline text-voxa-teal-600 hover:text-voxa-teal-500 active:text-voxa-teal-400 transition-all duration-150"
          >
            {t("createEditGoal.prompt.downloadExample")}
          </Link>
        ) : (
          fileFromGoal && (
            <Link
              href={fileFromGoal}
              download="goal_prompt.txt"
              className="text-xs font-medium hover:underline text-voxa-teal-600 hover:text-voxa-teal-500 active:text-voxa-teal-400 transition-all duration-150"
            >
              {t("createEditGoal.prompt.viewScript")}
            </Link>
          )
        )}
      </div>
      <div
        className={`w-full flex gap-4 ${
          selectedScriptType ? "flex-col" : "max-lg:flex-col"
        }`}
      >
        <div className="w-full flex items-center gap-2">
          <Switch
            id="script"
            onCheckedChange={(checked) => {
              dispatch(setSelectedScriptType(checked));
            }}
          />
          <Label htmlFor="script" className="flex items-center gap-2 h-10">
            {selectedScriptType
              ? t("createEditGoal.prompt.fromDatabase")
              : t("createEditGoal.prompt.fromComputer")}
          </Label>
        </div>
        {selectedScriptType ? (
          <div className="w-full grid grid-cols-1 md:grid-cols-2   xl:grid-cols-3 gap-4">
            {paginatedScripts.map((script, index) => (
              <ScriptsCard
                key={script.id || index}
                script={script}
                selected={selectedScript === script.id}
              />
            ))}
            <CustomPagination
              className="md:col-span-2 xl:col-span-3"
              itemsPerPage={itemsPerPage}
              totalItems={scripts.length}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
            />
          </div>
        ) : (
          <div className="w-full flex max-sm:flex-col gap-4">
            <Label className="flex text-nowrap items-center gap-1 dark:text-voxa-neutral-200">
              {t("createEditGoal.prompt.uploadPrompt")}{" "}
              <span className="w-full text-red-500">*</span>
            </Label>
            <div className="flex items-center gap-2">
              <Input
                onChange={handleFileChange}
                id="cin"
                name="cin"
                type="file"
                accept=".txt"
                required
                className="cursor-pointer text-xs"
              />
              {textPreview && (
                <Button
                  onClick={() => viewFile(textPreview)}
                  className="h-full bg-voxa-teal-600 hover:bg-voxa-teal-500"
                >
                  {t("createEditGoal.prompt.viewFile")}
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
