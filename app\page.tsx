"use client";
import React, { useEffect } from "react";
import businessLogo from "@/public/images/Icons/parrot.svg";
import Image from "next/image";
import { motion } from "motion/react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "next/navigation";
import { signIn, useSession } from "next-auth/react";
import NewsLetter from "@/components/ui/newsletter";

export default function SpotlightNewDemo() {
  const { t } = useTranslation(["home", "common"]);
  const searchParams = useSearchParams();
  const autoSignIn = searchParams?.get("autoSignIn");
  const { status } = useSession();

  useEffect(() => {
    if (autoSignIn === "true" && status === "unauthenticated") {
      signIn("cognito");
    }
    if (autoSignIn === "true" && status === "authenticated") {
      window.location.href = "/businessDash";
    }
  }, [autoSignIn, status]);
  return (
    <div className="w-full flex flex-col">
      <div className="mt-14 rounded-md flex flex-col items-center justify-evenly pt-20 sm:pt-28 px-8 sm:px-16 max-w-7xl mx-auto">
        <div className="mx-auto z-10">
          <motion.h1
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
            className="pb-2.5 text-5xl sm:text-7xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-b from-neutral-950 to-neutral-500 dark:from-neutral-50 dark:to-neutral-400 bg-opacity-50"
          >
            {t("common:logo")}
            <p className="font-semibold">{t("home:header")}</p>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.3 }}
            className="mt-4 font-normal text-base sm:text-xl dark:text-voxa-neutral-300 text-center mx-auto"
          >
            {t("home:description")}
          </motion.p>
        </div>
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
        >
          <Image
            src={businessLogo}
            alt="Echo Parrot Logo"
            className="m-10 w-44 sm:w-56 invert-[90%] dark:invert-[20%]"
          />
        </motion.div>
      </div>
      <NewsLetter />
    </div>
  );
}
