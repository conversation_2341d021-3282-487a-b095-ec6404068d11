"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, Trash2 } from "lucide-react";
import Image from "next/image";
import { SavedCard } from "@/actions/BillingActions";
import {
  getPaymentMethodDisplayName,
  getPaymentMethodIconPath,
} from "@/lib/billingUtils";

interface SavedCardItemProps {
  card: SavedCard;
  actionLoading: string | null;
  onSetDefault: (cardId: string) => void;
  onDeleteClick: (card: SavedCard) => void;
}

export default function SavedCardItem({
  card,
  actionLoading,
  onSetDefault,
  onDeleteClick,
}: SavedCardItemProps) {
  const getPaymentMethodIcon = (brand: string) => {
    const iconClass = "border border-gray-200 dark:border-gray-700 rounded";
    const iconPath = getPaymentMethodIconPath("card", brand);
    const altText = getPaymentMethodDisplayName("card", brand);

    return (
      <Image
        src={iconPath}
        alt={altText}
        width={32}
        height={20}
        className={iconClass}
      />
    );
  };

  const formatExpiry = (month: number, year: number) => {
    return `${month.toString().padStart(2, "0")}/${year.toString().slice(-2)}`;
  };

  const getFundingType = (funding: string) => {
    switch (funding) {
      case "credit":
        return "Credit";
      case "debit":
        return "Debit";
      case "prepaid":
        return "Prepaid";
      default:
        return "Card";
    }
  };

  return (
    <div className="flex items-center justify-between p-4 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-white dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-50 dark:hover:bg-voxa-neutral-800 transition-colors">
      <div className="flex items-center gap-4">
        {/* Card Icon */}
        <div className="flex-shrink-0">{getPaymentMethodIcon(card.brand)}</div>

        {/* Card Details */}
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-voxa-neutral-900 dark:text-voxa-neutral-100">
              {getPaymentMethodDisplayName("card", card.brand)}
            </span>
            <span className="text-voxa-neutral-500 dark:text-voxa-neutral-400">
              •••• {card.last4}
            </span>
            {card.is_default && (
              <Badge className="bg-voxa-teal-100 text-voxa-teal-800 dark:bg-voxa-teal-900 dark:text-voxa-teal-100">
                <Star className="w-3 h-3 mr-1" />
                Default
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-4 text-sm text-voxa-neutral-500 dark:text-voxa-neutral-400">
            <span>Expires {formatExpiry(card.exp_month, card.exp_year)}</span>
            <span>•</span>
            <span>{getFundingType(card.funding)}</span>
            {card.country && (
              <>
                <span>•</span>
                <span>{card.country.toUpperCase()}</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-2">
        {!card.is_default && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onSetDefault(card.id)}
            disabled={actionLoading === card.id}
            className="border-voxa-teal-200 text-voxa-teal-700 hover:bg-voxa-teal-50 dark:border-voxa-teal-700 dark:text-voxa-teal-300 dark:hover:bg-voxa-teal-900"
          >
            {actionLoading === card.id ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-voxa-teal-600 mr-2"></div>
                Setting...
              </>
            ) : (
              <>
                <Star className="w-3 h-3 mr-2" />
                Set Default
              </>
            )}
          </Button>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={() => onDeleteClick(card)}
          disabled={actionLoading === card.id}
          className="border-red-200 text-red-700 hover:bg-red-50 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900"
        >
          {actionLoading === card.id ? (
            <>
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600 mr-2"></div>
              Deleting...
            </>
          ) : (
            <>
              <Trash2 className="w-3 h-3 mr-2" />
              Delete
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
