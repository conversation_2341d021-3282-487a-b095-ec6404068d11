import NextAuth, { DefaultSession, DefaultUser } from "next-auth";
import { JWT } from "next-auth/jwt";

// Extend default User type to include `role`
interface CustomUser extends DefaultUser {
  role: string;
}

// Extend the session to include the custom user type
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: CustomUser;
    sessionId: string;
  }

  interface User extends CustomUser {}

  interface JWT {
    id: string;
    role: string;
  }
}
