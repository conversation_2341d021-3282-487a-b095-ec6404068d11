import { io } from "socket.io-client";

// "undefined" means the URL will be computed from the `window.location` object

const URL = process.env.NEXT_PUBLIC_SOCKET_URL;
console.log(URL);
export const socket = io(URL, {
  path: "/ws/socket.io",
  withCredentials: true,
  autoConnect: false,
  transports: ["websocket"],
  // this is a workaround, if we remove it we won't be able to assign the token to the socket connection
  query: {
    _: "",
  },
});

socket.on("connect", () => {});
