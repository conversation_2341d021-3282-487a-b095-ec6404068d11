import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { CopyIcon, UsersRoundIcon } from "lucide-react";
import CustomButton from "@/components/CustomFormItems/Button";
import { AssistantGoalDropdown } from "@/components/dropdowns/AssistantGoalDropdown";
import { handleCopyText } from "@/lib/Strings/Copy";
import { useState } from "react";

interface GoalHeaderSectionProps {
  goalID: string;
  templateType: string;
}

export const GoalHeaderSection: React.FC<GoalHeaderSectionProps> = ({
  goalID,
  templateType,
}) => {
  const { t } = useTranslation("assistants");
  const router = useRouter();
  const [openGoalID, setOpenGoalID] = useState<string | null>(null);
  const { goalName, goalStatus, goalType } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );

  return (
    <div className="w-full flex max-sm:gap-3 gap-2 max-md:flex-col items-center justify-between">
      <div className="max-sm:mx-auto text-xs sm:text-sm sm:gap-2 text-nowrap max-sm:max-w-[240px]">
        <div>
          <span className="font-semibold">
            {t("createEditGoal.topSection.goalName")}{" "}
          </span>
          <span>{goalName}</span>
        </div>
        <div className="dark:text-voxa-neutral-200 flex gap-1 items-center">
          <span className="font-semibold">
            {t("createEditGoal.topSection.goalID")}
          </span>
          <span title={goalID} className="truncate">
            {goalID}
          </span>
          <CopyIcon
            onClick={() => handleCopyText(goalID)}
            className="w-3 h-3 cursor-pointer hover:text-voxa-neutral-200 transition-colors duration-200"
          />
        </div>
      </div>
      <div className="w-full flex md:justify-end max-sm:flex-col items-center gap-3 sm:gap-2">
        <CustomButton
          props={{
            onClick: () =>
              router.push(
                `/businessDash/assistants/Goals/Clients/${goalID}?templateType=${templateType}`
              ),
            value: t("createEditGoal.topSection.goalClients"),
            className:
              "bg-background hover:bg-background w-full sm:w-fit px-4 py-2 rounded-md border-2 border-voxa-teal-600 text-voxa-teal-600 hover:border-voxa-teal-400 hover:text-voxa-teal-400 transition-colors",
            icon: <UsersRoundIcon className="w-8 h-8" />,
          }}
        />
        <AssistantGoalDropdown
          goalName={goalName}
          goalID={goalID}
          goalStatus={goalStatus}
          goalType={goalType}
          isOpen={goalID === openGoalID}
          setOpenGoalID={setOpenGoalID}
          goal_template_type={templateType}
        />
      </div>
    </div>
  );
};
