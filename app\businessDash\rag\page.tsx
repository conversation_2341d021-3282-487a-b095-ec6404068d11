"use client";
import {
  DeleteRagCollectionByID,
  GetRagCollections,
} from "@/actions/CollectionActions";
import { UpdateChromaDB } from "@/components/dialogs/UpdateChromaDB";
import { CreateRagCollectionSheet } from "@/components/Sidebars/CreateRagCollectionSheet";
import { Trash2Icon } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import Link from "next/link";
import MainLoader from "@/components/Loaders/MainLoader";
import CustomPagination from "@/components/pagination/CustomPagination";
import { usePagination } from "@/hooks/usePagination";
import { useTranslation } from "react-i18next";

export default function RagRoute() {
  const { t } = useTranslation("rag");
  const [collections, setCollections] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const itemsPerPage = 24;

  const {
    currentItems: currentCollections,
    currentPage,
    setCurrentPage,
  } = usePagination(collections, itemsPerPage);

  const getRagCollections = async () => {
    try {
      const response = await GetRagCollections();
      if (!response.success) throw new Error(response.error);
      if (response.collections) setCollections(response.collections);
      else throw new Error("No collections found");
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteButton = async (id: string) => {
    try {
      const response = await DeleteRagCollectionByID(id);
      if (!response.success) throw new Error(response.error);
      toast.success("Collection deleted successfully");
      getRagCollections();
    } catch (err: any) {
      toast.error(err.message);
    }
  };

  useEffect(() => {
    getRagCollections();
  }, []);
  return (
    <div className="relative w-full flex flex-col gap-4">
      <div className="flex max-md:flex-col max-lg:flex-wrap justify-between w-full gap-4">
        <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50 text-nowrap">
          {t("title")}
        </h1>
        <div className="w-full flex max-sm:flex-col gap-2 justify-end">
          <CreateRagCollectionSheet />
          <UpdateChromaDB />
        </div>
      </div>
      {loading ? (
        <div className="absolute w-full flex justify-center items-center h-[calc(100vh-2rem)] sm:h-[calc(100vh-3rem)]">
          <MainLoader />
        </div>
      ) : collections.length > 0 ? (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {currentCollections.map((collection, index) => (
              <div
                key={index}
                className="p-[18px] bg-sidebar border-sidebar-border border rounded-2xl transition-all duration-300 hover:scale-103 hover:shadow-lg flex flex-col justify-between"
              >
                <div className="flex w-full justify-between items-center mb-3">
                  <h1 className="text-base font-semibold dark:text-voxa-neutral-50">
                    {collection.name}
                  </h1>
                  <button onClick={() => handleDeleteButton(collection._id)}>
                    <Trash2Icon className="w-5 h-5 text-red-500 hover:text-red-500/80 active:text-red-600 transition-colors duration-150" />
                  </button>
                </div>
                <Link
                  href={collection.file}
                  target="_blank"
                  rel="noopener noreferrer"
                  download
                  className="w-full text-center px-4 py-1.5 text-sm bg-voxa-teal-600 hover:bg-voxa-teal-500 text-white font-medium rounded-lg  transition-colors duration-300"
                >
                  {t("view_rag")}
                </Link>
              </div>
            ))}
          </div>
          <CustomPagination
            itemsPerPage={itemsPerPage}
            totalItems={collections.length}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
        </>
      ) : (
        <div className="w-full p-2 flex flex-col gap-3 justify-center items-center bg-voxa-neutral-50 dark:bg-voxa-neutral-900 py-8 rounded-md text-center">
          <p className="text-voxa-neutral-600 dark:text-voxa-neutral-50 text-base font-semibold">
            {t("no_collections")}
          </p>
          <p className="text-voxa-neutral-700 dark:text-voxa-neutral-200 text-sm">
            {t("import_pdf")}
          </p>
        </div>
      )}
    </div>
  );
}
