// app/api/sheets/data/route.ts

import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export const runtime = "nodejs";

export async function GET() {
  const tokenCookie = (await cookies()).get("google_tokens_sheets")?.value;

  if (!tokenCookie) {
    return NextResponse.json({ error: "Not authorized" }, { status: 401 });
  }

  const { access_token } = JSON.parse(tokenCookie);

  const spreadsheetId = "1LQNypzzR6vNezOPEcw7rpEXTHF_tHutZEuSVdyyqgMU";
  const range = "'Feuille 1'!A1:E20"; // Corrected range based on actual sheet name

  const url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${encodeURIComponent(range)}`;

  const res = await fetch(url, {
    headers: {
      Authorization: `Bearer ${access_token}`,
    },
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.error("Error fetching sheet data:", res.status, errorText);
    return NextResponse.json({ error: "Failed to fetch sheet data" }, { status: res.status });
  }

  const data = await res.json();
  console.log("Fetched sheet data:", data);

  return NextResponse.json(data.values ?? []);
}
