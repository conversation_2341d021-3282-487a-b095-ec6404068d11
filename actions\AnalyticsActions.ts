"use server";

import Conversation from "@/models/Conversation";
import { getEntrepriseByAdminID } from "./Entreprise";
import { endOfDay, addHours } from "date-fns";
import dbConnect from "@/lib/mongodb";
import {
  ConversationTypesStats,
  Filters,
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { generateAnalyticsMatchCondition } from "@/lib/analytics/analyticsFilters";
import { CountryCode } from "@/lib/countries";

export async function getConversationsStats(
  fromDate?: Date,
  toDate?: Date,
  conversationTypes?: Filters["conversationTypes"],
  countries?: string[],
  callDuration?: Filters["callDuration"],
  ringingTime?: Filters["ringingTime"]
) {
  await dbConnect();
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const entrepriseID = entrepriseResponse.entreprise._id;

    // Base match conditions
    const baseMatchConditions: any = {
      entreprise_id: entrepriseID,
      type: "CALL", // Add filter for call type at the top level
    };

    // Apply date filters
    if (fromDate || toDate) {
      baseMatchConditions.parsedTimeStamp = {};
      if (fromDate) baseMatchConditions.parsedTimeStamp.$gte = fromDate;
      if (toDate) baseMatchConditions.parsedTimeStamp.$lte = toDate;
    }

    // Get analytics match conditions
    const typeMatchConditions = generateAnalyticsMatchCondition(
      conversationTypes,
      countries,
      callDuration,
      ringingTime
    );

    const stats = await Conversation.aggregate([
      {
        $addFields: {
          parsedTimeStamp: {
            $dateFromString: { dateString: "$completed_time" },
          },
        },
      },
      { $match: baseMatchConditions }, // First match with base conditions
      { $match: typeMatchConditions }, // Second match with conversation type filters
      {
        $facet: {
          total: [
            { $count: "total" }, // Add total count of all matching calls
          ],
          missed: [
            {
              $match: {
                // Removed type: "CALL" since it's already filtered above
                status: "MISSED",
              },
            },
            { $count: "total" },
          ],
          notTransferred: [
            {
              $match: {
                // Removed type: "CALL" since it's already filtered above
                status: "ANSWERED",
                $or: [
                  { child_type: { $exists: false } },
                  { child_type: null },
                  { child_type: "" },
                ],
              },
            },
            { $count: "total" },
          ],
          transferred: [
            {
              $match: {
                // Removed type: "CALL" since it's already filtered above
                status: "ANSWERED",
                child_type: "CALL",
              },
            },
            { $count: "total" },
          ],
        },
      },
    ]);

    const formatSingleCount = (arr: any[]) => arr[0]?.total || 0;
    const data = stats[0];

    return {
      success: true,
      data: {
        total: formatSingleCount(data.total), // Add total to the return object
        missed: formatSingleCount(data.missed),
        notTransferred: formatSingleCount(data.notTransferred),
        transferred: formatSingleCount(data.transferred),
      },
    };
  } catch (err: any) {
    console.error(
      "Error in getting entreprise conversation logs stats:",
      err.message
    );
    return { success: false, error: err.message };
  }
}

// this will get the number of conversations by type "CALL", "SMS", "WHATSAPP"
export const getConversationsNumberByType = async (
  fromDate?: Date,
  toDate?: Date,
  conversationTypes?: Filters["conversationTypes"],
  countries?: CountryCode[],
  callDuration?: Filters["callDuration"],
  ringingTime?: Filters["ringingTime"]
): Promise<{
  success: boolean;
  data?: ConversationTypesStats;
  error?: string;
}> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return {
        success: false,
        error: entrepriseResponse.error || "Failed to fetch entreprise data.",
      };
    }

    const entreprise_id = entrepriseResponse.entreprise._id;

    const matchConditions: any = {
      entreprise_id: entreprise_id,
      type: { $in: ["CALL", "WHATSAPP", "SMS", "MEET"] },
    };

    // Apply date filters as a combined condition if both exist
    if (fromDate || toDate) {
      matchConditions.parsedTimeStamp = {};
      if (fromDate) matchConditions.parsedTimeStamp.$gte = fromDate;
      if (toDate) matchConditions.parsedTimeStamp.$lte = toDate;
    }

    // Get analytics match conditions
    const typeMatchConditions = generateAnalyticsMatchCondition(
      conversationTypes,
      countries,
      callDuration,
      ringingTime
    );

    const conversationsCount = await Conversation.aggregate([
      {
        $addFields: {
          parsedTimeStamp: {
            $dateFromString: { dateString: "$completed_time" },
          },
          isInbound: {
            $regexMatch: {
              input: { $ifNull: ["$direction", ""] },
              regex: /inbound/i,
            },
          },
        },
      },
      {
        $match: matchConditions,
      },
      { $match: typeMatchConditions }, // Add conversation type filter
      {
        $group: {
          _id: {
            type: "$type",
            isInbound: "$isInbound",
          },
          count: { $sum: 1 },
        },
      },
      {
        $project: {
          _id: 0,
          type: "$_id.type",
          isInbound: "$_id.isInbound",
          count: 1,
        },
      },
    ]);

    console.log(
      "Conversations Count by Type and Direction:",
      conversationsCount
    );

    // Restructure the result to have type as main keys and direction as sub-keys
    const result: ConversationTypesStats = {
      CALL: { inbound: 0, outbound: 0, total: 0 },
      SMS: { inbound: 0, outbound: 0, total: 0 },
      WHATSAPP: { inbound: 0, outbound: 0, total: 0 },
      MEET: { inbound: 0, outbound: 0, total: 0 },
    };

    // Fill in the actual counts
    conversationsCount.forEach((item) => {
      if (item.type in result) {
        if (item.isInbound) {
          result[item.type as keyof ConversationTypesStats].inbound =
            item.count;
        } else {
          result[item.type as keyof ConversationTypesStats].outbound =
            item.count;
        }

        // Update total for this type
        result[item.type as keyof ConversationTypesStats].total += item.count;
      }
    });

    return {
      success: true,
      data: result,
    };
  } catch (err: any) {
    return {
      success: false,
      error:
        err.message ||
        "An error occurred while fetching conversations by type.",
    };
  }
};

export const getCallDurationAnalytics = async (
  fromDate?: Date,
  toDate?: Date,
  conversationTypes?: Filters["conversationTypes"],
  countries?: CountryCode[],
  callDuration?: Filters["callDuration"],
  ringingTime?: Filters["ringingTime"]
) => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return {
        success: false,
        error: entrepriseResponse.error || "Failed to fetch entreprise data.",
      };
    }

    const entreprise_id = entrepriseResponse.entreprise._id;

    const matchConditions: any = {
      entreprise_id: entreprise_id,
      type: "CALL",
      status: "ANSWERED",
      duration: { $ne: "0", $exists: true },
      // ringing_time: { $exists: true, $ne: "" },
      // inprogress_time: { $exists: true, $ne: "" },
    };

    // Apply date filters as a combined condition if both exist
    if (fromDate || toDate) {
      matchConditions.parsedTimeStamp = {};
      if (fromDate) matchConditions.parsedTimeStamp.$gte = fromDate;
      if (toDate) matchConditions.parsedTimeStamp.$lte = toDate;
    }

    // Get analytics match conditions
    const typeMatchConditions = generateAnalyticsMatchCondition(
      conversationTypes,
      countries,
      callDuration,
      ringingTime
    );

    const analytics = await Conversation.aggregate([
      {
        $addFields: {
          parsedTimeStamp: {
            $dateFromString: { dateString: "$completed_time" },
          },
          durationInSeconds: { $toInt: "$duration" },
          ringingDurationSeconds: {
            $divide: [
              {
                $subtract: [
                  { $dateFromString: { dateString: "$inprogress_time" } },
                  { $dateFromString: { dateString: "$ringing_time" } },
                ],
              },
              1000,
            ],
          },
        },
      },
      {
        $match: matchConditions,
      },
      { $match: typeMatchConditions }, // Add conversation type filter
      {
        $group: {
          _id: null,
          averageCallDuration: { $avg: "$durationInSeconds" },
          maxCallDuration: { $max: "$durationInSeconds" },
          averageRingingTime: { $avg: "$ringingDurationSeconds" },
          maxRingingTime: { $max: "$ringingDurationSeconds" },
          totalCallDuration: { $sum: "$durationInSeconds" },
          totalCalls: { $sum: 1 },
        },
      },
    ]);

    if (analytics.length === 0) {
      return {
        success: true,
        data: {
          averageCallDuration: 0,
          maxCallDuration: 0,
          averageRingingTime: 0,
          maxRingingTime: 0,
          totalCallDuration: 0, // Default for total
        },
      };
    }

    const result = {
      averageCallDuration: Math.round(analytics[0].averageCallDuration) || 0,
      maxCallDuration: analytics[0].maxCallDuration || 0,
      averageRingingTime: Math.round(analytics[0].averageRingingTime) || 0,
      maxRingingTime: analytics[0].maxRingingTime || 0,
      totalCallDuration: analytics[0].totalCallDuration || 0, // New field
    };

    return { success: true, data: result };
  } catch (err: any) {
    return {
      success: false,
      error: err.message || "An error occurred while fetching call analytics.",
    };
  }
};

// This will get the number of calls by hour and day of the week for the heatmap chart
export const getCallVolumeByHourAndDay = async (
  fromDate?: Date,
  toDate?: Date,
  conversationTypes?: Filters["conversationTypes"],
  countries?: CountryCode[],
  callDuration?: Filters["callDuration"],
  ringingTime?: Filters["ringingTime"]
) => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return {
        success: false,
        error: entrepriseResponse.error || "Failed to fetch entreprise data.",
      };
    }

    const entreprise_id = entrepriseResponse.entreprise._id;

    const matchConditions: any = {
      entreprise_id: entreprise_id,
      type: "CALL",
      completed_time: { $exists: true, $ne: "" },
    };

    // Apply date filters as a combined condition if both exist
    if (fromDate || toDate) {
      matchConditions.parsedTimeStamp = {};
      if (fromDate) matchConditions.parsedTimeStamp.$gte = fromDate;
      if (toDate) matchConditions.parsedTimeStamp.$lte = toDate;
    }

    // Get analytics match conditions
    const typeMatchConditions = generateAnalyticsMatchCondition(
      conversationTypes,
      countries,
      callDuration,
      ringingTime
    );

    const callsByHourAndDay = await Conversation.aggregate([
      {
        $addFields: {
          parsedTimeStamp: {
            $dateFromString: { dateString: "$completed_time" },
          },
        },
      },
      {
        $match: matchConditions,
      },
      { $match: typeMatchConditions }, // Add conversation type filter
      {
        $addFields: {
          dayOfWeek: { $dayOfWeek: "$parsedTimeStamp" }, // 1 for Sunday, 2 for Monday, etc.
          hourOfDay: { $hour: "$parsedTimeStamp" },
          isMissed: {
            $or: [{ $eq: ["$status", "missed"] }, { $eq: ["$duration", "0"] }],
          },
          isAnswered: {
            $or: [
              { $eq: ["$status", "answered"] },
              { $gt: ["$duration", "0"] },
            ],
          },
        },
      },
      {
        $group: {
          _id: {
            dayOfWeek: "$dayOfWeek",
            hourOfDay: "$hourOfDay",
          },
          total: { $sum: 1 },
          missed: {
            $sum: { $cond: ["$isMissed", 1, 0] },
          },
          answered: {
            $sum: { $cond: ["$isAnswered", 1, 0] },
          },
        },
      },
      {
        $sort: {
          "_id.dayOfWeek": 1,
          "_id.hourOfDay": 1,
        },
      },
    ]);

    // Reorder days to start with Monday (as in the HeatmapChart)
    const orderedDays = [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ];

    // Initialize result with separate datasets for total, missed and answered calls
    const result = {
      total: orderedDays.map((day) => ({
        name: day,
        data: Array(24).fill(0),
      })),
      missed: orderedDays.map((day) => ({
        name: day,
        data: Array(24).fill(0),
      })),
      answered: orderedDays.map((day) => ({
        name: day,
        data: Array(24).fill(0),
      })),
    };

    // Fill in the actual data
    callsByHourAndDay.forEach((entry) => {
      // Convert from MongoDB dayOfWeek (1=Sunday, 2=Monday) to our array index
      // We need to convert to our ordered array where Monday is index 0
      const dayIndex = entry._id.dayOfWeek === 1 ? 6 : entry._id.dayOfWeek - 2;
      const hourIndex = entry._id.hourOfDay; // No adjustment needed - hourOfDay is already 0-based

      // Ensure the hour is within the expected range (0-23)
      if (hourIndex >= 0 && hourIndex < 24) {
        result.total[dayIndex].data[hourIndex] = entry.total;
        result.missed[dayIndex].data[hourIndex] = entry.missed;
        result.answered[dayIndex].data[hourIndex] = entry.answered;
      }
    });

    return {
      success: true,
      data: result,
    };
  } catch (err: any) {
    return {
      success: false,
      error:
        err.message ||
        "An error occurred while fetching call volume by hour and day.",
    };
  }
};

export const getNumberOfCallsByTimePeriod = async (
  groupBy: "day" | "week" | "month" | "weekday" | "hour",
  fromDate?: Date,
  toDate?: Date,
  conversationTypes?: Filters["conversationTypes"],
  countries?: CountryCode[],
  callDuration?: Filters["callDuration"],
  ringingTime?: Filters["ringingTime"]
) => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return {
        success: false,
        error: entrepriseResponse.error || "Failed to fetch entreprise data.",
      };
    }

    const entreprise_id = entrepriseResponse.entreprise._id;

    const matchConditions: any = {
      entreprise_id,
      type: "CALL",
      completed_time: { $exists: true, $ne: "" },
    };

    // Apply date filters as a combined condition if both exist
    if (fromDate || toDate) {
      matchConditions.parsedTimeStamp = {};
      if (fromDate) matchConditions.parsedTimeStamp.$gte = fromDate;
      if (toDate) matchConditions.parsedTimeStamp.$lte = toDate;
    }

    // Get analytics match conditions
    const typeMatchConditions = generateAnalyticsMatchCondition(
      conversationTypes,
      countries,
      callDuration,
      ringingTime
    );

    // Base pipeline stages
    const pipeline: any[] = [
      {
        $addFields: {
          parsedTimeStamp: {
            $dateFromString: { dateString: "$completed_time" },
          },
          isMissed: {
            $or: [{ $eq: ["$status", "missed"] }, { $eq: ["$duration", "0"] }],
          },
          isAnswered: {
            $or: [
              { $eq: ["$status", "answered"] },
              { $gt: ["$duration", "0"] },
            ],
          },
        },
      },
      {
        $match: matchConditions,
      },
      { $match: typeMatchConditions }, // Add conversation type filter
    ];

    // For weekday grouping, use the same approach as getCallVolumeByHourAndDay
    if (groupBy === "weekday") {
      pipeline.push(
        {
          $addFields: {
            dayOfWeek: { $dayOfWeek: "$parsedTimeStamp" }, // 1=Sunday, 2=Monday, etc.
          },
        },
        {
          $group: {
            _id: {
              dayOfWeek: "$dayOfWeek",
            },
            total: { $sum: 1 },
            missed: { $sum: { $cond: ["$isMissed", 1, 0] } },
            answered: { $sum: { $cond: ["$isAnswered", 1, 0] } },
          },
        },
        {
          $sort: {
            "_id.dayOfWeek": 1,
          },
        }
      );

      const results = await Conversation.aggregate(pipeline);

      // Reorder days to start with Monday (as in getCallVolumeByHourAndDay)
      const orderedDays = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
      ];

      // Initialize complete result with all days
      const completeResults = orderedDays.map((day, index) => {
        // MongoDB dayOfWeek: 1=Sunday, 2=Monday, etc.
        const mongoDayOfWeek = index === 6 ? 1 : index + 2;
        const foundDay = results.find(
          (r) => r._id.dayOfWeek === mongoDayOfWeek
        );

        return {
          date: day,
          total: foundDay?.total || 0,
          missed: foundDay?.missed || 0,
          answered: foundDay?.answered || 0,
        };
      });

      return {
        success: true,
        data: completeResults,
      };
    }

    // Original grouping logic for other periods (day, week, month, hour)
    let groupStage: any;
    switch (groupBy) {
      case "day":
        groupStage = {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$parsedTimeStamp" },
          },
          date: {
            $first: {
              $dateToString: { format: "%Y-%m-%d", date: "$parsedTimeStamp" },
            },
          },
        };
        break;
      case "week":
        groupStage = {
          _id: {
            year: { $year: "$parsedTimeStamp" },
            week: { $week: "$parsedTimeStamp" },
          },
          date: {
            $first: {
              $dateToString: {
                format: "Week %U, %Y",
                date: "$parsedTimeStamp",
              },
            },
          },
        };
        break;
      case "month":
        groupStage = {
          _id: {
            year: { $year: "$parsedTimeStamp" },
            month: { $month: "$parsedTimeStamp" },
          },
          date: {
            $first: {
              $dateToString: { format: "%b %Y", date: "$parsedTimeStamp" },
            },
          },
        };
        break;
      case "hour":
        groupStage = {
          _id: {
            hour: { $hour: "$parsedTimeStamp" },
          },
          date: {
            $first: {
              $concat: [
                { $toString: { $hour: "$parsedTimeStamp" } },
                ":00-",
                { $toString: { $add: [{ $hour: "$parsedTimeStamp" }, 1] } },
                ":00",
              ],
            },
          },
        };
        break;
      default:
        return {
          success: false,
          error: "Invalid grouping period specified",
        };
    }

    // Add common group fields
    groupStage.total = { $sum: 1 };
    groupStage.missed = { $sum: { $cond: ["$isMissed", 1, 0] } };
    groupStage.answered = { $sum: { $cond: ["$isAnswered", 1, 0] } };

    pipeline.push({ $group: groupStage });

    // Sorting logic
    let sortStage: any = {};
    switch (groupBy) {
      case "day":
        sortStage = { _id: 1 };
        break;
      case "week":
        sortStage = { "_id.year": 1, "_id.week": 1 };
        break;
      case "month":
        sortStage = { "_id.year": 1, "_id.month": 1 };
        break;
      case "hour":
        sortStage = { "_id.hour": 1 };
        break;
    }

    pipeline.push({ $sort: sortStage });

    // Projection
    pipeline.push({
      $project: {
        _id: 0,
        date: 1,
        total: 1,
        missed: 1,
        answered: 1,
      },
    });

    const results = await Conversation.aggregate(pipeline);

    // For hour grouping, ensure all hours 0-23 are present
    if (groupBy === "hour") {
      const hourMap = new Map(
        results.map((item) => [parseInt(item.date.split(":")[0]), item])
      );

      const completeResults = Array.from({ length: 24 }, (_, hour) => {
        const existing = hourMap.get(hour);
        return (
          existing || {
            date: `${hour}:00-${hour + 1}:00`,
            total: 0,
            missed: 0,
            answered: 0,
          }
        );
      });

      return {
        success: true,
        data: completeResults,
      };
    }

    return {
      success: true,
      data: results,
    };
  } catch (err: any) {
    return {
      success: false,
      error: err.message || "An error occurred while fetching call statistics.",
    };
  }
};

// New server action to fetch conversations per country
export const getConversationsByCountry = async (
  fromDate?: Date,
  toDate?: Date,
  conversationTypes?: Filters["conversationTypes"],
  countries?: CountryCode[],
  callDuration?: Filters["callDuration"],
  ringingTime?: Filters["ringingTime"]
): Promise<{
  success: boolean;
  data?: Array<{ country: string; conversations: number }>;
  error?: string;
}> => {
  await dbConnect();
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }
    const entrepriseID = entrepriseResponse.entreprise._id;
    const matchConditions: any = {
      entreprise_id: entrepriseID,
      // ...existing code...
    };
    if (fromDate || toDate) {
      matchConditions.parsedTimeStamp = {};
      if (fromDate) matchConditions.parsedTimeStamp.$gte = fromDate;
      if (toDate) matchConditions.parsedTimeStamp.$lte = toDate;
    }
    const typeMatchConditions = generateAnalyticsMatchCondition(
      conversationTypes,
      countries,
      callDuration,
      ringingTime
    );
    const stats = await Conversation.aggregate([
      {
        $addFields: {
          parsedTimeStamp: {
            $dateFromString: { dateString: "$completed_time" },
          },
          clientCountry: {
            $cond: [
              { $eq: ["$direction", "inbound"] },
              "$from_country",
              "$to_country",
            ],
          },
        },
      },
      { $match: matchConditions },
      { $match: typeMatchConditions },
      {
        $group: {
          _id: "$clientCountry",
          conversations: { $sum: 1 },
        },
      },
      { $project: { country: "$_id", conversations: 1, _id: 0 } },
    ]);

    return { success: true, data: stats };
  } catch (err: any) {
    console.error("Error in getConversationsByCountry:", err.message);
    return { success: false, error: err.message };
  }
};

// This function gets the total call duration broken down by call types
export const getTotalCallDurationByType = async (
  fromDate?: Date,
  toDate?: Date,
  conversationTypes?: Filters["conversationTypes"],
  countries?: CountryCode[],
  callDuration?: Filters["callDuration"],
  ringingTime?: Filters["ringingTime"]
): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return {
        success: false,
        error: entrepriseResponse.error || "Failed to fetch entreprise data.",
      };
    }

    const entreprise_id = entrepriseResponse.entreprise._id;

    const matchConditions: any = {
      entreprise_id: entreprise_id,
      type: { $in: ["CALL", "MEET"] },
      duration: { $exists: true, $ne: "" },
    };

    // Apply date filters as a combined condition if both exist
    if (fromDate || toDate) {
      matchConditions.parsedTimeStamp = {};
      if (fromDate) matchConditions.parsedTimeStamp.$gte = fromDate;
      if (toDate) matchConditions.parsedTimeStamp.$lte = toDate;
    }

    // Get analytics match conditions
    const typeMatchConditions = generateAnalyticsMatchCondition(
      conversationTypes,
      countries,
      callDuration,
      ringingTime
    );

    const callDurationData = await Conversation.aggregate([
      {
        $addFields: {
          parsedTimeStamp: {
            $dateFromString: { dateString: "$completed_time" },
          },
          durationInSeconds: { $toInt: "$duration" },
          isInbound: {
            $regexMatch: {
              input: { $ifNull: ["$direction", ""] },
              regex: /inbound/i,
            },
          },
        },
      },
      {
        $match: matchConditions,
      },
      { $match: typeMatchConditions },
      {
        $group: {
          _id: {
            type: "$type",
            isInbound: "$isInbound",
          },
          totalDuration: { $sum: "$durationInSeconds" },
        },
      },
      {
        $project: {
          _id: 0,
          type: "$_id.type",
          isInbound: "$_id.isInbound",
          totalDuration: 1,
        },
      },
    ]);

    console.log("Total Call Duration by Type:", callDurationData);

    // Initialize the result structure
    const result = {
      CALL: { inbound: 0, outbound: 0, total: 0 },
      MEET: { total: 0 },
      grandTotal: 0,
    };

    // Fill in the actual durations
    callDurationData.forEach((item) => {
      if (item.type === "CALL") {
        if (item.isInbound) {
          result.CALL.inbound = item.totalDuration;
        } else {
          result.CALL.outbound = item.totalDuration;
        }
        result.CALL.total += item.totalDuration;
      } else if (item.type === "MEET") {
        result.MEET.total += item.totalDuration;
      }
    });

    // Calculate grand total
    result.grandTotal = result.CALL.total + result.MEET.total;

    return {
      success: true,
      data: result,
    };
  } catch (err: any) {
    return {
      success: false,
      error:
        err.message ||
        "An error occurred while fetching total call duration data.",
    };
  }
};

export const getConversationMetrics = async (
  fromDate?: Date,
  toDate?: Date,
  conversationTypes?: Filters["conversationTypes"],
  countries?: CountryCode[],
  callDuration?: Filters["callDuration"],
  ringingTime?: Filters["ringingTime"]
) => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return {
        success: false,
        error: entrepriseResponse.error || "Failed to fetch entreprise data.",
      };
    }

    const entreprise_id = entrepriseResponse.entreprise._id;

    const matchConditions: any = {
      entreprise_id: entreprise_id,
      type: "CALL",
      status: "ANSWERED",
      duration: { $ne: "0", $exists: true },
    };

    // Apply date filters as a combined condition if both exist
    if (fromDate || toDate) {
      matchConditions.parsedTimeStamp = {};
      if (fromDate) matchConditions.parsedTimeStamp.$gte = fromDate;
      if (toDate) matchConditions.parsedTimeStamp.$lte = toDate;
    }

    // Get analytics match conditions
    const typeMatchConditions = generateAnalyticsMatchCondition(
      conversationTypes,
      countries,
      callDuration,
      ringingTime
    );

    const analytics = await Conversation.aggregate([
      {
        $addFields: {
          parsedTimeStamp: {
            $dateFromString: { dateString: "$completed_time" },
          },
          durationInSeconds: { $toInt: "$duration" },
          ringingDurationSeconds: {
            $divide: [
              {
                $subtract: [
                  { $dateFromString: { dateString: "$inprogress_time" } },
                  { $dateFromString: { dateString: "$ringing_time" } },
                ],
              },
              1000,
            ],
          },
        },
      },
      {
        $match: matchConditions,
      },
      { $match: typeMatchConditions }, // Add conversation type filter
      {
        $group: {
          _id: null,
          averageCallDuration: { $avg: "$durationInSeconds" },
          maxCallDuration: { $max: "$durationInSeconds" },
          averageRingingTime: { $avg: "$ringingDurationSeconds" },
          maxRingingTime: { $max: "$ringingDurationSeconds" },
          totalCallDuration: { $sum: "$durationInSeconds" },
          averageClientEngagement: { $avg: "$client_engagement" },
          totalCalls: { $sum: 1 },
        },
      },
    ]);

    // Create a separate query for MEET conversations to find most talkative speaker
    const meetMatchConditions: any = {
      entreprise_id: entreprise_id,
      type: "MEET",
      most_talkative_speaker_name: { $exists: true, $ne: "" },
    };

    if (fromDate || toDate) {
      meetMatchConditions.parsedTimeStamp = {};
      if (fromDate) meetMatchConditions.parsedTimeStamp.$gte = fromDate;
      if (toDate) meetMatchConditions.parsedTimeStamp.$lte = toDate;
    }

    const meetAnalytics = await Conversation.aggregate([
      {
        $addFields: {
          parsedTimeStamp: {
            $dateFromString: { dateString: "$completed_time" },
          },
        },
      },
      {
        $match: meetMatchConditions,
      },
      { $match: typeMatchConditions }, // Apply same filters as above
      {
        $group: {
          _id: "$most_talkative_speaker_name",
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 }, // Sort by count in descending order
      },
      {
        $limit: 1, // Get only the top speaker
      },
    ]);

    const mostTalkativeSpeaker =
      meetAnalytics.length > 0
        ? {
            name: meetAnalytics[0]._id,
            count: meetAnalytics[0].count,
          }
        : { name: "", count: 0 };

    if (analytics.length === 0) {
      return {
        success: true,
        data: {
          averageCallDuration: 0,
          maxCallDuration: 0,
          averageRingingTime: 0,
          maxRingingTime: 0,
          totalCallDuration: 0,
          averageClientEngagement: 0,
          most_talkative_speaker_name: mostTalkativeSpeaker.name,
          mostTalkativeSpeakerCount: mostTalkativeSpeaker.count,
        },
      };
    }

    const result = {
      averageCallDuration: Math.round(analytics[0].averageCallDuration) || 0,
      maxCallDuration: analytics[0].maxCallDuration || 0,
      averageRingingTime: Math.round(analytics[0].averageRingingTime) || 0,
      maxRingingTime: analytics[0].maxRingingTime || 0,
      totalCallDuration: analytics[0].totalCallDuration || 0,
      averageClientEngagement: analytics[0].averageClientEngagement
        ? Math.round(analytics[0].averageClientEngagement * 10) / 10 // Round to 1 decimal place
        : 0,
      most_talkative_speaker_name: mostTalkativeSpeaker.name,
      mostTalkativeSpeakerCount: mostTalkativeSpeaker.count,
    };

    return { success: true, data: result };
  } catch (err: any) {
    return {
      success: false,
      error:
        err.message || "An error occurred while fetching conversation metrics.",
    };
  }
};
