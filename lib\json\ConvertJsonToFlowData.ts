let autoIdCounter = 0;

export function convertDynamicJsonToFlowData(data: any): {
  nodes: any[];
  edges: any[];
} {
  const nodes: any[] = [];
  const edges: any[] = [];

  // Function to generate unique IDs for nodes and edges
  function generateId() {
    return `auto-${autoIdCounter++}`;
  }

  // Function to extract a human-readable label for each node based on key-value pairs
  function extractLabel(key: string, value: any): string {
    if (typeof value === "object" && value !== null) {
      return key;
    }
    return `${key}: ${value}`;
  }

  // Track the positioning of nodes on the canvas
  let x = 0;
  let y = 0;

  // Recursive function to traverse the dynamic JSON and create nodes and edges
  function traverse(node: any, parentId: string | null = null): string {
    const currentNodeId = generateId(); // Unique ID for the current node

    // For each key-value pair in the node (object), create a new node for the attribute
    for (const [key, value] of Object.entries(node)) {
      const attributeNodeId = generateId(); // Unique ID for the attribute node

      // Create the attribute node
      nodes.push({
        id: attributeNodeId,
        data: { label: extract<PERSON>abel(key, value) }, // The label for the node is the key-value pair
        position: { x, y }, // Set the position of the node dynamically
      });

      // Create an edge between the parent and current attribute node if there's a parent
      if (parentId) {
        edges.push({
          id: `e${parentId}-${attributeNodeId}`,
          source: parentId,
          target: attributeNodeId,
          animated: true, // Animated edge
        });
      }

      // Adjust node position after adding a new one
      x += 200;
      if (x > 800) {
        x = 0;
        y += 150;
      }

      // If the value is an object or array, we need to traverse its properties as well
      if (typeof value === "object" && value !== null) {
        traverse(value, attributeNodeId); // Recursive call to handle nested structures
      }
    }

    return currentNodeId;
  }

  // Start traversing the initial JSON data
  traverse(data);

  // Return the nodes and edges arrays
  return { nodes, edges };
}
