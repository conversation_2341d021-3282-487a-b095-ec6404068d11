"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import AccountCircleRoundedIcon from "@mui/icons-material/AccountCircleRounded";
import CallEndRoundedIcon from "@mui/icons-material/CallEndRounded";
import ContentCopyRoundedIcon from "@mui/icons-material/ContentCopyRounded";
import {
  BotIcon,
  ExternalLink,
  MessageSquareTextIcon,
  PhoneCallIcon,
  PhoneMissed,
  RedoDotIcon,
  VoicemailIcon,
} from "lucide-react";
import { handleCopyText } from "@/lib/Strings/Copy";
import { getTimeFromTimestamp } from "@/lib/Strings/DateFormat";
import { IoLogoWhatsapp } from "react-icons/io";
import { toast } from "sonner";
import { checkGoalExists, GetGoalTemplateType } from "@/actions/GoalActions";
import { useRouter } from "next/navigation";
import NumberHistory from "./NumberHistory";
import { useTranslation } from "react-i18next";

type CallDetailsProps = {
  callDetails: any;
};

export function TimelineItem({
  icon,
  label,
  timestamp,
  color = "transparent",
}: {
  icon: React.ReactNode;
  label: string;
  timestamp: string;
  color?: string;
  showArc?: boolean;
}) {
  const { t } = useTranslation("callLogs");

  return (
    <div className="w-60 flex flex-col justify-start">
      {/* Icon and Label */}
      <div className="flex gap-2 items-center rtl:flex-row-reverse">
        <div
          className={`w-6 h-6 ${color} rounded-full flex items-center justify-center`}
        >
          {icon}
        </div>
        <div className="rtl:text-right text-black/80 dark:text-voxa-neutral-200 font-medium capitalize">
          {label.toLowerCase() === "answered"
            ? t("notesTabs.timeline.completed")
            : label}
          <p className="text-xs text-voxa-neutral-900 dark:text-voxa-neutral-500">
            {timestamp}
          </p>
        </div>
      </div>

      {/* SVG Arc Line */}
      {/* {false && (
        <svg
          width="40"
          height="60"
          viewBox="0 0 40 80"
          className="absolute left-[12px] top-[60%] text-voxa-teal-600 fill-voxa-teal-600 stroke-voxa-teal-600"
        >
          <path
            d="M20,0 
               C-10,40 -10,40 20,80"
            fill="none"
            strokeWidth="2"
          />
        </svg>
      )} */}
    </div>
  );
}

export function NotesTabs({ callDetails }: CallDetailsProps) {
  const { t, i18n } = useTranslation("callLogs");

  const ringing_time = getTimeFromTimestamp(
    callDetails.ringing_time,
    i18n.language
  );
  const inprogress_time = getTimeFromTimestamp(
    callDetails.inprogress_time,
    i18n.language
  );
  const completed_time = getTimeFromTimestamp(
    callDetails.completed_time,
    i18n.language
  );
  const initiated_time = getTimeFromTimestamp(
    callDetails.initiated_time,
    i18n.language
  );
  const forwarded_time = getTimeFromTimestamp(
    callDetails.forwarded_time,
    i18n.language
  );

  const isInbound = callDetails.Direction?.toLowerCase() === "inbound";
  const isVoicemail = callDetails.voicemailDetected;
  const isTransferred = callDetails.forwarded_time;
  const isTransfer = callDetails.is_transfer;
  const isFailed = callDetails.CallStatus?.toLowerCase() === "failed";
  const isMissed = callDetails.CallStatus?.toLowerCase() === "missed";

  console.log(callDetails.who_hang_up);
  const wasPickedUp = !["MISSED", "no-answer"].includes(
    callDetails.who_hang_up
  );

  const whoHangUp = callDetails.who_hang_up as keyof typeof hangUpLabels;

  const hangUpLabels = {
    AI: t("notesTabs.timeline.callEndedBy.ai"),
    Client: t("notesTabs.timeline.callEndedBy.client"),
    SM: t("notesTabs.timeline.callEndedBy.support"),
    TW: t("notesTabs.timeline.callEndedBy.thirdParty"),
    machine_end_other: t("notesTabs.timeline.callEndedBy.machineDetected"),
    "no-answer": t("notesTabs.timeline.callEndedBy.noAnswer"),
    completed:
      callDetails.CallDuration < 10
        ? t("notesTabs.timeline.callEndedBy.noAnswer")
        : t("notesTabs.timeline.callEndedBy.callCompleted"),
  } as const;

  const router = useRouter();

  const redirectToGoal = async () => {
    try {
      const response = await checkGoalExists(callDetails.goalId);
      if (!response.success) {
        toast.error(response.error);
        return;
      }

      const templateRes = await GetGoalTemplateType(callDetails.goalId);
      if (!templateRes.success) {
        toast.error(templateRes.error);
        return;
      }

      router.push(
        `/businessDash/assistants/Goals/CreateEdit/${callDetails.goalId}?templateType=${templateRes.templateType}`
      );
    } catch (err: any) {
      console.error(err);
      toast.error("Error redirecting to goal");
    }
  };

  const renderCallFlow = () => (
    <>
      {/* Start of Call */}
      <TimelineItem
        icon={
          isTransfer ? (
            <RedoDotIcon className="w-4 h-4 dark:text-voxa-neutral-200" />
          ) : (
            <BotIcon className="w-6 h-6 text-green-500" />
          )
        }
        label={
          isTransfer
            ? `${t("notesTabs.timeline.transferedFrom")} ${callDetails.From}`
            : isInbound
            ? t("notesTabs.timeline.incoming")
            : t("notesTabs.timeline.outgoing")
        }
        timestamp={!isInbound ? initiated_time : ""}
        color={isTransfer ? "bg-purple-500" : ""}
        showArc={true}
      />

      <TimelineItem
        icon={
          <div className="rounded-full animate-pulse">
            <PhoneCallIcon className="w-5 h-5 text-voxa-teal-600" />
          </div>
        }
        label={t("notesTabs.timeline.ringing")}
        timestamp={!isInbound ? ringing_time : ""}
        showArc={true}
      />

      {/* Call Outcomes */}
      {isInbound && (
        <TimelineItem
          icon={
            <PhoneMissed className="w-[1.1rem] h-[1.1rem] text-red-500 dark:text-voxa-neutral-200" />
          }
          label={t("notesTabs.timeline.callEnded")}
          timestamp={!isInbound ? completed_time : ""}
        />
      )}

      {!isInbound && isVoicemail && (
        <TimelineItem
          icon={
            <VoicemailIcon className="w-5 h-5 dark:text-voxa-neutral-200" />
          }
          label={t("notesTabs.timeline.voicemail")}
          timestamp={!isInbound ? completed_time : ""}
          color="bg-indigo-500"
        />
      )}

      {!isInbound &&
        !isVoicemail &&
        wasPickedUp &&
        !isTransfer &&
        !isFailed && (
          <>
            {/* Picked Up */}
            <TimelineItem
              icon={
                <AccountCircleRoundedIcon className="dark:text-voxa-neutral-200" />
              }
              label={t("notesTabs.timeline.pickedUp")}
              timestamp={!isInbound ? inprogress_time : ""}
              showArc={true}
            />

            {/* Transfer or End */}
            {isTransferred ? (
              <>
                <TimelineItem
                  icon={
                    <RedoDotIcon className="w-4 h-4 dark:text-voxa-neutral-200" />
                  }
                  label={t("notesTabs.timeline.transferedTo")}
                  timestamp={!isInbound ? forwarded_time : ""}
                  color="bg-purple-500"
                />
                <TimelineItem
                  icon={
                    <CallEndRoundedIcon
                      className="fill-voxa-neutral-200"
                      sx={{ fontSize: "17px" }}
                    />
                  }
                  label={t("notesTabs.timeline.callEnded")}
                  timestamp={!isInbound ? completed_time : ""}
                  color="bg-red-500"
                />
              </>
            ) : (
              <TimelineItem
                icon={
                  <CallEndRoundedIcon
                    className="dark:text-voxa-neutral-200"
                    sx={{ fontSize: "17px" }}
                  />
                }
                label={
                  hangUpLabels[whoHangUp] ??
                  (typeof callDetails.CallStatus === "string"
                    ? callDetails.CallStatus.toLowerCase()
                    : "Unknown")
                }
                timestamp={!isInbound ? completed_time : ""}
                color="bg-red-500"
              />
            )}
          </>
        )}

      {isTransfer &&
        (!isFailed && !isMissed ? (
          <>
            {/* Picked Up */}
            <TimelineItem
              icon={
                <AccountCircleRoundedIcon className="dark:text-voxa-neutral-200" />
              }
              label={t("notesTabs.timeline.pickedUpAgent")}
              timestamp={!isInbound ? inprogress_time : ""}
              showArc={true}
            />

            <TimelineItem
              icon={
                <CallEndRoundedIcon
                  className="dark:text-voxa-neutral-200"
                  sx={{ fontSize: "17px" }}
                />
              }
              label={t("notesTabs.timeline.callEnded")}
              timestamp={!isInbound ? completed_time : ""}
              color="bg-red-500"
            />
          </>
        ) : (
          <TimelineItem
            icon={
              <CallEndRoundedIcon
                className="dark:text-voxa-neutral-200"
                sx={{ fontSize: "17px" }}
              />
            }
            label={callDetails.CallStatus?.toLowerCase()}
            timestamp={!isInbound ? completed_time : ""}
            color="bg-red-500"
          />
        ))}

      {!wasPickedUp && !isVoicemail && !isInbound && !isTransfer && (
        <>
          <TimelineItem
            icon={
              <PhoneMissed className="w-[1.1rem] h-[1.1rem] text-red-500" />
            }
            label={t("notesTabs.timeline.missedCall")}
            timestamp={!isInbound ? completed_time : ""}
          />
          {callDetails.child_type?.toLowerCase() === "sms" ? (
            <TimelineItem
              icon={<MessageSquareTextIcon className="w-5 h-5 text-teal-500" />}
              label={t("notesTabs.timeline.sentMessage")}
              timestamp={completed_time}
            />
          ) : callDetails.child_type?.toLowerCase() === "whatsapp" ? (
            <TimelineItem
              icon={<IoLogoWhatsapp className="text-teal-500 w-5 h-5" />}
              label={t("notesTabs.timeline.sentWhatsapp")}
              timestamp={completed_time}
            />
          ) : (
            ""
          )}
        </>
      )}
    </>
  );

  const renderMessageFlow = () => {
    if (isInbound) {
      const type = callDetails.type?.toLowerCase();

      if (type === "sms") {
        return (
          <TimelineItem
            icon={<MessageSquareTextIcon className="w-5 h-5 text-teal-500" />}
            label={t("notesTabs.timeline.receivedMessage")}
            timestamp={initiated_time}
          />
        );
      }

      if (type === "whatsapp") {
        return (
          <TimelineItem
            icon={<IoLogoWhatsapp className="text-teal-500 w-6 h-6" />}
            label={t("notesTabs.timeline.receivedWhatsapp")}
            timestamp={initiated_time}
          />
        );
      }

      return null;
    }
    const type = callDetails.type?.toLowerCase();

    return (
      <>
        <TimelineItem
          icon={<BotIcon className="w-6 h-6 text-green-500" />}
          label={t("notesTabs.timeline.outgoing")}
          timestamp=""
          showArc={true}
        />
        <TimelineItem
          icon={
            <div className="rounded-full animate-pulse">
              <PhoneCallIcon className="w-5 h-5 text-voxa-teal-600" />
            </div>
          }
          label={t("notesTabs.timeline.ringing")}
          timestamp=""
          showArc={true}
        />
        <TimelineItem
          icon={
            <PhoneMissed className="w-[1.1rem] h-[1.1rem] dark:text-voxa-neutral-200" />
          }
          label={t("notesTabs.timeline.missedCall")}
          timestamp=""
          color="bg-red-500"
        />
        {type === "sms" && (
          <TimelineItem
            icon={<MessageSquareTextIcon className="w-5 h-5 text-teal-500" />}
            label={t("notesTabs.timeline.sentMessage")}
            timestamp={initiated_time}
          />
        )}
        {type === "whatsapp" && (
          <TimelineItem
            icon={<IoLogoWhatsapp className="text-teal-500 w-6 h-6" />}
            label={t("notesTabs.timeline.sentWhatsapp")}
            timestamp={initiated_time}
          />
        )}
      </>
    );
  };
  return (
    <Tabs defaultValue="account" className="w-full p-0 mt-4 mb-2">
      <TabsList className="grid w-full grid-cols-2 rounded-full border bg-voxa-neutral-100 dark:bg-voxa-neutral-950">
        <TabsTrigger
          value="account"
          className="rtl:order-2 rounded-full text-xs"
        >
          {callDetails.type?.toLowerCase() === "sms"
            ? t("notesTabs.messageFlow")
            : callDetails.type?.toLowerCase() === "whatsapp"
            ? t("notesTabs.whatsappFlow")
            : t("notesTabs.callFlow")}
        </TabsTrigger>
        <TabsTrigger
          value="password"
          className="rtl:order-1 rounded-full text-xs"
        >
          {t("notesTabs.password")}
        </TabsTrigger>
      </TabsList>

      {/* Call Flow Tab */}
      <TabsContent value="account">
        <Card className="h-full bg-voxa-neutral-100 dark:bg-voxa-neutral-950 pb-4">
          <CardHeader>
            <CardTitle className="text-voxa-teal-600 text-xl text-center pb-1">
              {callDetails.type?.toLowerCase() === "sms"
                ? t("notesTabs.messageFlow")
                : callDetails.type?.toLowerCase() === "whatsapp"
                ? t("notesTabs.whatsappFlow")
                : t("notesTabs.callFlow")}
            </CardTitle>
            {callDetails.type && (
              <CardDescription className="text-voxa-neutral-900 dark:text-voxa-neutral-200 rtl:text-right">
                {t("notesTabs.trackFlow")}{" "}
                {callDetails.type?.toLowerCase() === "sms"
                  ? t("notesTabs.message")
                  : callDetails.type?.toLowerCase() === "whatsapp"
                  ? t("notesTabs.whatsapp")
                  : t("notesTabs.call")}
              </CardDescription>
            )}
          </CardHeader>

          <CardContent className="flex flex-col gap-4 items-center">
            {callDetails.type?.toLowerCase() === "sms"
              ? renderMessageFlow()
              : callDetails.type?.toLowerCase() === "whatsapp"
              ? renderMessageFlow()
              : renderCallFlow()}
          </CardContent>

          <CardFooter className="bottom-0 flex flex-col gap-3 mt-4 px-4 pb-2 text-left rtl:text-right">
            <Button
              onClick={() => handleCopyText(callDetails.CallSid)}
              className="bg-voxa-neutral-500 dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-400 dark:hover:bg-voxa-neutral-800active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950 transition-all text-xs duration-150 text-white font-medium gap-1 w-full rounded-full px-4 flex rtl:flex-grow rtl:flex-row-reverse text-left rtl:text-right"
            >
              <p className="text-nowrap font-semibold">
                {t("notesTabs.callSid")}
              </p>
              <p className="w-full truncate">{callDetails.CallSid}</p>
              <ContentCopyRoundedIcon
                className="text-white w-2 h-2"
                fontSize="inherit"
              />
            </Button>
            {callDetails.parent_call_sid && (
              <Button
                onClick={() => handleCopyText(callDetails.parent_call_sid)}
                className="bg-voxa-neutral-500 dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-400 dark:hover:bg-voxa-neutral-800active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950 transition-all text-xs duration-150 text-white font-medium gap-1 w-full rounded-full px-4 flex rtl:flex-grow rtl:flex-row-reverse text-left rtl:text-right"
              >
                <p className="text-nowrap font-semibold">
                  {t("notesTabs.parentSid")}
                </p>
                <p className="w-full truncate">{callDetails.parent_call_sid}</p>
                <ContentCopyRoundedIcon
                  className="text-white"
                  fontSize="medium"
                />
              </Button>
            )}
            {callDetails.child_call_sid && (
              <Button
                onClick={() => handleCopyText(callDetails.child_call_sid)}
                className="bg-voxa-neutral-500 dark:bg-voxa-neutral-900 hover:bg-voxa-neutral-400 dark:hover:bg-voxa-neutral-800active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950 transition-all text-xs duration-150 text-white py-2 rounded-full font-medium gap-1 w-full px-4 flex rtl:flex-grow rtl:flex-row-reverse text-left rtl:text-right"
              >
                <p className="text-nowrap font-semibold">
                  {t("notesTabs.childSid")}
                </p>
                <p className="w-full truncate">{callDetails.child_call_sid}</p>
                <ContentCopyRoundedIcon
                  className="text-white"
                  fontSize="medium"
                />
              </Button>
            )}
            {callDetails.goalId && (
              <Button
                onClick={() => {
                  handleCopyText(callDetails.goalId);
                }}
                className="bg-voxa-neutral-500 dark:bg-voxa-neutral-900 text-xs cursor-pointer hover:bg-voxa-neutral-400 dark:hover:bg-voxa-neutral-800 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950 transition-all duration-150 text-white py-2 rounded-full font-medium gap-1 w-full px-4 flex rtl:flex-grow rtl:flex-row-reverse text-left rtl:text-right"
              >
                <p className="text-nowrap font-semibold">
                  {t("notesTabs.goalId")}
                </p>
                <p className="w-full truncate">{callDetails.goalId}</p>

                {/* Wrap the icons in a container */}
                <div className="flex items-center gap-2">
                  <div
                    className="p-0.5 rounded hover:bg-voxa-neutral-300   dark:hover:bg-voxa-neutral-700"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      redirectToGoal();
                    }}
                  >
                    <ExternalLink
                      className="dark:text-purple-400 text-purple-700/60 cursor-pointer w-4 h-4"
                      fontSize="medium"
                    />
                  </div>
                  <ContentCopyRoundedIcon
                    className="text-white cursor-pointer"
                    fontSize="inherit"
                    style={{ width: "16px", height: "16px" }}
                  />
                </div>
              </Button>
            )}
          </CardFooter>
        </Card>
      </TabsContent>
      {/* History Tab */}
      <TabsContent value="password">
        <NumberHistory conversationID={callDetails._id} to={callDetails.To} />
      </TabsContent>
    </Tabs>
  );
}
