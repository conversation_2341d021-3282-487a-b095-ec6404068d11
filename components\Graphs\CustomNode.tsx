import { Handle, Position, NodeProps } from 'reactflow';

const CustomNode = ({ data, sourcePosition = Position.Bottom, targetPosition = Position.Top }: NodeProps) => {
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-300 p-4 text-sm max-w-[200px]">
      <div className="text-black whitespace-pre-wrap">{data.label}</div>
      <Handle type="source" position={sourcePosition} className="bg-blue-500" />
      <Handle type="target" position={targetPosition} className="bg-green-500" />
    </div>
  );
};

export default CustomNode;
