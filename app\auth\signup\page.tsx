"use client";
import React, { useState } from "react";
import <PERSON> from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { signIn } from "next-auth/react";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { useRouter } from "next/navigation";
import LoginWithButton from "@/components/buttons/LoginWithButton";
import DividerComponent from "@/components/dividers/divider";
import ErrorIcon from "@mui/icons-material/Error";
import { useTranslation } from "react-i18next";

const Login: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [pending, setPending] = useState(false);
  const router = useRouter();
  const { t } = useTranslation(["signup", "common"]);

  const formSubmitted = async (event: React.FormEvent) => {
    event.preventDefault();
    setPending(true);
    setErrorMessage(null);
    try {
      const SignupResponse = await fetch("/api/auth/signup/entreprise", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password, name }),
      });
      if (SignupResponse.status !== 201) {
        const data = await SignupResponse.json();
        console.log("Signup error:", data.error);
        setErrorMessage(data.error);
        setPending(false);
        return;
      } else {
        const res = await signIn("credentials", {
          redirect: false,
          email,
          password,
          name,
        });

        if (res?.error) {
          console.log("Login error:", res.error);
          setErrorMessage(res.error);
          setPending(false);
        } else {
          const updatedSession = await fetch("/api/auth/session");
          const sessionData = await updatedSession.json();
          setPending(false);
          if (sessionData?.user?.role === "ADMIN") {
            router.push("/admin-dash");
          } else if (sessionData?.user?.role === "ENTREPRISE_AGENT") {
            router.push("/agent-dash");
          } else if (sessionData?.user?.role === "ENTREPRISE_ADMIN") {
            router.push("/businessDash");
          } else {
            router.push("/");
          }
        }
      }
    } catch (error) {
      console.error("Login error:", error);
      setErrorMessage("An error occurred during login");
      setPending(false);
    }
  };

  return (
    <div className="w-full lg:grid lg:min-h-[100vh] xl:min-h-[100vh]">
      <div className="flex items-center justify-center py-12 bg-secondary-600 px-6">
        <div className="mx-auto grid max-w-sm gap- max-sm:mt-20 max-sm:px-2 w-full">
          <div className="grid gap-2 text-center">
            <h1 className="text-3xl font-bold">{t("common:logo")}</h1>
            <p className="text-green-500 dark:text-green-400 text-xs text-start flex gap-2 items-center">
              <ErrorIcon fontSize="small" />
              <span>{t("signup:subtitle")}</span>
            </p>
          </div>
          <form onSubmit={formSubmitted} className="grid gap-3 mt-8">
            <div className="grid gap-2">
              <Label htmlFor="name">{t("signup:business")}</Label>
              <Input
                id="name"
                type="name"
                required
                className="border-secondary-900"
                name="name"
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                }}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">{t("signup:email")}</Label>
              <Input
                id="email"
                type="email"
                required
                className="border-secondary-900"
                name="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">{t("signup:password")}</Label>
              <Input
                id="password"
                type="password"
                name="password"
                required
                className="border-secondary-900"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <SignupButton pending={pending} />
            {errorMessage && (
              <p className="text-sm text-red-500">{errorMessage}</p>
            )}
          </form>
          <DividerComponent text={t("signup:or")} />
          <div className="flex gap-4 flex-col">
            <LoginWithButton provider="google" />
            {/* <LoginWithButton provider="microsoft" /> */}
          </div>
          <div className="mt-8 text-center text-sm">
            {t("signup:alreadyAccount")}
            <Link href="/" className="underline">
              {t("signup:Login")}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

function SignupButton({ pending }: { pending: boolean }) {
  const { t } = useTranslation("signup");
  return (
    <Button className="mt-4 w-full" aria-disabled={pending}>
      <p>{pending ? t("signing") : t("signup")}</p>
      <ChevronRightIcon className="h-6 w-6 rtl:rotate-180" fontSize="large" />
    </Button>
  );
}

export default Login;
