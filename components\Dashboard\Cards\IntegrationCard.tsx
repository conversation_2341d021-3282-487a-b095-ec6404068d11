import Image, { StaticImageData } from "next/image";
import { useTranslation } from "react-i18next";

interface IntegrationCard {
  icon: string | StaticImageData;
  name: string;
  bgColor: string;
  description?: string;
  comingSoon?: boolean;
  buttonText?: string;
  onClick: () => void;
}

export interface IntegrationCardsProps {
  title?: string;
  items: IntegrationCard[];
}

const IntegrationCard = ({ item }: { item: IntegrationCard }) => {
  const { t } = useTranslation("integrations");
  return (
    <div className="relative hover:scale-102 transition-transform duration-300">
      <div className="relative border bg-sidebar border-sidebar-border dark:border-voxa-bg-main/80 rounded-lg overflow-hidden">
        <div className={`p-6 ${item.bgColor} flex justify-center items-center`}>
          <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center">
            <Image src={item.icon} alt={item.name} className="w-12 h-12" />
          </div>
        </div>
        <div className="p-4 flex justify-between items-center">
          <p className="text-lg font-semibold text-voxa-bg-normal dark:text-voxa-secondary-normal">
            {item.name}
          </p>
          <button
            onClick={item.onClick}
            className="bg-voxa-teal-500 text-white py-2 px-4 rounded-full hover:bg-voxa-teal-600 transition-colors"
          >
            {item.buttonText
              ? item.buttonText
              : item.name === t("productivity.items.0")
              ? t("visit")
              : t("connect")}
          </button>
        </div>
      </div>

      {item.comingSoon && (
        <div className="absolute -top-2.5 right-4 text-xs rounded-full bg-neutral-500 text-white py-1 px-2 z-50 font-medium">
          Coming Soon
        </div>
      )}
    </div>
  );
};

export const IntegrationCards = ({ title, items }: IntegrationCardsProps) => {
  return (
    <div>
      <h2 className="pl-3 rtl:pl-0 rtl:pr-3 text-xl font-semibold text-voxa-bg-normal dark:text-voxa-secondary-normal mb-5">
        {title}
      </h2>
      <div className="grid gap-4 grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3">
        {items.map((item, index) => (
          <IntegrationCard key={index} item={item} />
        ))}
      </div>
    </div>
  );
};
