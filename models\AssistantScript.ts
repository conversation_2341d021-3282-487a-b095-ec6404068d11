import mongoose from 'mongoose'

const AssistantScriptSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    content: {
        type: String,
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    tags: {
        type: [mongoose.Schema.Types.Mixed],
        default: [],
    },
    entreprise: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Entreprise'
    },
    created_at: {
      type: Date,
      default: Date.now,
    }
})

const AssistantScript = mongoose.models.AssistantScript || mongoose.model('AssistantScript', AssistantScriptSchema)
export default AssistantScript