"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FileText, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import {
  GetEntrepriseDocuments,
  DeleteDocument,
  GetDocumentDownloadUrl,
} from "@/actions/DocumentActions";
import UploadDocumentDialog from "./UploadDocumentDialog";
import DocumentCard from "./DocumentCard";
import { Document as DocumentType } from "@/types";

export default function DocumentsList() {
  const [documents, setDocuments] = useState<DocumentType[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const fetchDocuments = async () => {
    try {
      const result = await GetEntrepriseDocuments();
      if (!result.success) {
        toast.error(result.error);
        return;
      }
      const sortedDocuments = ((result.data as DocumentType[]) || []).sort(
        (a, b) =>
          new Date(b.created_at || 0).getTime() -
          new Date(a.created_at || 0).getTime()
      );
      setDocuments(sortedDocuments);
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch documents");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  const handleDownload = async (documentId: string, fileName: string) => {
    setActionLoading(documentId);
    try {
      const result = await GetDocumentDownloadUrl(documentId);
      if (!result.success) {
        toast.error(result.error);
        return;
      }

      // Create a temporary link to download the file
      const link = document.createElement("a");
      link.href = result.data!;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success("Download started");
    } catch (error: any) {
      toast.error(error.message || "Failed to download document");
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async (documentId: string) => {
    setActionLoading(documentId);
    try {
      const result = await DeleteDocument(documentId);
      if (!result.success) {
        toast.error(result.error);
        return;
      }

      toast.success("Document deleted successfully");
      setDocuments(documents.filter((doc) => doc._id !== documentId));
    } catch (error: any) {
      toast.error(error.message || "Failed to delete document");
    } finally {
      setActionLoading(null);
      setDeleteDialogOpen(false);
      setDocumentToDelete(null);
    }
  };

  const handleDeleteClick = (documentId: string, documentName: string) => {
    setDocumentToDelete({ id: documentId, name: documentName });
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (documentToDelete) {
      await handleDelete(documentToDelete.id);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setDocumentToDelete(null);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Documents</CardTitle>
          <CardDescription>
            Manage your billing and business documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 border border-voxa-neutral-200 dark:border-voxa-neutral-700 rounded-lg bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <RefreshCw className="w-4 h-4 animate-spin" />
              Loading documents...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center flex-wrap gap-x-2">
        <div>
          <CardTitle>Documents</CardTitle>
          <CardDescription>
            Manage your billing and business documents
          </CardDescription>
        </div>
        <UploadDocumentDialog onUploadSuccess={fetchDocuments} />
      </CardHeader>
      <CardContent>
        {documents.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No documents uploaded yet</p>
            <p className="text-sm text-muted-foreground mt-1">
              Upload your first document to get started
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {documents.map((document) => (
              <DocumentCard
                key={document._id}
                document={document}
                actionLoading={actionLoading}
                onDownload={handleDownload}
                onDeleteClick={handleDeleteClick}
              />
            ))}
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Document</DialogTitle>
          </DialogHeader>
          <DialogDescription className="" style={{ overflowWrap: "anywhere" }}>
            Are you sure you want to delete {`"`}
            {documentToDelete?.name}
            {`"`}? This action cannot be undone and the document will be
            permanently removed.
          </DialogDescription>
          <DialogFooter>
            <Button variant="outline" onClick={handleDeleteCancel}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={actionLoading === documentToDelete?.id}
            >
              {actionLoading === documentToDelete?.id ? (
                <>
                  Deleting...
                  <div className="ml-2 animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </>
              ) : (
                "Delete Document"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
