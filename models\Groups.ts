import mongoose from "mongoose"

const GroupSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    members: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: "Client"
    },
    date: {
        type: Date,
        default: Date.now
    },
    entreprise: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Entreprise"
    },
    created_at: {
      type: Date,
      default: Date.now,
    }
})

const Group = mongoose.models.Group || mongoose.model("Group", GroupSchema)

export default Group