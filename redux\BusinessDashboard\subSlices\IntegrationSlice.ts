import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { saveAwsCredentials, getAwsCredentialsByEntrepriseId } from "@/actions/IntegrationActions";

interface AwsCredentials {
  AWS_ACCESS_KEY_ID: string;
  AWS_SECRET_ACCESS_KEY: string;
  REGION: string;
  BUCKET_NAME: string;
}

interface IntegrationState {
  awsDialogOpen: boolean;
  awsCredentials: AwsCredentials;
  saving: boolean;
}

const initialState: IntegrationState = {
  awsDialogOpen: false,
  awsCredentials: {
    AWS_ACCESS_KEY_ID: "",
    AWS_SECRET_ACCESS_KEY: "",
    REGION: "",
    BUCKET_NAME: "",
  },
  saving: false,
};

export const saveAwsCredentialsThunk = createAsyncThunk(
  "integration/saveAwsCredentials",
  async (creds: AwsCredentials, { dispatch }) => {
    if (!creds.AWS_ACCESS_KEY_ID) {
      toast.error("AWS Access Key ID is required.");
      return;
    }
    if (creds.AWS_ACCESS_KEY_ID.length !== 20) {
      toast.error("AWS Access Key ID must be exactly 20 characters.");
      return;
    }
    if (!creds.AWS_SECRET_ACCESS_KEY) {
      toast.error("AWS Secret Access Key is required.");
      return;
    }
    if (creds.AWS_SECRET_ACCESS_KEY.length !== 40) {
      toast.error("AWS Secret Access Key must be exactly 40 characters.");
      return;
    }
    if (!creds.REGION) {
      toast.error("Region is required.");
      return;
    }
    if (!creds.BUCKET_NAME) {
      toast.error("Bucket Name is required.");
      return;
    }
    dispatch(setAwsSaving(true));
    const result = await saveAwsCredentials(creds);
    dispatch(setAwsSaving(false));
    if (result?.error) {
      toast.error(result.error);
      return;
    }
    toast.success("AWS credentials saved successfully!");
  }
);

export const loadAwsCredentialsThunk = createAsyncThunk(
  "integration/loadAwsCredentials",
  async (_: void, { dispatch }) => {
    const creds = await getAwsCredentialsByEntrepriseId();
    if (creds) {
      dispatch(setAwsCredentials(creds));
    } else {
      dispatch(setAwsCredentials(initialState.awsCredentials));
    }
  }
);

const IntegrationSlice = createSlice({
  name: "integration",
  initialState,
  reducers: {
    setAwsDialogOpen(state, action: PayloadAction<boolean>) {
      state.awsDialogOpen = action.payload;
    },
    setAwsCredentials(state, action: PayloadAction<Partial<AwsCredentials>>) {
      state.awsCredentials = { ...state.awsCredentials, ...action.payload };
    },
    setAwsSaving(state, action: PayloadAction<boolean>) {
      state.saving = action.payload;
    },
    resetAwsCredentials(state) {
      state.awsCredentials = initialState.awsCredentials;
      state.saving = false;
      state.awsDialogOpen = false;
    },
  },
});

export const {
  setAwsDialogOpen,
  setAwsCredentials,
  setAwsSaving,
  resetAwsCredentials,
} = IntegrationSlice.actions;

export default IntegrationSlice.reducer; 