"use client";
import { getConversationLiveTranscription } from "@/actions/ConversationActions";
import CustomButton from "@/components/CustomFormItems/Button";
import { FileText } from "lucide-react";
import { useEffect, useRef, useState } from "react";

interface TranscriptionMessage {
  from: string;
  text: string;
  timestamp?: string;
}

interface PipWidgetProps {
  conversationId?: string;
}

export default function PipWidget({ conversationId }: PipWidgetProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [pipActive, setPipActive] = useState(false);
  const [transcription, setTranscription] = useState<TranscriptionMessage[]>(
    []
  );
  const [pipSupported, setPipSupported] = useState(true);
  const [scrollOffset, setScrollOffset] = useState(0);
  const [maxScrollOffset, setMaxScrollOffset] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const [showKeyboardHint, setShowKeyboardHint] = useState(true);

  /* ── core helpers ───────────────────────────────────────────── */
  async function fetchTranscriptionAndDraw(
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement
  ) {
    try {
      let messages: TranscriptionMessage[] = [];

      if (conversationId) {
        const response = await getConversationLiveTranscription(conversationId);
        if (response.success && response.transcription) {
          messages = response.transcription;
          setTranscription(messages);
        }
      } else {
        // Fallback to mock data for demo
        messages = [
          { from: "🤖 IA", text: "Hello! How can I help you today?" },
          { from: "Client", text: "I'm interested in your services." },
          {
            from: "🤖 IA",
            text: "Great! Let me tell you about our offerings...",
          },
        ];
        setTranscription(messages);
      }

      drawChatInterface(ctx, canvas, messages);
    } catch (err) {
      console.error("fetch/display error", err);
      // Draw error state
      drawErrorState(ctx, canvas);
    }
  }

  function drawChatInterface(
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement,
    messages: TranscriptionMessage[]
  ) {
    // Clear canvas
    ctx.fillStyle = "#F8FAFC";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Header
    const gradient = ctx.createLinearGradient(0, 0, 0, 60);
    gradient.addColorStop(0, "#0EA5E9");
    gradient.addColorStop(1, "#0284C7");
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, 60);

    // Header text
    ctx.fillStyle = "#FFFFFF";
    ctx.font = "bold 18px sans-serif";
    ctx.fillText("💬 Live Transcription", 20, 35);

    // Status indicator
    const time = Date.now() / 1000;
    const pulse = (Math.sin(time * 3) + 1) / 2;
    ctx.fillStyle = `rgba(34, 197, 94, ${0.7 + pulse * 0.3})`;
    ctx.beginPath();
    ctx.arc(canvas.width - 20, 30, 6, 0, Math.PI * 2);
    ctx.fill();

    // Scroll indicator (show when there are more messages)
    if (messages.length > 3) {
      ctx.fillStyle = "#FFFFFF";
      ctx.font = "10px sans-serif";
      const isPiP = !!document.pictureInPictureElement;
      const scrollText = isScrolling
        ? "Scrolling..."
        : isPiP
        ? "⌨️ Arrow Keys"
        : "🖱️ Scroll";
      ctx.fillText(scrollText, canvas.width - 70, 50);
    }

    // Chat messages area
    const chatAreaY = 60;
    const chatAreaHeight = canvas.height - chatAreaY;

    // Create clipping region for chat area
    ctx.save();
    ctx.beginPath();
    ctx.rect(0, chatAreaY, canvas.width, chatAreaHeight);
    ctx.clip();

    let yOffset = 80 - scrollOffset;
    const maxWidth = canvas.width - 40;
    const messageSpacing = 15;
    let totalContentHeight = 20; // Start with some padding

    // Calculate all message heights and positions
    const messageData: Array<{
      message: TranscriptionMessage;
      x: number;
      y: number;
      width: number;
      height: number;
      lines: string[];
      isAI: boolean;
    }> = [];

    messages.forEach((message) => {
      const isAI = message.from.includes("IA") || message.from.includes("🤖");
      const maxTextWidth = maxWidth * 0.75;

      // Word wrap
      const words = message.text.split(" ");
      const lines: string[] = [];
      let currentLine = "";

      ctx.font = "14px sans-serif";

      words.forEach((word) => {
        const testLine = currentLine + (currentLine ? " " : "") + word;
        const metrics = ctx.measureText(testLine);

        if (metrics.width > maxTextWidth && currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          currentLine = testLine;
        }
      });

      if (currentLine) {
        lines.push(currentLine);
      }

      const messageHeight = lines.length * 20 + 30;
      const messageWidth = Math.min(maxTextWidth + 20, maxWidth * 0.8);
      const x = isAI ? 20 : canvas.width - messageWidth - 20;

      messageData.push({
        message,
        x,
        y: yOffset,
        width: messageWidth,
        height: messageHeight,
        lines,
        isAI,
      });

      yOffset += messageHeight + messageSpacing;
      totalContentHeight += messageHeight + messageSpacing;
    });

    // Update max scroll offset
    const newMaxScrollOffset = Math.max(
      0,
      totalContentHeight - chatAreaHeight + 40
    );
    setMaxScrollOffset(newMaxScrollOffset);

    // Draw messages that are visible
    messageData.forEach(({ message, x, y, width, height, lines, isAI }) => {
      // Only draw if message is visible in the chat area
      if (y + height > chatAreaY && y < chatAreaY + chatAreaHeight) {
        // Draw bubble background
        ctx.fillStyle = isAI ? "#E0F2FE" : "#DBEAFE";
        drawRoundedRect(ctx, x, y, width, height, 12);
        ctx.fill();

        // Border
        ctx.strokeStyle = isAI ? "#0891B2" : "#3B82F6";
        ctx.lineWidth = 1;
        ctx.stroke();

        // Message text
        ctx.fillStyle = "#1F2937";
        ctx.font = "12px sans-serif";
        ctx.fillText(message.from, x + 10, y + 18);

        ctx.font = "14px sans-serif";
        lines.forEach((line, lineIndex) => {
          ctx.fillText(line, x + 10, y + 40 + lineIndex * 20);
        });
      }
    });

    // Restore clipping
    ctx.restore();

    // Draw scroll bar if needed
    if (newMaxScrollOffset > 0) {
      const scrollBarWidth = 6;
      const scrollBarHeight =
        (chatAreaHeight / (totalContentHeight / chatAreaHeight)) *
        chatAreaHeight;
      const scrollBarY =
        chatAreaY +
        (scrollOffset / newMaxScrollOffset) *
          (chatAreaHeight - scrollBarHeight);

      ctx.fillStyle = "rgba(0, 0, 0, 0.3)";
      drawRoundedRect(
        ctx,
        canvas.width - scrollBarWidth - 5,
        scrollBarY,
        scrollBarWidth,
        scrollBarHeight,
        3
      );
      ctx.fill();
    }

    // If no messages, show waiting state
    if (messages.length === 0) {
      ctx.fillStyle = "#6B7280";
      ctx.font = "16px sans-serif";
      ctx.textAlign = "center";
      ctx.fillText(
        "Waiting for conversation...",
        canvas.width / 2,
        canvas.height / 2
      );
      ctx.textAlign = "left";
    }

    // Show keyboard controls hint for PiP mode
    const isPiP = !!document.pictureInPictureElement;
    if (isPiP && messages.length > 3 && showKeyboardHint) {
      // Semi-transparent overlay at bottom
      ctx.fillStyle = "rgba(0, 0, 0, 0.7)";
      ctx.fillRect(0, canvas.height - 80, canvas.width, 80);

      ctx.fillStyle = "#FFFFFF";
      ctx.font = "12px sans-serif";
      ctx.textAlign = "center";
      ctx.fillText("Keyboard Controls:", canvas.width / 2, canvas.height - 60);
      ctx.font = "10px sans-serif";
      ctx.fillText(
        "↑↓ Scroll • PgUp/PgDn Fast scroll",
        canvas.width / 2,
        canvas.height - 45
      );
      ctx.fillText(
        "Home/End • Jump to top/bottom",
        canvas.width / 2,
        canvas.height - 30
      );
      ctx.fillText(
        "(Mouse disabled in PiP mode)",
        canvas.width / 2,
        canvas.height - 15
      );
      ctx.textAlign = "left";
    }
  }

  function drawRoundedRect(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    w: number,
    h: number,
    r: number
  ) {
    ctx.beginPath();
    ctx.moveTo(x + r, y);
    ctx.lineTo(x + w - r, y);
    ctx.quadraticCurveTo(x + w, y, x + w, y + r);
    ctx.lineTo(x + w, y + h - r);
    ctx.quadraticCurveTo(x + w, y + h, x + w - r, y + h);
    ctx.lineTo(x + r, y + h);
    ctx.quadraticCurveTo(x, y + h, x, y + h - r);
    ctx.lineTo(x, y + r);
    ctx.quadraticCurveTo(x, y, x + r, y);
    ctx.closePath();
  }

  function drawErrorState(
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement
  ) {
    ctx.fillStyle = "#FEF2F2";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.fillStyle = "#DC2626";
    ctx.font = "16px sans-serif";
    ctx.textAlign = "center";
    ctx.fillText(
      "Unable to load transcription",
      canvas.width / 2,
      canvas.height / 2
    );
    ctx.textAlign = "left";
  }

  /* ── initialise canvas → video stream ───────────────────────── */
  useEffect(() => {
    const canvas = canvasRef.current;
    const video = videoRef.current;
    if (!canvas || !video) return;

    // Check if Picture-in-Picture is supported
    if (!document.pictureInPictureEnabled || !video.requestPictureInPicture) {
      setPipSupported(false);
      return;
    }

    // scale for clear text
    canvas.width = 400;
    canvas.height = 500;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Add scroll event handlers - Use keyboard for PiP interaction
    const handleKeyDown = (e: KeyboardEvent) => {
      e.preventDefault();
      const scrollSpeed = 50;
      let newScrollOffset = scrollOffset;

      switch (e.key) {
        case "ArrowUp":
          newScrollOffset = Math.max(0, scrollOffset - scrollSpeed);
          break;
        case "ArrowDown":
          newScrollOffset = Math.min(
            maxScrollOffset,
            scrollOffset + scrollSpeed
          );
          break;
        case "PageUp":
          newScrollOffset = Math.max(0, scrollOffset - scrollSpeed * 3);
          break;
        case "PageDown":
          newScrollOffset = Math.min(
            maxScrollOffset,
            scrollOffset + scrollSpeed * 3
          );
          break;
        case "Home":
          newScrollOffset = 0;
          break;
        case "End":
          newScrollOffset = maxScrollOffset;
          break;
        default:
          return; // Don't update if key not recognized
      }

      setScrollOffset(newScrollOffset);
      setIsScrolling(true);
      setShowKeyboardHint(false); // Hide hint after first use
      setTimeout(() => setIsScrolling(false), 500);
    };

    // For non-PiP mode, keep mouse interactions
    const handleWheel = (e: WheelEvent) => {
      if (document.pictureInPictureElement) return; // Skip if in PiP mode
      e.preventDefault();
      const scrollSpeed = 30;
      const newScrollOffset = Math.max(
        0,
        Math.min(maxScrollOffset, scrollOffset + (e.deltaY * scrollSpeed) / 100)
      );
      setScrollOffset(newScrollOffset);
      setIsScrolling(true);
      setTimeout(() => setIsScrolling(false), 500);
    };

    let isDragging = false;
    let lastY = 0;

    const handleMouseDown = (e: MouseEvent) => {
      if (document.pictureInPictureElement) return; // Skip if in PiP mode
      isDragging = true;
      lastY = e.clientY;
      setIsScrolling(true);
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || document.pictureInPictureElement) return;

      const deltaY = e.clientY - lastY;
      const scrollSpeed = 2;
      const newScrollOffset = Math.max(
        0,
        Math.min(maxScrollOffset, scrollOffset - deltaY * scrollSpeed)
      );
      setScrollOffset(newScrollOffset);
      lastY = e.clientY;
    };

    const handleMouseUp = () => {
      isDragging = false;
      setIsScrolling(false);
    };

    // Add event listeners
    canvas.addEventListener("wheel", handleWheel, { passive: false });
    canvas.addEventListener("mousedown", handleMouseDown);
    canvas.addEventListener("mousemove", handleMouseMove);
    canvas.addEventListener("mouseup", handleMouseUp);
    canvas.addEventListener("mouseleave", handleMouseUp);

    // Add keyboard listener for PiP interaction
    document.addEventListener("keydown", handleKeyDown);

    // 1st paint
    fetchTranscriptionAndDraw(ctx, canvas);

    // attach MediaStream from canvas to video
    video.srcObject = canvas.captureStream(30); // 30 fps
    video.play().catch(() => {});

    /* cleanup on unmount */
    return () => {
      stopPolling();
      canvas.removeEventListener("wheel", handleWheel);
      canvas.removeEventListener("mousedown", handleMouseDown);
      canvas.removeEventListener("mousemove", handleMouseMove);
      canvas.removeEventListener("mouseup", handleMouseUp);
      canvas.removeEventListener("mouseleave", handleMouseUp);
      document.removeEventListener("keydown", handleKeyDown);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conversationId, scrollOffset, maxScrollOffset]);

  // Update canvas when scroll offset changes
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    fetchTranscriptionAndDraw(ctx, canvas);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scrollOffset]);

  // Auto-scroll to bottom when new messages arrive (unless user is scrolling)
  useEffect(() => {
    if (!isScrolling && transcription.length > 0) {
      setScrollOffset(maxScrollOffset);
    }
  }, [transcription.length, maxScrollOffset, isScrolling]);

  /* ── polling control ────────────────────────────────────────── */
  function startPolling() {
    if (intervalRef.current) return;
    intervalRef.current = setInterval(() => {
      const canvas = canvasRef.current;
      if (!canvas) return;
      const ctx = canvas.getContext("2d");
      fetchTranscriptionAndDraw(ctx!, canvas);
    }, 3000); // every 3 seconds for live updates
  }

  function stopPolling() {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }

  /* ── PiP toggle handler ─────────────────────────────────────── */
  async function togglePip() {
    const video = videoRef.current;
    if (!video || !pipSupported || !conversationId) return;

    try {
      if (!document.pictureInPictureElement) {
        // enter PiP
        await video.requestPictureInPicture();
        startPolling();
        setPipActive(true);
        setShowKeyboardHint(true); // Show hint when entering PiP
        video.addEventListener("leavepictureinpicture", onLeavePip, {
          once: true,
        });
      } else {
        // exit PiP
        await document.exitPictureInPicture();
      }
    } catch (err) {
      console.error("PiP error:", err);
    }
  }

  function onLeavePip() {
    stopPolling();
    setPipActive(false);
  }

  /* ── render ─────────────────────────────────────────────────── */
  if (!pipSupported) return null;

  return (
    <>
      {/* hidden elements required by PiP */}
      <canvas ref={canvasRef} className="hidden" />
      <video
        ref={videoRef}
        className="hidden"
        muted
        playsInline
        // poster as fallback
      />

      {/* PiP Button */}
      <CustomButton
        props={{
          onClick: togglePip,
          value: pipActive
            ? "Close live transcription"
            : conversationId
            ? "Open live transcription"
            : "No conversation selected",
          className: pipActive
            ? "w-full px-2 sm:px-4 py-0 h-6 rounded-md bg-red-600 hover:bg-red-500 dark:bg-red-700 dark:hover:bg-red-600 text-voxa-neutral-50 dark:text-voxa-neutral-100 text-[11px]"
            : conversationId
            ? "w-full px-2 sm:px-4 py-0 h-6 rounded-md bg-voxa-teal-600 hover:bg-voxa-teal-500 dark:bg-voxa-teal-700 dark:hover:bg-voxa-teal-600 text-voxa-neutral-50 dark:text-voxa-neutral-100 text-[11px]"
            : "w-full px-2 sm:px-4 py-0 h-6 rounded-md bg-gray-400 cursor-not-allowed text-voxa-neutral-50 dark:text-voxa-neutral-100 text-[11px]",
          loading: false,
          icon: <FileText className="w-3 h-3" />,
        }}
      />
    </>
  );
}
