"use client";

import React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes";
import { <PERSON>, Moon, Monitor } from "lucide-react";

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      {...props}
    >
      {children}
    </NextThemesProvider>
  );
}

export const themes = [
  { mode: "light", icon: <Sun /> },
  { mode: "dark", icon: <Moon /> },
  { mode: "system", icon: <Monitor /> },
];
