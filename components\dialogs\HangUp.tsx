"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";

export function HangUp({
  callsid,
  goalid,
}: {
  callsid: string;
  goalid: string;
}) {
  const handleHangUp = async () => {
    try {
      const res = await fetch(`/api/calls/hangup`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          call_sid: callsid,
        }),
      });

      if (!res.ok) {
        const errText = await res.text();
        throw new Error(errText || "Failed to hangup the call.");
      }

      const data = await res.json();
      toast.success("Successfully hung up.");
    } catch (error: any) {
      toast.error(`Error: ${error.message}`);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="h-6 w-full bg-red-600 hover:bg-red-500 dark:bg-red-800 dark:hover:bg-red-700 text-white text-[11px] rounded-md">
          Hang Up
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Hang Up</DialogTitle>
          <DialogDescription>
            Force hangup call.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="submit"
              className="hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950"
            >
              Cancel
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              onClick={handleHangUp}
              className="bg-voxa-teal-600 hover:bg-voxa-teal-500  text-white"
            >
              Hang Up
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
