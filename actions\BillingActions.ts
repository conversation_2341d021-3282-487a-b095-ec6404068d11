"use server";

import dbConnect from "@/lib/mongodb";
import { getServerSession } from "next-auth";
import { getEntrepriseByAdminID } from "./Entreprise";
import authOptions from "@/lib/AuthOptions";
import Entreprise from "@/models/Entreprise";
import Plan from "@/models/Plan";
import {
  Entreprise as EntrepriseType,
  Invoice as InvoiceType,
  PlanLink as PlanLinkType,
  Plan as PlanType,
} from "@/types";
import Stripe from "stripe";

import Invoice from "@/models/Invoice";
import PlanLink from "@/models/PlanLink";
import Subscription from "@/models/Subscription";
import { PlanFeature, Subscription as SubscriptionType } from "@/types";
import { nanoid } from "nanoid";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export interface CreatePaymentLinkResponse {
  success: boolean;
  error?: string;
  data?: {
    url: string;
    session_id: string;
  };
}

export interface CreatePaymentIntentResponse {
  success: boolean;
  error?: string;
  data?: {
    client_secret: string;
    payment_intent_id: string;
  };
}

export interface UpdateBalanceResponse {
  success: boolean;
  error?: string;
  data?: {
    new_balance: number;
  };
}

interface CreateInvoiceResponse {
  success: boolean;
  data?: {
    invoice_id: string;
    invoice_url?: string;
    status: string;
  };
  error?: string;
}

export async function topupWithCard(
  amount: number,
  payment_method_id: string,
  description?: string
): Promise<CreateInvoiceResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    // Get enterprise details
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const entreprise = entrepriseResponse.entreprise;
    if (!entreprise.stripe_customer_id) {
      return { success: false, error: "No customer found" };
    }

    // Verify the payment method belongs to the customer
    const paymentMethod = await stripe.paymentMethods.retrieve(
      payment_method_id
    );
    if (paymentMethod.customer !== entreprise.stripe_customer_id) {
      return { success: false, error: "Payment method not found" };
    }

    // Step 1: Create the invoice
    const invoice = await stripe.invoices.create({
      customer: entreprise.stripe_customer_id,
      currency: "eur",
      default_payment_method: payment_method_id,
      collection_method: "charge_automatically",
      auto_advance: true, // Automatically finalize the invoice
      automatic_tax: {
        enabled: true, // Enable automatic tax calculation
      },
      metadata: {
        entreprise_id: entreprise._id.toString(),
        admin_id: session.user.id,
        amount: amount.toString(),
        type: "credit_topup",
      },
    });

    if (!invoice.id) {
      return { success: false, error: "Failed to create invoice" };
    }

    // Step 2: Add line item to the invoice
    await stripe.invoiceItems.create({
      customer: entreprise.stripe_customer_id,
      invoice: invoice.id,
      amount: Math.round(amount * 100), // Convert to cents
      currency: "eur",
      description: description || `${amount}€ Credit Top-up`,
      tax_behavior: "exclusive", // Tax will be calculated separately
    });

    // Step 3: Finalize the invoice (this calculates taxes automatically)
    const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);

    if (!finalizedInvoice.id) {
      return { success: false, error: "Failed to finalize invoice" };
    }

    // Step 4: Pay the invoice immediately with the specified payment method
    const paidInvoice = await stripe.invoices.pay(finalizedInvoice.id, {
      payment_method: payment_method_id,
    });

    if (
      !paidInvoice.id ||
      paidInvoice.status !== "paid" ||
      !paidInvoice.hosted_invoice_url
    ) {
      return { success: false, error: "Failed to pay invoice" };
    }

    return {
      success: true,
      data: {
        invoice_id: paidInvoice.id,
        invoice_url: paidInvoice.hosted_invoice_url,
        status: paidInvoice.status,
      },
    };
  } catch (error: any) {
    console.error("Error creating invoice with card:", error);
    return {
      success: false,
      error: error.message || "Failed to create invoice with card",
    };
  }
}

export async function createPaymentLink(
  amount: number
): Promise<CreatePaymentLinkResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    // Get enterprise details
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const entreprise = entrepriseResponse.entreprise;

    // Create or get Stripe customer
    let customerId = entreprise.stripe_customer_id;

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: session.user.email!,
        name: entreprise.corpName || session.user.name!,
        metadata: {
          entreprise_id: entreprise._id.toString(),
          admin_id: session.user.id,
        },
        address: {
          country: "FR", // Default to France, adjust as needed
        },
      });
      customerId = customer.id;

      // Update enterprise with Stripe customer ID
      await dbConnect();
      await Entreprise.findByIdAndUpdate(entreprise._id, {
        stripe_customer_id: customerId,
      });
    }

    // Create a product for the credit purchase
    const product = await stripe.products.create({
      name: `${amount}€ Credit Top-up`,
      description: `Add ${amount}€ to your account balance`,
      metadata: {
        type: "credit_topup",
        amount: amount.toString(),
        entreprise_id: entreprise._id.toString(),
      },
    });

    // Create a price for the product
    const price = await stripe.prices.create({
      product: product.id,
      unit_amount: amount * 100, // Convert to cents
      currency: "eur",
    });

    // Create a checkout session instead of payment link
    // This ensures metadata is properly passed to webhooks
    const checkoutSession = await stripe.checkout.sessions.create({
      customer: customerId,
      line_items: [
        {
          price: price.id,
          quantity: 1,
        },
      ],
      mode: "payment",
      success_url: `${process.env.NEXTAUTH_URL}/businessDash/settings/billing?payment=success`,
      cancel_url: `${process.env.NEXTAUTH_URL}/businessDash/settings/billing?payment=cancelled`,
      automatic_tax: { enabled: true },
      metadata: {
        entreprise_id: entreprise._id.toString(),
        admin_id: session.user.id,
        amount: amount.toString(),
        type: "credit_topup",
      },
    });

    return {
      success: true,
      data: {
        url: checkoutSession.url!,
        session_id: checkoutSession.id,
      },
    };
  } catch (error: any) {
    console.error("Error creating checkout session:", error);
    return {
      success: false,
      error: error.message || "Failed to create checkout session",
    };
  }
}

export interface PayInvoiceResponse {
  success: boolean;
  error?: string;
  data?: {
    payment_url?: string;
    session_id?: string;
    status?: string;
  };
}

export async function payInvoice(
  invoiceId: string
): Promise<PayInvoiceResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    // Get enterprise details
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = entrepriseResponse.entreprise;

    if (!enterprise.stripe_customer_id) {
      return { success: false, error: "No payment method configured" };
    }

    // Get the invoice from Stripe with expanded line items
    const invoice = await stripe.invoices.retrieve(invoiceId, {
      expand: ["lines"],
    });

    if (invoice.customer !== enterprise.stripe_customer_id) {
      return { success: false, error: "Invoice not found" };
    }

    if (invoice.status === "paid") {
      return { success: false, error: "Invoice is already paid" };
    }

    // Create line items based on the invoice
    const lineItems = invoice.lines.data.map((line) => ({
      price_data: {
        currency: invoice.currency,
        product_data: {
          name: line.description || "Subscription Payment",
          description: line.period
            ? `${new Date(
                line.period.start * 1000
              ).toLocaleDateString()} - ${new Date(
                line.period.end * 1000
              ).toLocaleDateString()}`
            : undefined,
        },
        unit_amount: line.amount,
      },
      quantity: line.quantity || 1,
    }));

    // Create a checkout session for the invoice payment
    const checkoutSession = await stripe.checkout.sessions.create({
      customer: enterprise.stripe_customer_id,
      mode: "payment",
      line_items: lineItems,
      success_url: `${process.env.NEXTAUTH_URL}/businessDash/settings/billing?payment=success`,
      cancel_url: `${process.env.NEXTAUTH_URL}/businessDash/settings/billing?payment=cancelled`,
      metadata: {
        entreprise_id: enterprise._id.toString(),
        admin_id: session.user.id,
        invoice_id: invoiceId,
        type: "invoice_payment",
      },
    });

    return {
      success: true,
      data: {
        payment_url: checkoutSession.url!,
        session_id: checkoutSession.id,
        status: "redirect_to_payment",
      },
    };
  } catch (error: any) {
    console.error("Error paying invoice:", error);

    // Handle specific Stripe errors
    if (error.type === "StripeCardError") {
      return {
        success: false,
        error: `Payment failed: ${error.message}`,
      };
    }

    return {
      success: false,
      error: error.message || "Failed to pay invoice",
    };
  }
}

// Saved Cards Management Functions
export interface SavedCard {
  id: string;
  brand: string;
  last4: string;
  exp_month: number;
  exp_year: number;
  funding: string;
  country: string;
  is_default: boolean;
  created: number;
}

export interface SavedCardsResponse {
  success: boolean;
  error?: string;
  data?: {
    cards: SavedCard[];
  };
}

export interface CreateSetupIntentResponse {
  success: boolean;
  error?: string;
  data?: {
    client_secret: string;
    setup_intent_id: string;
  };
}

export interface DeleteCardResponse {
  success: boolean;
  error?: string;
  data?: {
    message: string;
  };
}

export interface SetDefaultCardResponse {
  success: boolean;
  error?: string;
  data?: {
    message: string;
  };
}

export async function getSavedCards(): Promise<SavedCardsResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    // Get enterprise details
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = entrepriseResponse.entreprise;

    if (!enterprise.stripe_customer_id) {
      return {
        success: true,
        data: { cards: [] },
      };
    }

    // Get payment methods from Stripe
    const paymentMethods = await stripe.paymentMethods.list({
      customer: enterprise.stripe_customer_id,
      type: "card",
    });

    // Get customer to check default payment method
    const customer = await stripe.customers.retrieve(
      enterprise.stripe_customer_id
    );
    const defaultPaymentMethodId = (customer as any).invoice_settings
      ?.default_payment_method;

    const formattedCards: SavedCard[] = paymentMethods.data.map((pm) => ({
      id: pm.id,
      brand: pm.card?.brand || "unknown",
      last4: pm.card?.last4 || "****",
      exp_month: pm.card?.exp_month || 0,
      exp_year: pm.card?.exp_year || 0,
      funding: pm.card?.funding || "unknown",
      country: pm.card?.country || "unknown",
      is_default: pm.id === defaultPaymentMethodId,
      created: pm.created,
    }));

    return {
      success: true,
      data: { cards: formattedCards },
    };
  } catch (error: any) {
    console.error("Error fetching saved cards:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch saved cards",
    };
  }
}

export async function setupCardIntent(): Promise<CreateSetupIntentResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    // Get enterprise details
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = entrepriseResponse.entreprise;

    // Create or get Stripe customer
    let customerId = enterprise.stripe_customer_id;

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: session.user.email || undefined,
        metadata: {
          entreprise_id: enterprise._id.toString(),
          admin_id: session.user.id,
        },
      });

      customerId = customer.id;

      // Update enterprise with customer ID
      await Entreprise.findByIdAndUpdate(enterprise._id, {
        stripe_customer_id: customerId,
      });
    }

    // Create setup intent
    const setupIntent = await stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ["card"],
      usage: "off_session",
      metadata: {
        entreprise_id: enterprise._id.toString(),
        admin_id: session.user.id,
      },
    });

    return {
      success: true,
      data: {
        client_secret: setupIntent.client_secret!,
        setup_intent_id: setupIntent.id,
      },
    };
  } catch (error: any) {
    console.error("Error creating setup intent:", error);
    return {
      success: false,
      error: error.message || "Failed to create setup intent",
    };
  }
}

export async function deleteCard(cardId: string): Promise<DeleteCardResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    // Get enterprise details
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = entrepriseResponse.entreprise;

    if (!enterprise.stripe_customer_id) {
      return { success: false, error: "No customer found" };
    }

    // Verify the payment method belongs to the customer
    const paymentMethod = await stripe.paymentMethods.retrieve(cardId);
    if (paymentMethod.customer !== enterprise.stripe_customer_id) {
      return { success: false, error: "Card not found" };
    }

    // Detach the payment method from the customer
    await stripe.paymentMethods.detach(cardId);

    return {
      success: true,
      data: { message: "Card deleted successfully" },
    };
  } catch (error: any) {
    console.error("Error deleting card:", error);
    return {
      success: false,
      error: error.message || "Failed to delete card",
    };
  }
}

export async function setDefaultCard(
  cardId: string
): Promise<SetDefaultCardResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    // Get enterprise details
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = entrepriseResponse.entreprise;

    if (!enterprise.stripe_customer_id) {
      return { success: false, error: "No customer found" };
    }

    // Verify the payment method belongs to the customer
    const paymentMethod = await stripe.paymentMethods.retrieve(cardId);
    if (paymentMethod.customer !== enterprise.stripe_customer_id) {
      return { success: false, error: "Card not found" };
    }

    // Update customer's default payment method
    await stripe.customers.update(enterprise.stripe_customer_id, {
      invoice_settings: {
        default_payment_method: cardId,
      },
    });

    return {
      success: true,
      data: { message: "Default card updated successfully" },
    };
  } catch (error: any) {
    console.error("Error setting default card:", error);
    return {
      success: false,
      error: error.message || "Failed to set default card",
    };
  }
}

export async function createSubscriptionWithCard(
  plan_id: string | undefined,
  plan_link_code: string | undefined,
  billing_period: "monthly" | "yearly" | "weekly" | "daily",
  payment_method_id: string
): Promise<CreatePaymentLinkResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    // Get enterprise details
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = entrepriseResponse.entreprise;

    if (!enterprise.stripe_customer_id) {
      return { success: false, error: "No customer found" };
    }

    // Verify the payment method belongs to the customer
    const paymentMethod = await stripe.paymentMethods.retrieve(
      payment_method_id
    );
    if (paymentMethod.customer !== enterprise.stripe_customer_id) {
      return { success: false, error: "Payment method not found" };
    }

    await dbConnect();

    // Determine plan to use
    let plan: PlanType | null = null;
    let planLink: PlanLinkType | null = null;
    let usedPlanId: string | undefined = undefined;

    if (plan_id) {
      plan = await Plan.findById<PlanType>(plan_id);
      usedPlanId = plan_id;
    } else if (plan_link_code) {
      planLink = await PlanLink.findOne({
        code: plan_link_code,
      }).lean<PlanLinkType>();
      if (planLink && planLink.plan_id) {
        usedPlanId = planLink.plan_id.toString();
        plan = await Plan.findById<PlanType>(usedPlanId);
      }
    } else {
      return { success: false, error: "No plan_id or plan_link_code provided" };
    }

    if (!plan) {
      return { success: false, error: "Plan not found" };
    }

    // Get pricing for the specified billing period
    const billingOptionData = plan.billing_options?.[billing_period];
    if (!billingOptionData || !billingOptionData.stripe_price_id) {
      return {
        success: false,
        error: `Billing options not available for ${billing_period} billing`,
      };
    }

    // Create subscription with saved payment method and send invoice
    const subscription = await stripe.subscriptions.create({
      customer: enterprise.stripe_customer_id,
      items: [{ price: billingOptionData.stripe_price_id }],
      default_payment_method: payment_method_id,
      // collection_method: "send_invoice",
      // days_until_due: getDaysUntilDue(billing_period),
      automatic_tax: { enabled: true },
      metadata: {
        entreprise_id: enterprise._id.toString(),
        plan_id: plan._id.toString(),
        billing_period: billing_period,
        admin_id: session.user.id,
        ...(plan_link_code ? { plan_link_code: plan_link_code } : {}),
      },
    });

    // If PlanLink was used, decrement uses_left and delete if 0
    if (planLink) {
      if (planLink.uses_left !== null) {
        const newUsesLeft = (planLink.uses_left ?? 0) - 1;
        if (newUsesLeft <= 0) {
          await PlanLink.findByIdAndDelete(planLink._id);
        } else {
          await PlanLink.findByIdAndUpdate(planLink._id, {
            uses_left: newUsesLeft,
          });
        }
      }
    }

    return {
      success: true,
      data: {
        url: `${process.env.NEXTAUTH_URL}/businessDash/settings/billing?subscription=success`,
        session_id: subscription.id,
      },
    };
  } catch (error: any) {
    console.error("Error creating subscription with card:", error);
    return {
      success: false,
      error: error.message || "Failed to create subscription with card",
    };
  }
}

// Convert billing period to Stripe interval format
const getStripeInterval = (
  billingPeriod: string
): "month" | "year" | "week" | "day" => {
  switch (billingPeriod) {
    case "monthly":
      return "month";
    case "yearly":
      return "year";
    case "weekly":
      return "week";
    case "daily":
      return "day";
    default:
      return "month"; // Default fallback
  }
};

// Get days until due for invoice generation based on billing period
const getDaysUntilDue = (billingPeriod: string): number => {
  switch (billingPeriod) {
    case "monthly":
      return 3; // Generate invoice 3 days before due date for monthly
    case "weekly":
      return 1; // Generate invoice 1 day before due date for weekly
    case "yearly":
      return 7; // Generate invoice 7 days before due date for yearly
    case "daily":
      return 0; // Generate invoice on the due date for daily
    default:
      return 3; // Default to 3 days
  }
};

export interface CreatePlanData {
  name: string;
  description: string;
  billing_options: {
    monthly?: {
      original_price: number;
      current_price: number;
      stripe_price_id?: string;
    };
    yearly?: {
      original_price: number;
      current_price: number;
      stripe_price_id?: string;
    };
    weekly?: {
      original_price: number;
      current_price: number;
      stripe_price_id?: string;
    };
    daily?: {
      original_price: number;
      current_price: number;
      stripe_price_id?: string;
    };
  };
  currency: string;
  features: PlanFeature[];
  visible: boolean;
  mostPopular: boolean;
  sort_order: number;
}

export interface PlanResponse {
  success: boolean;
  error?: string;
  data?: any;
}

export async function createPlan(
  planData: CreatePlanData
): Promise<PlanResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    await dbConnect();

    // Create Stripe product
    const stripeProduct = await stripe.products.create({
      name: planData.name,
      description: planData.description,
      metadata: {
        type: "subscription_plan",
      },
    });

    // Create Stripe prices for each billing period
    const updatedBillingOptions = { ...planData.billing_options };

    for (const [billingPeriod, periodData] of Object.entries(
      planData.billing_options
    )) {
      if (periodData) {
        const stripePrice = await stripe.prices.create({
          product: stripeProduct.id,
          unit_amount: periodData.current_price * 100,
          currency: planData.currency,
          recurring: {
            interval: getStripeInterval(billingPeriod),
            interval_count: 1,
          },
          metadata: {
            original_price: (periodData.original_price * 100).toString(),
            billing_period: billingPeriod,
          },
        });

        updatedBillingOptions[
          billingPeriod as keyof typeof updatedBillingOptions
        ] = {
          ...periodData,
          stripe_price_id: stripePrice.id,
        };
      }
    }

    // If creating this plan as most popular, unset other plans
    if (planData.mostPopular === true) {
      await Plan.updateMany({}, { $set: { mostPopular: false } });
    }

    // Create plan in database
    const plan = new Plan({
      name: planData.name,
      description: planData.description,
      billing_options: updatedBillingOptions,
      currency: planData.currency,
      features: planData.features,
      visible: planData.visible,
      mostPopular: planData.mostPopular,
      sort_order: planData.sort_order,
      stripe_product_id: stripeProduct.id,
    });

    await plan.save();

    const planObject = plan.toObject();

    // Create a clean object to avoid read-only property issues
    const cleanPlanObject = {
      ...planObject,
      _id: planObject._id.toString(),
    };

    // Convert feature IDs if they exist
    if (cleanPlanObject.features && Array.isArray(cleanPlanObject.features)) {
      cleanPlanObject.features = cleanPlanObject.features.map(
        (feature: any) => ({
          ...feature,
          _id: feature._id ? feature._id.toString() : feature._id,
        })
      );
    }

    return {
      success: true,
      data: cleanPlanObject,
    };
  } catch (error: any) {
    console.error("Error creating plan:", error);
    return {
      success: false,
      error: error.message || "Failed to create plan",
    };
  }
}

export async function getAllPlans(): Promise<PlanResponse> {
  try {
    await dbConnect();

    const plans = await Plan.find({ visible: true })
      .sort({ sort_order: 1, created_at: -1 })
      .lean<PlanType[]>();

    // Get subscription counts for each plan
    const plansWithCounts = await Promise.all(
      plans.map(async (plan) => {
        const subscriptionCount = await Subscription.countDocuments({
          plan_id: plan._id,
        });

        return {
          ...plan,
          subscription_count: subscriptionCount,
        };
      })
    );

    plansWithCounts.forEach((plan: PlanType) => {
      if (plan._id) plan._id = plan._id.toString();
      // Create a clean object to avoid read-only property issues
      if (plan.features && Array.isArray(plan.features)) {
        plan.features.forEach((feature: PlanFeature) => {
          if (feature._id) feature._id = feature._id.toString();
        });
      }
    });

    return {
      success: true,
      data: plansWithCounts,
    };
  } catch (error: any) {
    console.error("Error fetching plans:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch plans",
    };
  }
}

export async function getAllPlansForAdmin(): Promise<PlanResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    await dbConnect();

    const plans = await Plan.find({})
      .sort({ sort_order: 1, created_at: -1 })
      .lean<PlanType[]>();

    // Get subscription counts for each plan
    const plansWithCounts = await Promise.all(
      plans.map(async (plan) => {
        const subscriptionCount = await Subscription.countDocuments({
          plan_id: plan._id,
        });

        const activeSubscriptionCount = await Subscription.countDocuments({
          plan_id: plan._id,
          status: { $in: ["active", "trialing"] },
        });

        return {
          ...plan,
          subscription_count: subscriptionCount,
          active_subscription_count: activeSubscriptionCount,
        };
      })
    );

    plansWithCounts.forEach((plan: PlanType) => {
      if (plan._id) plan._id = plan._id.toString();
      // Create a clean object to avoid read-only property issues
      if (plan.features && Array.isArray(plan.features)) {
        plan.features.forEach((feature: PlanFeature) => {
          if (feature._id) feature._id = feature._id.toString();
        });
      }
    });

    return {
      success: true,
      data: plansWithCounts,
    };
  } catch (error: any) {
    console.error("Error fetching plans for admin:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch plans",
    };
  }
}

export async function updatePlan(
  planId: string,
  updateData: Partial<CreatePlanData>
): Promise<PlanResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    await dbConnect();

    const plan = await Plan.findById(planId);
    if (!plan) {
      return { success: false, error: "Plan not found" };
    }

    // Update Stripe product if name or description changed
    if (updateData.name || updateData.description) {
      await stripe.products.update(plan.stripe_product_id, {
        name: updateData.name || plan.name,
        description: updateData.description || plan.description,
      });
    }

    // Update Stripe prices if billing options changed
    let updatedBillingOptions = { ...plan.billing_options };
    if (updateData.billing_options) {
      for (const [billingPeriod, periodData] of Object.entries(
        updateData.billing_options
      )) {
        if (periodData) {
          // Create new price (Stripe doesn't allow updating existing prices)
          const stripePrice = await stripe.prices.create({
            product: plan.stripe_product_id,
            unit_amount: periodData.current_price * 100,
            currency: updateData.currency || plan.currency,
            recurring: {
              interval: getStripeInterval(billingPeriod),
              interval_count: 1,
            },
            metadata: {
              original_price: (periodData.original_price * 100).toString(),
              billing_period: billingPeriod,
            },
          });

          // Archive old price if it exists
          const oldPriceId =
            plan.billing_options?.[
              billingPeriod as keyof typeof plan.billing_options
            ]?.stripe_price_id;
          if (oldPriceId) {
            await stripe.prices.update(oldPriceId, { active: false });
          }

          updatedBillingOptions[
            billingPeriod as keyof typeof updatedBillingOptions
          ] = {
            ...periodData,
            stripe_price_id: stripePrice.id,
          };
        }
      }
      updateData.billing_options = updatedBillingOptions;
    }

    // If setting this plan as most popular, unset other plans
    if (updateData.mostPopular === true) {
      await Plan.updateMany(
        { _id: { $ne: planId } },
        { $set: { mostPopular: false } }
      );
    }

    const updatedPlan = await Plan.findByIdAndUpdate(planId, updateData, {
      new: true,
    });

    const updatedPlanObject = updatedPlan?.toObject();

    if (updatedPlanObject) {
      // Create a clean object to avoid read-only property issues
      const cleanUpdatedPlanObject = {
        ...updatedPlanObject,
        _id: updatedPlanObject._id.toString(),
      };

      // Convert feature IDs if they exist
      if (
        cleanUpdatedPlanObject.features &&
        Array.isArray(cleanUpdatedPlanObject.features)
      ) {
        cleanUpdatedPlanObject.features = cleanUpdatedPlanObject.features.map(
          (feature: any) => ({
            ...feature,
            _id: feature._id ? feature._id.toString() : feature._id,
          })
        );
      }

      return {
        success: true,
        data: cleanUpdatedPlanObject,
      };
    }

    return {
      success: true,
      data: updatedPlanObject,
    };
  } catch (error: any) {
    console.error("Error updating plan:", error);
    return {
      success: false,
      error: error.message || "Failed to update plan",
    };
  }
}

export async function deletePlan(planId: string): Promise<PlanResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    await dbConnect();

    const plan = await Plan.findById(planId);
    if (!plan) {
      return { success: false, error: "Plan not found" };
    }

    // Check if plan has any subscriptions (active or inactive)
    const totalSubscriptions = await Subscription.countDocuments({
      plan_id: planId,
    });

    if (totalSubscriptions > 0) {
      return {
        success: false,
        error:
          "Cannot delete plan with existing subscriptions. Hide it instead.",
      };
    }

    // Delete from Stripe first
    try {
      // Archive all Stripe prices for all billing periods
      if (plan.billing_options) {
        for (const [billingPeriod, periodData] of Object.entries(
          plan.billing_options
        )) {
          if (
            periodData &&
            typeof periodData === "object" &&
            "stripe_price_id" in periodData &&
            periodData.stripe_price_id
          ) {
            await stripe.prices.update(periodData.stripe_price_id as string, {
              active: false,
            });
          }
        }
      }

      // Archive and then delete the Stripe product
      await stripe.products.update(plan.stripe_product_id, { active: false });

      // Note: Stripe doesn't allow permanent deletion of products/prices with historical data
      // They can only be archived. This is by design for audit and compliance purposes.
    } catch (stripeError: any) {
      console.error("Error archiving Stripe product/price:", stripeError);
      // Continue with database deletion even if Stripe archiving fails
      // The plan will still be removed from our system
    }

    // Permanently delete the plan from our database
    await Plan.findByIdAndDelete(planId);

    return {
      success: true,
      data: { message: "Plan deleted successfully" },
    };
  } catch (error: any) {
    console.error("Error deleting plan:", error);
    return {
      success: false,
      error: error.message || "Failed to delete plan",
    };
  }
}

export async function togglePlanVisibility(
  planId: string,
  visible: boolean
): Promise<PlanResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    await dbConnect();

    const plan = await Plan.findById(planId);
    if (!plan) {
      return { success: false, error: "Plan not found" };
    }

    // Update plan visibility
    const updatedPlan = await Plan.findByIdAndUpdate(
      planId,
      { visible },
      { new: true }
    );

    const updatedPlanObject = updatedPlan?.toObject();

    if (updatedPlanObject) {
      // Create a clean object to avoid read-only property issues
      const cleanUpdatedPlanObject = {
        ...updatedPlanObject,
        _id: updatedPlanObject._id.toString(),
      };

      // Convert feature IDs if they exist
      if (
        cleanUpdatedPlanObject.features &&
        Array.isArray(cleanUpdatedPlanObject.features)
      ) {
        cleanUpdatedPlanObject.features = cleanUpdatedPlanObject.features.map(
          (feature: any) => ({
            ...feature,
            _id: feature._id ? feature._id.toString() : feature._id,
          })
        );
      }

      return {
        success: true,
        data: {
          ...cleanUpdatedPlanObject,
          message: `Plan ${visible ? "shown" : "hidden"} successfully`,
        },
      };
    }

    return {
      success: true,
      data: {
        message: `Plan ${visible ? "shown" : "hidden"} successfully`,
      },
    };
  } catch (error: any) {
    console.error("Error toggling plan visibility:", error);
    return {
      success: false,
      error: error.message || "Failed to toggle plan visibility",
    };
  }
}

export interface CreateSubscriptionData {
  plan_id: string;
  billing_period: "monthly" | "yearly" | "weekly" | "daily";
  trial_days?: number;
}

export async function createSubscription(
  subscriptionData: CreateSubscriptionData
): Promise<PlanResponse> {
  try {
    console.log("createSubscription called with:", subscriptionData);

    const session = await getServerSession(authOptions);
    if (!session) {
      console.error("No session found");
      return { success: false, error: "Unauthorized" };
    }

    console.log("Session found for user:", session.user.email);

    const enterpriseResponse = await getEntrepriseByAdminID();
    if (!enterpriseResponse.success) {
      console.error("Enterprise not found:", enterpriseResponse.error);
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = enterpriseResponse.entreprise;
    console.log("Enterprise found:", enterprise._id);

    await dbConnect();

    const plan = await Plan.findById(subscriptionData.plan_id);
    if (!plan) {
      console.error("Plan not found:", subscriptionData.plan_id);
      return { success: false, error: "Plan not found" };
    }

    // Get pricing for the specified billing period
    const billingOptionData =
      plan.billing_options?.[subscriptionData.billing_period];
    if (!billingOptionData || !billingOptionData.stripe_price_id) {
      console.error(
        "Billing options not found for billing period:",
        subscriptionData.billing_period
      );
      return {
        success: false,
        error: `Billing options not available for ${subscriptionData.billing_period} billing`,
      };
    }

    // Create or get Stripe customer
    let customerId = enterprise.stripe_customer_id;
    if (!customerId) {
      console.log("Creating new Stripe customer for:", session.user.email);
      const stripeCustomer = await stripe.customers.create({
        email: session.user.email!,
        metadata: {
          entreprise_id: enterprise._id.toString(),
          admin_id: session.user.id,
        },
      });
      customerId = stripeCustomer.id;
      console.log("Created Stripe customer:", customerId);

      // Update enterprise with customer ID
      await enterprise.updateOne({ stripe_customer_id: customerId });
    } else {
      console.log("Using existing Stripe customer:", customerId);
    }

    // Create Stripe checkout session for subscription
    const checkoutParams: any = {
      customer: customerId,
      line_items: [
        {
          price: billingOptionData.stripe_price_id,
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: `${process.env.NEXTAUTH_URL}/businessDash/settings/billing?subscription=success&plan=${plan._id}&billing_period=${subscriptionData.billing_period}&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/businessDash/settings/billing?subscription=cancelled`,
      metadata: {
        entreprise_id: enterprise._id.toString(),
        plan_id: plan._id.toString(),
        billing_period: subscriptionData.billing_period,
        admin_id: session.user.id,
      },
      subscription_data: {
        // collection_method: "send_invoice",
        days_until_due: getDaysUntilDue(subscriptionData.billing_period),
        auto_advance: true, // <-- Ensure invoices are finalized automatically
        metadata: {
          entreprise_id: enterprise._id.toString(),
          plan_id: plan._id.toString(),
          billing_period: subscriptionData.billing_period,
        },
      },
    };

    // Add trial period if specified
    if (subscriptionData.trial_days) {
      checkoutParams.subscription_data.trial_period_days =
        subscriptionData.trial_days;
      console.log("Adding trial period:", subscriptionData.trial_days, "days");
    }

    console.log("Creating Stripe checkout session with params:", {
      customer: customerId,
      mode: checkoutParams.mode,
      price_id: billingOptionData.stripe_price_id,
      billing_period: subscriptionData.billing_period,
      metadata: checkoutParams.metadata,
    });

    const checkoutSession = await stripe.checkout.sessions.create(
      checkoutParams
    );

    console.log("Stripe checkout session created:", {
      session_id: checkoutSession.id,
      url: checkoutSession.url,
    });

    return {
      success: true,
      data: {
        checkout_url: checkoutSession.url!,
        session_id: checkoutSession.id,
      },
    };
  } catch (error: any) {
    console.error("Error creating subscription checkout:", error);
    return {
      success: false,
      error: error.message || "Failed to create subscription checkout",
    };
  }
}

export async function getCurrentSubscription(): Promise<PlanResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    const enterpriseResponse = await getEntrepriseByAdminID();
    if (!enterpriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = enterpriseResponse.entreprise;
    await dbConnect();

    // Find the current active subscription
    let currentSubscription;

    // First, try to get the explicitly set current subscription
    if (enterprise.current_subscription) {
      currentSubscription = await Subscription.findOne(
        {
          _id: enterprise.current_subscription,
          entreprise_id: enterprise._id,
          status: { $in: ["active", "trialing", "past_due"] },
        },
        "-entreprise_id -stripe_customer_id -stripe_subscription_id -created_at -updated_at -__v"
      )
        .populate("plan_id")
        .lean<SubscriptionType>();
    }

    // If no current subscription is set or it's invalid, get the most recent active one
    if (!currentSubscription) {
      currentSubscription = await Subscription.findOne(
        {
          entreprise_id: enterprise._id,
          status: { $in: ["active", "trialing", "past_due"] },
        },
        "-entreprise_id -stripe_customer_id -stripe_subscription_id -created_at -updated_at -__v"
      )
        .populate("plan_id")
        .sort({ created_at: -1 })
        .lean<SubscriptionType>();

      // If we found a subscription but no current one was set, update enterprise
      if (currentSubscription) {
        const Entreprise = (await import("@/models/Entreprise")).default;
        await Entreprise.findByIdAndUpdate(enterprise._id, {
          current_subscription: currentSubscription._id,
        });
      }
    }

    if (!currentSubscription) {
      return { success: true, data: null };
    }

    currentSubscription._id = currentSubscription._id.toString();
    if (currentSubscription.plan_id) {
      const plan = currentSubscription.plan_id as PlanType;
      if (plan._id) {
        plan._id = plan._id.toString();
      }
      plan.features!.forEach((feature: PlanFeature) => {
        if (feature._id) {
          feature._id = feature._id.toString();
        }
      });
    }

    return {
      success: true,
      data: currentSubscription,
    };
  } catch (error: any) {
    console.error("Error fetching current subscription:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch current subscription",
    };
  }
}

export async function getSubscriptions(): Promise<PlanResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    const enterpriseResponse = await getEntrepriseByAdminID();
    if (!enterpriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = enterpriseResponse.entreprise;
    await dbConnect();

    // Populate subscriptions from the Entreprise model
    const populatedEntreprise = await Entreprise.findById(enterprise._id)
      .populate({
        path: "subscriptions",
        populate: {
          path: "plan_id",
          select: "-features",
        },
      })
      .lean<EntrepriseType>();

    const subscriptions: SubscriptionType[] = (
      populatedEntreprise?.subscriptions || []
    ).map((subscription: any) => {
      if (subscription._id) subscription._id = subscription._id.toString();
      return subscription;
    });

    return {
      success: true,
      data: JSON.parse(JSON.stringify(subscriptions)),
    };
  } catch (error: any) {
    console.error("Error fetching subscription history:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch subscription history",
    };
  }
}

export async function cancelSubscription(
  subscriptionId: string
): Promise<PlanResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    const enterpriseResponse = await getEntrepriseByAdminID();
    if (!enterpriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = enterpriseResponse.entreprise;
    await dbConnect();

    // Find the subscription
    const subscription = await Subscription.findOne({
      _id: subscriptionId,
      entreprise_id: enterprise._id,
    });

    if (!subscription) {
      return { success: false, error: "Subscription not found" };
    }

    // Cancel the subscription in Stripe
    if (subscription.stripe_subscription_id) {
      await stripe.subscriptions.update(subscription.stripe_subscription_id, {
        cancel_at_period_end: true,
      });
    }

    // Update the subscription in database
    await Subscription.findByIdAndUpdate(subscriptionId, {
      cancel_at_period_end: true,
      updated_at: new Date(),
    });

    return {
      success: true,
      data: {
        message:
          "Subscription will be canceled at the end of the current period",
      },
    };
  } catch (error: any) {
    console.error("Error canceling subscription:", error);
    return {
      success: false,
      error: error.message || "Failed to cancel subscription",
    };
  }
}

export async function reactivateSubscription(
  subscriptionId: string
): Promise<PlanResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    const enterpriseResponse = await getEntrepriseByAdminID();
    if (!enterpriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = enterpriseResponse.entreprise;
    await dbConnect();

    // Find the subscription
    const subscription = await Subscription.findOne({
      _id: subscriptionId,
      entreprise_id: enterprise._id,
    });

    if (!subscription) {
      return { success: false, error: "Subscription not found" };
    }

    // Reactivate the subscription in Stripe
    if (subscription.stripe_subscription_id) {
      await stripe.subscriptions.update(subscription.stripe_subscription_id, {
        cancel_at_period_end: false,
      });
    }

    // Update the subscription in database
    await Subscription.findByIdAndUpdate(subscriptionId, {
      cancel_at_period_end: false,
      updated_at: new Date(),
    });

    return {
      success: true,
      data: { message: "Subscription reactivated successfully" },
    };
  } catch (error: any) {
    console.error("Error reactivating subscription:", error);
    return {
      success: false,
      error: error.message || "Failed to reactivate subscription",
    };
  }
}

export interface AllSubscriptionsResponse {
  success: boolean;
  error?: string;
  data?: {
    subscriptions: SubscriptionType[];
    current_subscription_id?: string;
  };
}

export interface SwitchSubscriptionResponse {
  success: boolean;
  error?: string;
  data?: SubscriptionType;
}

export async function getAllValidSubscriptions(): Promise<AllSubscriptionsResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    const enterpriseResponse = await getEntrepriseByAdminID();
    if (!enterpriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = enterpriseResponse.entreprise;
    await dbConnect();

    // Find all valid subscriptions for this enterprise
    const validSubscriptions = await Subscription.find(
      {
        entreprise_id: enterprise._id,
        status: { $in: ["active", "trialing", "past_due"] },
      },
      "-entreprise_id -stripe_customer_id -created_at -updated_at -__v"
    )
      .populate("plan_id")
      .sort({ created_at: -1 })
      .lean<SubscriptionType[]>();

    // Process the subscriptions
    const processedSubscriptions = validSubscriptions.map((subscription) => {
      subscription._id = subscription._id.toString();
      if (subscription.plan_id) {
        const plan = subscription.plan_id as PlanType;
        if (plan._id) {
          plan._id = plan._id.toString();
        }
        plan.features!.forEach((feature: PlanFeature) => {
          if (feature._id) {
            feature._id = feature._id.toString();
          }
        });
      }
      return subscription;
    });

    // Find current active subscription ID from enterprise
    const currentSubscriptionId = enterprise.current_subscription?.toString();

    return {
      success: true,
      data: {
        subscriptions: processedSubscriptions,
        current_subscription_id: currentSubscriptionId,
      },
    };
  } catch (error: any) {
    console.error("Error fetching all valid subscriptions:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch subscriptions",
    };
  }
}

export async function switchActiveSubscription(
  subscriptionId: string
): Promise<SwitchSubscriptionResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    const enterpriseResponse = await getEntrepriseByAdminID();
    if (!enterpriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = enterpriseResponse.entreprise;
    await dbConnect();

    // Verify the subscription exists and belongs to this enterprise
    const subscription = await Subscription.findOne({
      _id: subscriptionId,
      entreprise_id: enterprise._id,
      status: { $in: ["active", "trialing", "past_due"] },
    })
      .populate("plan_id")
      .lean<SubscriptionType>();

    if (!subscription) {
      return {
        success: false,
        error: "Subscription not found or not valid",
      };
    }

    // Update enterprise to set this as the current subscription
    await Entreprise.findByIdAndUpdate(enterprise._id, {
      current_subscription: subscriptionId,
      updated_at: new Date(),
    });

    // Process the subscription data
    subscription._id = subscription._id.toString();
    if (subscription.plan_id) {
      const plan = subscription.plan_id as PlanType;
      if (plan._id) {
        plan._id = plan._id.toString();
      }
      plan.features!.forEach((feature: PlanFeature) => {
        if (feature._id) {
          feature._id = feature._id.toString();
        }
      });
    }
    subscription.entreprise_id = enterprise._id.toString();

    return {
      success: true,
      data: subscription,
    };
  } catch (error: any) {
    console.error("Error switching active subscription:", error);
    return {
      success: false,
      error: error.message || "Failed to switch subscription",
    };
  }
}

export interface SaveInvoiceParams {
  stripeInvoice: any; // Stripe invoice object
  entrepriseId: string;
  subscriptionId?: string;
}

export interface SaveInvoiceResponse {
  success: boolean;
  error?: string;
  invoice?: any;
}

/**
 * Save or update an invoice in the database from Stripe invoice data
 */
export async function saveInvoiceToDatabase(
  params: SaveInvoiceParams
): Promise<SaveInvoiceResponse> {
  try {
    await dbConnect();

    const { stripeInvoice, entrepriseId, subscriptionId } = params;

    // Convert Stripe line items
    const lines =
      stripeInvoice.lines?.data?.map((line: any) => ({
        id: line.id,
        amount: line.amount,
        currency: line.currency,
        description: line.description,
        quantity: line.quantity,
        unit_amount: line.price?.unit_amount,
        price: {
          id: line.price?.id,
          nickname: line.price?.nickname,
          product: line.price?.product,
        },
        period: {
          start: line.period?.start
            ? new Date(line.period.start * 1000)
            : undefined,
          end: line.period?.end ? new Date(line.period.end * 1000) : undefined,
        },
      })) || [];

    // Convert discount information
    const discount = stripeInvoice.discount
      ? {
          coupon: {
            id: stripeInvoice.discount.coupon?.id,
            name: stripeInvoice.discount.coupon?.name,
            percent_off: stripeInvoice.discount.coupon?.percent_off,
            amount_off: stripeInvoice.discount.coupon?.amount_off,
          },
          amount: stripeInvoice.discount.amount,
        }
      : undefined;

    // Convert payment method information
    const defaultPaymentMethod = stripeInvoice.default_payment_method
      ? {
          type: stripeInvoice.default_payment_method.type,
          brand: stripeInvoice.default_payment_method.card?.brand,
          last4: stripeInvoice.default_payment_method.card?.last4,
        }
      : undefined;

    const invoiceData = {
      stripe_invoice_id: stripeInvoice.id,
      entreprise_id: entrepriseId,
      subscription_id: subscriptionId ? subscriptionId : undefined,
      stripe_customer_id: stripeInvoice.customer,
      stripe_subscription_id: stripeInvoice.subscription,
      status: stripeInvoice.status,
      amount_due: stripeInvoice.amount_due,
      amount_paid: stripeInvoice.amount_paid,
      amount_remaining: stripeInvoice.amount_remaining,
      subtotal: stripeInvoice.subtotal,
      total: stripeInvoice.total,
      tax: stripeInvoice.tax || 0,
      currency: stripeInvoice.currency,
      description: stripeInvoice.description,
      number: stripeInvoice.number,
      created: new Date(stripeInvoice.created * 1000),
      due_date: stripeInvoice.due_date
        ? new Date(stripeInvoice.due_date * 1000)
        : undefined,
      period_start: stripeInvoice.period_start
        ? new Date(stripeInvoice.period_start * 1000)
        : undefined,
      period_end: stripeInvoice.period_end
        ? new Date(stripeInvoice.period_end * 1000)
        : undefined,
      finalized_at: stripeInvoice.finalized_at
        ? new Date(stripeInvoice.finalized_at * 1000)
        : undefined,
      paid_at: stripeInvoice.paid_at
        ? new Date(stripeInvoice.paid_at * 1000)
        : undefined,
      voided_at: stripeInvoice.voided_at
        ? new Date(stripeInvoice.voided_at * 1000)
        : undefined,
      next_payment_attempt: stripeInvoice.next_payment_attempt
        ? new Date(stripeInvoice.next_payment_attempt * 1000)
        : undefined,
      attempt_count: stripeInvoice.attempt_count || 0,
      hosted_invoice_url: stripeInvoice.hosted_invoice_url,
      invoice_pdf: stripeInvoice.invoice_pdf,
      lines,
      discount,
      default_payment_method: defaultPaymentMethod,
      auto_advance: stripeInvoice.auto_advance,
      collection_method: stripeInvoice.collection_method,
      metadata: stripeInvoice.metadata || {},
    };

    // Upsert the invoice (update if exists, create if not)
    const invoice = await Invoice.findOneAndUpdate(
      { stripe_invoice_id: stripeInvoice.id },
      invoiceData,
      { upsert: true, new: true }
    );

    // Add invoice to entreprise if it's not already there
    await Entreprise.findByIdAndUpdate(
      entrepriseId,
      { $addToSet: { invoices: invoice._id } },
      { new: true }
    );

    console.log(`Invoice ${stripeInvoice.id} saved to database successfully`);

    return {
      success: true,
      invoice,
    };
  } catch (error: any) {
    console.error("Error saving invoice to database:", error);
    return {
      success: false,
      error: error.message || "Failed to save invoice",
    };
  }
}

/**
 * Get invoices from database for the current entreprise
 */
export async function getInvoices(
  limit: number = 20,
  page: number = 1,
  status?: string,
  sortBy: string = "created",
  sortOrder: "asc" | "desc" = "desc"
): Promise<{
  success: boolean;
  error?: string;
  data?: {
    invoices: any[];
    total: number;
    page: number;
    totalPages: number;
    hasMore: boolean;
  };
}> {
  try {
    await dbConnect();

    // Get entreprise details using session
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }
    const entrepriseId = entrepriseResponse.entreprise._id;

    // Build query
    const query: any = { entreprise_id: entrepriseId };
    if (status && status !== "all") {
      query.status = status;
    }

    // Build sort
    const sort: any = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;

    // Execute queries
    const skip = (page - 1) * limit;
    const [invoices, total] = await Promise.all([
      Invoice.find(
        query,
        "entreprise_id stripe_invoice_id status amount_due amount_paid total currency created period_start period_end due_date hosted_invoice_url invoice_pdf subtotal"
      )
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate({
          path: "subscription_id",
          select: "plan_id billing_period status",
          populate: {
            path: "plan_id",
            select: "name",
          },
        })
        .lean<InvoiceType[]>(),
      Invoice.countDocuments(query),
    ]);

    // Add plan_name to each invoice if available
    invoices.forEach((invoice: any) => {
      invoice.plan_name = invoice.subscription_id?.plan_id?.name || undefined;
    });

    const totalPages = Math.ceil(total / limit);
    const hasMore = page < totalPages;

    invoices.forEach((invoice: InvoiceType) => {
      invoice._id = invoice._id.toString();
      invoice.entreprise_id = invoice.entreprise_id.toString();
      const subscription = invoice.subscription_id as SubscriptionType;
      if (subscription) {
        subscription._id = subscription._id.toString();
        const subscriptionPlan = subscription.plan_id as PlanType;
        if (subscriptionPlan) {
          subscriptionPlan._id = subscriptionPlan._id.toString();
        }
      }
    });

    return {
      success: true,
      data: {
        invoices,
        total,
        page,
        totalPages,
        hasMore,
      },
    };
  } catch (error: any) {
    console.error("Error getting invoices from database:", error);
    return {
      success: false,
      error: error.message || "Failed to get invoices",
    };
  }
}

/**
 * Get a single invoice by Stripe invoice ID
 */
export async function getInvoiceByStripeId(stripeInvoiceId: string): Promise<{
  success: boolean;
  error?: string;
  invoice?: any;
}> {
  try {
    await dbConnect();

    const invoice = await Invoice.findOne({
      stripe_invoice_id: stripeInvoiceId,
    })
      .populate("subscription_id", "plan_id billing_period status")
      .lean();

    if (!invoice) {
      return {
        success: false,
        error: "Invoice not found",
      };
    }

    return {
      success: true,
      invoice,
    };
  } catch (error: any) {
    console.error("Error getting invoice by Stripe ID:", error);
    return {
      success: false,
      error: error.message || "Failed to get invoice",
    };
  }
}

export interface CreatePlanLinkResponse {
  success: boolean;
  error?: string;
  data?: {
    code: string;
    plan_id: {
      _id: string;
      name: string;
    };
    uses_left: number | null;
    _id: string;
    createdAt: string;
    updatedAt: string;
  };
}

/**
 * Create a PlanLink with a random code
 */
export async function createPlanLink(
  planId: string,
  uses_left?: number | null
): Promise<CreatePlanLinkResponse> {
  try {
    await dbConnect();

    // Generate a unique code (length 8)
    let code: string;
    let exists = true;
    do {
      code = nanoid(8);
      exists = !!(await PlanLink.exists({ code }));
    } while (exists);

    const planLink = await PlanLink.create({
      code,
      plan_id: planId,
      uses_left: uses_left ?? null,
    });

    const plan = await Plan.findById(planId, "name");

    return {
      success: true,
      data: {
        _id: planLink._id.toString(),
        code: planLink.code,
        uses_left: planLink.uses_left,
        plan_id: {
          _id: plan?._id?.toString() || "",
          name: plan?.name || "",
        },
        createdAt: planLink.createdAt?.toISOString?.() || "",
        updatedAt: planLink.updatedAt?.toISOString?.() || "",
      },
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Failed to create PlanLink",
    };
  }
}

export async function getAllPlanLinksForAdmin(
  page: number = 1,
  limit: number = 20
): Promise<{
  success: boolean;
  error?: string;
  data?: {
    planLinks: PlanLinkType[];
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}> {
  try {
    await dbConnect();

    // Get total count
    const total = await PlanLink.countDocuments({});
    const totalPages = Math.ceil(total / limit);
    const skip = (page - 1) * limit;

    // Fetch paginated plan links
    const planLinks = await PlanLink.find({})
      .populate({ path: "plan_id", select: "name" })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean<PlanLinkType[]>();

    const formatted = planLinks.map((pl: any) => ({
      _id: pl._id.toString(),
      code: pl.code,
      plan_id: {
        _id: pl.plan_id?._id?.toString() || "",
        name: pl.plan_id?.name || "",
      },
      uses_left: pl.uses_left,
      createdAt: pl.createdAt?.toISOString?.() || "",
      updatedAt: pl.updatedAt?.toISOString?.() || "",
    }));

    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      success: true,
      data: {
        planLinks: formatted,
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Failed to fetch plan links",
    };
  }
}

/**
 * Delete a PlanLink by its ID
 */
export async function deletePlanLink(
  planLinkId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    await dbConnect();
    const deleted = await PlanLink.findByIdAndDelete(planLinkId);
    if (!deleted) {
      return { success: false, error: "PlanLink not found" };
    }
    return { success: true };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Failed to delete PlanLink",
    };
  }
}

/**
 * Fetch a Plan by PlanLink code
 */
export async function getPlanByCode(
  code: string
): Promise<{ success: boolean; error?: string; data?: any }> {
  try {
    await dbConnect();
    const planLink = await PlanLink.findOne({ code }).lean<PlanLinkType>();
    if (!planLink || !planLink.plan_id) {
      return { success: false, error: "PlanLink or Plan not found" };
    }

    // Check if a subscription exists with plan_link_code as the code
    const existingSubscription = await Subscription.findOne({
      plan_link_code: code,
    });
    if (existingSubscription) {
      return { success: false, error: "Plan already used" };
    }

    // Fetch the plan and populate its details
    const plan = await Plan.findById(planLink.plan_id).lean<PlanType>();
    if (!plan) {
      return { success: false, error: "Plan not found" };
    }
    // Convert _id and feature IDs to string for consistency
    plan._id = plan._id.toString();
    if (plan.features && Array.isArray(plan.features)) {
      plan.features = plan.features.map((feature: any) => ({
        ...feature,
        _id: feature._id ? feature._id.toString() : feature._id,
      }));
    }
    return { success: true, data: plan };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Failed to fetch Plan by code",
    };
  }
}

/**
 * Calculate tax for a given amount using Stripe Tax API.
 */
export interface CalculateTaxResponse {
  success: boolean;
  error?: string;
  data?: {
    amount: number;
    tax: number;
    total: number;
    currency: string;
  };
}

export async function calculateTaxForAmount(
  amount: number
): Promise<CalculateTaxResponse> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    // Get enterprise details
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const entreprise = entrepriseResponse.entreprise;

    if (!entreprise.stripe_customer_id) {
      return { success: false, error: "No customer found" };
    }

    const baseAmount = Math.round(amount * 100); // in cents

    // Calculate tax using Stripe Tax API
    const taxCalculation = await stripe.tax.calculations.create({
      currency: "eur",
      customer: entreprise.stripe_customer_id,
      line_items: [
        {
          amount: baseAmount,
          reference: "tax_calculation_line_item",
          quantity: 1,
          tax_behavior: "exclusive",
        },
      ],
    });

    return {
      success: true,
      data: {
        amount,
        tax: taxCalculation.tax_amount_exclusive / 100, // Convert from cents to main currency unit
        total: taxCalculation.amount_total / 100,
        currency: taxCalculation.currency,
      },
    };
  } catch (error: any) {
    console.error("Error calculating tax:", error);
    return {
      success: false,
      error: error.message || "Failed to calculate tax",
    };
  }
}

/**
 * Update the balanceAlertThreshold for the current entreprise.
 */
export async function updateBalanceAlertThreshold(
  newThreshold: number
): Promise<{
  success: boolean;
  error?: string;
  data?: { balanceAlertThreshold: number };
}> {
  try {
    await dbConnect();
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }
    const entreprise = entrepriseResponse.entreprise;
    entreprise.balanceAlertThreshold = newThreshold;
    await entreprise.save();
    return {
      success: true,
      data: { balanceAlertThreshold: entreprise.balanceAlertThreshold },
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Failed to update balance alert threshold",
    };
  }
}

/**
 * Fetch subscriptions for a specific plan id for the current entreprise.
 */
export async function getSubscriptionsByPlan(
  planId: string,
  limit: number = 20,
  page: number = 1,
  sortBy: string = "created_at",
  sortOrder: "asc" | "desc" = "desc"
): Promise<{
  success: boolean;
  error?: string;
  data?: {
    subscriptions: SubscriptionType[];
    total: number;
    page: number;
    totalPages: number;
    hasMore: boolean;
  };
}> {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return { success: false, error: "Unauthorized" };
    }

    const enterpriseResponse = await getEntrepriseByAdminID();
    if (!enterpriseResponse.success) {
      return { success: false, error: "Enterprise not found" };
    }

    const enterprise = enterpriseResponse.entreprise;
    await dbConnect();

    // Build query
    const query: any = {
      entreprise_id: enterprise._id,
      plan_id: planId,
    };

    // Build sort
    const sort: any = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;

    // Pagination
    const skip = (page - 1) * limit;

    // Execute queries
    const [subscriptions, total] = await Promise.all([
      Subscription.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate([
          {
            path: "plan_id",
            select: "-features",
          },
          {
            path: "entreprise_id",
            select: "name",
            populate: {
              path: "admin",
              select: "email",
            },
          },
        ])
        .lean<SubscriptionType[]>(),
      Subscription.countDocuments(query),
    ]);

    // Convert _id to string for each subscription
    const formattedSubscriptions = subscriptions.map((subscription: any) => {
      if (subscription._id) subscription._id = subscription._id.toString();
      return subscription;
    });

    const totalPages = Math.ceil(total / limit);
    const hasMore = page < totalPages;

    return {
      success: true,
      data: {
        subscriptions: JSON.parse(JSON.stringify(formattedSubscriptions)),
        total,
        page,
        totalPages,
        hasMore,
      },
    };
  } catch (error: any) {
    console.error("Error fetching subscriptions by plan:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch subscriptions by plan",
    };
  }
}
