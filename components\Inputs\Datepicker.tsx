"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon, CircleX } from "lucide-react";
import { DateRange } from "react-day-picker";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { useTranslation } from "react-i18next";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@radix-ui/react-popover";

interface DatePickerWithRangeProps {
  date: DateRange | undefined;
  setDate: (date: DateRange | undefined) => void;
  className?: string;
  buttonClass?: string;
  description?: string;
  maxRangeDays?: number;
}

export function DatePickerWithRange({
  date,
  setDate,
  className,
  buttonClass,
  description,
  maxRangeDays,
}: DatePickerWithRangeProps) {
  const { t } = useTranslation("callLogs");
  const disabledDays = React.useMemo(() => {
    if (!date?.from || !maxRangeDays) return undefined;
    try {
      const maxDate = new Date(date.from);
      maxDate.setDate(maxDate.getDate() + maxRangeDays - 1);

      return [{ before: date.from }, { after: maxDate }];
    } catch (error) {
      console.error("Error calculating disabled days:", error);
      return undefined;
    }
  }, [date?.from, maxRangeDays]);

  const handleSelect = (range: DateRange | undefined) => {
    if (!range) {
      setDate(undefined);
      return;
    }
    if (range.from && !range.to) {
      setDate(range);
      return;
    }
    if (range.from && range.to && maxRangeDays) {
      try {
        const fromDate =
          range.from instanceof Date ? range.from : new Date(range.from);
        const toDate = range.to instanceof Date ? range.to : new Date(range.to);
        const diffTime = Math.abs(toDate.getTime() - fromDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        if (diffDays > maxRangeDays) {
          const newTo = new Date(fromDate);
          newTo.setDate(newTo.getDate() + maxRangeDays);
          setDate({ from: fromDate, to: newTo });
          return;
        }
        setDate({ from: fromDate, to: toDate });
        return;
      } catch (error) {
        console.error("Error calculating date range:", error);
        if (range.from) {
          try {
            const fromDate =
              range.from instanceof Date ? range.from : new Date(range.from);
            setDate({ from: fromDate, to: undefined });
          } catch (e) {
            console.error("Error creating from date:", e);
            setDate(undefined);
          }
        } else {
          setDate(undefined);
        }
        return;
      }
    }
    setDate(range);
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "cursor-pointer px-2 group justify-start text-left font-normal border border-sidebar-border bg-sidebar dark:bg-voxa-neutral-950 hover:bg-voxa-neutral-50 dark:hover:bg-voxa-neutral-900 rounded-lg",
              buttonClass
            )}
          >
            <div className="w-full flex items-center gap-2">
              <CalendarIcon className="scale-115 ax-sm:hidden text-foreground/80" />
              {date?.from ? (
                date.to ? (
                  <span className="text-xs sm:text-base text-foreground/80">
                    {format(date.from, "LLL dd, y")} -{" "}
                    {format(date.to, "LLL dd, y")}
                  </span>
                ) : (
                  format(date.from, "LLL dd, y")
                )
              ) : (
                <span className="text-sm sm:text-base text-muted-foreground/50">
                  {t("export.selectedDateRange")}
                </span>
              )}
            </div>
            <div
              onClick={(e) => {
                e.stopPropagation();
                setDate(undefined);
              }}
              className="cursor-pointer z-50 hover:text-red-600"
            >
              <CircleX className="sm:scale-125" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1 bg-voxa-neutral-50 dark:bg-voxa-neutral-950 mx-1 md:mx-2 w-auto p-0 rounded-lg">
          <Calendar
            mode="range"
            defaultMonth={date?.from || new Date()}
            selected={date}
            onSelect={maxRangeDays ? handleSelect : setDate}
            numberOfMonths={2}
            disabled={disabledDays}
          />
          {description && (
            <p className="pt-2 pb-3 px-4 text-xs sm:text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
