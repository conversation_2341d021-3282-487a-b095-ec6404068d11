import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import { setFiltersInput } from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { Input } from "@/components/ui/input";

export default function DurationFilters() {
  const dispatch = useDispatch<AppDispatch>();
  const { callDuration, ringingTime } = useSelector(
    (state: RootState) => state.analytics.filtersInput // <-- use filtersInput
  );

  const handleCallDurationChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "min" | "max"
  ) => {
    const value = e.target.value === "" ? null : Number(e.target.value);
    dispatch(
      setFiltersInput({
        callDuration: {
          ...callDuration,
          [type]: value,
        },
      })
    );
  };

  const handleRingingTimeChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "min" | "max"
  ) => {
    const value = e.target.value === "" ? null : Number(e.target.value);
    dispatch(
      setFiltersInput({
        ringingTime: {
          ...ringingTime,
          [type]: value,
        },
      })
    );
  };

  return (
    <div className="mt-4 space-y-1">
      <h3 className="font-bold">Durations</h3>
      <div className="space-y-2">
        <label className="text-sm font-medium">
          Call Duration (in seconds)
        </label>
        <div className="flex gap-2">
          <Input
            type="number"
            placeholder="Min"
            value={callDuration.min ?? ""}
            onChange={(e) => handleCallDurationChange(e, "min")}
            className="w-full"
            min="0"
          />
          <Input
            type="number"
            placeholder="Max"
            value={callDuration.max ?? ""}
            onChange={(e) => handleCallDurationChange(e, "max")}
            className="w-full"
            min="0"
          />
        </div>
      </div>
      <div className="space-y-2">
        <label className="text-sm font-medium">Ringing Time (in seconds)</label>
        <div className="flex gap-2">
          <Input
            type="number"
            placeholder="Min"
            value={ringingTime.min ?? ""}
            onChange={(e) => handleRingingTimeChange(e, "min")}
            className="w-full"
            min="0"
          />
          <Input
            type="number"
            placeholder="Max"
            value={ringingTime.max ?? ""}
            onChange={(e) => handleRingingTimeChange(e, "max")}
            className="w-full"
            min="0"
          />
        </div>
      </div>
    </div>
  );
}
