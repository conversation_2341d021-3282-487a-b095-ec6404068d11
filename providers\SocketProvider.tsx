"use client";
import React, { useState, useContext, useEffect, createContext } from "react";
import { socket } from "@/lib/socket";
import { useSession } from "next-auth/react";

interface SocketContextType {
  isConnected: boolean;
  token: string | null;
}

const SocketContext = createContext<SocketContextType | null>(null);

function SocketProvider({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession();
  const user = session?.user;
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [token, setToken] = useState<string | null>(null);

  // Fetch token only once when user session is available
  useEffect(() => {
    async function fetchToken() {
      if (!user || token) return; // Don't fetch if no user or token already exists

      try {
        const response = await fetch("/api/auth/get_token", {
          method: "GET",
          credentials: "include", // Include cookies for NextAuth session
        });

        if (response.ok) {
          const data = await response.json();
          setToken(data.token);
        } else {
          console.log("Failed to get token:", response.status);
        }
      } catch (error) {
        console.error("Error getting token:", error);
      }
    }

    fetchToken();
  }, [user, token]);

  useEffect(() => {
    async function initializeSocket() {
      try {
        if (token) {
          // @ts-expect-error: socket._opts.query is not defined in the type
          socket._opts.query.token = token;
        } else {
        }

        socket.connect();

        socket.on("connect", () => {
          setIsConnected(true);
        });

        socket.on("disconnect", () => {
          setIsConnected(false);
        });
      } catch (error) {
        socket.connect();
      }
    }

    if (user && token) {
      initializeSocket();

      return () => {
        socket.disconnect();
      };
    }
  }, [user?.id, token]); // Depend on both user and token

  return (
    <SocketContext.Provider value={{ isConnected, token }}>
      {children}
    </SocketContext.Provider>
  );
}

export default SocketProvider;

export function useSocketContext(): SocketContextType {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error("useSocketContext must be used within a SocketProvider");
  }
  return context;
}
