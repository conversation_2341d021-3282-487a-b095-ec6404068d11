@tailwind base;
@tailwind components;
@tailwind utilities;

/* Workaround to fix the shadcn dialog blocking page when being closed problem */
body {
  pointer-events: auto !important;
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

.Mainloader {
  width: 80px;
  aspect-ratio: 1;
  --g1: conic-gradient(from 90deg at top 5px left 5px, #0000 90deg, #535353 0);
  --g2: conic-gradient(
    from -90deg at bottom 5px right 5px,
    #0000 90deg,
    #535353 0
  );
  background: var(--g1), var(--g1), var(--g1), var(--g1), var(--g2), var(--g2),
    var(--g2), var(--g2);
  background-position: 0 0, 100% 0, 100% 100%, 0 100%;
  background-size: 40px 40px;
  background-repeat: no-repeat;
  animation: l11 1.5s infinite;
}

.dark .Mainloader {
  --g1: conic-gradient(from 90deg at top 5px left 5px, #9c9c9c00 90deg, #fff 0);
  --g2: conic-gradient(
    from -90deg at bottom 5px right 5px,
    #9c9c9c00 90deg,
    #fff 0
  );
}

@keyframes l11 {
  0% {
    background-size: 50px 20px, 20px 20px, 20px 50px, 50px 50px;
  }
  25% {
    background-size: 50px 50px, 20px 50px, 20px 20px, 50px 20px;
  }
  50% {
    background-size: 20px 50px, 50px 50px, 50px 20px, 20px 20px;
  }
  75% {
    background-size: 20px 20px, 50px 20px, 50px 50px, 20px 50px;
  }
  100% {
    background-size: 50px 20px, 20px 20px, 20px 50px, 50px 50px;
  }
}

@layer base {
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.65rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 97%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 230 4.8% 85%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 86%;
    --sidebar-borded: 220 13% 92%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 6%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 0 0% 9%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 18%;
    --sidebar-borded: 240 3.7% 15%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.country-select,
.region-select {
  background-color: white;
  color: black;
  padding: 8px 12px;
}

.country-select option,
.region-select option {
  background-color: white;
  color: black;
  padding: 8px 12px;
}

/* Dark mode */
.dark .country-select option,
.dark .region-select option {
  background-color: #212121;
  color: white;
}

@media (prefers-color-scheme: dark) {
  select option {
    background-color: white;
    color: gray;
  }
  .dark select option {
    background-color: #212121;
    color: white;
  }
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  border-radius: 10px;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00786f;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00786f;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}

/* Track styling (the line) */
input[type="range"]::-webkit-slider-runnable-track {
  border-radius: 10px;
}

input[type="range"]::-moz-range-track {
  height: 8px;
  border-radius: 10px;
}

/* Dark mode adjustments */
.dark input[type="range"]::-webkit-slider-thumb {
  border-color: #eee;
}

.dark input[type="range"]::-moz-range-thumb {
  border-color: #eee;
}

.plan-link-highlight {
  background: #aaa !important;
}

.dark .plan-link-highlight {
  background: #555 !important;
}
/* Audio Player Styles */
audio {
  border: none;
}

audio:focus {
  outline: none;
}

@-moz-document url-prefix() {
  audio {
    filter: sepia(100%) saturate(1%) hue-rotate(120deg) brightness(150%);
  }
}
