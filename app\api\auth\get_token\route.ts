import authOptions from "@/lib/AuthOptions";
import jwt from "jsonwebtoken";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Get the NextAuth session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Aucune session NextAuth trouvée." },
        { status: 401 }
      );
    }

    // Generate a JWT token with user information
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error("JWT_SECRET non configuré");
      return NextResponse.json(
        { error: "Configuration du serveur manquante." },
        { status: 500 }
      );
    }

    const token = jwt.sign(
      { sub: { userId: session.user.id, sessionId: session.sessionId } },
      jwtSecret,
      {
        expiresIn: Math.floor(Date.now() / 1000) + 24 * 60 * 60,
      }
    );

    // Return the token and user info
    return NextResponse.json(
      {
        message: "Token généré avec succès.",
        token: token,
        // user: {
        //   id: session.user.id,
        //   email: session.user.email,
        //   name: session.user.name,
        //   image: session.user.image,
        // },
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.error("Erreur lors de la génération du token:", error);
    return NextResponse.json(
      { error: "Erreur interne du serveur." },
      { status: 500 }
    );
  }
}
