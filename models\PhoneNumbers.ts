import mongoose from "mongoose";

enum PhoneNumberStatus {
  ACTIVE = "ACTIVE",
  PENDING = "PENDING",
  INACTIVE = "INACTIVE"
}

const PhoneNumberSchema = new mongoose.Schema({
  number: {
    type: String,
  },
  country: {
    type: String,
  },
  assistant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Assistant",
  },
  status: {
    type: String,
    enum: PhoneNumberStatus,
    default: PhoneNumberStatus.PENDING,
  },
  goals: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Goal",
    }
  ],
  created_at: {
    type: Date,
    default: Date.now,
  },
  entreprise_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Entreprise",
  },
  is_whatsapp: {
    type: Boolean
  },
  is_sms: {
    type: Boolean
  },
  is_calls: {
    type: Boolean
  }
})

const Phone = mongoose.models.Phone || mongoose.model("Phone", PhoneNumberSchema)

export default Phone