import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import ButtonLoader from "@/components/Loaders/ButtonLoader";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";

interface WebhookSecretKeyProps {
  secretKey: string | null;
  onResetKey: () => Promise<void>;
}

export default function WebhookSecretKey({
  secretKey,
  onResetKey,
}: WebhookSecretKeyProps) {
  const [isResettingSecretKey, setIsResettingSecretKey] = useState(false);
  const [open, setOpen] = useState(false);

  const handleResetSecretKey = async () => {
    if (!isResettingSecretKey) {
      setIsResettingSecretKey(true);
      await onReset<PERSON><PERSON>();
      setIsResettingSecretKey(false);
      setOpen(false);
    }
  };

  return (
    <div className="w-full flex justify-between flex-wrap items-center gap-2 p-3 border border-voxa-neutral-200 dark:border-voxa-neutral-800 rounded-lg">
      <div className="flex items-center gap-2 flex-wrap">
        <Label htmlFor="webhook-secret-key" className="font-medium text-base">
          Webhook Secret Key
        </Label>
        <p className="text-sm text-voxa-neutral-400 dark:text-voxa-neutral-500 break-all">
          {secretKey || "No secret key set"}
        </p>
      </div>
      <div className="flex items-center ms-auto">
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              disabled={isResettingSecretKey}
              className={`${
                isResettingSecretKey
                  ? "cursor-not-allowed"
                  : "hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-700"
              }`}
              type="button"
            >
              {isResettingSecretKey ? <ButtonLoader /> : "Reset Secret Key"}
            </Button>
          </DialogTrigger>
          <DialogContent overlayClassName="bg-white/50">
            <DialogHeader>
              <DialogTitle>Reset Secret Key</DialogTitle>
            </DialogHeader>
            <DialogDescription>
              Are you sure you want to reset the webhook secret key? This action
              cannot be undone, and you will need to update your webhook
              endpoints with the new key.
            </DialogDescription>
            <DialogFooter>
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleResetSecretKey}>
                {isResettingSecretKey ? <ButtonLoader /> : "Reset Key"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
