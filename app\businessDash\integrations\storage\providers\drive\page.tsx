/* app/businessDash/drive/page.tsx */
"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

type DriveFile = {
  id: string;
  name: string;
  mimeType: string;
  iconLink: string;
};

export default function GoogleDrivePage() {
  const [files, setFiles] = useState<DriveFile[] | null>(null);
  const [error, setError] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState<string | null>(null);
  const router = useRouter();

  const fetchFiles = async () => {
    try {
      setLoading(true);
      setError(null);
      const res = await fetch("/api/drive/files");
      if(res.status !== 200) {
        setError(res.status);
        return;
      }
      const data = await res.json();
      setFiles(data);
    } catch (err: any) {
      setFiles(null);
      setError(500);
    } finally {
      setLoading(false);
    }
  };

  const disconnect = async () => {
    await fetch("/api/auth/google/logout", { method: "POST" });
    // Clear local state and refresh UI
    setFiles(null);
    setError(null);
    setLoading(false);
    router.refresh(); // remet la page à l'état initial
  };

  useEffect(() => {
    fetchFiles();
  }, []);

  /* —— états non connectés / loading —— */
  if (error === 401) {
    return (
      <div className="p-10">
        <button
          className="px-4 py-2 bg-blue-600 text-white rounded"
          onClick={() => (window.location.href = "/api/auth/google/drive")}
        >
          Connect Google Account
        </button>
      </div>
    );
  }

  if (loading) return <p className="p-10 text-gray-500">Chargement des fichiers…</p>;

  if (!files || files.length === 0) {
    return (
      <div className="p-10">
        <p className="text-gray-500">Aucun fichier trouvé.</p>
        <button className="mt-4 text-sm text-blue-600 hover:underline" onClick={fetchFiles}>
          ↻ Réessayer
        </button>
        {/* bouton de déconnexion quand même disponible */}
        <button className="block mt-6 text-sm text-red-600 hover:underline" onClick={disconnect}>
          ⎋ Se déconnecter
        </button>
      </div>
    );
  }

  /* —— vue fichiers —— */
  return (
    <div className="max-w-3xl mx-auto p-6">
      <div className="flex items-start justify-between">
        <h1 className="text-2xl font-semibold mb-4">Fichiers Google Drive</h1>
        <button
          className="text-sm text-red-600 hover:underline"
          onClick={disconnect}
        >
          ⎋ Se déconnecter
        </button>
      </div>

      <ul className="space-y-2">
        {files.map((file) => (
          <li
            key={file.id}
            className="flex items-center justify-between p-3 bg-gray-50 rounded shadow-sm"
          >
            <span className="flex items-center gap-2">
              <img src={file.iconLink} alt="" className="w-5 h-5" />
              {file.name}
            </span>
            <button
              disabled={downloading === file.id}
              className="text-sm text-blue-600 hover:underline disabled:opacity-50"
              onClick={() => {
                setDownloading(file.id);
                window.location.href = `/api/drive/download?fileId=${file.id}&mime=${encodeURIComponent(
                  file.mimeType
                )}`;
                setDownloading(null);
              }}
            >
              {downloading === file.id ? "Téléchargement…" : "Télécharger"}
            </button>
          </li>
        ))}
      </ul>

      <div className="mt-8 flex items-center gap-4">
        <button className="text-sm text-gray-500 hover:underline" onClick={fetchFiles}>
          ↻ Rafraîchir
        </button>
      </div>
    </div>
  );
}
