// import { DeleteGoal } from "@/actions/NewGoalActions";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Trash2Icon } from "lucide-react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { deleteGroup } from "@/redux/BusinessDashboard/subSlices/ClientsSlice";

export function DeleteGroupDialog({
  groupID,
  groupName,
}: {
  groupID: string;
  groupName: string;
}) {
  const dispatch = useDispatch<AppDispatch>();
  const handleDelete = async () => {
    await dispatch(deleteGroup(groupID));
    window.location.reload();
  };
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Trash2Icon className="text-red-500 w-5 h-5 hover:text-red-600 active:text-red-500/80 cursor-pointer transition-colors" />
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Group</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete{" "}
            <span className="font-semibold">{groupName} </span>? This action
            cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="submit"
              className="hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950"
            >
              {" "}
              Cancel{" "}
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              onClick={handleDelete}
              className="bg-red-500 hover:bg-red-600 active:bg-red-500/70"
            >
              {" "}
              Delete Group{" "}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
