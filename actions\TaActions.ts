"use server";

import dbConnect from "@/lib/mongodb";
import { getEntrepriseByAdminID } from "./Entreprise";
import TA from "@/models/TA";

export async function createTaAgent(
  name: string,
  email: string,
  phone: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const reponse = await getEntrepriseByAdminID();
    if (!reponse.success) {
      return { success: false, error: reponse.error };
    }
    const entrepriseID = reponse.entreprise._id;
    await dbConnect();
    const newTa = await TA.create({
      name,
      email,
      phone,
      entreprise: entrepriseID,
    });

    if (!newTa) {
      throw new Error("Failed to create TA Agent");
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function getTaAgents() {
  try {
    await dbConnect();
    const reponse = await getEntrepriseByAdminID();
    if (!reponse.success) {
      return { success: false, error: reponse.error };
    }
    const entrepriseID = reponse.entreprise._id;
    const taAgents = await TA.find({ entreprise: entrepriseID }).lean();

    if (!taAgents) {
      throw new Error("Failed to fetch TA Agents");
    }

    const formattedTaAgents = taAgents.map((ta) => ({
      id: ta._id?.toString(),
      name: ta.name,
      email: ta.email,
      phone: ta.phone,
    }));

    return { success: true, agents: formattedTaAgents };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}
