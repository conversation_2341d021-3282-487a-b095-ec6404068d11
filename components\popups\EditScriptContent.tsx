import { handleCopyText } from "@/lib/Strings/Copy";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import JsonView from "@uiw/react-json-view";
import { useTheme } from "next-themes";
import { lightTheme } from "@uiw/react-json-view/light";
import { vscodeTheme } from "@uiw/react-json-view/vscode";
import { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { convertDynamicJsonToFlowData } from "@/lib/json/ConvertJsonToFlowData";

const TreeGraph = dynamic(() => import("@/components/Graphs/TreeGraph"), {
  ssr: false,
});

export default function ScriptContentPopup({
  setScriptContentOpen,
  content,
}: {
  setScriptContentOpen: any;
  content: string;
}) {
  const { theme, systemTheme } = useTheme();
  const [isTreeView, setIsTreeView] = useState(false);
  const [parsedJson, setParsedJson] = useState<any>(null);
  const [isValidJson, setIsValidJson] = useState(false);
  const [flowData, setFlowData] = useState<{ nodes: any[]; edges: any[] }>({
    nodes: [],
    edges: [],
  });

  useEffect(() => {
    try {
      const parsed = JSON.parse(content);
      if (typeof parsed === "object" && parsed !== null) {
        setParsedJson(parsed);
        setIsValidJson(true);
        const { nodes, edges } = convertDynamicJsonToFlowData(parsed);
        setFlowData({ nodes, edges });
      } else {
        setIsValidJson(false);
      }
    } catch (error) {
      setIsValidJson(false);
      console.log("Not a valid JSON", error);
    }
  }, [content]);

  const isDarkMode =
    theme === "dark" || (theme === "system" && systemTheme === "dark");

  const toggleView = () => {
    setIsTreeView(!isTreeView);
  };

  return (
    <Dialog open={true} onOpenChange={(open) => setScriptContentOpen(open)}>
      <DialogTrigger asChild>
        <Button className="hidden">Open Dialog</Button>
      </DialogTrigger>
      <DialogContent className="flex flex-col max-w-[750px] h-[calc(100vh-5rem)] p-5 overflow-y-auto gap-4">
        <DialogTitle>Script Content</DialogTitle>
        <div className="grow bg-white dark:bg-[#1e1e1e] text-black/80 dark:text-gray-300 -mx-2 text-sm overflow-y-auto whitespace-pre-line border border-border-voxa-neutral-200 dark:border-voxa-neutral-900 rounded-lg">
          {(() => {
            if (isValidJson) {
              if (isTreeView) {
                return (
                  <div className="h-full w-full">
                    <TreeGraph
                      className="!m-0 !h-full !w-full"
                      nodes={flowData.nodes}
                      edges={flowData.edges}
                    />
                  </div>
                );
              } else {
                return (
                  <JsonView
                    value={parsedJson}
                    style={isDarkMode ? vscodeTheme : lightTheme}
                    displayDataTypes={false}
                    displayObjectSize={false}
                    shortenTextAfterLength={ 100 }
                  />
                );
              }
            }

            return <p dangerouslySetInnerHTML={{ __html: content }} />;
          })()}
        </div>
        <DialogFooter className="flex flex-wrap gap-2 justify-end">
          {isValidJson && (
            <Button
              onClick={toggleView}
              variant="outline"
              size="sm"
              className="text-voxa-teal-500 border-voxa-teal-500 hover:text-voxa-teal-400 hover:border-voxa-teal-400 font-semibold sm:text-sm sm:h-9 sm:px-4 text-[10px] h-7 px-2"
            >
              {isTreeView ? "Display as JSON" : "Display as Tree"}
            </Button>
          )}
          <Button
            onClick={() => {
              const blob = new Blob([content], {
                type: "text/plain;charset=utf-8",
              });
              const url = URL.createObjectURL(blob);
              const a = document.createElement("a");
              a.href = url;
              a.download = "script.txt";
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            }}
            className="bg-voxa-neutral-500 hover:bg-voxa-neutral-600 text-voxa-neutral-50 sm:text-sm sm:h-9 sm:px-4 text-[10px] h-7 px-2"
          >
            Download File
          </Button>
          <Button
            onClick={() => handleCopyText(content)}
            className="bg-voxa-teal-600 hover:bg-voxa-teal-500 transition-all duration-150 text-white rounded-md font-medium flex justify-center items-center gap-2 sm:text-sm sm:h-9 sm:px-4 text-[10px] h-7 px-2"
          >
            Copy Content
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
