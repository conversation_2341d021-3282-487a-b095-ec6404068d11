"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@radix-ui/react-label";
import { toast } from "sonner";
import { useState } from "react";
import ButtonLoader from "../Loaders/ButtonLoader";
import { createTag } from "@/redux/BusinessDashboard/subSlices/AssistantsSlice";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Tag, Check, PlusCircle } from "lucide-react";
import { useTranslation } from "react-i18next";

const colors = [
  "#10B981",
  "#3B82F6",
  "#F59E0B",
  "#EF4444",
  "#8B5CF6",
  "#06B6D4",
  "#84CC16",
  "#F97316",
  "#EC4899",
  "#6B7280",
];

export default function CreateTag() {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const [TagName, setTagName] = useState("");
  const [TagColor, setTagColor] = useState("");
  const [Loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const HandleCreateTagItem = async () => {
    try {
      if (TagName === "" || TagColor === "") {
        toast.error(t("createTag.fillAllFields"));
        return;
      }
      setLoading(true);
      await dispatch(createTag({ TagName, TagColor }));
      setTagName("");
      setTagColor("");
      setOpen(false);
    } catch (error) {
      console.error("Error creating tag:", error);
      toast.error(t("createTag.failedToCreate"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <button className="w-full hover:bg-voxa-neutral-50 dark:hover:bg-accent flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0">
          <PlusCircle />
          <span>{t("createTag.createTag")}</span>
        </button>
      </DialogTrigger>
      <DialogContent className="max-w-96">
        <DialogHeader>
          <DialogTitle>{t("createTag.createNewTag")}</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col gap-3 mt-4">
          <div className="flex w-full gap-2 max-sm:flex-col">
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="Name" className="text-sm pb-1">
                {t("createTag.tagName")}
              </Label>
              <Input
                value={TagName}
                onChange={(e) => setTagName(e.target.value)}
                id="Name"
                placeholder={t("createTag.namePlaceholder")}
              />
            </div>
          </div>
          <Label htmlFor="Name" className="mt-2 text-sm">
            {t("createTag.tagColor")}
          </Label>
          <div className="flex gap-1.5 sm:justify-center items-center">
            <p
              style={{ backgroundColor: TagColor }}
              className={`${
                !TagColor &&
                "border border-sidebar-border bg-sidebar text-voxa-neutral-100"
              } whitespace-nowrap text-sm rounded-full p-1.5 cursor-pointer hover:bg-opacity-75 active:bg-opacity-80 transition-all duration-150 flex items-center gap-1 h-fit w-fit`}
            >
              <Tag className="w-5 h-5" />
            </p>
            <div className="gap-1 p-1 flex flex-wrap items-cente rounded-xl sm:rounded-full z-50 max-sm:w-[146px] bg-sidebar border border-sidebar-border">
              {colors.map((color, index) => (
                <div
                  className={cn(
                    "border-2 border-sidebar-border text-white h-6 w-6 rounded-full flex justify-center items-center cursor-pointer",
                    TagColor === color && "border-voxa-neutral-50"
                  )}
                  key={index}
                  style={{ backgroundColor: color }}
                  onClick={() => setTagColor(color)}
                >
                  {TagColor === color && <Check className="w-3 h-3" />}
                </div>
              ))}
            </div>
          </div>

          <DialogFooter className="mt-8">
            <DialogClose asChild>
              <Button className="dark:hover:text-voxa-neutral-100 hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950">
                {t("createTag.cancel")}
              </Button>
            </DialogClose>
            <Button
              onClick={HandleCreateTagItem}
              disabled={Loading}
              className={`max-sm:mb-3 ${
                Loading
                  ? "cursor-not-allowed bg-voxa-teal-400"
                  : "bg-voxa-teal-600 hover:bg-voxa-teal-500 "
              } transition-all duration-150 text-white px-4 py-2 rounded-md font-medium flex justify-center items-center gap-2`}
            >
              {Loading ? (
                <>
                  {t("createTag.creatingTag")}
                  <ButtonLoader />
                </>
              ) : (
                t("createTag.createTag")
              )}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
