import mongoose from "mongoose";

const InvoiceSchema = new mongoose.Schema(
  {
    // Stripe invoice ID
    stripe_invoice_id: {
      type: String,
      unique: true,
    },

    // Enterprise reference
    entreprise_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Entreprise",
    },

    // Subscription reference (if invoice is for subscription)
    subscription_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subscription",
    },

    // Stripe customer ID
    stripe_customer_id: {
      type: String,
    },

    // Stripe subscription ID (if applicable)
    stripe_subscription_id: {
      type: String,
    },

    // Invoice details
    status: {
      type: String,
      enum: ["draft", "open", "paid", "void", "uncollectible", "upcoming"],
    },

    // Amount details
    amount_due: {
      type: Number,

      min: 0,
    },
    amount_paid: {
      type: Number,

      min: 0,
      default: 0,
    },
    amount_remaining: {
      type: Number,

      min: 0,
    },
    subtotal: {
      type: Number,

      min: 0,
    },
    total: {
      type: Number,

      min: 0,
    },
    tax: {
      type: Number,
      min: 0,
      default: 0,
    },

    // Currency
    currency: {
      type: String,

      default: "eur",
      enum: ["eur", "usd"],
    },

    // Description and metadata
    description: {
      type: String,
    },
    number: {
      type: String, // Invoice number from Stripe
    },

    // Dates
    created: {
      type: Date,
    },
    due_date: {
      type: Date,
    },
    period_start: {
      type: Date,
    },
    period_end: {
      type: Date,
    },
    finalized_at: {
      type: Date,
    },
    paid_at: {
      type: Date,
    },
    voided_at: {
      type: Date,
    },

    // Payment attempts
    next_payment_attempt: {
      type: Date,
    },
    attempt_count: {
      type: Number,
      default: 0,
    },

    // URLs
    hosted_invoice_url: {
      type: String,
    },
    invoice_pdf: {
      type: String,
    },

    // Line items
    lines: [
      {
        id: { type: String },
        amount: { type: Number },
        currency: { type: String },
        description: { type: String },
        quantity: { type: Number },
        unit_amount: { type: Number },
        price: {
          id: { type: String },
          nickname: { type: String },
          product: { type: String },
        },
        period: {
          start: { type: Date },
          end: { type: Date },
        },
      },
    ],

    // Discount and coupons
    discount: {
      coupon: {
        id: { type: String },
        name: { type: String },
        percent_off: { type: Number },
        amount_off: { type: Number },
      },
      amount: { type: Number },
    },

    // Payment method
    default_payment_method: {
      type: { type: String },
      brand: { type: String },
      last4: { type: String },
    },

    // Auto advance (automatically finalize drafts)
    auto_advance: {
      type: Boolean,
      default: true,
    },

    // Collection method
    collection_method: {
      type: String,
      enum: ["charge_automatically", "send_invoice"],
      default: "charge_automatically",
    },

    // Metadata from Stripe
    metadata: {
      type: Map,
      of: String,
      default: new Map(),
    },

    // Timestamps
    created_at: {
      type: Date,
      default: Date.now,
    },
    updated_at: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: { createdAt: "created_at", updatedAt: "updated_at" },
  }
);

const Invoice =
  mongoose.models.Invoice || mongoose.model("Invoice", InvoiceSchema);

export default Invoice;
