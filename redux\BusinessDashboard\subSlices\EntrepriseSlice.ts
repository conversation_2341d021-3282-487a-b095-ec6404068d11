import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { toast } from "sonner";
import {
  getEntrepriseByAdminID,
  getEntrepriseByAdminWithFields,
} from "@/actions/Entreprise";
import type { Entreprise } from "@/types";
import { RootState } from "@/redux/store";

interface EntrepriseState {
  entreprise: Entreprise | null;
  loading: boolean;
  error: string | null;
}

const initialState: EntrepriseState = {
  entreprise: null,
  loading: false,
  error: null,
};

export const fetchEntreprise = createAsyncThunk(
  "BusinessDashboardEntreprise/fetchEntreprise",
  async (
    fields: ENTREPRISE_ALLOWED_FIELDS_TYPE[] | undefined,
    { dispatch, getState, rejectWithValue }
  ) => {
    try {
      dispatch(setEntrepriseLoading(true));
      const response = await getEntrepriseByAdminWithFields(fields);
      if (response.success && response.data) {
        const { entreprise: oldEntreprise } = (getState() as RootState)
          .businessDashboard.businessDashboardEntreprise;
        dispatch(
          setEntreprise({
            ...(oldEntreprise || {}),
            ...response.data,
          })
        );
      } else {
        toast.error(response.error || "Failed to fetch entreprise");
        return rejectWithValue(response.error || "Failed to fetch entreprise");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch entreprise");
      return rejectWithValue(error.message || "Failed to fetch entreprise");
    } finally {
      dispatch(setEntrepriseLoading(false));
    }
  }
);

const EntrepriseSlice = createSlice({
  name: "BusinessDashboardEntreprise",
  initialState,
  reducers: {
    setEntreprise: (state, action) => {
      state.entreprise = action.payload;
      state.error = null;
    },
    setEntrepriseLoading: (state, action) => {
      state.loading = action.payload;
    },
    setEntrepriseError: (state, action) => {
      state.error = action.payload;
    },
    resetEntrepriseState: (state) => {
      state.entreprise = null;
      state.loading = false;
      state.error = null;
    },
  },
});

export const {
  setEntreprise,
  setEntrepriseLoading,
  setEntrepriseError,
  resetEntrepriseState,
} = EntrepriseSlice.actions;

export default EntrepriseSlice.reducer;

export const ENTREPRISE_ALLOWED_FIELDS = [
  "name",
  "assistants",
  "admin",
  "agents",
  "numbers",
  "siret",
  "kbis",
  "sepa",
  "rib",
  "phone",
  "corpName",
  "country",
  "region",
  "street",
  "appartment",
  "legalRepresentantName",
  "legalRepresentantIdendity",
  "cpa",
  "field",
  "credit",
  "balance",
  "balanceAlertThreshold",
  "representantIsOwner",
  "stripe_customer_id",
  "payments",
  "subscriptions",
  "current_subscription",
  "invoices",
  "documents",
  "clients",
  "tags",
  "scripts",
  "aws_credentials",
  "created_at",
] as const;

export type ENTREPRISE_ALLOWED_FIELDS_TYPE =
  (typeof ENTREPRISE_ALLOWED_FIELDS)[number];
