"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { SunMoon, Languages, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { useTheme } from "next-themes";
import { themes } from "@/providers/ThemeProvider";
import { cn } from "@/lib/utils";
import i18nConfig from "@/next-i18next.config.js";
import SettingsRoundedIcon from "@mui/icons-material/SettingsRounded";

export default function SettingsDropdown({
  triggerClass,
  contentClass,
  variant = "ghost",
}: {
  triggerClass?: string;
  contentClass?: string;
  chevronClass?: string;
  variant?: "ghost" | "outline";
}) {
  const { theme, setTheme } = useTheme();
  const { i18n } = useTranslation();
  const { t } = useTranslation("home");

  React.useEffect(() => {
    document.documentElement.dir = i18n.language === "ar" ? "rtl" : "ltr";
  }, [i18n.language]);

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className={cn("w-10 h-10", triggerClass)}>
        <Button variant={variant}>
          <SettingsRoundedIcon className={cn("scale-125 transition-all")} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className={cn(contentClass)}>
        <DropdownMenuSub>
          <DropdownMenuSubTrigger className="flex gap-1 items-center">
            <Languages className="h-4 w-4" />
            {t("settings.0.title")}
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            {i18nConfig.i18n.locales.map((code) => (
              <DropdownMenuCheckboxItem
                className={cn(
                  "gap-1 flex items-center",
                  i18n.language === code &&
                    "bg-voxa-neutral-100 dark:bg-voxa-neutral-800"
                )}
                key={code}
                onClick={() => changeLanguage(code)}
              >
                {i18n.language === code && (
                  <Check className="absolute left-2 h-4 w-4" />
                )}
                <span>{t(`settings.0.items.${code}`)}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuSubContent>
        </DropdownMenuSub>
        <DropdownMenuSub>
          <DropdownMenuSubTrigger className="flex gap-1 items-center">
            <SunMoon className="h-4 w-4" />
            <span>{t("settings.1.title")}</span>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            {themes.map(({ mode, icon }) => (
              <DropdownMenuCheckboxItem
                className={cn(
                  "gap-1 flex items-center",
                  theme === mode &&
                    "bg-voxa-neutral-100 dark:bg-voxa-neutral-800"
                )}
                key={mode}
                onClick={() => setTheme(mode)}
              >
                {theme === mode && (
                  <Check className="absolute left-2 h-4 w-4" />
                )}
                {React.cloneElement(icon, {
                  className: "h-4 w-4",
                })}
                <span>{t(`settings.1.items.${mode}`)}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuSubContent>
        </DropdownMenuSub>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
