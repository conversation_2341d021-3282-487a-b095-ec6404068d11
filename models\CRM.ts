import mongoose from "mongoose";

const CRMSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    entreprise: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Entreprise',
        required: true
    },
    apiKey: {
        type: String,
        required: true
    },
    baseUrl: {
        type: String,
        required: true
    },
    created_at: {
      type: Date,
      default: Date.now,
    }
})

const CRM = mongoose.models.CRM || mongoose.model('CRM', CRMSchema)

export default CRM