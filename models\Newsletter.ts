import mongoose from "mongoose";

const newsletterSchema = new mongoose.Schema({
  email: {
    type: String,
    required: [true, "Email is required"],
    unique: [true, "Em<PERSON> already subscribed"],
    validate: {
      validator: function (email: string) {
        return /^\S+@\S+\.\S+$/.test(email);
      },
      message: "L'email est invalide.",
    },
  },
  subscribedAt: {
    type: Date,
    default: Date.now,
  },
});

const Newsletter = mongoose.models.Newsletter || mongoose.model("Newsletter", newsletterSchema);
export default Newsletter;