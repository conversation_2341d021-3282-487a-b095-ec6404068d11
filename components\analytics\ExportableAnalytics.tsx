"use client";

import { AnalyticsCard } from "@/components/analytics/AnalyticsCard";
import CallStatusByTimeLineChart from "@/components/analytics/CallStatusByTimeLineChart";
import { CallStatusChart } from "@/components/analytics/CallStatusChart";
import ConversationsMapChartWrapper from "@/components/analytics/ConversationsMapChartWrapper";
import ConversationsTypesChart from "@/components/analytics/ConversationsTypeChart";
import ExportInfoBar from "@/components/analytics/ExportInfoBar";
import HeatmapChart from "@/components/analytics/HeatmapChart";
import TotalCallDurationChart from "@/components/analytics/TotalCallDurationChart";
import { CountryCode } from "@/lib/countries";
import {
  convertCountriesToUppercase,
  setLoading,
} from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { RootState, store } from "@/redux/store";
import { forwardRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import useSWR from "swr";

// Helper function to format seconds to a readable time string
const formatSeconds = (seconds: number): string => {
  if (!seconds && seconds !== 0) return "N/A";

  if (seconds < 60) {
    return `${Math.round(seconds)} sec`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return remainingSeconds > 0
      ? `${minutes} min ${remainingSeconds} sec`
      : `${minutes} min`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
};

// SWR fetcher for conversation metrics (local logic, not via redux thunk)
const fetchConversationMetricsSWR = async (): Promise<any> => {
  const filters = store.getState().analytics.filters;
  const {
    startDate,
    endDate,
    conversationTypes,
    countries,
    callDuration,
    ringingTime,
  } = filters;

  const { getConversationMetrics } = await import("@/actions/AnalyticsActions");
  const res = await getConversationMetrics(
    startDate ? new Date(startDate) : undefined,
    endDate ? new Date(endDate) : undefined,
    conversationTypes,
    countries.length
      ? convertCountriesToUppercase(countries as CountryCode[])
      : countries,
    callDuration,
    ringingTime
  );

  if (!res.success) {
    throw new Error(res.error || "Failed to fetch conversation metrics");
  }
  if (!("data" in res)) {
    throw new Error("No data returned from getConversationMetrics");
  }
  return res.data;
};

interface ExportableAnalyticsProps {
  visible?: boolean;
}

const ExportableAnalytics = forwardRef<
  HTMLDivElement,
  ExportableAnalyticsProps
>(({ visible = false }, ref) => {
  const { exportedAnalytics } = useSelector(
    (state: RootState) => state.analytics
  );
  const filters = useSelector((state: RootState) => state.analytics.filters);
  const dispatch = useDispatch();

  // Hardcoded setLoading for each chart type
  const setCallStatusChartLoading = (loading: boolean) => {
    dispatch(setLoading({ callStatusChart: loading }));
  };
  const setTotalCallDurationChartLoading = (loading: boolean) => {
    dispatch(setLoading({ totalCallDurationChart: loading }));
  };
  const setConversationsTypesChartLoading = (loading: boolean) => {
    dispatch(setLoading({ conversationsTypesChart: loading }));
  };
  const setTimelineDayLoading = (loading: boolean) => {
    dispatch(setLoading({ callStatusByTimeLine: { day: loading } }));
  };
  const setTimelineWeekLoading = (loading: boolean) => {
    dispatch(setLoading({ callStatusByTimeLine: { week: loading } }));
  };
  const setTimelineMonthLoading = (loading: boolean) => {
    dispatch(setLoading({ callStatusByTimeLine: { month: loading } }));
  };
  const setTimelineWeekdayLoading = (loading: boolean) => {
    dispatch(setLoading({ callStatusByTimeLine: { weekday: loading } }));
  };
  const setTimelineHourLoading = (loading: boolean) => {
    dispatch(setLoading({ callStatusByTimeLine: { hour: loading } }));
  };
  const setHeatmapTotalLoading = (loading: boolean) => {
    dispatch(setLoading({ heatmap: { total: loading } }));
  };
  const setHeatmapAnsweredLoading = (loading: boolean) => {
    dispatch(setLoading({ heatmap: { answered: loading } }));
  };
  const setHeatmapMissedLoading = (loading: boolean) => {
    dispatch(setLoading({ heatmap: { missed: loading } }));
  };
  const setConversationsMapLoading = (loading: boolean) => {
    dispatch(setLoading({ conversationsMap: loading }));
  };

  // Use SWR to fetch conversation metrics (local fetcher)
  const { data: conversationMetrics, isLoading: conversationMetricsLoading } =
    useSWR(["conversationMetrics", filters], fetchConversationMetricsSWR, {
      revalidateOnFocus: false,
      dedupingInterval: 10000,
    });

  useEffect(() => {
    dispatch(
      setLoading({
        averageCallDuration: conversationMetricsLoading,
        averageClientEngagement: conversationMetricsLoading,
        averageRingingTime: conversationMetricsLoading,
        mostTalkativeSpeaker: conversationMetricsLoading,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conversationMetricsLoading]);

  return (
    <div
      ref={ref}
      className={`analytics-container flex flex-col gap-2 bg-background ${
        !visible ? "" : ""
      }`}
      style={{ width: visible ? undefined : "100%" }}
    >
      <ExportInfoBar />
      <div className="flex flex-wrap [&>*]:grow [&>*]:w-fit gap-2">
        {exportedAnalytics.callStatusChart && (
          <CallStatusChart
            isAnimationActive={false}
            setLoading={setCallStatusChartLoading}
          />
        )}
        {exportedAnalytics.totalCallDurationChart && (
          <TotalCallDurationChart
            isAnimationActive={false}
            setLoading={setTotalCallDurationChartLoading}
          />
        )}
      </div>
      <div className="flex flex-wrap [&>*]:grow [&>*]:w-fit gap-2">
        {exportedAnalytics.conversationsTypesChart && (
          <ConversationsTypesChart
            isAnimationActive={false}
            setLoading={setConversationsTypesChartLoading}
          />
        )}
      </div>
      <div className="flex flex-wrap gap-2">
        {exportedAnalytics.averageRingingTime && (
          <AnalyticsCard
            title="Average Ringing Time"
            tooltip="Average time a caller waits before an agent picks up the call"
            value={formatSeconds(conversationMetrics?.averageRingingTime || 0)}
            rightLabel={`max: ${formatSeconds(
              conversationMetrics?.maxRingingTime || 0
            )}`}
            loading={conversationMetricsLoading}
          />
        )}
        {exportedAnalytics.averageCallDuration && (
          <AnalyticsCard
            title="Average Call Duration"
            tooltip="Average length of all completed calls"
            value={formatSeconds(conversationMetrics?.averageCallDuration || 0)}
            rightLabel={`max: ${formatSeconds(
              conversationMetrics?.maxCallDuration || 0
            )}`}
            loading={conversationMetricsLoading}
          />
        )}
        {exportedAnalytics.averageClientEngagement && (
          <AnalyticsCard
            title="Average Client Engagement"
            tooltip="Average engagement score for all conversations"
            value={`${conversationMetrics?.averageClientEngagement || 0} sec`}
            loading={conversationMetricsLoading}
          />
        )}
        {exportedAnalytics.mostTalkativeSpeaker &&
          conversationMetrics?.mostTalkativeSpeakerCount && (
            <AnalyticsCard
              title="Most Talkative Speaker"
              tooltip="The participant who talks the most in meetings"
              value={conversationMetrics.most_talkative_speaker_name}
              rightLabel={`${conversationMetrics.mostTalkativeSpeakerCount} meetings`}
              loading={conversationMetricsLoading}
            />
          )}
      </div>
      {exportedAnalytics.callStatusByTimeLine.day && (
        <CallStatusByTimeLineChart
          timelinePeriod={"day"}
          isAnimationActive={false}
          setLoading={setTimelineDayLoading}
        />
      )}
      {exportedAnalytics.callStatusByTimeLine.week && (
        <CallStatusByTimeLineChart
          timelinePeriod={"week"}
          isAnimationActive={false}
          setLoading={setTimelineWeekLoading}
        />
      )}
      {exportedAnalytics.callStatusByTimeLine.month && (
        <CallStatusByTimeLineChart
          timelinePeriod={"month"}
          isAnimationActive={false}
          setLoading={setTimelineMonthLoading}
        />
      )}
      {exportedAnalytics.callStatusByTimeLine.weekday && (
        <CallStatusByTimeLineChart
          timelinePeriod={"weekday"}
          isAnimationActive={false}
          setLoading={setTimelineWeekdayLoading}
        />
      )}
      {exportedAnalytics.callStatusByTimeLine.hour && (
        <CallStatusByTimeLineChart
          timelinePeriod={"hour"}
          isAnimationActive={false}
          setLoading={setTimelineHourLoading}
        />
      )}
      {exportedAnalytics.heatmap.total && (
        <HeatmapChart
          baseId="heatmap-0"
          defaultMetric="total"
          isAnimationActive={false}
          setLoading={setHeatmapTotalLoading}
        />
      )}
      {exportedAnalytics.heatmap.answered && (
        <HeatmapChart
          baseId="heatmap-1"
          defaultMetric="answered"
          isAnimationActive={false}
          setLoading={setHeatmapAnsweredLoading}
        />
      )}
      {exportedAnalytics.heatmap.missed && (
        <HeatmapChart
          baseId="heatmap-2"
          defaultMetric="missed"
          isAnimationActive={false}
          setLoading={setHeatmapMissedLoading}
        />
      )}
      {exportedAnalytics.conversationsMap && (
        <ConversationsMapChartWrapper setLoading={setConversationsMapLoading} />
      )}
    </div>
  );
});

ExportableAnalytics.displayName = "ExportableAnalytics";

export default ExportableAnalytics;
