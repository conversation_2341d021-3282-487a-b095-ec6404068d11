import React from "react";
import { useTranslation } from "react-i18next";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  setMessageOnMissedCallContent,
  setMessagesOnMissedCall,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";

export const MessagesDrop: React.FC = () => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();
  const { messagesOnMissedCall, messageOnMissedCallContent } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );
  return (
    <div className="space-y-2">
      <div className="flex max-sm:flex-col sm:items-center gap-2">
        <h1 className="font-semibold text-lg text-nowrap">
          {t("createEditGoal.advanced.messagesDrop")}
        </h1>
        <RadioGroup
          value={messagesOnMissedCall}
          className="flex flex-wrap max-sm:text-xs"
          onValueChange={(value) => dispatch(setMessagesOnMissedCall(value))}
        >
          <div className="flex text-nowrap items-center space-x-1">
            <RadioGroupItem value="NONE" id="r1" />
            <Label htmlFor="r1">{t("createEditGoal.advanced.noMessage")}</Label>
          </div>
          <div className="flex items-center space-x-1">
            <RadioGroupItem value="SMS" id="r2" />
            <Label htmlFor="r2">{t("createEditGoal.advanced.sms")}</Label>
          </div>
          <div className="flex items-center space-x-1">
            <RadioGroupItem value="WHATSAPP" id="r3" />
            <Label htmlFor="r3">{t("createEditGoal.advanced.whatsapp")}</Label>
          </div>
        </RadioGroup>
      </div>
      <p className="text-voxa-neutral-700 text-sm flex flex-col">
        {t("createEditGoal.advanced.messagesDropInfo")}
        {messageOnMissedCallContent.length > 160 &&
          messagesOnMissedCall === "SMS" && (
            <span className="text-xs text-orange-500 max-w-[35rem]">
              {t("createEditGoal.advanced.smsLimitInfo")}
            </span>
          )}
      </p>
      {["SMS", "WHATSAPP"].includes(messagesOnMissedCall) && (
        <>
          <Textarea
            placeholder={t("createEditGoal.advanced.messagePlaceholder")}
            rows={5}
            onChange={(e) =>
              dispatch(setMessageOnMissedCallContent(e.target.value))
            }
            value={messageOnMissedCallContent}
          />
          <span className="pr-2 pt-1 w-full flex justify-end text-xs font-medium text-nowrap">
            {messageOnMissedCallContent.length}{" "}
            {t("createEditGoal.advanced.charactersLabel")}
          </span>
        </>
      )}
    </div>
  );
};
