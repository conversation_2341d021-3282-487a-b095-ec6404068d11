import { createPlanLink } from "@/actions/BillingActions";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { toast } from "sonner";
import { mutate } from "swr";

interface PlanLinkDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  planId: string;
}

export default function PlanLinkDialog({
  open,
  onOpenChange,
  planId,
}: PlanLinkDialogProps) {
  const [usesLeftType, setUsesLeftType] = useState<"unlimited" | "limited">(
    "unlimited"
  );
  const [usesLeftValue, setUsesLeftValue] = useState<string>("");
  const [planLinkLoading, setPlanLinkLoading] = useState(false);

  const handleCreatePlanLink = async () => {
    setPlanLinkLoading(true);
    try {
      const uses_left =
        usesLeftType === "unlimited" ? null : Number(usesLeftValue) || 1;
      const res = await createPlanLink(planId, uses_left);
      if (res.success && res.data) {
        toast.success("Plan link created!");
        // Mutate the admin plan links cache to add the new plan link
        mutate(["admin-plan-links", 1, 10], (current: any) => {
          if (!current) return;
          return {
            ...current,
            planLinks: [res.data, ...(current.planLinks || [])],
            total: (current.total || 0) + 1,
          };
        });
        setTimeout(() => {
          const planLinkEl = document.querySelector(".plan-link");
          if (planLinkEl) {
            planLinkEl.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
            planLinkEl.classList.add("plan-link-highlight");
            setTimeout(() => {
              planLinkEl.classList.remove("plan-link-highlight");
            }, 600);
          }
        }, 100);
        onOpenChange(false); // Close the dialog
      } else {
        toast.error(res.error || "Failed to create plan link");
      }
    } catch (err: any) {
      toast.error(err?.message || "Failed to create plan link");
    }
    setPlanLinkLoading(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto border border-voxa-neutral-200 dark:border-voxa-neutral-800 rounded-lg dark:bg-voxa-neutral-800">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-foreground/80">
            Create Plan Link
          </DialogTitle>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleCreatePlanLink();
          }}
        >
          <div className="space-y-4 mt-2">
            {/* Usage Limit box: solid border */}
            <div className="dark:border-voxa-neutral-600 rounded-lg bg-muted/20">
              <Label className="text-sm font-medium">Usage Limit</Label>
              <div className="flex gap-3 mt-2">
                <Button
                  type="button"
                  variant={usesLeftType === "unlimited" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setUsesLeftType("unlimited")}
                  className={`whitespace-nowrap ${
                    usesLeftType === "unlimited"
                      ? "bg-voxa-teal-600 text-white border-voxa-teal-600 hover:bg-voxa-teal-700 dark:bg-voxa-teal-700 dark:text-voxa-teal-400"
                      : "border-voxa-teal-600 text-voxa-teal-700 hover:bg-voxa-teal-50 dark:hover:bg-voxa-teal-800"
                  }`}
                >
                  Unlimited
                </Button>
                <Button
                  type="button"
                  variant={usesLeftType === "limited" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setUsesLeftType("limited")}
                  className={`whitespace-nowrap ${
                    usesLeftType === "limited"
                      ? "bg-voxa-teal-600 text-white border-voxa-teal-600 hover:bg-voxa-teal-700 dark:bg-voxa-teal-700 dark:text-voxa-teal-400"
                      : "border-voxa-teal-600 text-voxa-teal-700 hover:bg-voxa-teal-50 dark:hover:bg-voxa-teal-800"
                  }`}
                >
                  Limited
                </Button>
              </div>
              {usesLeftType === "limited" && (
                <div className="mt-4">
                  <Label
                    htmlFor="usesLeftValue"
                    className="text-sm font-medium"
                  >
                    Number of uses
                  </Label>
                  <Input
                    id="usesLeftValue"
                    type="number"
                    min={1}
                    value={usesLeftValue}
                    onChange={(e) => setUsesLeftValue(e.target.value)}
                    placeholder="Enter number of uses"
                    className="mt-1"
                  />
                </div>
              )}
            </div>
          </div>
          <DialogFooter className="mt-4">
            <Button
              type="submit"
              disabled={
                planLinkLoading ||
                (usesLeftType === "limited" && !usesLeftValue)
              }
              className="flex-1 text-voxa-neutral-50 bg-voxa-teal-600 hover:bg-voxa-teal-500"
            >
              {planLinkLoading ? "Creating..." : "Create Link"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
