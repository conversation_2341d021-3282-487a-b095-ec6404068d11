import { NextResponse } from "next/server";
import { subscribeToNewsletter } from "@/actions/NewsletterActions";
export async function POST(request: Request) {
    try {
        const { email } = await request.json();
        if (!email || typeof email !== "string" || !email.includes("@")) {
        return NextResponse.json({ success: false, error: "Invalid email address." }, { status: 400 });
        }
    
        const response = await subscribeToNewsletter(email);
        if (!response.success) {
        return NextResponse.json({ success: false, error: response.error }, { status: 500 });
        }
    
        return NextResponse.json({ success: true, message: response.message }, { status: 200 });
    } catch (error) {
        console.error("Error in newsletter subscription:", error);
        return NextResponse.json({ success: false, error: "Internal server error." }, { status: 500 });
    }
}