"use client";

import CloseRoundedIcon from "@mui/icons-material/CloseRounded";
import { NotesTabs } from "@/components/tabs/NotesTabs";
import AudioPlayer from "@/components/Player/AudioPlayer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  setNotesOpen,
  setConversationLiked,
  setIsHistoryVisible,
} from "@/redux/BusinessDashboard/subSlices/RootSlice";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import {
  CheckCheckIcon,
  MessageSquareReplyIcon,
  MessageSquareShareIcon,
  XCircleIcon,
  PhoneIncomingIcon,
  PhoneMissedIcon,
  PhoneOutgoingIcon,
  ArrowDownLeft,
  ArrowUpRight,
  VoicemailIcon,
  ThumbsUp,
  ThumbsDown,
} from "lucide-react";
import { getTimeFromTimestamp } from "@/lib/Strings/DateFormat";
import { IoLogoWhatsapp } from "react-icons/io";
import phoneNumberFormat from "@/lib/Strings/PhoneNumberFormat";
import { useTranslation } from "react-i18next";
import Image from "next/image";
import GoogleMeetIcon from "@/public/images/Icons/google_meet.svg";
import TagsNotesDrawer from "./TagsNotesDrawer";
import { cn } from "@/lib/utils";
import React, { useEffect } from "react";
import CircularStat from "@/components/progress/CirclarStat";
import CircularLoaderSmall from "@/components/Loaders/CircularLoaderSmall";
import { useTranslationButton } from "@/hooks/useTranslationButton";
import ExportCallDialog from "./ExportCallDialog";

export default function History() {
  const { t } = useTranslation("callLogs");
  const dispatch = useDispatch<AppDispatch>();
  const { callDetails, NotesOpen, isHistoryVisible, callTranscriptOpen } =
    useSelector(
      (state: RootState) => state.businessDashboard.businessDashboardRoot
    );

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (NotesOpen) {
      timeout = setTimeout(() => dispatch(setIsHistoryVisible(true)), 0.5);
    } else {
      dispatch(setIsHistoryVisible(false));
    }
    return () => clearTimeout(timeout);
  }, [NotesOpen, dispatch]);

  const formatSpeakingTime = (seconds?: number) => {
    if (!seconds) return "-";

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Number((seconds % 60).toFixed(2));

    if (minutes === 0) {
      return `${remainingSeconds}s`;
    }

    return `${minutes}m ${remainingSeconds}s`;
  };

  const getStatusIcon = (status: string | undefined) => {
    const upperStatus = status?.toUpperCase() ?? "UNKNOWN";
    const wrapperClass = "self-end w-4 h-4 mt-2";
    switch (upperStatus) {
      case "FAILED":
      case "UNDELIVERED":
      case "CANCELED":
        return (
          <div title={upperStatus} className={wrapperClass}>
            <XCircleIcon className="text-red-500 w-4 h-4" />
          </div>
        );
      default:
        return (
          <div title={upperStatus} className={wrapperClass}>
            <CheckCheckIcon className="text-gray-500 w-4 h-4" />
          </div>
        );
    }
  };

  const handleThumbClick = (like: "up" | "down") => {
    if (!callDetails?._id) return;
    let newValue: boolean | null;
    if (like === "up") {
      newValue = callDetails.isLiked === true ? null : true;
    } else {
      newValue = callDetails.isLiked === false ? null : false;
    }
    dispatch(
      setConversationLiked({
        conversationId: callDetails._id,
        isLiked: newValue,
      })
    );
  };

  const {
    translatedText: summaryTranslatedText,
    loading: summaryLoading,
    button: summaryButton,
  } = useTranslationButton({ text: callDetails.summary });

  const {
    translatedText: feelingsTranslatedText,
    loading: feelingsLoading,
    button: feelingsButton,
  } = useTranslationButton({ text: callDetails.feelings });

  return (
    <div
      className={cn(
        "max-md:fixed max-w-md w-full h-full top-0 right-0 duration-300 transition-all ease-in-out -ml-[450px]",
        isHistoryVisible && "-ml-0",
        callTranscriptOpen && "duration-500"
      )}
    >
      <div
        className={cn(
          "md:fixed z-50 rounded-sm max-w-md w-full flex flex-col gap-4 h-screen items-center right-0 rtl:right-auto rtl:left-0 top-0 bg-voxa-neutral-50/80 dark:bg-black/60 backdrop-blur-sm duration-300 transition-all ease-in-out translate-x-[450px]",
          isHistoryVisible && "translate-x-0",
          callTranscriptOpen && "duration-500"
        )}
      >
        <div className="relative flex justify-between pt-3 px-3 items-center w-full text-foreground/50 dark:text-voxa-neutral-50">
          <h1 className="ml-6 w-full text-xl font-semibold text-center">
            {callDetails.type?.toLowerCase() === "sms"
              ? t("callDetails.smsDetails")
              : callDetails.type?.toLowerCase() === "whatsapp"
              ? t("callDetails.whatsappDetails")
              : t("callDetails.title")}
          </h1>
          <button
            onClick={() => {
              dispatch(setIsHistoryVisible(false));
              setTimeout(() => {
                dispatch(setNotesOpen(false));
              }, 200);
            }}
            className="hover:text-gray-400 transition-all duration-150"
          >
            <CloseRoundedIcon />
          </button>
        </div>

        <div className="overflow-y-auto overflow-x-hidden w-full">
          <div className="mt-4 flex flex-col gap-1 items-center w-full px-3 sm:px-5 pb-5">
            {callDetails.is_voicemail_drop === true ? (
              <div className="bg-indigo-500/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
                <VoicemailIcon className="text-indigo-500 w-8 h-8 mr-px" />
              </div>
            ) : callDetails.type?.toLowerCase() === "meet" ? (
              <div className="bg-orange-500/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
                <Image
                  src={GoogleMeetIcon}
                  alt="Google Meet Icon"
                  className="w-5 h-5 mr-px"
                />
              </div>
            ) : callDetails.type?.toLowerCase() === "sms" ? (
              callDetails.Direction?.toLowerCase() === "outbound_api" ? (
                <MessageSquareShareIcon className="text-teal-500 w-8 h-8 mr-px" />
              ) : callDetails.Direction?.toLowerCase() === "inbound" ? (
                <MessageSquareReplyIcon className="text-teal-500 w-8 h-8 mr-px" />
              ) : (
                ""
              )
            ) : callDetails.type?.toLowerCase() === "whatsapp" ? (
              callDetails.Direction?.toLowerCase() === "outbound_api" ? (
                <div className="flex flex-col w-full justify-center items-center gap-2">
                  <div className="relative w-fit h-fit">
                    <div className="bg-voxa-teal-600/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
                      <IoLogoWhatsapp className="text-teal-500 w-8 h-8 mr-px" />
                    </div>
                    <ArrowUpRight
                      className="absolute -top-2 -right-2 text-teal-500 w-6 h-6"
                      strokeWidth={3}
                    />
                  </div>
                  <span className="text-xs font-light dark:text-gray-200">
                    Outgoing Message
                  </span>
                </div>
              ) : callDetails.Direction?.toLowerCase() === "inbound" ? (
                <div className="flex flex-col w-full justify-center items-center gap-2">
                  <div className="relative w-fit h-fit">
                    <div className="bg-voxa-teal-600/20 rounded-full p-2 w-fit h-fit flex items-center justify-center">
                      <IoLogoWhatsapp className="text-teal-500 w-8 h-8 mr-px" />
                    </div>
                    <ArrowDownLeft
                      className="absolute -top-2 -right-2 text-teal-500 w-6 h-6"
                      strokeWidth={3}
                    />
                  </div>
                  <span>Incoming Message</span>
                </div>
              ) : (
                ""
              )
            ) : callDetails.Direction === "inbound" ? (
              <PhoneIncomingIcon className="text-indigo-600 w-8 h-8" />
            ) : callDetails.CallStatus === "missed" ||
              callDetails.CallStatus === "no-answer" ||
              callDetails.CallDuration == 0 ? (
              <PhoneMissedIcon className="text-red-600 w-8 h-8" />
            ) : (
              <PhoneOutgoingIcon className="text-green-600 w-8 h-8" />
            )}
            <p className="text-black/80 dark:text-voxa-neutral-50 font-medium">
              {callDetails.client_name}
            </p>
            <p className="text-black/80 dark:text-voxa-neutral-50 font-medium">
              {phoneNumberFormat(callDetails.To, callDetails.country)}
            </p>
            <p className="text-black/60 dark:text-voxa-neutral-200 font-medium text-xs">
              {getTimeFromTimestamp(callDetails.TimeStamp)}
            </p>
            <div className="my-2 relative gap-2 flex items-center justify-center w-full">
              <TagsNotesDrawer />
              <ExportCallDialog />
            </div>
            <p className="text-black/80 dark:text-voxa-neutral-50 font-semibold">
              {t("callDetails.evaluateAI")}
            </p>
            <div className="-mt-0.5 mb-5 relative gap-1 flex items-center justify-center w-full">
              <button
                aria-label="Thumbs Up"
                onClick={() => handleThumbClick("up")}
                className={`transition-colors p-1 rounded-full border border-transparent ${
                  callDetails.isLiked === true
                    ? "bg-sky-600 text-white"
                    : "hover:bg-sky-200 dark:hover:bg-sky-900 text-sky-600"
                }`}
              >
                <ThumbsUp className="size-4" />
              </button>
              <button
                aria-label="Thumbs Down"
                onClick={() => handleThumbClick("down")}
                className={`transition-colors p-1 rounded-full border border-transparent ${
                  callDetails.isLiked === false
                    ? "bg-red-500 text-white"
                    : "hover:bg-red-200 dark:hover:bg-red-800/40 text-red-500"
                }`}
              >
                <ThumbsDown className="size-4" />
              </button>
            </div>

            {callDetails.RecordingURL ? (
              <AudioPlayer
                skip={
                  callDetails.forward_start_time_in_audio > 0
                    ? parseInt(callDetails.forward_start_time_in_audio)
                    : 0 || 0
                }
              />
            ) : (
              callDetails.duration > 0 &&
              !callDetails.RecordingURL && (
                <Card className="w-full mt-4 bg-voxa-neutral-50 dark:bg-voxa-neutral-900">
                  <CardHeader className="pb-2 ">
                    <CardTitle className="text-lg font-medium text-center text-voxa-teal-600">
                      {t("callDetails.audioRecording")}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="w-full text-sm text-start text-black/40 dark:text-gray-300">
                      {t("callDetails.processingAudio")}
                    </p>
                  </CardContent>
                </Card>
              )
            )}
            {["sms", "whatsapp"].includes(callDetails.type?.toLowerCase()) ? (
              callDetails.body && (
                <div className="w-full mt-4 bg-voxa-neutral-100 dark:bg-voxa-neutral-950  rounded-md p-2 text-xs dark:text-voxa-neutral-300 flex justify-between items-center flex-col border border-sidebar-border">
                  <p className="w-full text-start">{callDetails.body}</p>
                  {getStatusIcon(callDetails.CallStatus)}
                </div>
              )
            ) : (
              <>
                {/* Call Summary */}
                {callDetails.summary && (
                  <Card className="p-3 w-full mt-4 bg-voxa-neutral-100 dark:bg-voxa-neutral-950">
                    <CardHeader className="relative p-0 flex flex-row items-center justify-between">
                      <CardTitle className="text-lg font-medium text-center text-voxa-teal-600">
                        {t("callDetails.summary")}
                      </CardTitle>
                      <div className="absolute right-0">{summaryButton}</div>
                    </CardHeader>
                    <CardContent className="p-0 pt-2">
                      <div className="text-voxa-neutral-800 dark:text-gray-300 text-sm rounded-md flex flex-col">
                        {summaryLoading ? (
                          <div className="flex justify-center items-center w-full">
                            <CircularLoaderSmall />
                          </div>
                        ) : (
                          summaryTranslatedText
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
                {/* Conversation Feelings */}
                {callDetails.feelings && callDetails.type !== "MEET" && (
                  <Card className="p-3 w-full mt-4 bg-voxa-neutral-100 dark:bg-voxa-neutral-950">
                    <CardHeader className="relative p-0 flex flex-row items-center justify-between">
                      <CardTitle className="text-lg font-medium text-center text-voxa-teal-600">
                        {t("callDetails.conversationFeelings")}
                      </CardTitle>
                      <div className="absolute right-0">{feelingsButton}</div>
                    </CardHeader>
                    <CardContent className="w-full mx-auto p-0 pt-2">
                      <div className="text-sm text-voxa-neutral-800 dark:text-gray-300">
                        {feelingsLoading ? (
                          <div className="flex justify-center items-center w-full">
                            <CircularLoaderSmall />
                          </div>
                        ) : (
                          feelingsTranslatedText
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
                {/* Client Engagment */}
                {callDetails.type?.toLowerCase?.() === "call" && (
                  <Card className="p-3 w-full mt-4 bg-voxa-neutral-100 dark:bg-voxa-neutral-950">
                    <CardContent className="p-0 w-full gap-3 flex justify-center items-center rtl:flex-row-reverse text-nowrap">
                      <div
                        className={`w-6 h-6 rounded-full flex items-center justify-center`}
                      >
                        <CircularStat
                          withText
                          className="mt-1 scale-150"
                          percentage={callDetails.clientEngagement}
                        />{" "}
                      </div>
                      <div className="rtl:text-right font-medium text-lg text-voxa-teal-600">
                        {t("notesTabs.timeline.clientEngagement")}
                        <p className="text-sm text-voxa-neutral-900 dark:text-voxa-neutral-500">
                          {callDetails.clientEngagement.toString() +
                            " " +
                            t("callDetails.percent")}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}
                {/* Meeting Statistics */}
                {Array.isArray(callDetails.speaker_durations_list) &&
                  callDetails.speaker_durations_list.length > 0 && (
                    <Card className="p-3 w-full mt-4 bg-voxa-neutral-100 dark:bg-voxa-neutral-950">
                      <CardHeader className="p-0">
                        <CardTitle className="text-lg font-medium text-center text-voxa-teal-600">
                          {t("callDetails.meetingStatistics")}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex flex-col gap-1 text-sm">
                          {callDetails.speaker_durations_list.map(
                            (speakerInfo: any, idx: number) => (
                              <div
                                key={idx}
                                className="flex justify-between items-center border-b border-voxa-neutral-200 dark:border-voxa-neutral-800 py-1 last:border-b-0"
                              >
                                <span className="text-voxa-neutral-600 dark:text-voxa-neutral-400 font-medium">
                                  {speakerInfo.speaker || "-"}
                                </span>
                                <span className="text-voxa-stone-500 dark:text-voxa-stone-400 font-medium">
                                  {formatSpeakingTime(
                                    speakerInfo.total_duration
                                  )}
                                </span>
                              </div>
                            )
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}
              </>
            )}
            <NotesTabs callDetails={callDetails} />
          </div>
        </div>
      </div>
    </div>
  );
}
