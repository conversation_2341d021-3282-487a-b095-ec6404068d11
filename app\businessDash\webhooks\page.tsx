"use client";

import WebhookCard from "@/components/webhooks/WebhookCard";
import WebhookLogsAccordion from "@/components/webhooks/WebhookLogsAccordion";
import WebhookEnabledToggle from "@/components/webhooks/WebhookEnabledToggle";
import WebhookSecretKey from "@/components/webhooks/WebhookSecretKey";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchWebhook,
  updateWebhookEvent,
  setWebhookEnabled,
  resetWebhookSecretKey,
} from "@/redux/BusinessDashboard/subSlices/WebhookSlice";
import { RootState, AppDispatch } from "@/redux/store";
import MainLoader from "@/components/Loaders/MainLoader";
import { Button } from "@/components/ui/button";
import ButtonLoader from "@/components/Loaders/ButtonLoader";

// URL validation regex pattern
const isValidUrl = (url: string): boolean => {
  // Allow empty URLs
  if (!url.trim()) return true;

  const urlPattern =
    /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/;
  return urlPattern.test(url);
};

export default function WebhooksPage() {
  const dispatch = useDispatch<AppDispatch>();
  const { webhook, loading } = useSelector(
    (state: RootState) => state.webhooks
  );

  // Update state to track modified webhook URLs and their validation status
  const [modifiedWebhooks, setModifiedWebhooks] = useState<
    Record<string, { url: string; isValid: boolean }>
  >({});
  const [isSaving, setSaving] = useState(false);

  useEffect(() => {
    dispatch(fetchWebhook() as any);
  }, [dispatch]);

  // Handler for URL changes in WebhookCard with validation
  const handleUrlChange = (event: string, url: string) => {
    const isValid = isValidUrl(url);
    setModifiedWebhooks((prev) => ({
      ...prev,
      [event]: { url, isValid },
    }));
  };

  // Handle webhook enabled toggle
  const handleEnabledToggle = async () => {
    if (webhook) {
      await dispatch(setWebhookEnabled(!webhook.enabled) as any);
    }
  };

  // Handle reset secret key
  const handleResetSecretKey = async () => {
    await dispatch(resetWebhookSecretKey() as any);
  };

  // Save all modified webhooks
  const handleSaveAll = () => {
    setSaving(true);
    // Create array of webhook updates, filtering out invalid URLs
    const updates = Object.entries(modifiedWebhooks)
      .filter(([_, data]) => data.isValid)
      .map(([eventName, data]) => ({
        eventName,
        method:
          webhook?.events?.[eventName as keyof typeof webhook.events]?.method ||
          "POST",
        path: data.url,
      }));

    // Dispatch updates for all valid modified webhooks
    Promise.all(
      updates.map((update) => dispatch(updateWebhookEvent(update) as any))
    )
      .then(() => {
        setModifiedWebhooks({});
        setSaving(false);
        // Refresh webhook data
        dispatch(fetchWebhook() as any);
      })
      .catch(() => {
        setSaving(false);
      });
  };

  // Reset all modifications
  const handleReset = () => {
    setModifiedWebhooks({});
  };

  // Check if there are any modifications and if all are valid
  const hasModifications = Object.keys(modifiedWebhooks).length > 0;
  const hasInvalidUrls = Object.values(modifiedWebhooks).some(
    (data) => !data.isValid
  );

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (hasModifications && !isSaving && !hasInvalidUrls) {
      handleSaveAll();
    }
  };

  return (
    <div className="relative w-full flex flex-col gap-4">
      <div className="w-full max-sm:text-center flex max-md:flex-col sm:items-center gap-4 justify-between">
        <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
          Webhooks
        </h1>{" "}
      </div>
      {loading && webhook === null ? (
        <div className="absolute w-full flex justify-center items-center h-[calc(100vh-2rem)] sm:h-[calc(100vh-3rem)]">
          <MainLoader />
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="max-w-screen-2xl mx-auto px-4 w-full flex gap-1 sm:gap-3 items-end">
            <p className="max-sm:w-full text-center h-full py-2 px-4 font-medium bg-voxa-neutral-50 dark:bg-voxa-neutral-900 rounded-t-lg text-sm">
              Webhook Events
            </p>
            <a
              href="https://telecomapi-prod.echoparrot.com/redoc"
              target="_blank"
              rel="noopener noreferrer"
              className="max-sm:w-full text-sm h-full font-medium py-2 px-4 bg-voxa-neutral-400 dark:bg-voxa-neutral-800 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-900 transition-all duration-150 rounded-t-lg items-center justify-between text-white"
            >
              Learn More
            </a>
          </div>
          <div className="max-w-screen-2xl mx-auto flex flex-col bg-voxa-neutral-50 dark:bg-voxa-neutral-900 w-full items-center p-4 gap-3 rounded-2xl">
            {" "}
            {/* Webhook Enable/Disable Toggle */}
            <WebhookEnabledToggle
              enabled={webhook?.enabled || false}
              onToggle={handleEnabledToggle}
            />
            {/* Webhook Secret Key Section */}
            <WebhookSecretKey
              secretKey={webhook?.webhook_secret_key || null}
              onResetKey={handleResetSecretKey}
            />
            <div className="w-full flex flex-col gap-4">
              {webhook &&
                webhook.events &&
                Object.entries(webhook.events).map(
                  ([eventKey, eventData], eventIndex) => {
                    if (eventData) {
                      const modifiedData = modifiedWebhooks[eventKey];
                      const currentUrl = modifiedData
                        ? modifiedData.url
                        : eventData.path;
                      const isValid = modifiedData
                        ? modifiedData.isValid
                        : true;

                      return (
                        <WebhookCard
                          key={eventIndex}
                          webhook={{
                            event: eventKey,
                            method: eventData.method,
                            url: currentUrl,
                          }}
                          onUrlChange={handleUrlChange}
                          isValidUrl={isValid}
                        />
                      );
                    }
                    return null;
                  }
                )}
            </div>
            {/* Display validation error message if any URLs are invalid */}
            {hasInvalidUrls && (
              <div className="w-full text-red-500 text-sm mt-2">
                Please correct invalid URLs before saving.
              </div>
            )}
            {/* Save and Reset buttons */}
            <div className="w-full flex justify-end gap-3 mt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={!hasModifications || isSaving}
                className={`${
                  isSaving
                    ? "cursor-not-allowed"
                    : "bg-voxa-neutral-700 hover:bg-voxa-neutral-800 dark:bg-voxa-neutral-800 dark:hover:bg-voxa-neutral-700"
                } text-white hover:text-white dark:text-voxa-neutral-300 flex justify-center items-center gap-2`}
              >
                Reset
              </Button>
              <Button
                type="submit"
                disabled={!hasModifications || isSaving || hasInvalidUrls}
                className={` ${
                  isSaving || hasInvalidUrls
                    ? "cursor-not-allowed"
                    : "bg-voxa-teal-600 hover:bg-voxa-teal-500"
                } text-voxa-neutral-50 flex justify-center items-center gap-2`}
              >
                {isSaving ? (
                  <>
                    Saving <ButtonLoader />
                  </>
                ) : (
                  "Save Events"
                )}
              </Button>
            </div>
            {/* Webhook Logs Accordion */}
            <WebhookLogsAccordion webhook={webhook!} dispatch={dispatch} />
          </div>
        </form>
      )}
    </div>
  );
}
