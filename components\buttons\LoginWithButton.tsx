"use client";
import Image from "next/image";
import GoogleIcon from "@/public/images/Icons/GoogleIcon.svg";
import MicrosoftIcon from "@/public/images/Icons/microsoftIcon.svg";
import { Button } from "../ui/button";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";

export default function LoginWithButton({ provider }: { provider: string }) {
  const { t } = useTranslation("signup");
  const router = useRouter();
  const GoogleSignin = async () => {
    await signIn("google");

    const updatedSession = await fetch("/api/auth/session");
    const sessionData = await updatedSession.json();

    if (sessionData?.user?.role === "ADMIN") {
      router.push("/");
    } else if (sessionData?.user?.role === "ENTREPRISE_AGENT") {
      router.push("/");
    } else if (sessionData?.user?.role === "ENTREPRISE_ADMIN") {
      router.push("/businessDash");
    } else {
      router.push("/");
    }
  };

  return (
    <Button
      className="w-full py-5"
      onClick={async () => {
        if (provider === "google") {
          await GoogleSignin();
        } else if (provider === "microsoft") {
          window.location.href = "/api/auth/signin/microsoft";
        }
      }}
    >
      {provider === "google" ? (
        <>
          <Image src={GoogleIcon} alt="Google Icon" className="w-5 h-5" />
          <p>{t("google")}</p>
        </>
      ) : provider === "microsoft" ? (
        <>
          <Image src={MicrosoftIcon} alt="Microsoft Icon" className="w-5 h-5" />
          <p>{t("microsoft")}</p>
        </>
      ) : (
        ""
      )}
    </Button>
  );
}
