"use client";

import { usePara<PERSON>, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { Settings } from "lucide-react";
import { StatsCards } from "@/components/CreateEditGoal/StatsCards";
import { GoalDetails } from "@/components/CreateEditGoal/GoalDetails";
import { ForwardingSection } from "@/components/CreateEditGoal/ForwardingSection";
import { ConversationPrompt } from "@/components/CreateEditGoal/ConversationPrompt";
import { InputParameters } from "@/components/CreateEditGoal/InputParameters";
import { MessagesDrop } from "@/components/CreateEditGoal/MessagesDrop";
import { VoicemailDrop } from "@/components/CreateEditGoal/VoicemailDrop";
import { HumanIntroduction } from "@/components/CreateEditGoal/HumanIntroduction";
import { NumberRetries } from "@/components/CreateEditGoal/NumberRetries";
import { PronounceClient } from "@/components/CreateEditGoal/PronounceClient";
import { RangesSlider } from "@/components/CreateEditGoal/RangesSlider";
import { VoiceParameters } from "@/components/CreateEditGoal/VoiceParameters";
import { AIVoiceMuteSwitch } from "@/components/CreateEditGoal/AIVoiceMuteSwitch";
import { GoalHeaderSection } from "@/components/CreateEditGoal/GoalHeaderSection";
import { GoalTitle } from "@/components/CreateEditGoal/GoalTitle";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  createDemarchageGoal,
  getDemarchageGoal,
  updateDemarchageGoal,
  createMultiTranslationGoal,
  getMultiTranslationGoal,
  updateMultiTranslationGoal,
  createMeetGoal,
  getMeetGoal,
  updateMeetGoal,
  resetGoalState,
  setGoalId,
  setAssistantId,
  setPhoneId,
  setDurationBetweenCalls,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";

export default function CreateEditGoal() {
  const { t } = useTranslation("assistants");
  const router = useRouter();

  const params = useParams();
  const goalID = params?.id as string;

  const searchParams = useSearchParams();
  const templateType = searchParams?.get("templateType") as string;
  const assistantID = searchParams?.get("assistantID") as string;
  const phoneID = searchParams?.get("phoneID") as string;

  const dispatch = useDispatch<AppDispatch>();
  const { enableAIVoice, updatedAt, loading } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );

  const [file, setFile] = useState<File | null>(null);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [humanAudioBlob, setHumanAudioBlob] = useState<Blob | null>(null);

  useEffect(() => {
    dispatch(resetGoalState());
    setFile(null);
    setAudioBlob(null);
    setHumanAudioBlob(null);

    if (goalID === "create") {
      dispatch(setAssistantId(assistantID));
      dispatch(setPhoneId(phoneID));
    } else {
      dispatch(setGoalId(goalID));
      if (templateType === "MULTI_TRANSLATION") {
        dispatch(setDurationBetweenCalls(0));
      }
    }
  }, [templateType, goalID, assistantID, phoneID, dispatch]);

  const handleGetGoal = useCallback(async () => {
    if (goalID !== "create") {
      if (templateType === "GOOGLE_MEET") {
        await dispatch(getMeetGoal({ goalID }));
      } else if (templateType === "DEMARCHAGE") {
        await dispatch(
          getDemarchageGoal({
            goalID,
            setFile,
            setAudioBlob,
            setHumanAudioBlob,
          })
        );
      } else if (templateType === "MULTI_TRANSLATION") {
        await dispatch(getMultiTranslationGoal({ goalID }));
      }
    }
  }, [templateType, dispatch, goalID]);

  useEffect(() => {
    handleGetGoal();
  }, [templateType, goalID, dispatch, handleGetGoal]);

  const handleSubmit = async () => {
    if (goalID !== "create") {
      if (templateType === "GOOGLE_MEET") {
        await dispatch(updateMeetGoal({ router, goalID }));
      } else if (templateType === "DEMARCHAGE") {
        await dispatch(
          updateDemarchageGoal({
            router,
            goalID,
            file,
            audioBlob,
            humanAudioBlob,
          })
        );
      } else if (templateType === "MULTI_TRANSLATION") {
        await dispatch(updateMultiTranslationGoal({ router, goalID }));
      }
    } else {
      if (templateType === "GOOGLE_MEET") {
        await dispatch(createMeetGoal({ router }));
      } else if (templateType === "DEMARCHAGE") {
        await dispatch(
          createDemarchageGoal({
            router,
            file,
            audioBlob,
            humanAudioBlob,
          })
        );
      } else if (templateType === "MULTI_TRANSLATION") {
        await dispatch(createMultiTranslationGoal({ router }));
      }
    }
  };

  return (
    <div className="space-y-6">
      <GoalTitle goalID={goalID} templateType={templateType} />
      {goalID !== "create" && (
        <div className="relative space-y-2">
          <GoalHeaderSection goalID={goalID} templateType={templateType} />
          {["DEMARCHAGE", "MULTI_TRANSLATION"].includes(templateType) && (
            <StatsCards />
          )}
          {updatedAt && (
            <div className="relative sm:absolute right-0 w-fit flex flex-col bg-voxa-main border shadow-md rounded-xl p-2 pt-1 pb-1.5 bg-voxa-neutral-50 dark:bg-voxa-neutral-950 text-xs">
              <span className="font-semibold text-indigo-500 text-base">
                Last Update
              </span>
              <span>{updatedAt}</span>
            </div>
          )}
        </div>
      )}
      <GoalDetails
        goalID={goalID}
        phoneID={phoneID}
        templateType={templateType}
      />
      {templateType === "DEMARCHAGE" ? (
        <>
          <ForwardingSection />
          <RangesSlider />
          <ConversationPrompt setFile={setFile} goalID={goalID} />
        </>
      ) : (
        templateType === "MULTI_TRANSLATION" && <RangesSlider />
      )}

      <Accordion
        type="single"
        collapsible
        className="w-full rounded-xl border border-voxa-neutral-200 dark:border-voxa-neutral-900 shadow-sm"
      >
        <AccordionItem value="item-1">
          <AccordionTrigger className="text-base sm:text-lg font-semibold flex items-center justify-start gap-2 w-full bg-voxa-neutral-50 dark:bg-voxa-neutral-950 px-3 rounded-xl">
            <Settings className="w-4 h-4 sm:w-5 sm:h-5" />{" "}
            {t("createEditGoal.advanced.section")}
          </AccordionTrigger>
          <AccordionContent className="grid grid-cols-1 lg:grid-cols-2 gap-6 pt-4 pb-6 px-2 sm:px-4 text-sm dark:text-voxa-neutral-200">
            {templateType === "DEMARCHAGE" ? (
              <>
                <InputParameters />
                <MessagesDrop />
                <VoicemailDrop setAudioBlob={setAudioBlob} />
                <HumanIntroduction setHumanAudioBlob={setHumanAudioBlob} />
                <NumberRetries />
                <PronounceClient />
                <VoiceParameters />
              </>
            ) : templateType === "GOOGLE_MEET" ? (
              <>
                <AIVoiceMuteSwitch />
                {enableAIVoice && (
                  <div className="space-y-6 lg:col-span-2">
                    <ConversationPrompt setFile={setFile} goalID={goalID} />
                    <VoiceParameters />
                  </div>
                )}
              </>
            ) : (
              templateType === "MULTI_TRANSLATION" && (
                <>
                  <InputParameters />
                  <NumberRetries />
                </>
              )
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <div className="flex justify-center gap-2 sm:gap-4 w-full">
        <Button variant="primary" loading={loading} onClick={handleSubmit}>
          {goalID === "create"
            ? loading
              ? t("createEditGoal.submit.creatingGoal")
              : t("createEditGoal.submit.createGoal")
            : loading
            ? t("createEditGoal.submit.editingGoal")
            : t("createEditGoal.submit.editGoal")}
        </Button>
        <Button
          disabled={loading}
          onClick={() => router.push("/businessDash/assistants")}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
}
