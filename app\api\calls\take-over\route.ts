import { NextResponse } from "next/server";

const TELECOM_ENDPOINT = process.env.TELECOM_ENDPOINT;

export async function POST(req: Request) {
  try {
    const { call_sid, goal_id, phone_number } = await req.json();

    if (!call_sid || !goal_id || !phone_number) {
      return NextResponse.json({ error: "Missing fields" }, { status: 400 });
    }

    console.log({
      call_sid,
      goal_id,
      phone_number,
    })

    const externalRes = await fetch(`${TELECOM_ENDPOINT}/take_over`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        call_sid,
        goal_id,
        phone_number,
      }),
    });

    console.log("External API Response:", externalRes.status, externalRes);

    const text = await externalRes.text();

    if (!externalRes.ok) {
      return NextResponse.json(
        { error: "External API error", details: text },
        { status: externalRes.status }
      );
    }

    return NextResponse.json({ message: "Success", response: text });
  } catch (error: any) {
    console.error("API Route Error:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    );
  }
}
