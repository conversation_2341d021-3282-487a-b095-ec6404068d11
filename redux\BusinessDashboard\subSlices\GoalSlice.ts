import { getEntrepriseScripts } from "@/actions/ScriptsActions";
import { getTimeFromTimestamp } from "@/lib/Strings/DateFormat";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { VOICE_MAIL_DETECTION_INITIAL_VALUE } from "@/components/CreateEditGoal/VoicemailDrop";
import { toast } from "sonner";
import {
  CheckPhoneHasIncomingCallsGoal,
  CreateDemarchageGoal,
  GetDemarchageGoal,
  UpdateDemarchageGoal,
  CreateMeetGoal,
  GetMeetGoal,
  UpdateMeetGoal,
  CreateMultiTranslationGoal,
  GetMultiTranslationGoal,
  UpdateMultiTranslationGoal,
} from "@/actions/GoalActions";
import {
  INITIAL_RANGE,
  sanitizeRanges,
} from "@/components/CreateEditGoal/RangesSlider";
import { Dispatch, SetStateAction } from "react";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

interface GoalStateType {
  ranges: [number, number][];
  goalId: string | null;
  assistantId: string | null;
  phoneId: string | null;
  canSelectIncomingCalls: boolean;
  canTransferIncomingCalls: boolean;
  incomingSelected: boolean;
  goalName: string;
  goalContext: string;
  country: string;
  goalDuration: number;
  goalConversations: number;
  missedCalls: number;
  answeredCalls: number;
  calledClients: number;
  fileFromGoal: string | null;
  scriptContent: string;
  scriptContentOpen: boolean;
  totalClients: number;
  scripts: any[];
  totalCost: number;
  groupId: string;
  messageOnMissedCallContent: string;
  messagesOnMissedCall: string;
  voicemailDropContent: string;
  forwardToNumber: string;
  voicemailDropType: string;
  audioUrl: string | null;
  audioDuration: number | null;
  humanIntroductionEnabled: string;
  humanAudioUrl: string | null;
  humanAudioDuration: number | null;
  updatedAt: string;
  maleVoice: { id: string; provider: string };
  femaleVoice: { id: string; provider: string };
  isPronounceClientHonorificEnabled: boolean;
  isPronounceClientNameEnabled: boolean;
  durationBetweenCalls: string;
  ringingDuration: string;
  retryCount: number;
  goalType: string;
  goalStatus: string;
  ringoverApiKey: string;
  ringoverBaseUrl: string;
  groups: { id: string; name: string }[];
  goalAssistantNameForFemale: string;
  goalAssistantNameForMale: string;
  agents: any[];
  promptContent: string | null;
  textPreview: string | null;
  selectedScriptType: boolean;
  selectedScript: string | null;
  // loadings
  gettingGoal: boolean;
  scriptsLoading: boolean;
  loading: boolean;
  //meet specific
  enableAIVoice: boolean;
  enableMute: boolean;
  crmId: string;
  crmToken: string;
}

const initialState: GoalStateType = {
  ranges: sanitizeRanges([INITIAL_RANGE]),
  goalId: null,
  assistantId: null,
  phoneId: null,
  canSelectIncomingCalls: false,
  canTransferIncomingCalls: false,
  incomingSelected: false,
  goalName: "",
  goalContext: "",
  country: "FR",
  goalDuration: 0,
  goalConversations: 0,
  missedCalls: 0,
  answeredCalls: 0,
  calledClients: 0,
  fileFromGoal: null,
  scriptContent: "",
  scriptContentOpen: false,
  totalClients: 0,
  scripts: [],
  selectedScriptType: false,
  selectedScript: null,
  totalCost: 0,
  groupId: "",
  messagesOnMissedCall: "NONE",
  messageOnMissedCallContent: "",
  voicemailDropContent: VOICE_MAIL_DETECTION_INITIAL_VALUE,
  forwardToNumber: "",
  voicemailDropType: "NONE",
  audioUrl: null,
  audioDuration: null,
  humanIntroductionEnabled: "NONE",
  humanAudioUrl: null,
  humanAudioDuration: null,
  updatedAt: "",
  maleVoice: { id: "", provider: "" },
  femaleVoice: { id: "", provider: "" },
  isPronounceClientHonorificEnabled: false,
  isPronounceClientNameEnabled: false,
  durationBetweenCalls: "30",
  ringingDuration: "30",
  retryCount: 0,
  goalType: "",
  goalStatus: "",
  ringoverApiKey: "",
  ringoverBaseUrl: "",
  groups: [],
  goalAssistantNameForFemale: "Male",
  goalAssistantNameForMale: "Female",
  agents: [],
  promptContent: null,
  textPreview: null,
  // loadings
  gettingGoal: true,
  scriptsLoading: true,
  loading: false,
  //meet specific
  enableAIVoice: false,
  enableMute: false,
  crmId: "",
  crmToken: "",
};

// Demarchage Template
export const createDemarchageGoal = createAsyncThunk(
  "goal/createDemarchageGoal",
  async (
    {
      router,
      file,
      audioBlob,
      humanAudioBlob,
    }: {
      router: AppRouterInstance;
      file: File | null;
      audioBlob: Blob | null;
      humanAudioBlob: Blob | null;
    },
    { dispatch, getState }
  ) => {
    const state = (getState() as any).businessDashboard.businessDashboardGoals;
    const {
      assistantId,
      phoneId,
      goalName,
      goalContext,
      country,
      selectedScriptType,
      scriptContent,
      incomingSelected,
      groupId,
      forwardToNumber,
      retryCount,
      messagesOnMissedCall,
      messageOnMissedCallContent,
      voicemailDropType,
      voicemailDropContent,
      goalAssistantNameForMale,
      goalAssistantNameForFemale,
      isPronounceClientNameEnabled,
      isPronounceClientHonorificEnabled,
      durationBetweenCalls,
      ringingDuration,
      maleVoice,
      femaleVoice,
      humanIntroductionEnabled,
      canTransferIncomingCalls,
      ranges,
      audioDuration,
      humanAudioDuration,
    } = state;
    try {
      if (
        parseInt(durationBetweenCalls) > 3600 ||
        parseInt(durationBetweenCalls) < 0
      ) {
        throw new Error(
          "Duration between multiple calls should be between 0 and 3600 seconds"
        );
      }
      if (parseInt(ringingDuration) > 120 || parseInt(ringingDuration) < 10) {
        throw new Error(
          "Ringing duration should be between 10 and 120 seconds"
        );
      }
      if (parseInt(ringingDuration) < 40 && voicemailDropType !== "NONE") {
        throw new Error(
          "Calls must ring for at least 40 seconds to ensure a successful delivery of voicemail drops. Increase your ringing duration."
        );
      }

      if (voicemailDropType === "TEXT" && !voicemailDropContent) {
        throw new Error("Please provide a voicemail drop text content");
      }
      if (voicemailDropType === "AUDIO") {
        if (!audioDuration) {
          throw new Error("Please provide a voicemail drop audio file");
        }
        if (audioDuration <= 0 || audioDuration > 60) {
          throw new Error(
            "Voicemail drop audio file should be less than 60 seconds"
          );
        }
      }
      if (humanIntroductionEnabled === "AUDIO") {
        if (!audioDuration) {
          throw new Error("Please provide a human introduction audio file");
        }
        if (humanAudioDuration <= 0 || humanAudioDuration > 60) {
          throw new Error(
            "Human introduction audio file should be less than 60 seconds"
          );
        }
      }
      if (!canTransferIncomingCalls && !scriptContent && !file) {
        throw new Error(
          "You have disabled incoming call transfer, please select a script or a file or this incoming goal."
        );
      }
      if (!ranges || ranges.length === 0) {
        throw new Error("Please select a valid range");
      }
      if (!forwardToNumber && !groupId) {
        throw new Error(
          "Please select a group or a number to forward the calls"
        );
      }
      if (!goalName || !country) {
        throw new Error("Please provide a goal name and a country");
      }
      if (
        ["SMS", "WHATSAPP"].includes(messagesOnMissedCall) &&
        !messageOnMissedCallContent
      ) {
        throw new Error(
          "You've selected to send messages on missed calls, please provide a message content"
        );
      }
      if (!scriptContent && !file && !canTransferIncomingCalls) {
        throw new Error("Please select a script or a file");
      }
      let content = "";
      if (selectedScriptType) {
        content = scriptContent;
      } else if (file) {
        const reader = new FileReader();
        content = await new Promise<string>((resolve, reject) => {
          reader.onload = () => {
            resolve(reader.result as string);
          };
          reader.onerror = (err) => {
            console.error(err);
            reject(new Error("Failed to read the file"));
          };
          reader.readAsText(file as File);
        });
      }
      dispatch(setLoading(true));
      const response = await CreateDemarchageGoal({
        name: goalName,
        assistantID: assistantId,
        prompt: content,
        country: country,
        numberID: phoneId,
        IncomingSelected: incomingSelected,
        goalContext: goalContext,
        groupid: groupId || "",
        retry_count: retryCount || 0,
        ranges,
        ForwardToNumber: forwardToNumber,
        incoming_call_transfer_on: canTransferIncomingCalls,
        messages_on_missed_call: messagesOnMissedCall,
        message_on_missed_call_content: messageOnMissedCallContent,
        voicemail_drop_content: voicemailDropContent,
        goal_assistant_name_for_male: goalAssistantNameForMale,
        goal_assistant_name_for_female: goalAssistantNameForFemale,
        pronounce_client_name_enabled: isPronounceClientNameEnabled,
        pronounce_client_honorific_enabled: isPronounceClientHonorificEnabled,
        duration_between_calls: durationBetweenCalls,
        ringing_duration: ringingDuration,
        male_voice: maleVoice,
        female_voice: femaleVoice,
        voicemail_drop_type: voicemailDropType,
        audio_blob: audioBlob,
        audio_duration: audioDuration,
        human_introduction_enabled: humanIntroductionEnabled,
        human_introduction_audio_blob: humanAudioBlob,
        human_introduction_audio_duration: humanAudioDuration,
      });
      if (response.success) {
        router.push("/businessDash/assistants");
        toast.success("Goal created successfully");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to create goal");
    } finally {
      dispatch(setLoading(false));
    }
  }
);
export const getDemarchageGoal = createAsyncThunk(
  "goal/getDemarchageGoal",
  async (
    {
      goalID,
      setFile,
      setAudioBlob,
      setHumanAudioBlob,
    }: {
      goalID: string;
      setFile: Dispatch<SetStateAction<File | null>>;
      setAudioBlob: Dispatch<SetStateAction<Blob | null>>;
      setHumanAudioBlob: Dispatch<SetStateAction<Blob | null>>;
    },
    { dispatch }
  ) => {
    try {
      dispatch(setGettingGoal(true));
      const response = await GetDemarchageGoal(goalID);
      if (response.success) {
        const { goal } = response;
        dispatch(setGoalStatus(goal.status ?? ""));
        dispatch(setGoalType(goal.goalType ?? ""));
        dispatch(setGoalName(goal.name ?? ""));
        dispatch(setGoalContext(goal.goalContext ?? ""));
        dispatch(setCountry(goal.country ?? ""));
        dispatch(setGoalDuration(goal.totalDuration ?? 0));
        dispatch(setGoalConversations(goal.conversations ?? 0));
        dispatch(setTotalClients(goal.TotalClients ?? 0));
        dispatch(setMissedCalls(goal.missedCalls ?? 0));
        dispatch(setAnsweredCalls(goal.answeredCalls ?? 0));
        dispatch(setCalledClients(goal.calledClients ?? 0));
        dispatch(setTotalCost(goal.TotalCost ?? 0));
        dispatch(setGroupId(goal.forwarded_to_group_id ?? ""));
        dispatch(setForwardToNumber(goal.forwarded_to_number ?? ""));
        dispatch(
          setRanges(sanitizeRanges(goal.availability || [INITIAL_RANGE]))
        );
        dispatch(setRetryCount(goal.retry_count ?? null));
        dispatch(
          setCanTransferIncomingCalls(
            goal.avoid_script_and_direct_transfer ?? false
          )
        );
        dispatch(setSelectedScript(goal.prompt));
        dispatch(
          setMessagesOnMissedCall(goal.messages_on_missed_call ?? "NONE")
        );
        dispatch(
          setMessageOnMissedCallContent(
            goal.message_on_missed_call_content ?? ""
          )
        );
        dispatch(
          setVoiceMailDetectionContent(
            goal.voicemail_drop_content ?? VOICE_MAIL_DETECTION_INITIAL_VALUE
          )
        );
        dispatch(
          setUpdatedAt(
            goal.updated_at ? getTimeFromTimestamp(goal.updated_at) : ""
          )
        );
        dispatch(
          setIsPronounceClientNameEnabled(
            goal.pronounce_client_name_enabled ?? false
          )
        );
        dispatch(
          setIsPronounceClientHonorificEnabled(
            goal.pronounce_client_honorific_enabled ?? false
          )
        );
        dispatch(setDurationBetweenCalls(goal.duration_between_calls ?? "30"));
        dispatch(setRingingDuration(goal.ringing_duration ?? "30"));
        dispatch(setMaleVoice(goal.male_voice));
        dispatch(setFemaleVoice(goal.female_voice));
        dispatch(setGoalAssistantNameForFemale(goal.ai_name_female));
        dispatch(setGoalAssistantNameForMale(goal.ai_name_male));
        dispatch(setVoiceMailDetection(goal.voicemail_drop_type));
        dispatch(setAudioUrl(goal.voicemail_drop_audio_url));
        dispatch(setAudioDuration(goal.voicemail_drop_audio_duration ?? null));
        dispatch(setHumanIntroductionEnabled(goal.human_introduction_enabled));
        dispatch(setHumanAudioUrl(goal.human_introduction_url));
        dispatch(
          setHumanAudioDuration(goal.human_introduction_audio_duration ?? null)
        );
        if (goal.voicemail_drop_audio_url) {
          try {
            const audioResponse = await fetch(goal.voicemail_drop_audio_url);
            const audioBlob = await audioResponse.blob();
            setAudioBlob(audioBlob);
          } catch (error) {
            console.error("Error fetching voicemail audio:", error);
          }
        }

        if (goal.human_introduction_url) {
          try {
            const humanAudioResponse = await fetch(goal.human_introduction_url);
            const humanAudioBlob = await humanAudioResponse.blob();
            setHumanAudioBlob(humanAudioBlob);
          } catch (error) {
            console.error("Error fetching human introduction audio:", error);
          }
        }
        if (goal.prompt) {
          const blob = new Blob([goal.prompt], { type: "text/plain" });
          setFile(blob as File);
          const fileURL = URL.createObjectURL(blob);
          dispatch(setFileFromGoal(fileURL));
        }
      }
    } catch (err: any) {
      toast.error("Failed to fetch goal details. ", err.message);
    } finally {
      dispatch(setGettingGoal(false));
    }
  }
);
export const updateDemarchageGoal = createAsyncThunk(
  "goal/updateDemarchageGoal",
  async (
    {
      router,
      goalID,
      file,
      audioBlob,
      humanAudioBlob,
    }: {
      router: AppRouterInstance;
      goalID: string;
      file: File | null;
      audioBlob: Blob | null;
      humanAudioBlob: Blob | null;
    },
    { dispatch, getState }
  ) => {
    const state = (getState() as any).businessDashboard.businessDashboardGoals;
    const {
      goalName,
      goalContext,
      country,
      selectedScriptType,
      scriptContent,
      selectedScript,
      groupId,
      forwardToNumber,
      ranges,
      retryCount,
      messagesOnMissedCall,
      messageOnMissedCallContent,
      voicemailDropContent,
      canTransferIncomingCalls,
      isPronounceClientNameEnabled,
      isPronounceClientHonorificEnabled,
      durationBetweenCalls,
      ringingDuration,
      goalAssistantNameForMale,
      goalAssistantNameForFemale,
      maleVoice,
      femaleVoice,
      voicemailDropType,
      audioDuration,
      humanIntroductionEnabled,
      humanAudioDuration,
    } = state;
    try {
      if (!groupId && !forwardToNumber) {
        throw new Error("Fill forward to number or group ID");
      }
      if (
        !goalName ||
        !country ||
        (!(file || selectedScript) && !canTransferIncomingCalls) ||
        !ranges ||
        ranges.length === 0 ||
        retryCount === null
      ) {
        throw new Error("Please fill all fields");
      }
      if (
        parseInt(durationBetweenCalls) > 3600 ||
        parseInt(durationBetweenCalls) < 0
      ) {
        throw new Error(
          "Duration between multiple calls should be between 0 and 3600 seconds"
        );
      }
      if (parseInt(ringingDuration) > 120 || parseInt(ringingDuration) < 10) {
        throw new Error(
          "Ringing duration should be between 10 and 120 seconds"
        );
      }
      if (parseInt(ringingDuration) < 40 && voicemailDropType !== "NONE") {
        throw new Error(
          "Calls must ring for at least 40 seconds to ensure a successful delivery of voicemail drops. Increase your ringing duration."
        );
      }
      if (
        ["SMS", "WHATSAPP"].includes(messagesOnMissedCall) &&
        !messageOnMissedCallContent
      ) {
        throw new Error(
          "You've selected to send messages on missed calls, please provide a message content"
        );
      }

      if (voicemailDropType === "TEXT" && !voicemailDropContent) {
        throw new Error(
          "You've selected to send voicemail drop, please provide a message content"
        );
      }
      if (voicemailDropType === "AUDIO") {
        if (!audioDuration) {
          throw new Error("Please provide a voicemail drop audio file");
        }
        if (audioDuration <= 0 || audioDuration > 60) {
          throw new Error(
            "Voicemail drop audio file should be less than 60 seconds"
          );
        }
      }
      if (humanIntroductionEnabled === "AUDIO") {
        if (!humanAudioDuration) {
          throw new Error("Please provide a human introduction audio file");
        }
        if (humanAudioDuration <= 0 || humanAudioDuration > 60) {
          throw new Error(
            "Human introduction audio file should be less than 60 seconds"
          );
        }
      }

      let Content = "";
      if (selectedScriptType) {
        Content = scriptContent;
      } else if (file) {
        const reader = new FileReader();
        Content = await new Promise<string>((resolve, reject) => {
          reader.onload = () => {
            resolve(reader.result as string);
          };
          reader.onerror = (err) => {
            console.error(err);
            reject(new Error("Failed to read the file"));
          };
          reader.readAsText(file as File);
        });
      } else {
        Content = "";
      }
      dispatch(setLoading(true));
      const response = await UpdateDemarchageGoal({
        goalID,
        name: goalName,
        prompt: Content,
        country,
        goalContext,
        groupid: groupId.toString(),
        forwardTo: forwardToNumber,
        ranges,
        retry_count: retryCount,
        messages_on_missed_call: messagesOnMissedCall,
        message_on_missed_call_content: messageOnMissedCallContent,
        voicemail_drop_content: voicemailDropContent,
        goal_assistant_name_for_male: goalAssistantNameForMale,
        goal_assistant_name_for_female: goalAssistantNameForFemale,
        pronounce_client_name_enabled: isPronounceClientNameEnabled,
        pronounce_client_honorific_enabled: isPronounceClientHonorificEnabled,
        duration_between_calls: durationBetweenCalls,
        ringing_duration: ringingDuration,
        male_voice: maleVoice,
        female_voice: femaleVoice,
        voicemail_drop_type: voicemailDropType,
        audio_blob: audioBlob,
        audio_duration: audioDuration,
        human_introduction_enabled: humanIntroductionEnabled,
        human_introduction_audio_blob: humanAudioBlob,
        human_introduction_audio_duration: humanAudioDuration,
      });
      if (response.success) {
        router.push("/businessDash/assistants");
        toast.success("Goal updated successfully");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to update goal");
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Meet Template
export const createMeetGoal = createAsyncThunk(
  "goal/createMeetGoal",
  async ({ router }: { router: AppRouterInstance }, { dispatch, getState }) => {
    const state = (getState() as any).businessDashboard.businessDashboardGoals;
    const {
      assistantId,
      phoneId,
      goalName,
      goalContext,
      country,
      crmId,
      crmToken,
      enableAIVoice,
      enableMute,
      selectedScript,
      textPreview,
      selectedScriptType,
      goalAssistantNameForFemale,
      goalAssistantNameForMale,
      maleVoice,
      femaleVoice,
      scripts,
    } = state;
    try {
      if (!goalName || !country) {
        throw new Error("Name and country are required.");
      }
      if (enableAIVoice && !selectedScript && !textPreview) {
        throw new Error(
          "Please select a script or upload a prompt file since you chose to enable AI."
        );
      }
      if (enableAIVoice && (!maleVoice || !goalAssistantNameForMale)) {
        throw new Error(
          "Please add a male voice and name for the AI assistant."
        );
      }
      if (enableAIVoice && (!femaleVoice || !goalAssistantNameForFemale)) {
        throw new Error(
          "Please add a female voice and name for the AI assistant."
        );
      }
      let content = "";
      if (selectedScriptType) {
        content = scripts.find((script: any) => script.id === selectedScript)
          ?.content as string;
      } else {
        content = textPreview || "";
      }
      dispatch(setLoading(true));
      const response = await CreateMeetGoal({
        assistantID: assistantId,
        numberID: phoneId,
        name: goalName,
        goalContext,
        country,
        crmID: crmId,
        crmToken: crmToken,
        enable_ai_voice: enableAIVoice,
        enable_mute: enableMute,
        script_content: content,
        ai_name_female: goalAssistantNameForFemale,
        ai_name_male: goalAssistantNameForMale,
        male_voice: maleVoice,
        female_voice: femaleVoice,
      });
      if (response.success) {
        router.push("/businessDash/assistants");
        toast.success("Goal created successfully");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to create goal");
    } finally {
      dispatch(setLoading(false));
    }
  }
);
export const getMeetGoal = createAsyncThunk(
  "goal/getMeetGoal",
  async ({ goalID }: { goalID: string }, { dispatch }) => {
    try {
      dispatch(setGettingGoal(true));
      const response = await GetMeetGoal(goalID);
      if (response.success) {
        const { goal } = response;
        dispatch(setGoalName(goal.name));
        dispatch(setGoalContext(goal.goalContext));
        dispatch(setCountry(goal.country));
        dispatch(setCrmId(goal.crm_id));
        dispatch(setCrmToken(goal.crm_token));
        dispatch(setUpdatedAt(goal.updated_at));
        dispatch(setEnableMute(goal.enable_mute));
        dispatch(setEnableAIVoice(goal.enable_ai_voice));
        dispatch(setPromptContent(goal.prompt || ""));
        dispatch(setMaleVoice(goal.male_voice));
        dispatch(setFemaleVoice(goal.female_voice));
        dispatch(setGoalAssistantNameForFemale(goal.ai_name_female || ""));
        dispatch(setGoalAssistantNameForMale(goal.ai_name_male || ""));
      }
    } catch (err: any) {
      toast.error("Failed to fetch goal details. ", err.message);
    } finally {
      dispatch(setGettingGoal(false));
    }
  }
);
export const updateMeetGoal = createAsyncThunk(
  "goal/updateMeetGoal",
  async (
    { router, goalID }: { router: AppRouterInstance; goalID: string },
    { dispatch, getState }
  ) => {
    const state = (getState() as any).businessDashboard.businessDashboardGoals;
    const {
      goalName,
      goalContext,
      country,
      crmId,
      crmToken,
      enableMute,
      enableAIVoice,
      goalAssistantNameForMale,
      goalAssistantNameForFemale,
      maleVoice,
      femaleVoice,
      selectedScript,
      textPreview,
      selectedScriptType,
      promptContent,
      scripts,
    } = state;
    try {
      if (!goalName || !country) {
        throw new Error("name and country are required fields");
      }
      if (
        enableAIVoice &&
        (!selectedScript || !textPreview || !promptContent)
      ) {
        throw new Error(
          "Please select a script or upload a prompt file since you chose to enable AI."
        );
      }
      if (enableAIVoice && (!maleVoice || !goalAssistantNameForMale)) {
        throw new Error(
          "Please add a male voice and name for the AI assistant."
        );
      }
      if (enableAIVoice && (!femaleVoice || !goalAssistantNameForFemale)) {
        throw new Error(
          "Please add a female voice and name for the AI assistant."
        );
      }
      let content = "";
      if (selectedScript || textPreview) {
        if (selectedScriptType) {
          content =
            (scripts.find((script: any) => script.id === selectedScript)
              ?.content as string) || "";
        } else {
          content = textPreview || "";
        }
      } else {
        content = promptContent || "";
      }
      dispatch(setLoading(true));
      const response = await UpdateMeetGoal({
        goalID,
        name: goalName,
        goalContext,
        country,
        crmID: crmId,
        crmToken,
        enable_mute: enableMute,
        enable_ai_voice: enableAIVoice,
        ai_name_male: goalAssistantNameForMale,
        ai_name_female: goalAssistantNameForFemale,
        male_voice: maleVoice,
        female_voice: femaleVoice,
        script_content: content,
      });
      if (response.success) {
        router.push("/businessDash/assistants");
        toast.success("Goal updated successfully");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to update goal");
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Multi Translation Template
export const createMultiTranslationGoal = createAsyncThunk(
  "goal/createTranslationGoal",
  async ({ router }: { router: AppRouterInstance }, { dispatch, getState }) => {
    const state = (getState() as any).businessDashboard.businessDashboardGoals;
    const {
      assistantId,
      phoneId,
      goalName,
      goalContext,
      country,
      ranges,
      durationBetweenCalls,
      ringingDuration,
      retryCount,
    } = state;

    try {
      if (
        parseInt(durationBetweenCalls) > 3600 ||
        parseInt(durationBetweenCalls) < 0
      ) {
        throw new Error(
          "Duration between multiple calls should be between 0 and 3600 seconds"
        );
      }
      if (parseInt(ringingDuration) > 120 || parseInt(ringingDuration) < 10) {
        throw new Error(
          "Ringing duration should be between 10 and 120 seconds"
        );
      }
      if (!ranges || ranges.length === 0) {
        throw new Error("Please select a valid range");
      }
      if (!goalName || !country) {
        throw new Error("Please provide a goal name and a country");
      }
      dispatch(setLoading(true));
      const response = await CreateMultiTranslationGoal({
        name: goalName,
        assistantID: assistantId,
        country: country,
        numberID: phoneId,
        goalContext: goalContext,
        retry_count: retryCount || 0,
        ranges,
        duration_between_calls: durationBetweenCalls,
        ringing_duration: ringingDuration,
      });
      if (response.success) {
        router.push("/businessDash/assistants");
        toast.success("Goal created successfully");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to create goal");
    } finally {
      dispatch(setLoading(false));
    }
  }
);
export const getMultiTranslationGoal = createAsyncThunk(
  "goal/getTranslationGoal",
  async ({ goalID }: { goalID: string }, { dispatch }) => {
    try {
      dispatch(setGettingGoal(true));
      const response = await GetMultiTranslationGoal(goalID);
      if (response.success) {
        const { goal } = response;
        dispatch(setGoalStatus(goal.status ?? ""));
        dispatch(setGoalName(goal.name ?? ""));
        dispatch(setGoalContext(goal.goalContext ?? ""));
        dispatch(setCountry(goal.country ?? ""));
        dispatch(setGoalDuration(goal.totalDuration ?? 0));
        dispatch(setGoalConversations(goal.conversations ?? 0));
        dispatch(setTotalClients(goal.TotalClients ?? 0));
        dispatch(setMissedCalls(goal.missedCalls ?? 0));
        dispatch(setAnsweredCalls(goal.answeredCalls ?? 0));
        dispatch(setCalledClients(goal.calledClients ?? 0));
        dispatch(setTotalCost(goal.TotalCost ?? 0));
        dispatch(
          setRanges(sanitizeRanges(goal.availability || [INITIAL_RANGE]))
        );
        dispatch(setRetryCount(goal.retry_count ?? null));
        dispatch(setDurationBetweenCalls(goal.duration_between_calls ?? "0"));
        dispatch(setRingingDuration(goal.ringing_duration ?? "30"));
        dispatch(
          setUpdatedAt(
            goal.updated_at ? getTimeFromTimestamp(goal.updated_at) : ""
          )
        );
      }
    } catch (err: any) {
      toast.error("Failed to fetch goal details. ", err.message);
    } finally {
      dispatch(setGettingGoal(false));
    }
  }
);
export const updateMultiTranslationGoal = createAsyncThunk(
  "goal/updateTranslationGoal",
  async (
    { router, goalID }: { router: AppRouterInstance; goalID: string },
    { dispatch, getState }
  ) => {
    const state = (getState() as any).businessDashboard.businessDashboardGoals;
    const {
      goalName,
      goalContext,
      country,
      ranges,
      retryCount,
      durationBetweenCalls,
      ringingDuration,
    } = state;
    try {
      if (!goalName || !country || !ranges || ranges.length === 0) {
        throw new Error("Please fill all fields");
      }
      if (
        parseInt(durationBetweenCalls) > 3600 ||
        parseInt(durationBetweenCalls) < 0
      ) {
        throw new Error(
          "Duration between multiple calls should be between 0 and 3600 seconds"
        );
      }
      if (parseInt(ringingDuration) > 120 || parseInt(ringingDuration) < 10) {
        throw new Error(
          "Ringing duration should be between 10 and 120 seconds"
        );
      }
      dispatch(setLoading(true));
      const response = await UpdateMultiTranslationGoal({
        goalID,
        name: goalName,
        country,
        goalContext,
        ranges,
        retry_count: retryCount,
        duration_between_calls: durationBetweenCalls,
        ringing_duration: ringingDuration,
      });
      if (response.success) {
        router.push("/businessDash/assistants");
        toast.success("Goal updated successfully");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to update goal");
    } finally {
      dispatch(setLoading(false));
    }
  }
);

//other than templates
export const getScripts = createAsyncThunk(
  "goal/getScripts",
  async (_, { dispatch }) => {
    try {
      const response = await getEntrepriseScripts();
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      dispatch(setScripts(response.scripts));
    } catch (error) {
      toast.error((error as Error).message);
    }
  }
);

export const checkPhoneHasIncomingGoals = createAsyncThunk(
  "goal/CheckPhoneHasIncomingGoals",
  async ({ phoneID }: { phoneID: string }, { dispatch }) => {
    try {
      const response = await CheckPhoneHasIncomingCallsGoal(phoneID);
      if (!response.success) {
        toast.error(response.error);
      }
      if (response.success) {
        dispatch(setCanSelectIncomingCalls(!response.hasIncomingGoal));
      }
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

const goalSlice = createSlice({
  name: "goal",
  initialState,
  reducers: {
    setRanges: (state, action) => {
      state.ranges = sanitizeRanges(action.payload);
    },
    setGoalId: (state, action) => {
      state.goalId = action.payload;
    },
    setAssistantId: (state, action) => {
      state.assistantId = action.payload;
    },
    setPhoneId: (state, action) => {
      state.phoneId = action.payload;
    },
    setCanSelectIncomingCalls: (state, action) => {
      state.canSelectIncomingCalls = action.payload;
    },
    setCanTransferIncomingCalls: (state, action) => {
      state.canTransferIncomingCalls = action.payload;
    },
    setIncomingSelected: (state, action) => {
      state.incomingSelected = action.payload;
    },
    setGoalName: (state, action) => {
      state.goalName = action.payload;
    },
    setGoalContext: (state, action) => {
      state.goalContext = action.payload;
    },
    setCountry: (state, action) => {
      state.country = action.payload;
    },
    setGoalDuration: (state, action) => {
      state.goalDuration = action.payload;
    },
    setGoalConversations: (state, action) => {
      state.goalConversations = action.payload;
    },
    setMissedCalls: (state, action) => {
      state.missedCalls = action.payload;
    },
    setAnsweredCalls: (state, action) => {
      state.answeredCalls = action.payload;
    },
    setCalledClients: (state, action) => {
      state.calledClients = action.payload;
    },
    setTotalClients: (state, action) => {
      state.totalClients = action.payload;
    },
    setTotalCost: (state, action) => {
      state.totalCost = action.payload;
    },
    setGroupId: (state, action) => {
      state.groupId = action.payload;
    },
    setMessageOnMissedCallContent: (state, action) => {
      state.messageOnMissedCallContent = action.payload;
    },
    setMessagesOnMissedCall: (state, action) => {
      state.messagesOnMissedCall = action.payload;
    },
    setVoiceMailDetection: (state, action) => {
      state.voicemailDropType = action.payload;
    },
    setVoiceMailDetectionContent: (state, action) => {
      state.voicemailDropContent = action.payload;
    },
    setForwardToNumber: (state, action) => {
      state.forwardToNumber = action.payload;
    },
    setAudioUrl: (state, action) => {
      state.audioUrl = action.payload;
    },
    setAudioDuration: (state, action) => {
      state.audioDuration = action.payload;
    },
    setUpdatedAt: (state, action) => {
      state.updatedAt = action.payload;
    },
    setMaleVoice: (state, action) => {
      state.maleVoice = action.payload;
    },
    setFemaleVoice: (state, action) => {
      state.femaleVoice = action.payload;
    },
    setIsPronounceClientHonorificEnabled: (state, action) => {
      state.isPronounceClientHonorificEnabled = action.payload;
    },
    setIsPronounceClientNameEnabled: (state, action) => {
      state.isPronounceClientNameEnabled = action.payload;
    },
    setDurationBetweenCalls: (state, action) => {
      state.durationBetweenCalls = action.payload;
    },
    setRingingDuration: (state, action) => {
      state.ringingDuration = action.payload;
    },
    setRetryCount: (state, action) => {
      state.retryCount = action.payload;
    },
    setGoalType: (state, action) => {
      state.goalType = action.payload;
    },
    setGoalStatus: (state, action) => {
      state.goalStatus = action.payload;
    },
    setRingoverApiKey: (state, action) => {
      state.ringoverApiKey = action.payload;
    },
    setRingoverBaseUrl: (state, action) => {
      state.ringoverBaseUrl = action.payload;
    },
    setGroups: (state, action) => {
      state.groups = action.payload;
    },
    setGoalAssistantNameForFemale: (state, action) => {
      state.goalAssistantNameForFemale = action.payload;
    },
    setGoalAssistantNameForMale: (state, action) => {
      state.goalAssistantNameForMale = action.payload;
    },
    setHumanAudioUrl: (state, action) => {
      state.humanAudioUrl = action.payload;
    },
    setHumanAudioDuration: (state, action) => {
      state.humanAudioDuration = action.payload;
    },
    setHumanIntroductionEnabled: (state, action) => {
      state.humanIntroductionEnabled = action.payload;
    },
    setAgents: (state, action) => {
      state.agents = action.payload;
    },
    setFileFromGoal: (state, action) => {
      state.fileFromGoal = action.payload;
    },
    setScriptContent: (state, action) => {
      state.scriptContent = action.payload;
    },
    setScriptContentOpen: (state, action) => {
      state.scriptContentOpen = action.payload;
    },
    setScripts: (state, action) => {
      state.scripts = action.payload;
    },
    setPromptContent: (state, action) => {
      state.promptContent = action.payload;
    },
    setTextPreview: (state, action) => {
      state.textPreview = action.payload;
    },
    setSelectedScriptType: (state, action) => {
      state.selectedScriptType = action.payload;
    },
    setSelectedScript: (state, action) => {
      state.selectedScript = action.payload;
    },
    // loadings
    setScriptsLoading: (state, action) => {
      state.scriptsLoading = action.payload;
    },
    setGettingGoal: (state, action) => {
      state.gettingGoal = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    // meet specific
    setEnableAIVoice: (state, action) => {
      state.enableAIVoice = action.payload;
    },
    setEnableMute: (state, action) => {
      state.enableMute = action.payload;
    },
    setCrmId: (state, action) => {
      state.crmId = action.payload;
    },
    setCrmToken: (state, action) => {
      state.crmToken = action.payload;
    },
    // reset all
    resetGoalState: () => initialState,
  },
});

export const {
  setRanges,
  setGoalId,
  setAssistantId,
  setPhoneId,
  setIncomingSelected,
  setCanSelectIncomingCalls,
  setCanTransferIncomingCalls,
  setRetryCount,
  setGoalName,
  setGoalContext,
  setCountry,
  setGoalDuration,
  setGoalConversations,
  setMissedCalls,
  setAnsweredCalls,
  setCalledClients,
  setFileFromGoal,
  setGettingGoal,
  setScriptContent,
  setScriptContentOpen,
  setTotalClients,
  setScriptsLoading,
  setScripts,
  setSelectedScriptType,
  setSelectedScript,
  setTotalCost,
  setGroupId,
  setMessageOnMissedCallContent,
  setMessagesOnMissedCall,
  setVoiceMailDetection,
  setVoiceMailDetectionContent,
  setForwardToNumber,
  setAudioUrl,
  setAudioDuration,
  setUpdatedAt,
  setMaleVoice,
  setFemaleVoice,
  setIsPronounceClientHonorificEnabled,
  setIsPronounceClientNameEnabled,
  setDurationBetweenCalls,
  setRingingDuration,
  setGoalType,
  setGoalStatus,
  setRingoverApiKey,
  setRingoverBaseUrl,
  setGroups,
  setLoading,
  setGoalAssistantNameForFemale,
  setGoalAssistantNameForMale,
  setHumanAudioUrl,
  setHumanAudioDuration,
  setHumanIntroductionEnabled,
  setAgents,
  setEnableAIVoice,
  setEnableMute,
  setPromptContent,
  setTextPreview,
  setCrmId,
  setCrmToken,
  resetGoalState,
} = goalSlice.actions;
export default goalSlice.reducer;
