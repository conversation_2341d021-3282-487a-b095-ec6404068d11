"use client";

import React from "react";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import { Label } from "@/components/ui/label";
import CountrySelect from "@/components/region/CountrySelect";
import { Switch } from "@/components/ui/switch";
import { useEffect, useState } from "react";
import {
  getBusinessFilesPresignedUrl,
  GetSignedURL,
} from "@/actions/UploadFiles";
import { toast } from "sonner";
import { BusinessDashSettingsVerify } from "@/verifyFields/BusinessDashSettingsVerify";
import { SaveBusinessDetails } from "@/actions/SaveBusinessDetails";
import { getEntrepriseDetails } from "@/actions/Entreprise";
import { PlusIcon } from "lucide-react";
import CustomInput from "@/components/CustomFormItems/Input";
import CustomButton from "@/components/CustomFormItems/Button";
import { getEntrepriseTotalCosts } from "@/actions/ConversationActions";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import CustomSelect from "@/components/CustomFormItems/Select";
import { useTranslation } from "react-i18next";
import { useTheme } from "next-themes";
import { themes } from "@/providers/ThemeProvider";
import { Languages, SunMoon } from "lucide-react";
import i18nConfig from "@/next-i18next.config.js";

export default function BusinessDashSettings() {
  const [data, setData] = useState<BusinessDetailsFormType>({
    kbis: "",
    siret: "",
    sepa: "",
    rib: "",
    phone: "",
    corname: "",
    country: "",
    city: "",
    street: "",
    appnb: "",
    name: "",
    cin: undefined,
    poa: undefined,
    isOwner: false,
  });
  const [cinPreview, setCinPreview] = useState<string | null>(null);
  const [poaPreview, setPoaPreview] = useState<string | null>(null);
  const [total_cost, set_total_cost] = useState<number>();
  const [costLoading, setCostLoading] = useState(true);
  const [loading, setLoading] = useState(false);

  // Language and theme selection logic
  const { theme, setTheme } = useTheme();
  const { i18n } = useTranslation();
  const { t } = useTranslation("home");

  useEffect(() => {
    document.documentElement.dir = i18n.language === "ar" ? "rtl" : "ltr";
  }, [i18n.language]);

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
  };

  const handleFieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.name === "siret") {
      const value = e.target.value.replace(/\D/g, "");
      setData({
        ...data,
        siret: value.length > 14 ? value.slice(0, 14) : value,
      });
      return;
    }
    if (e.target.name === "cin") {
      const file = e.target.files?.[0];
      if (file) {
        const PreviewUrl = URL.createObjectURL(file);
        setCinPreview(PreviewUrl);
      }
    }
    if (e.target.name === "poa") {
      const file = e.target.files?.[0];
      if (file) {
        const PreviewUrl = URL.createObjectURL(file);
        setPoaPreview(PreviewUrl);
      }
    }
    setData({
      ...data,
      [e.target.name]:
        e.target.type === "file" ? e.target.files?.[0] || null : e.target.value,
    });
  };

  const handleSaveChanges = async () => {
    try {
      const verify = BusinessDashSettingsVerify(data);
      if (!verify.success) {
        toast.error(verify.error);
        return;
      }
      if (data.siret && data.siret.length !== 14) {
        toast.error("SIRET number must be 14 digits long");
        return;
      }

      setLoading(true);
      let cinUrl = "";
      let poaUrl = "";
      if (data.cin) {
        const signedURLResult = await GetSignedURL("cin");
        if (signedURLResult.failure !== undefined) {
          toast(signedURLResult.failure);
          setLoading(false);
          return;
        }
        const url = signedURLResult.success.url;
        cinUrl = url.split("?")[0];
        await fetch(url, {
          method: "PUT",
          body: data.cin,
          headers: {
            "Content-Type": data.cin?.type,
          },
        });
      }
      if (data.poa) {
        const signedURLResult = await GetSignedURL("poa");
        if (signedURLResult.failure !== undefined) {
          toast(signedURLResult.failure);
          setLoading(false);
          return;
        }
        const url = signedURLResult.success.url;
        poaUrl = url.split("?")[0];
        await fetch(url, {
          method: "PUT",
          body: data.poa,
          headers: {
            "Content-Type": data.poa?.type,
          },
        });
      }
      const { ...filteredData } = data;
      const saveResult = await SaveBusinessDetails(
        filteredData,
        cinUrl,
        poaUrl
      );
      if (saveResult.success) {
        toast("Business details uploaded");
        setLoading(false);
      } else {
        toast(saveResult.error);
        setLoading(false);
      }
    } catch (err: any) {
      toast(err.message);
      setLoading(false);
    }
  };

  const ViewFile = (preview: string) => {
    window.open(preview, "_blank");
  };

  const getEntrepriseCostsData = async () => {
    try {
      const response = await getEntrepriseTotalCosts();
      if (!response.success) {
        toast.error(response.error);
        return;
      }
      set_total_cost(response.data.totalCost);
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setCostLoading(false);
    }
  };

  useEffect(() => {
    const fetchEntrepriseDetails = async () => {
      try {
        const response = await getEntrepriseDetails();
        if (!response.success || !response.entreprise) {
          toast.error("Can't fetch business details");
          return;
        }
        console.log(response.entreprise);
        setData({
          kbis: response.entreprise.kbis,
          siret: response.entreprise.siret,
          sepa: response.entreprise.sepa,
          rib: response.entreprise.rib,
          phone: response.entreprise.phone,
          corname: response.entreprise.corname,
          country: response.entreprise.country,
          city: response.entreprise.city,
          street: response.entreprise.street,
          appnb: response.entreprise.appnb,
          name: response.entreprise.name,
          cin: undefined,
          poa: undefined,
          isOwner: response.entreprise.isOwner,
        });
        if (response.entreprise.cin) {
          const cin = await getBusinessFilesPresignedUrl(
            response.entreprise.cin
          );
          console.log(cin);
          if (cin) {
            setCinPreview(cin);
          }
        }
        if (response.entreprise.poa) {
          const poa = await getBusinessFilesPresignedUrl(
            response.entreprise.poa
          );
          console.log(poa);
          if (poa) {
            setPoaPreview(poa);
          }
        }
      } catch (err: any) {
        toast.error(err.message);
      }
    };
    getEntrepriseCostsData();
    fetchEntrepriseDetails();
  }, []);

  return (
    <div className="w-full flex flex-col gap-4">
      <h1 className="w-full max-md:text-center max-sm:px-12 text-2xl sm:text-3xl font-semibold text-voxa-neutral-500 dark:text-voxa-neutral-50">
        Account Settings
      </h1>
      <div className="grid grid-cols-1 sm:grid-cols-3 w-full justify-between items-center bg-sidebar border-sidebar-border border rounded-lg p-3 sm:p-5 gap-3">
        <div className="sm:col-span-2 flex flex-col gap-2 w-full">
          <div className="flex gap-2 items-center">
            <div className="w-2 h-2 bg-voxa-teal-600 rounded-full text-voxa-voxa-teal-600" />
            <h3 className="text-lg sm:text-xl font-semibold text-foreground/70">
              Total Costs of all campaigns
            </h3>
          </div>
          {costLoading ? (
            ""
          ) : (
            <h1 className="max-sm:text-center text-2xl sm:text-4xl font-bold text-voxa-teal-600">
              {total_cost?.toFixed(2)} €
            </h1>
          )}
        </div>
        <div className="p-3 sm:p-4 rounded-md bg-voxa-teal-600 text-white flex items-center justify-between  w-full">
          <div className="flex font-semibold flex-col gap-4">
            <p>Account balance</p>
            <h1 className="text-lg sm:text-2xl">1,000.00 €</h1>
          </div>
          <button className="p-1 bg-white text-voxa-teal-600 rounded-full hover:bg-voxa-teal-600 hover:border-white border hover:text-white transition-all">
            <PlusIcon className="w-5 h-5" />
          </button>
        </div>
      </div>
      <div className="flex flex-col gap-4 mt-4 w-full">
        <p className="text-sm flex gap-2 items-center justify-center text-orange-400">
          <WarningAmberIcon />
          <span className="text-md">
            Upload your business papers here, this will help us verify your
            business and provide you with a better service.
          </span>
        </p>
        <div className="flex flex-col gap-6">
          <Accordion
            type="multiple"
            defaultValue={[
              "business-details",
              "address-details",
              "legal-representant",
              "general-settings",
            ]}
            className="w-full"
          >
            {/* Business Details Section */}
            <AccordionItem value="business-details" className="border-b">
              <AccordionTrigger className="text-lg font-semibold text-zinc-500 dark:text-voxa-neutral-50 hover:no-underline">
                Business Details
              </AccordionTrigger>
              <AccordionContent className="pt-4">
                <div className="grid sm:grid-cols-2 gap-4 w-full">
                  <CustomInput
                    props={{
                      label: "Certificate of Incorperation (KBIS)",
                      name: "kbis",
                      required: true,
                      value: data.kbis,
                      onChange: handleFieldChange,
                    }}
                  />
                  <CustomInput
                    props={{
                      label: "SIRET Number",
                      name: "siret",
                      required: true,
                      value: data.siret,
                      onChange: handleFieldChange,
                      inputMode: "numeric",
                      pattern: "d{14}",
                      maxLength: 14,
                    }}
                  />

                  <CustomInput
                    props={{
                      label: "Sampling Warrant (SEPA)",
                      name: "sepa",
                      required: true,
                      value: data.sepa,
                      onChange: handleFieldChange,
                    }}
                  />

                  <CustomInput
                    props={{
                      label: "Bank Details (RIB)",
                      name: "rib",
                      required: true,
                      value: data.rib,
                      onChange: handleFieldChange,
                    }}
                  />

                  <CustomInput
                    props={{
                      label: "Phone Number",
                      name: "phone",
                      placeholder: "eg. +33 6 12 34 56 78",
                      required: true,
                      value: data.phone,
                      onChange: handleFieldChange,
                    }}
                  />

                  <CustomInput
                    props={{
                      label: "Corporate Name",
                      name: "corname",
                      required: true,
                      value: data.corname,
                      onChange: handleFieldChange,
                    }}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Address Details Section */}
            <AccordionItem value="address-details" className="border-b">
              <AccordionTrigger className="text-lg font-semibold text-zinc-500 dark:text-voxa-neutral-50 hover:no-underline">
                Address Details
              </AccordionTrigger>
              <AccordionContent className="pt-4">
                <CountrySelect data={data} setData={setData} />
                <div className="grid sm:grid-cols-2 gap-4 w-full mt-4">
                  <CustomInput
                    props={{
                      label: "Street and ZIP Code",
                      name: "street",
                      required: true,
                      value: data.street,
                      onChange: handleFieldChange,
                    }}
                  />
                  <CustomInput
                    props={{
                      label: "Address Line 2",
                      name: "appnb",
                      required: true,
                      value: data.appnb,
                      onChange: handleFieldChange,
                    }}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Legal Representant Section */}
            <AccordionItem value="legal-representant" className="border-b">
              <AccordionTrigger className="text-lg font-semibold text-zinc-500 dark:text-voxa-neutral-50 hover:no-underline">
                Legal Representant
              </AccordionTrigger>
              <AccordionContent className="pt-4">
                <div className="grid sm:grid-cols-2 gap-4 w-full">
                  <CustomInput
                    props={{
                      label: "Name",
                      name: "name",
                      required: true,
                      value: data.name,
                      onChange: handleFieldChange,
                    }}
                  />

                  <CustomInput
                    props={{
                      label: (
                        <div className="flex items-center gap-2">
                          <span>Idendity Card</span>
                          {cinPreview && (
                            <button
                              className="text-blue-500 hover:text-blue-500/70 active:text-blue-600 transition-all duration-150"
                              onClick={() => ViewFile(cinPreview)}
                            >
                              View File
                            </button>
                          )}
                        </div>
                      ),
                      name: "cin",
                      required: true,
                      type: "file",
                      onChange: handleFieldChange,
                      accept: "image/*, application/pdf",
                    }}
                  />
                </div>
                <div className="flex items-center space-x-2 mt-4">
                  <Label htmlFor="owner">
                    The Legal Representant {data.isOwner ? "is" : "isn't"} the
                    Owner.
                  </Label>
                  <Switch
                    id="owner"
                    onCheckedChange={(checked) =>
                      setData({ ...data, isOwner: checked })
                    }
                  />
                </div>
                {!data.isOwner && (
                  <div className="grid sm:grid-cols-2 gap-4 w-full mt-4">
                    <CustomInput
                      props={{
                        label: (
                          <div className="flex items-center gap-2">
                            <span>Corporate Power of Attorney</span>
                            {poaPreview && (
                              <button
                                className="text-blue-500 hover:text-blue-500/70 active:text-blue-600 transition-all duration-150"
                                onClick={() => ViewFile(poaPreview)}
                              >
                                View File
                              </button>
                            )}
                          </div>
                        ),
                        name: "poa",
                        required: true,
                        type: "file",
                        onChange: handleFieldChange,
                        accept: "image/*, application/pdf",
                      }}
                    />
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>

            {/* General Settings Section */}
            <AccordionItem value="general-settings" className="border-b">
              <AccordionTrigger className="text-lg font-semibold text-zinc-500 dark:text-voxa-neutral-50 hover:no-underline">
                General Settings
              </AccordionTrigger>
              <AccordionContent className="pt-4">
                <div className="grid sm:grid-cols-2 gap-4 w-full">
                  <CustomSelect
                    label={
                      <div className="flex items-center gap-1.5">
                        <Languages className="h-4 w-4" />
                        <span>{t("settings.0.title")}</span>
                      </div>
                    }
                    placeholder="Select language"
                    value={i18n.language}
                    onValueChange={changeLanguage}
                    items={i18nConfig.i18n.locales.map((code) => ({
                      value: code,
                      label: t(`settings.0.items.${code}`),
                    }))}
                    searchable={true}
                  />

                  <CustomSelect
                    label={
                      <div className="flex items-center gap-1.5">
                        <SunMoon className="h-4 w-4" />
                        <span>{t("settings.1.title")}</span>
                      </div>
                    }
                    placeholder="Select theme"
                    value={theme}
                    onValueChange={setTheme}
                    items={themes.map(({ mode, icon }) => ({
                      value: mode,
                      label: t(`settings.1.items.${mode}`),
                      icon: React.cloneElement(icon, {
                        className: "h-4 w-4",
                      }),
                    }))}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
      <CustomButton
        props={{
          value: loading ? "Uploading..." : "Save Changes",
          className: "mt-3 max-sm:w-full w-fit mx-auto",
          onClick: handleSaveChanges,
          loading: loading,
        }}
      />
    </div>
  );
}
