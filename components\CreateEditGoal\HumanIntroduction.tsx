import React, { Dispatch, SetStateAction } from "react";
import { useTranslation } from "react-i18next";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import AudioRecorder from "@/components/Player/AudioRecorder";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  setHumanIntroductionEnabled,
  setHumanAudioUrl,
  setHumanAudioDuration,
} from "@/redux/BusinessDashboard/subSlices/GoalSlice";

type HumanIntroductionProps = {
  setHumanAudioBlob: Dispatch<SetStateAction<Blob | null>>;
};

export const HumanIntroduction: React.FC<HumanIntroductionProps> = ({
  setHumanAudioBlob,
}) => {
  const { t } = useTranslation("assistants");
  const dispatch = useDispatch<AppDispatch>();

  const { humanIntroductionEnabled, humanAudioUrl } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardGoals
  );

  return (
    <div className="space-y-2">
      <div className="flex max-sm:flex-col sm:items-center gap-2">
        <h1 className="font-semibold text-lg">
          {t("createEditGoal.advanced.humanIntroduction")}
        </h1>
        <div className="flex items-center gap-2">
          <RadioGroup
            value={humanIntroductionEnabled}
            className="flex flex-wrap max-sm:text-xs"
            onValueChange={(value) =>
              dispatch(setHumanIntroductionEnabled(value))
            }
          >
            <div className="flex  text-nowrap items-center space-x-1">
              <RadioGroupItem value="NONE" id="r1" />
              <Label htmlFor="r1">
                {t("createEditGoal.advanced.noIntroduction")}
              </Label>
            </div>
            <div className="flex items-center space-x-1">
              <RadioGroupItem value="AUDIO" id="r2" />
              <Label htmlFor="r2">{t("createEditGoal.advanced.audio")}</Label>
            </div>
          </RadioGroup>
        </div>
      </div>
      <p className="text-voxa-neutral-700 text-sm flex flex-col">
        {t("createEditGoal.advanced.humanIntroTextInfo")}
      </p>
      {humanIntroductionEnabled === "AUDIO" && (
        <AudioRecorder
          setAudioBlob={(blob: Blob | null) => setHumanAudioBlob(blob)}
          audioUrl={humanAudioUrl}
          setAudioUrl={(url: string) => dispatch(setHumanAudioUrl(url))}
          setDuration={(duration: number) =>
            dispatch(setHumanAudioDuration(duration))
          }
        />
      )}
    </div>
  );
};
