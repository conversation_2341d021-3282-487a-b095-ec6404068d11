import { Button } from "@/components/ui/button";
import CustomSelect from "@/components/CustomFormItems/Select";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogHeader,
  DialogTrigger,
  DialogDescription,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { DownloadIcon } from "lucide-react";
import { useSelector } from "react-redux";
import { useState } from "react";
import { RootState } from "@/redux/store";
import html2pdf from "html2pdf.js";
import parrotString from "@/lib/Strings/parrot";

const formatValueForCSV = (value: any) => {
  if (value == null || value == undefined || value == "") return "null";
  if (typeof value === "object") {
    let jsonString = JSON.stringify(value, null, 2);
    jsonString = jsonString.replace(/"([^"]+)":/g, "$1:");
    jsonString = jsonString.replace(/: "([^"]*)"/g, ": $1");
    jsonString = jsonString.replace(/,/g, "");
    return `${jsonString}`;
  }
  return value;
};

const formatValueForPDF = (value: any) => {
  if (value == null || value == undefined || value == "") {
    if (
      typeof value === "object" &&
      Array.isArray(value) &&
      value.length === 0
    ) {
      return <span className="text-black">[]</span>;
    } else {
      return <span className="text-black">null</span>;
    }
  } else if (typeof value === "boolean") {
    return <span className="text-black">{JSON.stringify(value, null, 2)}</span>;
  } else if (typeof value === "object") {
    let jsonString = JSON.stringify(value, null, 2);
    jsonString = jsonString.replace(/: "([^"]*)"/g, ": $1");
    jsonString = jsonString.replace(/,/g, "");
    const htmlString = jsonString.replace(
      /"([^"]+)":/g,
      '<span class="text-[#1e7d73] font-semibold">$1</span>:'
    );
    return (
      <pre
        className="text-black overflow-visible whitespace-pre-wrap"
        dangerouslySetInnerHTML={{ __html: htmlString }}
      />
    );
  }
  return <span className="text-black">{value}</span>;
};

export default function ExportCallDialog() {
  const { callDetails } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardRoot
  );

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [format, setFormat] = useState<string | undefined>(undefined);

  const handleExport = async () => {
    if (!callDetails || !format) return;

    if (format === "csv") {
      const csvContent = Object.entries(callDetails)
        .map(([key, value]) => `${key}: ${formatValueForCSV(value)}`)
        .join("\n");
      const blob = new Blob(["\uFEFF" + csvContent], {
        type: "text/csv;charset=utf-8;",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "call_details.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setDrawerOpen(false);
    }

    if (format === "pdf") {
      const tempContainer = document.createElement("div");
      tempContainer.style.position = "absolute";
      tempContainer.style.left = "-9999px";
      tempContainer.style.top = "-9999px";
      tempContainer.style.visibility = "hidden";
      tempContainer.innerHTML = `
        <div id="pdf-content-temp" style="font-size: 14px; padding-bottom: 6px; position: relative;">
          <div style="text-align: center; margin-bottom: 15px; font-size: 24px; font-weight: bold; color: #1e7d73;transform: translateY(-10px);">
          Call Details
          </div>
          <div className="w-full flex justify-end">
            <div style="position: absolute; top: 0; right: 0; width: 30px; height: 30px; overflow: visible;">${parrotString}</div>*
          </div>
          ${Object.entries(callDetails)
            .map(([key, value]) => {
              const formattedValue = formatValueForPDF(value);
              if (typeof formattedValue === "object" && formattedValue.props) {
                if (formattedValue.type === "pre") {
                  return `<div style="break-inside: avoid; page-break-inside: avoid;">
                    <span style="color: #1e7d73; font-weight: 600;">${key}: </span>
                    <pre style="color: black; overflow: visible; white-space: pre-wrap; word-wrap: break-word;">${formattedValue.props.dangerouslySetInnerHTML.__html}</pre>
                  </div>`;
                } else {
                  return `<div style="break-inside: avoid; page-break-inside: avoid;">
                    <span style="color: #1e7d73; font-weight: 600;">${key}: </span>
                    <span style="color: black;">${formattedValue.props.children}</span>
                  </div>`;
                }
              }
              return `<div style="break-inside: avoid; page-break-inside: avoid;">
                <span style="color: #1e7d73; font-weight: 600;">${key}: </span>
                <span style="color: black;">${value || "null"}</span>
              </div>`;
            })
            .join("")}
        </div>
      `;

      document.body.appendChild(tempContainer);

      const element = document.getElementById("pdf-content-temp");
      const opt = {
        margin: 0.5,
        filename: "call_details.pdf",
        image: { type: "jpeg", quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: "in", format: "letter", orientation: "portrait" },
      };

      try {
        await html2pdf().from(element).set(opt).save();
        setDrawerOpen(false);
      } finally {
        if (document.body.contains(tempContainer)) {
          document.body.removeChild(tempContainer);
        }
      }
    }
  };

  const formatOptions: { value: string; label: string }[] = [
    { value: "csv", label: "CSV" },
    { value: "pdf", label: "PDF" },
  ];
  return (
    <Dialog open={drawerOpen} onOpenChange={setDrawerOpen}>
      <DialogTrigger asChild>
        <div className="size-8 p-0">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outlined"
                className="size-8 rounded-full bg-background"
              >
                <DownloadIcon className="size-6 dark:text-voxa-neutral-200 cursor-pointer" />
              </Button>
            </TooltipTrigger>
            <TooltipContent className="bg-white fill-white dark:fill-sidebar dark:bg-sidebar dark:border-sidebar-border">
              Export call details
            </TooltipContent>
          </Tooltip>
        </div>
      </DialogTrigger>
      <DialogContent className="flex flex-col justify-between max-w-md p-5">
        <DialogHeader>
          <DialogTitle>Export Call Details</DialogTitle>
        </DialogHeader>
        <DialogDescription className="text-sm text-muted-foreground">
          You can export the call details as CSV or PDF format.
        </DialogDescription>
        <div className="flex flex-col">
          <CustomSelect
            label="Select an export format"
            placeholder="Select Format"
            value={format}
            onValueChange={setFormat}
            items={formatOptions.map((option) => ({
              value: option.value,
              label: option.label,
            }))}
          />
        </div>
        <DialogFooter className="mt-4">
          <DialogClose asChild>
            <Button
              type="button"
              className="hover:text-white hover:bg-voxa-neutral-300 dark:hover:bg-voxa-neutral-900 active:bg-voxa-neutral-200 dark:active:bg-voxa-neutral-950"
            >
              Cancel
            </Button>
          </DialogClose>
          <Button
            variant="primary"
            className="w-fit"
            onClick={handleExport}
            disabled={!format}
          >
            Export
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
