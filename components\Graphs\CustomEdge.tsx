import { BaseEdge, EdgeLabelRenderer } from "@reactflow/core";

const getStraightPath = ({ sourceX, sourceY, targetX, targetY }: any) => {
  return [`M${sourceX},${sourceY} L${targetX},${targetY}`];
};

const CustomEdge = ({ id, sourceX, sourceY, targetX, targetY, label }: any) => {
  const [edgePath] = getStraightPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });

  return (
    <>
      <BaseEdge
        id={id}
        path={edgePath}
        style={{
          stroke: "#3b82f6",
          strokeWidth: 2,
        }}
      />
      {label && (
        <EdgeLabelRenderer>
          <div className="absolute text-xs bg-white p-1">{label}</div>
        </EdgeLabelRenderer>
      )}
    </>
  );
};

export default CustomEdge;
