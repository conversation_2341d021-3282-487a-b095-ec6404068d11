"use server";

import { GetObjectCommand, HeadObjectCommand  } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { TwilioLogsS3 } from "@/lib/S3Client";

const BUCKET_NAME = process.env.TWILIO_LOGS_BUCKET_NAME;

export async function getPresignedUrl(key: string) {
    if (!key) {
        throw new Error("File name is required.");
    }

    try {
        // ✅ Check if file exists using HeadObjectCommand
        await TwilioLogsS3.send(new HeadObjectCommand({ Bucket: BUCKET_NAME, Key: key }));

        // ✅ If it exists, generate the presigned URL
        const command = new GetObjectCommand({
            Bucket: BUCKET_NAME,
            Key: key,
            ResponseContentDisposition: "attachment",
        });

        return await getSignedUrl(TwilioLogsS3, command, { expiresIn: 3600 });

    } catch (error: any) {
        if (error.name === "NotFound" || error.$metadata?.httpStatusCode === 404) {
            return null; // File not found
        }
        console.error("S3 Error:", error);
        throw new Error("Failed to fetch file.");
    }
}

