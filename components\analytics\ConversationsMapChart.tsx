"use client";

import { COUNTRIES_OBJ, CountryCode } from "@/lib/countries";
import { convertCountriesToUppercase } from "@/redux/BusinessDashboard/subSlices/AnalyticsSlice";
import { store } from "@/redux/store";
import * as am5 from "@amcharts/amcharts5";
import am5geodata_worldLow from "@amcharts/amcharts5-geodata/worldLow";
import * as am5map from "@amcharts/amcharts5/map";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import { useEffect, useId, useLayoutEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import useSWR from "swr";

type MapData = {
  id: string;
  name: string;
  conversations: number;
};

// SWR fetcher for conversations by country
const fetchConversationsByCountrySWR = async (): Promise<MapData[]> => {
  const filters = store.getState().analytics.filters;
  const {
    startDate,
    endDate,
    conversationTypes,
    countries,
    callDuration,
    ringingTime,
  } = filters;

  const { getConversationsByCountry } = await import(
    "@/actions/AnalyticsActions"
  );
  const res = await getConversationsByCountry(
    startDate ? new Date(startDate) : undefined,
    endDate ? new Date(endDate) : undefined,
    conversationTypes,
    countries.length
      ? convertCountriesToUppercase(countries as CountryCode[])
      : countries,
    callDuration,
    ringingTime
  );

  if (!res.success) {
    throw new Error(res.error || "Failed to fetch conversations by country");
  }
  if (!("data" in res)) {
    throw new Error("No data returned from getConversationsByCountry");
  }
  // Map to MapData[]
  return (res.data as any[]).map((item) => ({
    id: item.country,
    name:
      COUNTRIES_OBJ[item.country?.toLowerCase() as CountryCode]?.name ||
      item.country,
    conversations: item.conversations,
  }));
};

export default function ConversationsMapChart({
  setLoading,
}: {
  setLoading?: (loading: boolean) => void;
}) {
  const chartRef = useRef<am5map.MapChart | null>(null);
  const seriesRef = useRef<am5map.MapPolygonSeries | null>(null);
  const [mapData, setMapData] = useState<MapData[]>([]);
  const currentActive = useRef<am5map.MapPolygon | undefined>(undefined);
  const chartId = useId();
  const uniqueChartId = `mapChart-${chartId}`;

  // Use SWR to fetch conversations by country
  const filters = useSelector((state: any) => state.analytics.filters);
  const {
    data: swrMapData,
    isLoading,
    error,
  } = useSWR<MapData[]>(
    ["conversationsByCountry", filters],
    fetchConversationsByCountrySWR,
    {
      revalidateOnFocus: false,
      dedupingInterval: 10000,
    }
  );

  // Call setLoading when loading state changes
  useEffect(() => {
    if (setLoading) setLoading(isLoading);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading]);

  // Set map data when SWR data changes
  useEffect(() => {
    if (swrMapData) setMapData(swrMapData);
  }, [swrMapData]);

  useLayoutEffect(() => {
    const root = am5.Root.new(uniqueChartId);

    // Create custom theme for colors
    const myTheme = am5.Theme.new(root);
    myTheme.rule("InterfaceColors").setAll({
      primaryButton: am5.color(0x2986cc),
      // primaryButtonHover: am5.Color.lighten(am5.color(0x2986cc), 0.2),
      primaryButtonDown: am5.Color.lighten(am5.color(0x2986cc), -0.2),
      primaryButtonActive: am5.color(0xd9cec8),
    });
    myTheme.rule("Label").setAll({
      fontSize: "0.8em",
    });
    root.setThemes([am5themes_Animated.new(root), myTheme]);
    // root.setThemes([am5themes_Animated.new(root)]);

    var chart = root.container.children.push(
      am5map.MapChart.new(root, {
        panX: "translateX",
        panY: "translateY",
        projection: am5map.geoMercator(),
      })
    );

    const zoomControl = chart.set(
      "zoomControl",
      am5map.ZoomControl.new(root, {})
    );
    zoomControl.homeButton.set("visible", true);

    const polygonSeries = chart.series.push(
      am5map.MapPolygonSeries.new(root, {
        geoJSON: am5geodata_worldLow,
        valueField: "conversations",
        calculateAggregates: true,
      })
    );

    // Create a hover state for the polygons
    const polygonTemplate = polygonSeries.mapPolygons.template;
    polygonTemplate.states.create("hover", {
      fill: am5.color(0x555555),
      stroke: am5.color(0xffffff),
      strokeWidth: 2,
    });

    polygonTemplate.states.create("active", {
      shadowColor: am5.color(0x000000),
      shadowOpacity: 0.5,
      shadowBlur: 10,
      shadowOffsetX: 4,
      shadowOffsetY: 4,
      strokeWidth: 1,
    });

    // polygonTemplate.set("fill", am5.color(0x2986cc));

    // update template with white stroke and new tooltip text
    polygonTemplate.setAll({
      toggleKey: "active",
      interactive: true,
      stroke: am5.color(0xffffff),
    });

    polygonSeries.mapPolygons.template.on(
      "active",
      (value: boolean | undefined, target?: am5map.MapPolygon) => {
        if (currentActive.current) {
          currentActive.current.set("active", false);
        }
        currentActive.current = target;
      }
    );

    // Remove or comment out the existing "active" event handler
    // polygonSeries.mapPolygons.template.on(
    //   "active",
    //   (value: boolean | undefined, target?: am5map.MapPolygon) => {
    //     console.log("target: ", target?.dataItem?.dataContext, value);
    //   }
    // );

    // add heat rules for dynamic country colors based on their value
    polygonSeries.set("heatRules", [
      {
        target: polygonSeries.mapPolygons.template,
        dataField: "value",
        min: am5.color(0xccdfff), // light blue for low intensity
        max: am5.color(0x0044aa), // dark blue for high intensity
        key: "fill",
        logarithmic: true, // logarithmic scale for better distribution
      },
    ]);

    // Add adapter for tooltipHTML instead of tooltipText to handle undefined values and add flags
    polygonSeries.mapPolygons.template.adapters.add(
      "tooltipHTML",
      (text, target) => {
        if (target.dataItem) {
          let countryId = (target.dataItem.dataContext as MapData)?.id;
          let countryName = (target.dataItem.dataContext as MapData)?.name;
          if (countryId === "IL") {
            countryId = "PS";
            countryName = "Palestinian Territories";
          }

          const value = (target.dataItem.dataContext as MapData)?.conversations;

          // Convert country code to lowercase for flagcdn
          const countryCode = countryId?.toLowerCase();

          // Check if country code is valid (2 letters) before showing flag
          const flagHtml =
            countryCode && countryCode.length === 2
              ? `<img src="https://flagcdn.com/16x12/${countryCode}.png" style="vertical-align: middle; margin-right: 5px;" />`
              : "";

          return `<div style="display: flex; align-items: center; padding: 2px;">
                   ${flagHtml}
                   <span><strong>${countryName || "Unknown"}</strong>: ${
            value !== undefined ? value.toLocaleString() : "0"
          } Calls</span>
                 </div>`;
        }
        return text;
      }
    );

    // Create a hover adapter to preserve colors based on data
    polygonTemplate.adapters.add("fill", function (fill, target) {
      const value =
        target.dataItem &&
        (target.dataItem.dataContext as MapData)?.conversations;

      if (target.get("active") || target.isHover()) return fill;

      // If in hover state, apply a completely different color
      //   if (target.get("active") || target.isHover()) {
      //     if (!value) {
      //       return am5.color(0x999999);
      //     }

      //     return am5.color(0x2986cc); // Bright blue color on hover
      //   }

      if (!value) {
        return am5.color(0xcccccc);
      }

      return fill;
    });

    chartRef.current = chart;
    seriesRef.current = polygonSeries;

    return () => root && root.dispose();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useLayoutEffect(() => {
    try {
      if (seriesRef?.current) {
        seriesRef.current.data.setAll(mapData);
      }
    } catch (err) {
      console.error("Error updating map data:", err);
    }
  }, [mapData]);

  return (
    <>
      {/* Optionally show loading/error states */}
      {isLoading ? (
        <div className="w-full h-[500px] flex items-center justify-center text-muted-foreground">
          Loading map data...
        </div>
      ) : error ? (
        <div className="w-full h-[500px] flex items-center justify-center text-red-500">
          Error loading map data
        </div>
      ) : (
        ""
      )}
      <div
        hidden={isLoading || error}
        id={uniqueChartId}
        style={{ width: "100%", height: "500px" }}
      ></div>
    </>
  );
}
